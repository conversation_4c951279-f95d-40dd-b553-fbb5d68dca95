import random
from typing import Annotated, Optional
import uuid
from xxlimited import new
import requests
from fastapi.responses import JSONResponse
from fastapi import APIR<PERSON>er,Depends, Header, Request
from app.helper import StandardResponse, ErrorResponse
from app.schemas import CollectPayment, InitiatePayment, CreateAccount, RefundPayment, RequestPayment, TransactionStatusRequest, UpdateAccount
from app.database import get_db
from sqlalchemy.ext.asyncio import AsyncSession
from app.notification import send_push_notification
from app.utils import StatusType, TransactionTypeType, check_datetime, get_gtipay_transaction_detail, get_id_header,create_record, update_payment_rec, delete_record, update_record, log_activity
from app.models import Invoice, Payment, Transaction, Account, UserProfiles, ArtisanAssigned, InvoiceItem
from uuid import UUID
from app.auth import permission_checker
import json
from app.config import settings
from sqlalchemy.future import select
from sqlalchemy import func, or_, and_, update
# from sqlalchemy.orm import flag_modified
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy.ext.declarative import DeclarativeMeta
from app.models_enum import ArtisanAssignStatus, ActivityType


# from payment.app.routers.transaction import AlchemyEncoder
router = APIRouter()
class AlchemyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj.__class__, DeclarativeMeta):
            # Convert SQLAlchemy model to dictionary
            fields = {}
            for field in [
                x for x in dir(obj) if not x.startswith("_") and x != "metadata"
            ]:
                data = obj.__getattribute__(field)
                try:
                    json.dumps(data)
                    fields[field] = data
                except TypeError:
                    fields[field] = None
            return fields
        return json.JSONEncoder.default(self, obj)
    
@router.post("/initiate-payment")
async def initiate_payment(
    req: Request,
    request: InitiatePayment,
    db: AsyncSession = Depends(get_db),
    # Authorization: Annotated[str, Header()] = None,
    user=Depends(permission_checker),

):
    try:
        print(request, "request")
        request_dict = request.dict()
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        Authorization = req.headers.get("Authorization")
        account_id = user.get("user_id") if isinstance(user, dict) else None
        if request.user_id:
            account_id = request.user_id
        # account_id = '3286fe8e-6da1-48d2-8842-2bbc64cd3346'
        invoice_id = request.invoice_id
        # print(resp.json())
        # if resp.status_code == 200:
        #     resp_json = resp.json()
        #     account_id = request.user_id
        #     invoice_id = request.invoice_id
        # else:
        #     return ErrorResponse(
        #         status_code=400, message="ERROR", error={"error": resp.json()}
        #     )
        u_acc_id = account_id
        result = await db.execute(select(UserProfiles).where(UserProfiles.id == account_id))
        user = result.scalars().first()
        if request_dict['payment_method'].lower() == "wallet":
            u_query = select(Account).filter(Account.user_id == u_acc_id, Account.account_type == request_dict["payment_method"])
            u_result = await db.execute(u_query)
            u_account_obj = u_result.scalars().first()
            if not u_account_obj:
                return ErrorResponse(status_code=404, message=f"Account not found")
        # u_account_obj["email"]=user.email
        # u_account_obj["phone_number"]=user.phone_number
        
        if not invoice_id:
            return ErrorResponse(
                status_code=400,
                message="ERROR",
                error={"error": "Invoice ID is required for payment"},
            )
        i_query = select(Invoice).filter(Invoice.id == invoice_id)
        i_result = await db.execute(i_query)
        invoice_obj = i_result.scalars().first()
        print(invoice_obj, "invoice_obj")
        bill_amt = request.amount if request.amount else invoice_obj.pending_amount

        if bill_amt == 0:
            return ErrorResponse(
                status_code=400,
                message="ERROR",
                error={"error": "No pending amount to pay"},
            )

        request_dict = request.dict()
        new_dict = request_dict
        if request_dict["payment_method"].lower() == "cash":
            new_dict["status"] = "SUCCESS"
            # send_push_notification(
            #     auth_token=Authorization,
            #     title="Payment Confirmed",
            #     message="Payment has been confirmed",
            #     sender_id=account_id,
            #     type="user",
            # )
            # send_push_notification(
            #     auth_token=Authorization,
            #     title="Payment Pending",
            #     message="Please collect cash from the customer",
            #     sender_id=request.artisan_id,
            #     type="service_provider",
            # )

            new_payment = await create_record(db, Payment, {
                "user_id": account_id,
                "invoice_id": request.invoice_id,
                "invoice_item_id": request.invoice_item_id,
                "payment_method": request.payment_method,
                "amount": bill_amt,
                "status": StatusType.SUCCESS,
                "is_multiple": False,
                "currency": request.currency or "GHS",
            })
            if isinstance(new_payment, str):
                return ErrorResponse(
                    status_code=400,
                    message=str(new_payment),
                    error={"error": new_payment},
                )
            new_payment_id = str(new_payment.id)

            user_transaction = Transaction(
                payment_id=new_payment_id,
                from_entity=str(account_id),
                payment_method=request_dict["payment_method"],
                to_entity=str(invoice_obj.sp_id),
                transaction_type=TransactionTypeType.PAYMENT,
                status=StatusType.SUCCESS,
                amount=request_dict["amount"],
            )
            
            u_record = await create_record(db, Transaction, user_transaction.as_dict())


            await db.commit()
            await db.refresh(u_record)

            await log_activity(
                db=db,
                title="Service Fee Paid",
                description=f"Customer paid ₵{bill_amt} service fee",
                reference_id=invoice_obj.booking_id,
                customer_id=account_id,
                activity_type=ActivityType.SERVICE_FEE_PAID,
            )


            # if u_account_balance == 0:
            #     setattr(u_account_obj, "balance", 0)
            #     await db.commit()
            #     await db.refresh(u_account_obj)

            return StandardResponse(
                status_code=200,
                message="Payment completed successfully",
                data={
                    "data": "Payment completed successfully",
                    "payment_id": new_payment_id,
                },
            )
      
        elif request_dict["payment_method"].lower() == "wallet":
            new_dict["status"] = "INIT"
            login_url = f"{settings.WALLET_API_URL}/Authentication/Login"

            login_payload = json.dumps(
                {
                    "username": settings.WALLET_USERNAME,
                    "password": settings.WALLET_PASSWORD,
                }
            )
            login_headers = {"accept": "*/*", "Content-Type": "application/json"}

            login_response = requests.request(
                "POST", login_url, headers=login_headers, data=login_payload
            )
            response_data = login_response.json()
            token = response_data.get("token")
            print(token)
            print(bill_amt, "bill_amt")
            if token:
                wallet_txn_id = str(random.randint(10**13, 10**14 - 1))
                user_transaction = Transaction(
                        payment_id=None,
                        from_entity=str(account_id),
                        payment_method=request_dict["payment_method"],
                        to_entity=str(invoice_obj.sp_id),
                        transaction_type=TransactionTypeType.PAYMENT,
                        status=StatusType.INIT,
                        amount=bill_amt,
                        txn_ref_no=wallet_txn_id
                    )
                u_txn_record = await create_record(db, Transaction, user_transaction.as_dict())

                send_money_url = (f"{settings.WALLET_API_URL}/DebitMoney/DebitMoneyService")
                send_money_payload = json.dumps(
                    {
                        "accountName": u_account_obj.account_details["accountName"],
                        "accountNumber": u_account_obj.account_details["accountNumber"],
                        "amount": request_dict["amount"],
                        "channel": "MNO",
                        "institutionCode": u_account_obj.account_details["institutionCode"],
                        "transactionId": wallet_txn_id,
                        "debitNaration": "JOBCONNECT Service Payment",
                        "currency": "GHS",
                    }
                )

                print(send_money_payload, "payload")

                send_money_headers = {
                    "accept": "text/plain",
                    "Authorization": f"Bearer {token}",  # Use the token from the previous response
                    "Content-Type": "application/json",
                }

                send_money_response = requests.post(
                    send_money_url, headers=send_money_headers, data=send_money_payload
                )

                print(send_money_response.json(), "send_money_response")

                if send_money_response.json()["statusCode"] in ["200", "202"]:
                    print("success")
                    new_dict["status"] = "SUCCESS"
                    new_dict["payment_details"] = json.loads(send_money_response.text)
                    new_dict["user_id"] = account_id
                    new_dict['invoice_item_id'] = request.invoice_item_id
                    new_dict.pop('booking_id', None)
                    print(new_dict, 'new_dict')
                    new_payment = await create_record(db, Payment, new_dict)
                    if isinstance(new_payment, str):
                        return ErrorResponse(
                            status_code=400,
                            message=str(new_payment),
                            error={"error": new_payment},
                        )
                    new_payment_id = str(new_payment.id)

                    # Updating payment_id in transaction entry
                    setattr(u_txn_record, 'payment_id', new_payment_id)
                    await db.commit()
                    await db.refresh(u_txn_record)
                    # user_transaction = Transaction(
                    #     payment_id=new_payment_id,
                    #     from_entity=str(account_id),
                    #     payment_method=request_dict["payment_method"],
                    #     to_entity=str(request_dict["artisan_id"]),
                    #     transaction_type=TransactionTypeType.PAYMENT,
                    #     status=StatusType.SUCCESS,
                    #     amount=total_payable,
                    #     transaction_id=wallet_txn_id
                    # )
                    # u_record = await create_record(db, Transaction, user_transaction.as_dict())
                    # setattr(
                    #     a_account_obj,
                    #     "balance",
                    #     a_account_balance + request_dict["base_service_fee"],
                    # )
                    # await db.commit()
                    # await db.refresh(a_account_obj)

                    # if u_account_balance == 0:
                    #     setattr(u_account_obj, "balance", 0)
                    #     await db.commit()
                    #     await db.refresh(u_account_obj)

                    await log_activity(
                        db=db,
                        title="Service Fee Paid",
                        description=f"Customer paid ₵{bill_amt} service fee",
                        reference_id=invoice_obj.booking_id,
                        customer_id=account_id,
                        activity_type=ActivityType.SERVICE_FEE_PAID,
                    )

                    return StandardResponse(
                        status_code=200,
                        message="Payment completed successfully",
                        data={
                            "message": "Payment completed successfully",
                            "payment_id": new_payment_id,
                            "invoice_id": request_dict["invoice_id"],
                            "payment_details": json.loads(send_money_response.text),
                        },
                    )
                else:
                    return ErrorResponse(
                        status_code=500,
                        message="Payment Failed",
                        error={"error": send_money_response.text},
                    )

        elif request_dict["payment_method"].lower() == "card":
            new_dict["status"] = "INIT"
            try:
                user_transaction = Transaction(
                        payment_id=None,
                        from_entity=str(account_id),
                        payment_method=request_dict["payment_method"],
                        to_entity=str(invoice_obj.sp_id),
                        transaction_type=TransactionTypeType.PAYMENT,
                        status=StatusType.INIT,
                        amount=request_dict["amount"],
                    )

                u_record = await create_record(db, Transaction, user_transaction.as_dict())
                print(new_dict["status"])
                country_code = user.country_code.replace("+", "")

                url = f"{settings.CARD_API_URL}/open/orders/create"
                payload = json.dumps(
                  
                    {
                        "data": {
                            "action": "SALE",
                            "class": "ECOM",
                            "capture_method": "AUTOMATIC",
                           
                            "customer_details": {
                                "m_customer_id": str(account_id),
                                "name": user.first_name + " " + user.last_name,
                                "email": user.email,
                                "mobile": user.phone_number,
                                "code": country_code,
                            },
                            "billing_details": {
                                "address_line1": "123 Main St",
                                "city": "Accra",
                                "country": "GH",
                              
                            },
                            "shipping_details": {
                                "address_line1": "123 Main St",
                                "city": "Accra",
                                "country": "GH",
                               
                            },
                            "order_details": {
                                "m_order_id": str(request_dict["invoice_id"]),
                                "amount": bill_amt,
                                "currency": request_dict["currency"],
                                "description": "Jobconnectz Service Payment",
                            },
                            "urls": {},
                        }
                    }
                )
                print(payload)
                headers = {
                    "merchant-key": settings.CARD_MERCHANT_KEY,
                    "merchant-secret": settings.CARD_MERCHANT_SECRET,
                    "Content-Type": "application/json",
                    "Cookie": "AWSALB=8JFTC8y88vPc/fSN6IXX+pa8VVP3meHjNy1KdAi0FZKpLgeNlmJ+rEvky4Ho+hgTHDytrBWiSPWtRHOQMwqX1ZTfVz+RlkwVPg52gtFLiyRyU1iEDJU2Yf82Lq7K; AWSALBCORS=8JFTC8y88vPc/fSN6IXX+pa8VVP3meHjNy1KdAi0FZKpLgeNlmJ+rEvky4Ho+hgTHDytrBWiSPWtRHOQMwqX1ZTfVz+RlkwVPg52gtFLiyRyU1iEDJU2Yf82Lq7K",
                }
                response = requests.request("POST", url, headers=headers, data=payload)

                print(response.text)

                if json.loads(response.text)["status"] == "SUCCESS":
                    print("success")
                    p_order_id = json.loads(response.text)["p_order_id"]
                    payment_link = json.loads(response.text)["payment_link"]
                    new_dict["status"] = "SUCCESS"
                    new_dict["user_id"] = account_id
                    new_dict['invoice_item_id'] = request.invoice_item_id
                    new_dict.pop('booking_id', None)
                    new_dict["payment_details"] = json.loads(response.text)
                    new_payment = await create_record(db, Payment, new_dict)

                    if isinstance(new_payment, str):
                        return ErrorResponse(
                            status_code=400,
                            message=str(new_payment),
                            error={"error": new_payment},
                        )
                    new_payment_id = str(new_payment.id)

                    # Updating payment_id & transaction_id in transaction entry
                    setattr(u_record, 'payment_id', new_payment_id)
                    setattr(u_record, 'txn_ref_no', p_order_id)
                    await db.commit()
                    await db.refresh(u_record)
                    # user_transaction = Transaction(
                    #     payment_id=new_payment_id,
                    #     from_entity=str(account_id),
                    #     payment_method=request_dict["payment_method"],
                    #     to_entity=str(request_dict["artisan_id"]),
                    #     transaction_type=TransactionTypeType.PAYMENT,
                    #     status=StatusType.SUCCESS,
                    #     amount=total_payable,
                    #     transaction_id=p_order_id,
                    # )

                    # u_record = await create_record(
                    #     db, Transaction, user_transaction.as_dict()
                    # )

                    # setattr(
                    #     a_account_obj,
                    #     "balance",
                    #     a_account_balance + request_dict["base_service_fee"],
                    # )
                    # await db.commit()
                    # await db.refresh(a_account_obj)

                    # if u_account_balance == 0:
                    #     setattr(u_account_obj, "balance", 0)
                    #     await db.commit()
                    #     await db.refresh(u_account_obj)

                    await log_activity(
                        db=db,
                        title="Service Fee Paid",
                        description=f"Customer paid ₵{bill_amt} service fee",
                        reference_id=invoice_obj.booking_id,
                        customer_id=account_id,
                        activity_type=ActivityType.SERVICE_FEE_PAID,
                    )

                    print("payment_link", payment_link)
                    return StandardResponse(
                        status_code=200,
                        message="Payment completed successfully",
                        data={
                            "data": "Payment completed successfully",
                            "p_order_id": p_order_id,
                            "payment_id": new_payment_id,
                            "payment_link": payment_link,
                        },
                    )
                else:
                    return ErrorResponse(
                        status_code=500, message="ERROR", error={"error": response.text}
                    )
            except Exception as e:
                return ErrorResponse(status_code=500, message=f"Error: {e}")

    except Exception as e:
        return ErrorResponse(status_code=500, message="Internal server error", error=str(e))


@router.post("/collect-payment")
async def collect_payment(
    request: CollectPayment,
    db: AsyncSession = Depends(get_db),
    # Authorization: Annotated[str, Header()] = None,
    user=Depends(permission_checker)
):
    """
    Collect payment from the customer.
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        artisan_assign_id = request.artisan_assign_id
        if artisan_assign_id:
            artisan_assign = await db.get(ArtisanAssigned, artisan_assign_id)
            if not artisan_assign:
                return ErrorResponse(
                    status_code=404,
                    message="ERROR",
                    error={"error": "Artisan assignment not found"},
                )
        
        # Authorization = request.headers.get("Authorization")
        account_id = user.get("user_id") if isinstance(user, dict) else None
        invoice_id = request.invoice_id

        if not invoice_id:
            return ErrorResponse(
                status_code=400,
                message="ERROR",
                error={"error": "Invoice ID is required for payment"},
            )
        i_query = select(Invoice).filter(Invoice.id == invoice_id)
        i_result = await db.execute(i_query)
        invoice_obj = i_result.scalars().first()
        if not i_result:
            return ErrorResponse(
                status_code=404,
                message="ERROR",
                error={"error": "Invoice not found"},
            )

        # # Upating invoice item status to PAID
        # invoice_item_data = {"status": "PAID"}
        # invoice_item_query = select(InvoiceItem).filter(InvoiceItem.id == artisan_assign.invoice_item_id).update(**invoice_item_data, synchronize_session=False)
        # await db.execute(invoice_item_query)
        # await db.commit()

        # Fetch the InvoiceItem object
        result = await db.execute(
            select(InvoiceItem).where(InvoiceItem.id == artisan_assign.invoice_item_id)
        )
        invoice_item = result.scalar_one_or_none()

        # Update and commit if it exists
        if invoice_item:
            invoice_item.status = "PAID"
            await db.commit()

        pending_invoice_query = select(InvoiceItem).filter(InvoiceItem.invoice_id == invoice_id, InvoiceItem.status == 'PENDING')
        pending_invoice_res = await db.execute(pending_invoice_query)
        pending_invoice_items = pending_invoice_res.scalars().all()
        if len(pending_invoice_items) == 0:
            # Updating invoice status to PAID if all items are paid
            invoice_data = {"payment_status": "PAID", "pending_amount": 0}
            res = await update_payment_rec(db, invoice_data, invoice_obj)
        else:
            invoice_item_obj = await db.get(InvoiceItem, artisan_assign.invoice_item_id)
            # Updating pending amount in invoice
            invoice_data = {"pending_amount": invoice_obj.pending_amount - invoice_item_obj.total_amount}
            res = await update_payment_rec(db, invoice_data, invoice_obj)

        # Updating Artisan status to COMPLETED
        # if artisan_assign:
        #     artisan_assign.status = ArtisanAssignStatus.COMPLETED
        #     await db.commit()
        #     await db.refresh(artisan_assign)


        if isinstance(res, str):
            return ErrorResponse(
                status_code=400,
                message=str(res),
                error={"error": res},
            )
        # artisan_assign = db.query(ArtisanAssigned).filter(ArtisanAssigned.id == account_id).first()
        # artisan_assign.status = ArtisanAssignStatus.COMPLETED
        # await db.commit()
        # await db.refresh(artisan_assign)
        # Logic to collect payment goes here
        # For example, you can call the initiate_payment function with the request data
        # artisan_assign = db.query(ArtisanAssigned).filter(ArtisanAssigned.id == account_id).first()
        # artisan_assign.status = ArtisanAssignStatus.COMPLETED
        # await db.commit()
        # await db.refresh(artisan_assign)
        
        return StandardResponse(
            status_code=200,
            message="Payment collected successfully",
            data={"invoice_id": invoice_id},
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")
    

@router.post("/refund-payment")
async def refund_payment(
    request: Request,
    request_data: RefundPayment,
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker)
):
    """
    Update transaction status, gateway_status, and gateway_response in the Payment and Transaction tables.
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
                
        request_amount = request_data.amount
        user_id = request_data.user_id
        payment_method = request_data.payment_method
        p_order_id = request_data.p_order_id
        payment_id = request_data.payment_id
        is_amount_refunded = False
        cancellation_id = request_data.cancellation_id

        print(payment_method, 'payment_method')
        print(payment_id, 'payment_id')
        print(request_amount, 'request_amount')


        account_id = uuid.UUID(user_id)
    
        if payment_method == "wallet":
            u_query = select(Account).filter(Account.user_id == account_id, Account.account_type == "WALLET")
            u_result = await db.execute(u_query)
            u_account_obj = u_result.scalars().first()
            u_account_details = u_account_obj.account_details
            if u_account_obj is None:
                return ErrorResponse(status_code=404, message="Account not found")
            
            u_app_query = select(Account).filter(Account.user_id == account_id, Account.account_type == "APP")
            u_app_result = await db.execute(u_app_query)
            u_account_app_obj = u_app_result.scalars().first()
            u_account_balance = u_account_app_obj.balance

            # Authenticate with the wallet API
            login_url = f"{settings.WALLET_API_URL}/Authentication/Login"
            login_payload = json.dumps(
                {
                    "username": settings.WALLET_USERNAME,
                    "password": settings.WALLET_PASSWORD,
                }
            )
            login_headers = {"accept": "*/*", "Content-Type": "application/json"}
            login_response = requests.request(
                "POST", login_url, headers=login_headers, data=login_payload
            )
            auth_token = json.loads(login_response.text)["token"]
            print("auth_token", auth_token)
            if auth_token:
                transact_id = str(random.randint(10**13, 10**14 - 1))
                # Creating Transaction Entry
                a_transaction = Transaction(
                    payment_id=payment_id,
                    from_entity=str(account_id),
                    payment_method=payment_method.upper(),
                    to_entity=str(account_id),
                    transaction_type=TransactionTypeType.REFUND,
                    status=StatusType.INIT,
                    amount=request_amount,
                    txn_ref_no=transact_id,
                    cancellation_ref_id=cancellation_id
                )
                u_record = await create_record(db, Transaction, a_transaction.as_dict())

                receive_money_payload = json.dumps(
                    {
                        "accountName": u_account_details["accountName"],
                        "accountNumber": u_account_details["accountNumber"],
                        "amount": request_amount,
                        "channel": "MNO",
                        "institutionCode": u_account_details["institutionCode"],
                        "transactionId": transact_id,
                        "creditNaration": "JOBCONNECTZ REFUND",
                        "currency": "GHS",
                        "currencyAmount": 0.00,
                        "originCountryCode": "",
                        "senderName": "jobconnectz",
                    }
                )
                receive_money_url = (
                    f"{settings.WALLET_API_URL}/SendMoney/SendMoneyService"
                )
                receive_money_headers = {
                    "accept": "text/plain",
                    "Authorization": f"Bearer {auth_token}",  # Use the token from the previous response
                    "Content-Type": "application/json",
                }
                receive_money_response = requests.post(
                    receive_money_url,
                    headers=receive_money_headers,
                    data=receive_money_payload,
                )
                print(receive_money_response.json(), "receive_money_response")
                receive_money_resp = receive_money_response.json()

                if receive_money_resp["statusCode"] in ["200", "202"]:
                    is_amount_refunded = True
                    # Updating Account Balance after payment refund through wallet
                    # setattr(u_account_app_obj, "balance", u_account_balance + request_amount)
                    # await db.commit()
                    # await db.refresh(u_account_app_obj)
                else:
                    is_amount_refunded = False
            else:
                return ErrorResponse(
                    status_code=400,
                    message="ERROR",
                    error={"error": "Wallet authentication failed"},
                )
        elif payment_method == "card":
            # Getting transaction details from GTI API
            trans_resp = get_gtipay_transaction_detail(p_order_id)
            print(trans_resp, "trans respppp")
            if trans_resp.get("status") == "success":
                transact_id = trans_resp["data"]["transaction_history"][-1]["transaction_id"]
            else:
                print(trans_resp, "error getting transaction details")
                return ErrorResponse(
                    status_code=400, message="ERROR", error={"error": str(trans_resp)}
                )
            
            # Creating Transaction Entry
            a_transaction = Transaction(
                payment_id=payment_id,
                from_entity=str(account_id),
                payment_method=payment_method.upper(),
                to_entity=str(account_id),
                transaction_type=TransactionTypeType.REFUND,
                status=StatusType.INIT,
                amount=request_amount,
                txn_ref_no=p_order_id,
                cancellation_ref_id=cancellation_id
            )
            u_record = await create_record(db, Transaction, a_transaction.as_dict())
            
            url = f"{settings.CARD_API_URL}/open/orders/transaction"
            receive_money_payload = json.dumps(
                {
                    "action": "REFUND",
                    "transaction_id": transact_id,
                    "amount": {"currencyCode": "GHS", "value": request_amount},
                    "reason": "jobconnectz refund",
                }
            )
            print(receive_money_payload, "receive_money_payload")
            # print(x)

            headers = {
                "merchant-key": settings.CARD_MERCHANT_KEY,
                "merchant-secret": settings.CARD_MERCHANT_SECRET,
                "Content-Type": "application/json",
                "Cookie": "AWSALB=8JFTC8y88vPc/fSN6IXX+pa8VVP3meHjNy1KdAi0FZKpLgeNlmJ+rEvky4Ho+hgTHDytrBWiSPWtRHOQMwqX1ZTfVz+RlkwVPg52gtFLiyRyU1iEDJU2Yf82Lq7K; AWSALBCORS=8JFTC8y88vPc/fSN6IXX+pa8VVP3meHjNy1KdAi0FZKpLgeNlmJ+rEvky4Ho+hgTHDytrBWiSPWtRHOQMwqX1ZTfVz+RlkwVPg52gtFLiyRyU1iEDJU2Yf82Lq7K",
            }
            response = requests.request(
                "POST", url, headers=headers, data=receive_money_payload
            )
            print(response.text, "response")
            receive_money_resp = response.json()
            # print(resp_json, "resp_json")
            if receive_money_resp["status"].upper() == "SUCCESS":
                is_amount_refunded = True
            else:
                is_amount_refunded = False
        else:
            return ErrorResponse(status_code=400, message="Invalid payment method")

        if is_amount_refunded:
            pass
        else:
            return ErrorResponse(
                status_code=500,
                message="ERROR",
                error={"error": receive_money_resp.get("statusDesc", "")})
        
        return StandardResponse(
            status_code=200,
            message="Refund initiated successfully",
            data="Refund initiated successfully",
        )
    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message=f"Error: {e}")

@router.post("/request-payment")
async def request_payment(
    request: Optional[RequestPayment] = None,
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker)
):
    """
    Update transaction status, gateway_status, and gateway_response in the Payment and Transaction tables.
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        account_id = user.get("user_id") if isinstance(user, dict) else None
        
        request_amount = request.amount if request and request.amount else None
        payment_mode = request.payment_mode if request and request.payment_mode else "wallet"
        payment_method = payment_mode.upper()
 
        acc_id = uuid.UUID(account_id)
        a_query = select(Account).filter(Account.user_id == acc_id)
        a_result = await db.execute(a_query)
        accounts = a_result.scalars().all()
        
        if accounts is None:
            return ErrorResponse(status_code=404, message="Account not found")

        app_account = next((acc for acc in accounts if acc.account_type.upper() == "APP"), None)
        print(app_account, "app_account")
        # a_account_balance = app_account.get("balance", 0) if app_account else 0
        a_account_balance = app_account.balance if app_account and app_account.balance else 0
        print(a_account_balance, "a_account_balance")
        # account_details = next((acc for acc in accounts if acc.get("account_type", "").lower() == payment_mode.upper()), None)
        account_details = next((acc for acc in accounts if acc.account_type.upper() == payment_mode.upper()), None)
        request_amount = a_account_balance
        new_dict = account_details

        # # Find the payment
        # query = select(Payment).filter(Payment.artisan_id == account_id)
        # result = await db.execute(query)
        # payments = result.scalars().all()

        if a_account_balance == 0:
            return ErrorResponse(status_code=404, message="Your account balance is 0")

        if request_amount is None or request_amount == 0:
            request_amount = a_account_balance

        print(request_amount, "request_amount")
        print(a_account_balance, "a_account_balance")

        if a_account_balance < request_amount:
            return ErrorResponse(
                status_code=404,
                message="Your account balance is less than the requested amount",
            )

        trans_query = (
            select(Transaction)
            .filter(
                Transaction.from_entity == str(acc_id),
                Transaction.to_entity == str(acc_id),
                Transaction.transaction_type == "PAYOUT",
            )
            .order_by(Transaction.created_at.desc())
            .limit(1)
        )
        trans_result = await db.execute(trans_query)
        trans_obj = trans_result.scalars().first()

        if trans_obj:
            if not check_datetime(trans_obj.created_at):
                return ErrorResponse(
                    status_code=404,
                    message="You can only request payment once in 24 hours",
                )

        # Authenticate with the wallet API
        login_url = f"{settings.WALLET_API_URL}/Authentication/Login"
        login_payload = json.dumps(
            {
                "username": settings.WALLET_USERNAME,
                "password": settings.WALLET_PASSWORD,
            }
        )
        login_headers = {"accept": "*/*", "Content-Type": "application/json"}
        login_response = requests.request(
            "POST", login_url, headers=login_headers, data=login_payload
        )
        auth_token = json.loads(login_response.text)["token"]
        print("auth_token", auth_token)
        if auth_token:
            enquiry_url = f"{settings.WALLET_API_URL}/NameEnquiry/NameEnquiryService"
            # temp_account_obj = json.dumps(account_details["account_details"], cls=AlchemyEncoder)
            temp_account_obj = account_details
            print(temp_account_obj, "checkpoint2")
            # temp_account_obj = json.loads(temp_account_obj)
            print(temp_account_obj, type(temp_account_obj), "checkpoint")

            account_details = temp_account_obj.account_details or {}
            wallet_number = account_details.get("accountNumber")
            wallet_code = account_details.get("institutionCode")
            print(wallet_number, wallet_code, "checkpoint3")


            if not wallet_number or not wallet_code:
                return ErrorResponse(
                    status_code=422,
                    message="Missing wallet details",
                    error={"error": "accountNumber or institutionCode is missing"},
                )
            
            enquiry_payload = {
                "accountNumber": wallet_number,
                "institutionCode": wallet_code,
                "transactionId": str(random.randint(10**13, 10**14 - 1)),
                "channel": "MNO",
            }

            print(enquiry_payload, "checkpoint1-----------")
            enquiry_headers = {
                "accept": "text/plain",
                "Authorization": f"Bearer {auth_token}",  # Use the token from the previous response
                "Content-Type": "application/json",
            }
            print(enquiry_payload, enquiry_headers, "checkpoint2==========")
            # enquiry_resonse = requests.request(
            #     "POST", enquiry_url, headers=enquiry_headers, data=enquiry_payload
            # )
            try:
                enquiry_resonse = requests.post(
                    enquiry_url,
                    headers=enquiry_headers,
                    json=enquiry_payload  # Automatically serializes to JSON
                )
            except requests.RequestException as e:
                # Network-level error handling
                return ErrorResponse(
                    status_code=500,
                    message="ERROR",
                    error={"error": f"Request failed: {str(e)}"},
                )
            print(enquiry_resonse.text, "enquiry_resonse")
            response_data = json.loads(enquiry_resonse.text)
            print(response_data, "response_data")

            if json.loads(enquiry_resonse.text)["statusCode"] == "200":
                # print(enquiry_resonse.text)
                # temp_account_obj["account_details"]["accountName"] = json.loads(
                #     enquiry_resonse.text
                # )["accountName"]
                account_name = response_data["data"].get("accountName")
                account_number = response_data["data"].get("accountNumber")
                if not account_name or not account_number:
                    return ErrorResponse(
                        status_code=400,
                        message="Missing account details from wallet name enquiry",
                        error={"error": "accountName or accountNumber not found"},
                    )
                # temp_account_obj["account_details"]["accountName"] = account_name
                # temp_account_obj["account_details"]["accountNumber"] = account_number
                # request_dict["account_details"]["accountNumber"] = json.loads(enquiry_resonse.text)["accountNumber"]
            else:
                return ErrorResponse(
                    status_code=400,
                    message="ERROR",
                    error={"error": "Wallet enquiry failed"},
                )
        else:
            return ErrorResponse(
                status_code=400,
                message="ERROR",
                error={"error": "Wallet authentication failed"},
            )
        # payment_balance = 0
        # for payment in payments:
        #     print(payment.base_service_fee, "feeeeeeeeeeee")
        #     payment_balance += payment.base_service_fee
        transact_id = str(random.randint(10**13, 10**14 - 1))
        if payment_mode == "bank":
            receive_money_payload = json.dumps(
                {
                    "accountName": temp_account_obj.account_details["accountName"],
                    "accountNumber": temp_account_obj.account_details["accountNumber"],
                    "amount": request_amount,
                    "channel": "INTERBANK",
                    "institutionCode": temp_account_obj.account_details["bankCode"],
                    "transactionId": transact_id,
                    "creditNaration": "JOBCONNECT Service Payment",
                    "currency": "GHS",
                    "currencyAmount": 0.00,
                    "originCountryCode": "",
                    "senderName": "jobconnectz",
                }
            )
        elif payment_mode == "wallet":
            receive_money_payload = json.dumps(
                {
                    "accountName": temp_account_obj.account_details["accountName"],
                    "accountNumber": temp_account_obj.account_details["accountNumber"],
                    "amount": request_amount,
                    "channel": "MNO",
                    "institutionCode": temp_account_obj.account_details["institutionCode"],
                    "transactionId": transact_id,
                    "creditNaration": "JOBCONNECT Service Payment",
                    "currency": "GHS",
                    "currencyAmount": 0.00,
                    "originCountryCode": "",
                    "senderName": "jobconnectz",
                }
            )
        else:
            return ErrorResponse(status_code=400, message="Invalid payment mode")

        # Creating Transaction Entry
        a_transaction = Transaction(
                payment_id=None,
                from_entity=str(account_id),
                payment_method=payment_method,
                to_entity=str(account_id),
                transaction_type=TransactionTypeType.PAYOUT,
                status=StatusType.INIT,
                amount=request_amount,
                txn_ref_no=transact_id,
            )
        u_record = await create_record(db, Transaction, a_transaction.as_dict())
        
        receive_money_url = f"{settings.WALLET_API_URL}/SendMoney/SendMoneyService"
        receive_money_headers = {
            "accept": "text/plain",
            "Authorization": f"Bearer {auth_token}",  # Use the token from the previous response
            "Content-Type": "application/json",
        }

        receive_money_response = requests.post(
            receive_money_url, headers=receive_money_headers, data=receive_money_payload
        )

        print(receive_money_response.json(), "receive_money_response")
        receive_money_resp = receive_money_response.json()
        if receive_money_resp["statusCode"] in ["200", "202"]:
            print("withdraw success")
            # new_dict["status"] = "SUCCESS"
            # new_dict["payment_details"] = json.loads(receive_money_response.text)
            # a_transaction = Transaction(
            #     payment_id=transact_id,
            #     from_entity=str(account_id),
            #     payment_method=payment_method,
            #     to_entity=str(account_id),
            #     transaction_type=TransactionTypeType.PAYOUT,
            #     status=StatusType.SUCCESS,
            #     amount=request_amount,
            #     transaction_id=transact_id,
            # )
        else:
            # a_transaction = Transaction(
            #     payment_id=transact_id,
            #     from_entity=str(account_id),
            #     payment_method=payment_method,
            #     to_entity=str(account_id),
            #     transaction_type=TransactionTypeType.PAYOUT,
            #     status=StatusType.FAILED,
            #     amount=request_amount,
            #     transaction_id=transact_id,
            # )
            return ErrorResponse(
                status_code=500,
                message="ERROR",
                error={"error": receive_money_resp.get("statusDesc", "")},
            )

        # Updating Account Balance after payment disbursement
        # setattr(app_account, "balance", a_account_balance - request_amount)
        # await db.commit()
        # await db.refresh(app_account)
        return StandardResponse(
            status_code=200,
            message="Payment disbursed successfully",
            data="Payment disbursed successfully",
        )

    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message=f"Error: {e}")



@router.post("/payment-status")
async def get_payment_status(
    request: TransactionStatusRequest,
    db: AsyncSession = Depends(get_db),
    # Authorization: Annotated[str, Header()] = None,
    user=Depends(permission_checker)
):
    """
    Get the status of a payment by its ID.
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user


        payment_id = request.payment_id
        invoice_id = request.invoice_id

        query = select(Transaction).filter(Transaction.payment_id == payment_id, Transaction.transaction_type == 'PAYMENT').order_by(Transaction.created_at.desc()).limit(1)
        result = await db.execute(query)
        trans_obj = result.scalars().first()
        print(trans_obj, "trans_obj")

        if not trans_obj:
            return ErrorResponse(status_code=404, message="Transaction not found")
        
        status = trans_obj.status
        print(status, "status")
        if status != 'INIT':
            payment_query = select(Payment).filter(Payment.id == payment_id)
            payment_result = await db.execute(payment_query)
            payment_obj = payment_result.scalars().first()
            payment_obj.status = status
            await db.commit()
            await db.refresh(payment_obj)

            if status == 'SUCCESS':
                i_query = select(Invoice).filter(Invoice.id == invoice_id)
                i_result = await db.execute(i_query)
                invoice_obj = i_result.scalars().first()
                
                # Payment happened for invoice
                if not payment_obj.invoice_item_id:
                    # Updating invoice status to PAID
                    invoice_data = {"payment_status": "PAID", "pending_amount": 0}
                    await update_payment_rec(db, invoice_data, invoice_obj)

                    # # Upating invoice item status to PAID
                    # invoice_item_data = {"status": "PAID"}
                    # invoice_item_query = select(InvoiceItem).filter(InvoiceItem.invoice_id == invoice_id).update(**invoice_item_data, synchronize_session=False)
                    # await db.execute(invoice_item_query)
                    # await db.commit()
                    invoice_item_query = (update(InvoiceItem).where(InvoiceItem.invoice_id == invoice_id).values(status="PAID").execution_options(synchronize_session=False))
                    await db.execute(invoice_item_query)
                    await db.commit()
                else:
                    # Payment happened for invoice item
                    invoice_data = {"pending_amount": invoice_obj.pending_amount - trans_obj.amount}

                    pending_invoice_query = select(InvoiceItem).filter(InvoiceItem.invoice_id == invoice_id, InvoiceItem.status == 'PENDING')
                    pending_invoice_res = await db.execute(pending_invoice_query)
                    pending_invoice_items = pending_invoice_res.scalars().all()
                    if len(pending_invoice_items) == 0:
                        invoice_data["payment_status"] = "PAID"

                    # Updating invoice status to PAID
                    await update_payment_rec(db, invoice_data, invoice_obj)

                    # Upating invoice item status to PAID
                    # invoice_item_data = {"status": "PAID"}
                    # invoice_item_query = select(InvoiceItem).filter(or_(InvoiceItem.id == payment_obj.invoice_item_id, InvoiceItem.parent_id == payment_obj.invoice_item_id)).update(**invoice_item_data, synchronize_session=False)
                    # await db.execute(invoice_item_query)
                    # await db.commit()
                    invoice_item_query = (update(InvoiceItem).where(or_(InvoiceItem.id == payment_obj.invoice_item_id, InvoiceItem.parent_id == payment_obj.invoice_item_id)).values(status="PAID").execution_options(synchronize_session=False))
                    await db.execute(invoice_item_query)
                    await db.commit()
            else:
                pass
        return StandardResponse(
            status_code=200,
            message="Transaction fetched Successfully",
            data=trans_obj.as_dict(),
            # data={"status": "COMPLETED", "payment_id": str(payment_id)},
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")
# ================================
# Account Routes
# ================================
@router.post("/account-create")
async def create_account(
    request_obj: CreateAccount,
    db: AsyncSession = Depends(get_db),
    # Authorization: Annotated[str, Header()] = None,
    user=Depends(permission_checker)
):
    """
    Create a new Account record.
    """
    try:
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user

        # resp = await get_id_header(Authorization)
        # if resp.status_code != 200:
        #     return ErrorResponse(
        #         status_code=400, message="ERROR", error={"error": resp.json()}
        #     )

        request_dict = request_obj.dict()
        request_dict["account_type"] = request_dict["account_type"].upper()
        # print(request_dict["accountNumber"] and request_dict["institutionCode"])
        if request_dict["account_details"] and request_dict["account_details"].get(
            "accountNumber", None
        ):
            login_url = f"{settings.WALLET_API_URL}/Authentication/Login"

            login_payload = json.dumps(
                {
                    "username": settings.WALLET_USERNAME,
                    "password": settings.WALLET_PASSWORD,
                }
            )
            login_headers = {"accept": "*/*", "Content-Type": "application/json"}

            login_response = requests.request(
                "POST", login_url, headers=login_headers, data=login_payload
            )
            # wallet_response = requests.request(
            #     "POST", login_url, headers=login_headers, data=login_payload
            # )
            auth_token = login_response.json()["token"]
            print(auth_token)
            if auth_token:
                enquiry_url = (
                    f"{settings.WALLET_API_URL}/NameEnquiry/NameEnquiryService"
                )
                enquiry_payload = json.dumps(
                    {
                        "accountNumber": request_dict["account_details"][
                            "accountNumber"
                        ],
                        "channel": "MNO" if request_dict["account_type"].upper() == "WALLET" else "INTERBANK",
                        "institutionCode": request_dict["account_details"][
                            "institutionCode"
                        ],
                        "transactionId": str(random.randint(10**13, 10**14 - 1)),
                    }
                )
                print(enquiry_payload)
                enquiry_headers = {
                    "accept": "text/plain",
                    "Authorization": f"Bearer {auth_token}",
                    "Content-Type": "application/json",
                }
                enquiry_resonse = requests.request(
                    "POST", enquiry_url, headers=enquiry_headers, data=enquiry_payload
                )

                
                print(enquiry_resonse.json())
                temp_acc_details = request_dict["account_details"]
                if enquiry_resonse.json()["statusCode"] == "200":
                    
                    temp_acc_details["accountName"] = enquiry_resonse.json()["data"][
                        "accountName"
                    ]
                else:
                    return ErrorResponse(
                        status_code=400,
                        message="ERROR",
                        error={"error": "Provide correct account details"},
                    )
                print("123444")
                request_dict["account_details"] = temp_acc_details
        new_account = await create_record(db, Account, request_dict)
        if isinstance(new_account, str):
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": new_account}
            )

        return StandardResponse(
            status_code=200,
            message="Account created successfully",
            data={"account_id": str(new_account.id)},
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/account-read/{id}")
async def read_account(
    id: str,
    db: AsyncSession = Depends(get_db),
    # Authorization: Annotated[str, Header()] = None,
    user=Depends(permission_checker)
):
    """
    Fetch a single Account record by UUID `id`.
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user

        account_id = user.get("user_id") if isinstance(user, dict) else None
        
        # resp = await get_id_header(Authorization)
        # if resp.status_code == 200:
        #     resp_json = resp.json()
        #     account_id = resp_json.get("account_id")
        #     account_type = resp_json.get("account_type")
        # else:
        #     return ErrorResponse(
        #         status_code=400, message="ERROR", error={"error": resp.json()}
        #     )

        # acc_id = uuid.UUID(account_id)
        query = select(Account).filter(Account.id == id)
        result = await db.execute(query)
        account_obj = result.scalars().first()

        if not account_obj:
            # account = {
            #         "user_id": account_id,
            #         "account_type": "APP", #account_type.upper(),
            #         # "balance": 0,
            #         "currency": "GHS",
            #         "status": "ACTIVE",
            #         "account_details":{
            #             "accountName": "",
            #             "accountNumber": "",
            #             "institutionCode": "",
            #         }
            #         }
            return ErrorResponse(status_code=404, message="Account not found")
        
            # new_account = await create_record(db, Account, account)
            # return StandardResponse(
            #     status_code=200,
            #     message="Account created successfully",
            #     data=new_account.as_dict(),
            # )
            # return ErrorResponse(status_code=404, message="Account not found")

        return StandardResponse(
            status_code=200,
            message="Account fetched successfully",
            data=account_obj.as_dict(),
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/account-list")
async def list_accounts(
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker)
    # Authorization: Annotated[str, Header()] = None,
):
    """
    Retrieve a paginated list of Accounts.
    """
    try:
        # resp = await get_id_header(Authorization)
        # if resp.status_code != 200:
        #     return ErrorResponse(
        #         status_code=400, message="ERROR", error={"error": resp.json()}
        #     )
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        # resp_json = resp.json()
        # user_id = resp_json.get("account_id")
        user_id = user.get("user_id") if isinstance(user, dict) else None
        query = select(Account).filter(Account.user_id == user_id)
        result = await db.execute(query)
        accounts = result.scalars().all()
        if not accounts:
                    await create_record(
                        db,
                        Account,
                        {
                            "user_id": user_id,
                            "account_type": "APP",
                            "balance": 0,
                            "currency": "GHS",
                            "status": "ACTIVE",
                            "account_details": {
                                "accountName": "",
                                "accountNumber": "",
                                "institutionCode": "",
                            },
                        },
                    )
        count_query = select(func.count(Account.id))
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()

        return StandardResponse(
            status_code=200,
            message="Accounts fetched successfully",
            data=[a.as_dict() for a in accounts],
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.put("/account-update")
async def update_account(
    # id: str,
    request: UpdateAccount,
    db: AsyncSession = Depends(get_db),
    # Authorization: Annotated[str, Header()] = None,
    user=Depends(permission_checker)
):
    """
    Update an existing Account record.
    """
    try:
        # resp = await get_id_header(Authorization)
        # if resp.status_code != 200:
        #     return ErrorResponse(
        #         status_code=400, message="ERROR", error={"error": resp.json()}
        #     )
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        request_dict = request.dict()
        balance = request_dict.pop("balance")
        print(request_dict)
        # acc_id = str(request_dict.get("user_id"))
        acc_id = str(request.user_id)
        print(acc_id, "acc idddd")
        query = select(Account).filter(Account.user_id == acc_id)
        result = await db.execute(query)
        get_account = result.scalars().first()
        print(get_account, "acc obj")
        if not get_account:
            return ErrorResponse(status_code=404, message="Account not found")

        # print(request_dict["accountNumber"])
        if request_dict["account_details"].get("walletNumber", None):
            login_url = f"{settings.WALLET_API_URL}/Authentication/Login"

            login_payload = json.dumps(
                {
                    "username": settings.WALLET_USERNAME,
                    "password": settings.WALLET_PASSWORD,
                }
            )
            login_headers = {"accept": "*/*", "Content-Type": "application/json"}

            login_response = requests.request(
                "POST", login_url, headers=login_headers, data=login_payload
            )
            auth_token = login_response.json()["token"]
            print(auth_token)
            if auth_token:
                enquiry_url = (
                    f"{settings.WALLET_API_URL}/NameEnquiry/NameEnquiryService"
                )
                print(1)
                enquiry_payload = json.dumps(
                    {
                        "accountNumber": request_dict["account_details"][
                            "accountNumber"
                        ],
                        "channel": "MNO" if request_dict["account_type"].upper() == "WALLET" else "INTERBANK",
                        "institutionCode": request_dict["account_details"][
                            "institutionCode"
                        ],
                        "transactionId": str(random.randint(10**13, 10**14 - 1)),
                    }
                )
                print(2)
                print(enquiry_payload)
                enquiry_headers = {
                    "accept": "text/plain",
                    "Authorization": f"Bearer {auth_token}",  # Use the token from the previous response
                    "Content-Type": "application/json",
                }
                enquiry_resonse = requests.request(
                    "POST", enquiry_url, headers=enquiry_headers, data=enquiry_payload
                )
                print(enquiry_resonse.json(), "enquiry_resonse")

                print(enquiry_resonse.json())
                if enquiry_resonse.json()["statusCode"] == "200":
                    print(enquiry_resonse.text)
                    request_dict["account_details"][
                        "accountName"
                    ] = enquiry_resonse.json()["data"]["accountName"]
                else:
                    return ErrorResponse(
                        status_code=400,
                        message="ERROR",
                        error={"error": "Provide correct account details"},
                    )

        print(request_dict, "final request_dict")
        res = await update_payment_rec(db, request_dict, get_account)
        return StandardResponse(
            status_code=200, message="Account updated successfully", data=res
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.delete("/account-delete/{id}")
async def delete_account(
    id: str,
    db: AsyncSession = Depends(get_db),
    # Authorization: Annotated[str, Header()] = None,
    user=Depends(permission_checker)
):
    """
    Delete an Account record by UUID `id`.
    """
    try:
        # resp = await get_id_header(Authorization)
        # if resp.status_code != 200:
        #     return ErrorResponse(
        #         status_code=400, message="ERROR", error={"error": resp.json()}
        #     )
        if isinstance(user, JSONResponse):  # Permission failed
            return user

        acc_id = uuid.UUID(id)
        query = select(Account).filter(Account.id == acc_id)
        result = await db.execute(query)
        get_account = result.scalars().first()

        if not get_account:
            return ErrorResponse(status_code=404, message="Account not found")

        res = await delete_record(db, get_account)
        return StandardResponse(
            status_code=200, message="Account deleted successfully", data=res
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")

