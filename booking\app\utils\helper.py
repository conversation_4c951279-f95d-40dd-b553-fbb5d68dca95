SURCHARGES = 2.5
TAX = 17.5
CANCEL_THRESHOLD = 2
PENALTY_AMOUNT = 10


def get_surcharges(base_service_fee):
    return round(base_service_fee * SURCHARGES / 100, 2)

def get_tax(base_service_fee):
    return round(base_service_fee * TAX / 100, 2)

from app.models import Account

def apply_penalty(db, penalty_user_type, penalty_user_id, penalty):
    if penalty_user_id and penalty_user_type.lower() == "user":
        acc = db.query(Account).filter(Account.user_id == penalty_user_id).first()
        acc.balance = acc.balance - penalty
        db.commit()
        db.refresh(acc)
    elif penalty_user_id and penalty_user_type.lower() == "artisan":
        acc = db.query(Account).filter(Account.user_id == penalty_user_id).first()
        acc.balance = acc.balance - penalty
        db.commit()
        db.refresh(acc)
    return
