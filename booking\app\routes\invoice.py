from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
from datetime import datetime, timedelta
from io import Bytes<PERSON>
from pathlib import Path
import base64
from xhtml2pdf import pisa
from sqlalchemy.orm import Session
from app.models import Booking, Payment
from app.models import Services, ServiceProvider, Users
from app.schemas.helper import ErrorResponse
from app.database import get_db
from app.config import get_settings

router = APIRouter()
settings = get_settings()

BASE_DIR = Path(__file__).resolve().parent.parent
TEMPLATE_PATH = BASE_DIR / "templates" / "invoice_template.html"
IMAGE_PATH = BASE_DIR / "templates" / "static" / "job-connectz-icon.png"

def render_invoice_html(invoice_data: dict) -> str:
    if not TEMPLATE_PATH.exists():
        raise FileNotFoundError(f"Template not found at {TEMPLATE_PATH}")
    if not IMAGE_PATH.exists():
        raise FileNotFoundError(f"Image not found at {IMAGE_PATH}")

    html = TEMPLATE_PATH.read_text()

    # Embed logo
    with open(IMAGE_PATH, "rb") as image_file:
        encoded_image = base64.b64encode(image_file.read()).decode()
    html = html.replace('src="./static/job-connectz-icon.png"', f'src="data:image/png;base64,{encoded_image}"')

    mobile_money_html = ""
    if invoice_data.get("payment_method", "").upper() == "MOMO":
        mobile_money_html = f"""
        <div style="margin-bottom: 0px;">
            <span style="font-weight: bold; display: inline-block; min-width: 90px;">Mobile Money Number:</span>
            <span>{invoice_data.get("mobile_money_number", "---")}</span>
        </div>
        """
    html = html.replace('{{mobile_money_section}}', mobile_money_html)

    card_number_html = ""
    if invoice_data.get("payment_method", "").upper() == "CARD":
        card_number = invoice_data.get("card_number", "XXXX")
        card_number_html = f"""
        <div style="margin-bottom: 0px;">
            <span style="font-weight: bold; display: inline-block; min-width: 90px;">Card Number:</span>
            <span>**** **** **** {card_number}</span>
        </div>
        """
    html = html.replace('{{card_number_section}}', card_number_html)


    # Replace placeholders
    replacements = {
        'invoice_number': invoice_data.get('invoice_number', 'N/A'),
        'transaction_id': invoice_data.get('transaction_id', 'Not specified'),
        'transaction_date': invoice_data.get('transaction_date', 'Not specified'),
        'billed_to_name': invoice_data['billed_to'].get('name', 'Customer'),
        'billed_to_email': invoice_data['billed_to'].get('email', 'N/A'),
        'billed_to_phone': invoice_data['billed_to'].get('phone', 'N/A'),
        'from_name': invoice_data['from_details'].get('name', 'JobConnectz'),
        'from_email': invoice_data['from_details'].get('email', '<EMAIL>'),
        'from_phone': invoice_data['from_details'].get('phone', '+2330000000'),
        'subtotal': f"{invoice_data['currency']} {invoice_data.get('subtotal', 0.0):.2f}",
        'platform_fee': f"{invoice_data['currency']}  {invoice_data.get('platform_fee', 0.0):.2f}",
        'tax': f"{invoice_data['currency']}  {invoice_data.get('tax', 0.0):.2f}",
        'total': f"{invoice_data['currency']}  {invoice_data.get('total', 0.0):.2f}",
        'payment_method': invoice_data.get('payment_method', 'Not specified'),
        'job_location': invoice_data.get('job_location', 'Not specified'),
    }

    for key, value in replacements.items():
        html = html.replace(f"{{{{{key}}}}}", str(value))
    print('hours')
    # Services list
    service_rows = ""
    for service in invoice_data.get('services', []):
        service_rows += f"""
        <tr>
            <td style="padding: 8px 3px; border-bottom: 1px solid #eee;">{service.get('name')}</td>
            <td style="padding: 8px 3px; text-align: left; border-bottom: 1px solid #eee;">{service.get('hours')}</td>
            <td style="padding: 8px 3px; text-align: right; border-bottom: 1px solid #eee;">{service.get('currency')} {service.get('rate', 0.0):.2f}</td>
            <td style="padding: 8px 3px; text-align: right; border-bottom: 1px solid #eee;">{service.get('currency')} {service.get('total', 0.0):.2f}</td>
        </tr>
        """
    html = html.replace('{{service_rows}}', service_rows)

    return html

def generate_pdf_from_html(html: str) -> BytesIO:
    pdf_stream = BytesIO()
    pisa_status = pisa.CreatePDF(html, dest=pdf_stream)
    if pisa_status.err:
        raise Exception("PDF generation failed")
    pdf_stream.seek(0)
    return pdf_stream

@router.get("/generate-invoice", tags=["INVOICE"])
async def generate_invoice(booking_id: str, db: Session = Depends(get_db)):
    booking = db.query(Booking).filter(Booking.id == booking_id).first()
    if not booking:
        return ErrorResponse(status_code=404, message="Booking doesn't exist")

    user_details = db.query(Users).filter(Users.id == booking.user_id).first()
    if not user_details:
        return ErrorResponse(status_code=404, message="User not found")
    
    payment = db.query(Payment).filter(Payment.id == booking.payment_id).first()
    if not payment:
        return ErrorResponse(status_code=404, message="Payment details not found")

    service_data = db.query(Services).filter(Services.id == booking.service_id).first()
    service_name = service_data.name if service_data else "Unnamed Service"

    artisan = db.query(ServiceProvider).filter(ServiceProvider.id == booking.artisan_id).first()
    if artisan:
        artisan_name = f"{artisan.first_name or ''} {artisan.last_name or ''}".strip() or "Service Provider"
        artisan_email = artisan.email or "---"
        artisan_phone = artisan.phone_number or "---"
    else:
        artisan_name = "Service Provider"
        artisan_email = "---"
        artisan_phone = "---"

    # Calculate hours between start_datetime and end_datetime
    time_range_display = '---'
    try:
        if booking.start_time and booking.end_time and booking.booking_date:
            # Combine date and time
            start_dt = datetime.combine(booking.booking_date, booking.start_time)
            end_dt = datetime.combine(booking.booking_date, booking.end_time)

            # Handle overnight service
            if end_dt < start_dt:
                end_dt += timedelta(days=1)

            # Calculate hours
            duration_hours = round((end_dt - start_dt).total_seconds() / 3600, 2)

            # Format times to 07.30 PM
            start_str = start_dt.strftime('%I.%M %p')
            end_str = end_dt.strftime('%I.%M %p')

            # Compose final string
            time_range_display = f"{start_str} - {end_str} ({duration_hours} Hrs)"
    except Exception as e:
        print("Error formatting time range:", e)
        time_range_display = '---'

    
    transaction_id = "---"
    try:
        if payment.payment_method.name == "WALLET":
            transaction_id = payment.payment_details.get("data", {}).get("transactionId", "---")
        elif payment.payment_method.name == "CARD":
            transaction_id = payment.payment_details.get("p_order_id", "---")
    except Exception as e:
        print("Error extracting transaction ID:", e)
        transaction_id = "---"

    invoice_data = {
        "invoice_number": str(booking.booking_order_id or "N/A"),
        "transaction_id": transaction_id,
        "transaction_date": booking.requested_time.strftime("%d/%m/%Y") if booking.requested_time else "Not specified",
        "billed_to": {
            "name": user_details.first_name or "Customer",
            "email": user_details.email or "---",
            "phone": user_details.phone_number or "---"
        },
        "from_details": {
            "name": artisan_name,
            "email": artisan.email if artisan else "---",
            "phone": artisan.phone_number if artisan else "---"
        },
        "services": [
            {
                "name": service_name,
                "rate": float(booking.base_service_fee or 0.0),
                "quantity": 1,
                "hours": time_range_display,
                "total": float(booking.base_service_fee or 0.0),
                "currency": str(payment.currency.value)
            }
        ],
        "subtotal": float(booking.base_service_fee or 0.0) - float(booking.tax or 0.0) - float(booking.service_fee or 0.0),
        "platform_fee": float(booking.service_fee or 0.0),
        "tax": float(booking.tax or 0.0),
        "total": float(booking.base_service_fee or 0.0),
        "payment_method": str(payment.payment_method.name or "Not specified"),
        "mobile_money_number": payment.payment_details.get("mobile_money_number", "---") if payment.payment_method.name == "MOMO" else "",
        "card_number": "**** **** **** 4312",
        "job_location": str(booking.user_address or "No location provided"),
        "currency": str(payment.currency.value)
    }

    try:
        html = render_invoice_html(invoice_data)
        pdf_stream = generate_pdf_from_html(html)

        return StreamingResponse(
            pdf_stream,
            media_type="application/pdf",
            headers={"Content-Disposition": "attachment; filename=invoice.pdf"}
        )
    except Exception as e:
        raise Exception(f"Error generating invoice: {str(e)}")
