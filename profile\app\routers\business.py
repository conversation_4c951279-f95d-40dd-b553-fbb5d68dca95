# from fastapi import APIRouter, Depends
# from sqlalchemy.future import select
# from sqlalchemy.ext.asyncio import AsyncSession

# from app.database import get_db
# from app.models import Business
# from app.schemas import BusinessSignUp
# from app.helper import StandardResponse, ErrorResponse
# from app.cognito_utils import (
#     admin_get_user,
#     cognito_sign_up,
#     add_user_to_group,
#     update_cognito_attributes,
# )
# from datetime import datetime
# import uuid
# from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, Header, Query
# from app.s3_upload import (
#     upload_file_direct,
#     s3_delete_file,
#     S3_IMAGES_FOLDER,
#     S3_DOCS_FOLDER,
# )
# from sqlalchemy import delete, or_, func
# from app import schemas
# from app.database import get_db
# from app.utils import (
#     get_id_header,
#     create_record,
#     update_record,
# )
# from typing import Optional
# from sqlalchemy.future import select
# from sqlalchemy.ext.asyncio import AsyncSession
# from typing import Annotated, List
# from app.schemas import UpdateBusiness, BusinessResponse
# # from app.models import *
# from app.models import Business, BusinessServiceMapping, BusinessArea,Services
# from app.models_enum import BusinessType, BusinessProviderStatus
# from app.helper import StandardResponse, ErrorResponse
# from app.image_validator import validate_image
# import logging

# # Configure logger
# logger = logging.getLogger(__name__)

# router = APIRouter()


# async def create_business_record(request_dict: dict, model, db: AsyncSession):
#     try:
#         auth_id = request_dict.get("auth_id")

#         new_business = model(**request_dict)
#         db.add(new_business)
#         await db.commit()
#         await db.refresh(new_business)

#         business_id = new_business.id

#         await update_cognito_attributes(
#             auth_id, {"custom:account_id": str(business_id)}, False
#         )

#         return {
#             "status_code": 200,
#             "message": "Business Provider created successfully",
#             "data": str(business_id),
#         }

#     except Exception as e:
#         return {
#             "status_code": 500,
#             "message": f"Something went wrong while creating record in DB: {str(e)}",
#         }


# @router.post("/business-signup", tags=["BUSINESS-SIGNUP"])
# async def create_business(
#     request: BusinessSignUp, db: AsyncSession = Depends(get_db)
# ):
#     try:
#         # Check if business already exists in DB
#         query = select(Business).filter(Business.phone_number == request.phone_number)
#         result = await db.execute(query)
#         if result.scalars().first():
#             return ErrorResponse(
#                 status_code=400, message="Business already exists"
#             )

#         # Check if user exists in Cognito
#         business_exists = admin_get_user(request.phone_number)

#         if business_exists is None:
#             try:
#                 auth_id = cognito_sign_up(request.phone_number)
#                 add_user_to_group(auth_id.get("UserSub"), "business_owner")
#             except Exception as e:
#                 return ErrorResponse(
#                     status_code=500,
#                     message="Error creating Business provider account in Cognito.",
#                     error=str(e),
#                 )
#         else:
#             auth_id = {"UserSub": business_exists["Username"]}

#         # Prepare payload for DB insert
#         payload = request.model_dump()
#         payload["auth_id"] = auth_id.get("UserSub")

#         # Create the business record
#         business_provider_response = await create_business_record(payload, Business, db)

#         return StandardResponse(
#             status_code=201,
#             message="Account created successfully",
#             data=business_provider_response,
#         )

#     except Exception as e:
#         return ErrorResponse(
#             status_code=500, message="Unexpected error occurred", error=str(e)
#         )


# @router.put("/business-update")
# async def update_business(
#     data: schemas.UpdateBusiness = Depends(),
#     business_logo: Optional[UploadFile] = File(None),
#     business_registration_document: Optional[UploadFile] = File(None),
#     id_proof: Optional[UploadFile] = File(None),
#     portfolio_images: Optional[List[UploadFile]] = File(None),
#     signature: Optional[UploadFile] = File(None),
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     try:
#         print(data.__dict__, "data.__dict__")
#         resp = await get_id_header(Authorization)
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             account_id = resp_json.get("account_id")
#         else:
#             return ErrorResponse(
#                 status_code=401, message="Unauthorized", error=resp.json()
#             )

#         services_offered = data.__dict__.pop("services_offered", None)
#         query = select(Business).filter(Business.id == account_id)
#         result = await db.execute(query)
#         get_business = result.scalars().first()
#         if get_business is None:
#             return ErrorResponse(
#                 status_code=404, message="Business Not found", error=None
#             )

#         business_id = get_business.id
#         # print(business_id, "business_id", data.__dict__)

#         if business_logo:
#             # Validate business logo
#             try:
#                 is_valid, error_message = await validate_image(business_logo)
#                 if not is_valid:
#                     logger.error(f"Image validation failed: {error_message}")
#                     return ErrorResponse(
#                         status_code=400,
#                         message=error_message,
#                         error="Business logo validation failed"   
#                     )
#             except Exception as e:
#                 logger.error(f"Error validating image: {str(e)}", exc_info=True)
#                 return ErrorResponse(
#                     status_code=400,
#                     message="Failed to validate image",
#                     error=str(e)
#                 )

#             # Delete existing business logo
#             if get_business.business_logo:
#                 try:
#                     s3_delete_file(get_business.business_logo)
#                 except Exception as e:
#                     logger.warning(f"Failed to delete existing business logo: {str(e)}")
            
#             # Upload original image
#             logo_url = upload_file_direct(business_logo, path=S3_IMAGES_FOLDER)
#             if "message" in logo_url:
#                 return ErrorResponse(
#                     status_code=400,
#                     message="Failed to upload image",
#                     error=f"Failed to upload image: {logo_url['message']}"
#                 )

#             # Update business record with logo
#             setattr(get_business, "business_logo", logo_url["filename"])

#         if business_registration_document:
#             # Validate business registration document
#             try:
#                 is_valid, error_message = await validate_image(business_registration_document)
#                 if not is_valid:
#                     logger.error(f"Image validation failed: {error_message}")
#                     return ErrorResponse(
#                         status_code=400,
#                         message=error_message,
#                         error="Business registration document validation failed"   
#                     )
#             except Exception as e:
#                 logger.error(f"Error validating image: {str(e)}", exc_info=True)
#                 return ErrorResponse(
#                     status_code=400,
#                     message="Failed to validate image",
#                     error=str(e)
#                 )

#             if get_business.business_registration_document:
#                 # Delete existing document
#                 try:
#                     s3_delete_file(get_business.business_registration_document)
#                 except Exception as e:
#                     logger.warning(f"Failed to delete existing business registration document: {str(e)}")
#             # Calling S3 upload function
#             doc_url = upload_file_direct(business_registration_document, path=S3_DOCS_FOLDER)
#             setattr(get_business, "business_registration_document", doc_url["filename"])

#         if id_proof:
#             # Validate ID proof
#             try:
#                 is_valid, error_message = await validate_image(id_proof)
#                 if not is_valid:
#                     logger.error(f"Image validation failed: {error_message}")
#                     return ErrorResponse(
#                         status_code=400,
#                         message=error_message,
#                         error="ID proof validation failed"   
#                     )
#             except Exception as e:
#                 logger.error(f"Error validating image: {str(e)}", exc_info=True)
#                 return ErrorResponse(
#                     status_code=400,
#                     message="Failed to validate image",
#                     error=str(e)
#                 )

#             if get_business.id_proof:
#                 # Delete existing id_proof
#                 try:
#                     s3_delete_file(get_business.id_proof)
#                 except Exception as e:
#                     logger.warning(f"Failed to delete existing ID proof: {str(e)}")
#             # Calling S3 upload function
#             id_proof_url = upload_file_direct(id_proof, path=S3_DOCS_FOLDER)
#             setattr(get_business, "id_proof", id_proof_url["filename"])

#         if portfolio_images:
#             portfolio_urls = []
#             # Delete existing portfolio images if any
#             if get_business.portfolio_image:
#                 for existing_image in get_business.portfolio_image:
#                     try:
#                         s3_delete_file(existing_image)
#                     except Exception as e:
#                         logger.warning(f"Failed to delete existing portfolio image {existing_image}: {str(e)}")
            
#             for portfolio_image in portfolio_images:
#                 # Validate portfolio image
#                 try:
#                     is_valid, error_message = await validate_image(portfolio_image)
#                     if not is_valid:
#                         logger.error(f"Portfolio image validation failed: {error_message}")
#                         return ErrorResponse(
#                             status_code=400,
#                             message=error_message,
#                             error="Portfolio image validation failed"   
#                         )
#                 except Exception as e:
#                     logger.error(f"Error validating portfolio image: {str(e)}", exc_info=True)
#                     return ErrorResponse(
#                         status_code=400,
#                         message="Failed to validate portfolio image",
#                         error=str(e)
#                     )
                
#                 # Upload portfolio image
#                 portfolio_url = upload_file_direct(portfolio_image, path=S3_IMAGES_FOLDER)
#                 if "message" in portfolio_url:
#                     return ErrorResponse(
#                         status_code=400,
#                         message="Failed to upload portfolio image",
#                         error=f"Failed to upload portfolio image: {portfolio_url['message']}"
#                     )
#                 portfolio_urls.append(portfolio_url["filename"])
            
#             # Update portfolio images
#             setattr(get_business, "portfolio_image", portfolio_urls)

#         if signature:
#             # Validate signature
#             try:
#                 is_valid, error_message = await validate_image(signature)
#                 if not is_valid:
#                     logger.error(f"Signature validation failed: {error_message}")
#                     return ErrorResponse(
#                         status_code=400,
#                         message=error_message,
#                         error="Signature validation failed"   
#                     )
#             except Exception as e:
#                 logger.error(f"Error validating signature: {str(e)}", exc_info=True)
#                 return ErrorResponse(
#                     status_code=400,
#                     message="Failed to validate signature",
#                     error=str(e)
#                 )

#             if get_business.signature:
#                 # Delete existing signature
#                 try:
#                     s3_delete_file(get_business.signature)
#                 except Exception as e:
#                     logger.warning(f"Failed to delete existing signature: {str(e)}")
            
#             # Upload signature
#             signature_url = upload_file_direct(signature, path=S3_DOCS_FOLDER)
#             if "message" in signature_url:
#                 return ErrorResponse(
#                     status_code=400,
#                     message="Failed to upload signature",
#                     error=f"Failed to upload signature: {signature_url['message']}"
#                 )

#             # Update business record with signature
#             setattr(get_business, "signature", signature_url["filename"])

#         # Creating services list
#         if services_offered:
#             # Removing existing services before adding new ones
#             stmt = delete(BusinessServiceMapping).where(
#                 BusinessServiceMapping.business_id == business_id
#             )
#             await db.execute(stmt)
#             await db.commit()
            
#             # Just validate each UUID and create mappings
#             for service_id in services_offered:
#                 if service_id:
#                     try:
#                         # Validate UUID format
#                         uuid.UUID(service_id)
#                         val = {"business_id": business_id, "service_id": service_id}
#                         await create_record(db, BusinessServiceMapping, val)
#                     except ValueError:
#                         logger.error(f"Invalid UUID format: {service_id}")
#                         continue

#         res = await update_record(db, data, get_business)
#         return StandardResponse(
#             status_code=200, message="Business registration completed and currently under review"
#         )
#     except Exception as e:
#         return ErrorResponse(status_code=500, message="Business updation failed", error=str(e))


# @router.get("/businesses")
# async def get_all_businesses(
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
#     page: int = Query(1),
#     limit: int = Query(10),
#     status: Optional[str] = Query(None),
#     business_type: Optional[str] = Query(None),
#     search: Optional[str] = Query(None),
# ):
#     """Get all businesses with pagination and filtering options"""
#     try:
#         # Build the base query
#         query = select(Business)
        
#         # Apply filters
#         if status:
#             query = query.filter(Business.status == status)
        
#         if business_type:
#             query = query.filter(Business.business_type == business_type)
        
#         if search:
#             search_filter = or_(
#                 Business.business_name.ilike(f"%{search}%"),
#                 Business.full_name.ilike(f"%{search}%"),
#                 Business.email.ilike(f"%{search}%"),
#                 Business.phone_number.ilike(f"%{search}%"),
#                 Business.business_registration_number.ilike(f"%{search}%"),
#                 Business.ghana_post_gps_address.ilike(f"%{search}%"),
#                 Business.business_location.ilike(f"%{search}%"),
#                 Business.tax_identification_number.ilike(f"%{search}%")
#             )
#             query = query.filter(search_filter)
        
#         # Get total count for pagination
#         count_query = select(func.count()).select_from(query.subquery())
#         total_count = await db.execute(count_query)
#         total = total_count.scalar()
        
#         # Apply pagination
#         offset = (page - 1) * limit
#         query = query.offset(offset).limit(limit).order_by(Business.submit_date.desc())
        
#         # Execute query
#         result = await db.execute(query)
#         businesses = result.scalars().all()
        
#         # Convert to BusinessResponse objects
#         business_list = []
#         for business in businesses:
#             business_response = BusinessResponse(
#                 id=business.id,
#                 auth_id=business.auth_id,
#                 full_name=business.full_name,
#                 business_name=business.business_name,
#                 business_type=business.business_type,
#                 business_registration_number=business.business_registration_number,
#                 ghana_post_gps_address=business.ghana_post_gps_address,
#                 business_location=business.business_location,
#                 tax_identification_number=business.tax_identification_number,
#                 email=business.email,
#                 country_code=business.country_code,
#                 phone_number=business.phone_number,
#                 business_registration_document=business.business_registration_document,
#                 business_logo=business.business_logo,
#                 portfolio_image=business.portfolio_image,
#                 id_proof=business.id_proof,
#                 signature=business.signature,
#                 submit_date=business.submit_date,
#                 page_position=business.page_position,
#                 status=business.status,
#                 reason=business.reason,
#                 service_area_ids=business.service_area_ids
#             )
#             business_list.append(business_response)
        
#         # Calculate pagination info
#         total_pages = (total + limit - 1) // limit
        
#         pagination_info = {
#             "current_page": page,
#             "total_pages": total_pages,
#             "total_items": total,
#             "items_per_page": limit
#         }
        
#         return StandardResponse(
#             status_code=200,
#             message="Businesses retrieved successfully",
#             data={
#                 "businesses": business_list,
#                 "pagination": pagination_info
#             }
#         )
#     except Exception as e:
#         logger.error(f"Error retrieving businesses: {str(e)}", exc_info=True)
#         return ErrorResponse(
#             status_code=500,
#             message="Failed to retrieve businesses",
#             error=str(e)
#         )


# @router.get("/businesses/{business_id}")
# async def get_business_by_id(
#     business_id: str,
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     """Get a specific business by ID with full details"""
#     try:
#         # Convert string to UUID
#         try:
#             business_uuid = uuid.UUID(business_id)
#         except ValueError:
#             return ErrorResponse(
#                 status_code=400,
#                 message="Invalid business ID format",
#                 error="Business ID must be a valid UUID"
#             )
        
#         # Get business
#         result = await db.execute(
#             select(Business).filter(Business.id == business_uuid)
#         )
#         business = result.scalars().first()
        
#         if not business:
#             return ErrorResponse(
#                 status_code=404,
#                 message="Business not found",
#                 error="Business ID not found"
#             )
        
#         # Get service mappings for this business
#         service_query = select(BusinessServiceMapping).filter(
#             BusinessServiceMapping.business_id == business.id
#         )
#         service_result = await db.execute(service_query)
#         service_mappings = service_result.scalars().all()
        
#         # Get service details with names
#         service_details = []
#         for mapping in service_mappings:
#             # Get service name from services table
#             service_query = select(Services).filter(Services.id == mapping.service_id)
#             service_result = await db.execute(service_query)
#             service = service_result.scalars().first()
#             if service:
#                 service_details.append({
#                     "id": str(service.id),
#                     "name": service.name
#                 })
        
#         # Get business areas if service_area_ids exist
#         service_area_details = []
#         if business.service_area_ids:
#             try:
#                 # Ensure service_area_ids is a list of valid UUIDs
#                 area_ids = business.service_area_ids
#                 if isinstance(area_ids, list) and area_ids:
#                     # Convert string UUIDs to UUID objects if needed
#                     valid_area_ids = []
#                     for area_id in area_ids:
#                         try:
#                             if isinstance(area_id, str):
#                                 valid_area_ids.append(uuid.UUID(area_id))
#                             else:
#                                 valid_area_ids.append(area_id)
#                         except (ValueError, TypeError):
#                             logger.warning(f"Invalid area ID format: {area_id}")
#                             continue
                    
#                     if valid_area_ids:
#                         area_query = select(BusinessArea).filter(
#                             BusinessArea.id.in_(valid_area_ids)
#                         )
#                         area_result = await db.execute(area_query)
#                         business_areas = area_result.scalars().all()
                        
#                         # Create area objects with ID and name
#                         for area in business_areas:
#                             service_area_details.append({
#                                 "id": str(area.id),
#                                 "name": area.area_name
#                             })
#             except Exception as e:
#                 logger.error(f"Error retrieving business areas: {str(e)}")
        
#         # Create BusinessResponse object
#         business_response = BusinessResponse(
#             id=business.id,
#             auth_id=business.auth_id,
#             full_name=business.full_name,
#             business_name=business.business_name,
#             business_type=business.business_type,
#             business_registration_number=business.business_registration_number,
#             ghana_post_gps_address=business.ghana_post_gps_address,
#             business_location=business.business_location,
#             tax_identification_number=business.tax_identification_number,
#             email=business.email,
#             country_code=business.country_code,
#             phone_number=business.phone_number,
#             business_registration_document=business.business_registration_document,
#             business_logo=business.business_logo,
#             portfolio_image=business.portfolio_image,
#             id_proof=business.id_proof,
#             signature=business.signature,
#             submit_date=business.submit_date,
#             page_position=business.page_position,
#             status=business.status,
#             reason=business.reason,
#             services_offered=service_details,  # Use service objects with ID and name
#             service_area_ids=service_area_details  # Use area objects with ID and name
#         )
        
#         return StandardResponse(
#             status_code=200,
#             message="Business retrieved successfully",
#             data=business_response
#         )
#     except Exception as e:
#         logger.error(f"Error retrieving business: {str(e)}", exc_info=True)
#         return ErrorResponse(
#             status_code=500,
#             message="Failed to retrieve business",
#             error=str(e)
#         )

# @router.post("/business-area")
# async def create_business_area(
#     data: schemas.BusinessAreaCreate,
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     """Create a new business area"""
#     try:
#         # Validate area name
#         if not data.area_name or not data.area_name.strip():
#             return ErrorResponse(
#                 status_code=400,
#                 message="Area name cannot be empty",
#                 error="Area name is required"
#             )
        
#         # Check if area name already exists
#         existing_area = await db.execute(
#             select(BusinessArea).filter(BusinessArea.area_name == data.area_name.strip())
#         )
#         if existing_area.scalars().first():
#             return ErrorResponse(
#                 status_code=400,
#                 message="Business area with this name already exists",
#                 error="Duplicate area name"
#             )
        
#         # Create new business area
#         business_area_data = {"area_name": data.area_name.strip()}
#         result = await create_record(db, BusinessArea, business_area_data)
        
#         return StandardResponse(
#             status_code=201,
#             message="Business area created successfully"
         
#         )
#     except Exception as e:
#         logger.error(f"Error creating business area: {str(e)}", exc_info=True)
#         return ErrorResponse(
#             status_code=500,
#             message="Failed to create business area",
#             error=str(e)
#         )


# @router.get("/business-area")
# async def get_all_business_areas(
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     """Get all business areas"""
#     try:
#         result = await db.execute(select(BusinessArea))
#         business_areas = result.scalars().all()
        
#         return StandardResponse(
#             status_code=200,
#             message="Business areas retrieved successfully",
#             data=business_areas
#         )
#     except Exception as e:
#         logger.error(f"Error retrieving business areas: {str(e)}", exc_info=True)
#         return ErrorResponse(
#             status_code=500,
#             message="Failed to retrieve business areas",
#             error=str(e)
#         )


# @router.put("/business-area/{area_id}")
# async def update_business_area(
#     area_id: str,
#     data: schemas.BusinessAreaUpdate,
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     """Update a business area"""
#     try:
#         # Convert string to UUID
#         try:
#             area_uuid = uuid.UUID(area_id)
#         except ValueError:
#             return ErrorResponse(
#                 status_code=400,
#                 message="Invalid area ID format",
#                 error="Area ID must be a valid UUID"
#             )
        
#         # Check if business area exists
#         result = await db.execute(
#             select(BusinessArea).filter(BusinessArea.id == area_uuid)
#         )
#         business_area = result.scalars().first()
        
#         if not business_area:
#             return ErrorResponse(
#                 status_code=404,
#                 message="Business area not found",
#                 error="Area ID not found"
#             )
        
#         # Check if any fields are provided for update
#         update_data = {k: v for k, v in data.dict().items() if v is not None}
#         if not update_data:
#             return ErrorResponse(
#                 status_code=400,
#                 message="No fields provided for update",
#                 error="At least one field must be provided"
#             )
        
#         # Check if new area name already exists (if updating name)
#         if data.area_name and data.area_name != business_area.area_name:
#             existing_area = await db.execute(
#                 select(BusinessArea).filter(
#                     BusinessArea.area_name == data.area_name,
#                     BusinessArea.id != area_uuid
#                 )
#             )
#             if existing_area.scalars().first():
#                 return ErrorResponse(
#                     status_code=400,
#                     message="Business area with this name already exists",
#                     error="Duplicate area name"
#                 )
        
#         # Update business area directly
#         for key, value in update_data.items():
#             setattr(business_area, key, value)
        
#         await db.commit()
#         await db.refresh(business_area)
        
#         return StandardResponse(
#             status_code=200,
#             message="Business area updated successfully",
#             data=business_area
#         )
#     except Exception as e:
#         logger.error(f"Error updating business area: {str(e)}", exc_info=True)
#         return ErrorResponse(
#             status_code=500,
#             message="Failed to update business area",
#             error=str(e)
#         )


# @router.delete("/business-area/{area_id}")
# async def delete_business_area(
#     area_id: str,
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     """Delete a business area"""
#     try:
#         # Convert string to UUID
#         try:
#             area_uuid = uuid.UUID(area_id)
#         except ValueError:
#             return ErrorResponse(
#                 status_code=400,
#                 message="Invalid area ID format",
#                 error="Area ID must be a valid UUID"
#             )
        
#         # Check if business area exists
#         result = await db.execute(
#             select(BusinessArea).filter(BusinessArea.id == area_uuid)
#         )
#         business_area = result.scalars().first()
        
#         if not business_area:
#             return ErrorResponse(
#                 status_code=404,
#                 message="Business area not found",
#                 error="Area ID not found"
#             )
        
#         # Delete business area
#         await db.delete(business_area)
#         await db.commit()
        
#         return StandardResponse(
#             status_code=200,
#             message="Business area deleted successfully"
#         )
#     except Exception as e:
#         logger.error(f"Error deleting business area: {str(e)}", exc_info=True)
#         return ErrorResponse(
#             status_code=500,
#             message="Failed to delete business area",
#             error=str(e)
#         )