import json
from app.kafka_consumer.config import create_broadcast_consumer
from app.utils.broadcast import process_broadcast


broadcast_consumer = create_broadcast_consumer()

async def consume_broadcast_consumer():
    async for msg in broadcast_consumer:
        print(f"Got msg at offset {msg.offset}")
        data = msg.value.decode()
        data_dict = json.loads(data)
        try:
            status = await process_broadcast(data_dict)
            if status == 200:
                await broadcast_consumer.commit()
                print(f"Committed offset {msg.offset}")
            else:
                print(f"Failed to process message at offset {msg.offset}")
        except BaseException as err:
            print(f"Exception caused due to: {err}")
            print(f"Error: {err} – Not committing offset {msg.offset}")