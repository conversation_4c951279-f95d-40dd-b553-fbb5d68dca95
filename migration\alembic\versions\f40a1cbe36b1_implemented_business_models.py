"""Implemented Business models

Revision ID: f40a1cbe36b1
Revises: 0f9e3a323467
Create Date: 2025-06-23 11:00:50.992846
"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'f40a1cbe36b1'
down_revision: Union[str, None] = '0f9e3a323467'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add new columns
    op.add_column('business', sa.Column('auth_id', sa.String(), nullable=True))
    op.add_column('business', sa.Column('page_position', sa.String(), nullable=True))

    # Make business_name nullable
    op.alter_column('business', 'business_name',
               existing_type=sa.VARCHAR(),
               nullable=True)

    # Create the ENUM type if it doesn't exist
    business_type_enum = postgresql.ENUM(
        'SOLE_PROPRIETOR', 'LIMITED_LIABILITY', 'INFORMAL_BUSINESS',
        name='businesstype'
    )
    business_type_enum.create(op.get_bind(), checkfirst=True)

    # Convert business_type column from VARCHAR to ENUM using explicit cast
    op.execute(
        "ALTER TABLE business ALTER COLUMN business_type TYPE businesstype USING business_type::businesstype"
    )

    # Ensure the column is nullable (optional if already is)
    op.alter_column('business', 'business_type', nullable=True)

    # Create constraints
    op.create_unique_constraint(None, 'business', ['id'])
    op.create_unique_constraint(None, 'business_area', ['id'])
    op.create_unique_constraint(None, 'business_service_mapping', ['id'])

    # Make service_rate_sheet.units nullable (enum type already exists)
    op.alter_column('service_rate_sheet', 'units',
               existing_type=postgresql.ENUM('HOUR', 'MINUTE', 'SQ_FT', 'METER', name='serviceunits'),
               nullable=True)


def downgrade() -> None:
    """Downgrade schema."""
    # Revert service_rate_sheet.units to not nullable
    op.alter_column('service_rate_sheet', 'units',
               existing_type=postgresql.ENUM('HOUR', 'MINUTE', 'SQ_FT', 'METER', name='serviceunits'),
               nullable=False)

    # Drop constraints
    op.drop_constraint(None, 'business_service_mapping', type_='unique')
    op.drop_constraint(None, 'business_area', type_='unique')
    op.drop_constraint(None, 'business', type_='unique')

    # Revert ENUM back to VARCHAR
    op.alter_column('business', 'business_type',
               existing_type=postgresql.ENUM('SOLE_PROPRIETOR', 'LIMITED_LIABILITY', 'INFORMAL_BUSINESS', name='businesstype'),
               type_=sa.VARCHAR(),
               nullable=False)

    # Revert business_name back to not nullable
    op.alter_column('business', 'business_name',
               existing_type=sa.VARCHAR(),
               nullable=False)

    # Drop newly added columns
    op.drop_column('business', 'page_position')
    op.drop_column('business', 'auth_id')

    # Optionally drop ENUM type (if you want to clean it up)
    op.execute("DROP TYPE IF EXISTS businesstype")
