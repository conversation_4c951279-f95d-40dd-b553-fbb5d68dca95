from typing import List, Optional, Union
from fastapi import Form, UploadFile, File
from pydantic import UUID4, BaseModel, Field
from datetime import datetime, date, time
from app.utils.enums import DisputeStatus, DisputeType, DisputePriority, UserType, ChargebackStatus
from pydantic import BaseModel
from uuid import UUID

class DisputesCreate:
    def __init__(
        self,
        booking_id: str = Form(),
        dispute_type: DisputeType = Form(...),
        dispute_sub_type: str = Form(...),
        issue_category_id: str = Form(...),
        description: Optional[str] = Form(None),
        user_type: UserType = Form(...),
        priority: Optional[DisputePriority] = None,
    ):
        self.booking_id = booking_id
        self.dispute_type = dispute_type
        self.dispute_sub_type = dispute_sub_type
        self.issue_category_id = issue_category_id
        self.description = description
        self.user_type = user_type
        self.priority = priority  # We'll set this in the route handler based on category
    
    def _get_priority_from_type(self, dispute_type: DisputeType) -> DisputePriority:
        priority_map = {
            DisputeType.PAYMENT_AND_REFUND: DisputePriority.CRITICAL,
            DisputeType.BOOKING_ISSUES: DisputePriority.HIGH,
            DisputeType.SERVICE_ISSUES: DisputePriority.MEDIUM,
            DisputeType.OTHERS: DisputePriority.LOW
        }
        return priority_map.get(dispute_type, DisputePriority.MEDIUM)


class DisputesUpdate(BaseModel):
    status: Optional[DisputeStatus] = None
    priority: Optional[DisputePriority] = None
    assigned_to: Optional[str] = None
    resolution_notes: Optional[str] = None
    resolution_date: Optional[datetime] = None
    refund_amount: Optional[float] = None
    chargeback_status: Optional[ChargebackStatus] = None


class IssueDetails(BaseModel):
    code: str
    description: str


class IssuesCreate(BaseModel):
    title: str
    issues: List[IssueDetails]


class IssuesUpdate(BaseModel):
    id: UUID4
    title: str
    issues: List[IssueDetails]


class DisputeCategory(BaseModel):
    category_name: str
    issues: List[IssueDetails]


class DisputeCommentResponse(BaseModel):
    id: UUID4
    author_id: UUID4
    author_name: str
    comment: str
    date: datetime
    
    class Config:
        orm_mode = True


class DisputeHistoryResponse(BaseModel):
    id: UUID4
    event_code: str
    event_name: Optional[str] = None  # We'll populate this from the event_type relationship
    user: str
    date: datetime
    
    class Config:
        orm_mode = True


class DisputeResponse(BaseModel):
    id: UUID4
    case_number: str
    dispute_type: DisputeType
    dispute_sub_type: str
    status: DisputeStatus
    priority: DisputePriority
    created_date: datetime
    last_updated_date: Optional[datetime]
    user_type: UserType
    customer_id: UUID4
    customer_name: Optional[str]
    merchant_id: UUID4
    merchant_name: Optional[str]
    booking_id: UUID4
    assigned_to: Optional[str]
    transaction_id: Optional[str]
    transaction_date: Optional[datetime]
    transaction_amount: Optional[float]
    disputed_amount: Optional[float]
    currency: Optional[str]
    description: Optional[str]
    supporting_documents: Optional[List[str]]
    resolution_date: Optional[datetime]
    resolution_notes: Optional[str]
    refund_amount: Optional[float]
    chargeback_status: Optional[ChargebackStatus]
    created_by: Optional[str]
    updated_by: Optional[str]
    comments: Optional[List[DisputeCommentResponse]] = []
    history: Optional[List[DisputeHistoryResponse]] = []


class IssueCategorySchema(BaseModel):
    id: UUID4
    name: str
    code: str
    description: Optional[str] = None
    default_priority: DisputePriority
    
    class Config:
        orm_mode = True


class IssueCategoryCreate(BaseModel):
    name: str
    code: str
    description: Optional[str] = None
    user_type: UserType = None
    default_priority: DisputePriority = DisputePriority.MEDIUM
    issue_types: List[IssueDetails] = []


class DisputeCommentCreate(BaseModel):
    dispute_id: UUID4
    comment: str
    status: Optional[DisputeStatus] = None
    resolution_notes: Optional[str] = None


class DisputeEventTypeCreate(BaseModel):
    code: str
    name: str
    description: Optional[str] = None


class DisputeEventTypeResponse(BaseModel):
    id: UUID4
    code: str
    name: str
    description: Optional[str] = None
    
    class Config:
        orm_mode = True


class DisputeListFilters(BaseModel):
    q: Optional[str] = None
    user_id: Optional[str] = None
    artisan_id: Optional[str] = None
    priority: Optional[List[DisputePriority]] = None
    status: Optional[List[DisputeStatus]] = None
    dispute_type: Optional[List[DisputeType]] = None
    assigned_to: Optional[str] = None
    limit: int = 10
    skip: int = 1


class DisputeAssignmentUpdate(BaseModel):
    dispute_id: UUID
    assigned_to: str