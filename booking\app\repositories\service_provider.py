from sqlalchemy.orm import Session
from app.models import ServiceProvider
from app.schemas.service_provider import ServiceProviderStatusUpdate, ServiceProviderLeaveCreate, ServiceProviderLeaveUpdate
from sqlalchemy import and_
from datetime import date
from app.models import ServiceProviderLeave, ArtisanDetail

def update_service_provider_status(db: Session, account_id: str, status_data: ServiceProviderStatusUpdate):
    print(account_id, ' Account ID')
    service_provider = db.query(ArtisanDetail).filter(ArtisanDetail.user_id == account_id).first()
    print(service_provider, ' Service provider')
    if not service_provider:
        return None  # Return None if not found

    service_provider.live_status = status_data.live_status
    service_provider.latitude = status_data.current_latitude
    service_provider.longitude = status_data.current_longitude
    db.commit()
    db.refresh(service_provider)
    
    return service_provider

def create_leave(db: Session, service_provider_id: str, leave_data: ServiceProviderLeaveCreate):
    leave = ServiceProviderLeave(
        service_provider_id=service_provider_id,
        **leave_data.dict()
    )
    db.add(leave)
    db.commit()
    db.refresh(leave)
    return leave

def get_service_provider_leaves(db: Session, service_provider_id: str, start_date: date = None):
    query = db.query(ServiceProviderLeave).filter(
        ServiceProviderLeave.service_provider_id == service_provider_id
    )
    
    if start_date:
        query = query.filter(ServiceProviderLeave.leave_date >= start_date)
    
    return query.all()

def update_leave(db: Session, leave_id: str, leave_data: ServiceProviderLeaveUpdate):
    leave = db.query(ServiceProviderLeave).filter(ServiceProviderLeave.id == leave_id).first()
    if not leave:
        return None
        
    for key, value in leave_data.dict(exclude_unset=True).items():
        setattr(leave, key, value)
    
    db.commit()
    db.refresh(leave)
    return leave

def delete_leave(db: Session, leave_id: str):
    leave = db.query(ServiceProviderLeave).filter(ServiceProviderLeave.id == leave_id).first()
    if leave:
        db.delete(leave)
        db.commit()
    return leave
