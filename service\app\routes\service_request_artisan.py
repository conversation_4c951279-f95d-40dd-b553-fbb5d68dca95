from fastapi import APIRouter, Depends, Header,Request
from app.schemas.service_request_artisan import UpdateArtisanStatusRequest
from app.models_enum import BookingStatus,ArtisanAssignStatus, ActivityType
from app.database import get_db
from app.kafka_producer.producer import service_request_producer
from sqlalchemy.orm import Session
from app.utils.auth import get_id_header
from app.utils.activity_feed import log_activity
from app.utils.crud import service_ongoing, start_service,end_service,start_ongoing
from typing import Annotated
from app.schemas.helper import StandardResponse, ErrorResponse
from app.models import Booking, Invoice,InvoiceItem,ArtisanAssigned
from app.models_enum import InvoiceStatusEnum
from app.utils.notification import send_push_notification
from app.utils.auth import permission_checker
from fastapi.responses import JSONResponse
from datetime import date
from sqlalchemy import func

router = APIRouter(tags=["Service Request-artisan"])


@router.put("/status-update")
async def update_artisan_status(
    request_data: UpdateArtisanStatusRequest,
    request:Request,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        today = date.today()

        token = request.headers.get("Authorization")

        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user
        #print("RAW HEADERS ↓↓↓")
        # for key, value in request.headers.items():
        #     print("{key}: {value}")
        # print("Extracted Authorization →", Authorization)
        # # 1. Authorization check
        # resp = await get_id_header(Authorization)
        # print(resp,"authresp")
        # if resp.status_code != 200:
        #     return ErrorResponse(status_code=401, message="Unauthorized")

        booking_obj = db.query(Booking).filter(Booking.id == request_data.booking_id).first()
        if not booking_obj:
            return ErrorResponse(status_code=404, message="Booking not found")

        # 2. Validate Invoice by booking_id
        invoice = db.query(Invoice).filter(Invoice.booking_id == request_data.booking_id).first()
        if not invoice:
            return ErrorResponse(status_code=404, message="Invoice not found for given booking_id")

        # 3. Find related InvoiceItem by service_id and invoice_id
        invoice_id = invoice.id
        invoice_item = (
            db.query(InvoiceItem)
            .filter(
                InvoiceItem.invoice_id == invoice_id,
                InvoiceItem.service_id == request_data.service_id
            )
            .first()
        )
        if not invoice_item:
            return ErrorResponse(status_code=404, message="InvoiceItem not found for the given service_id")
        print(f"✅ Invoice ID: {invoice.id} | Invoice Item ID: {invoice_item.id}")
        # 4. Validate ArtisanAssigned with invoice_id, invoice_item_id, artisan_id
        artisan_assignment = (
            db.query(ArtisanAssigned)
            .filter(
                ArtisanAssigned.invoice_id == invoice.id,
                ArtisanAssigned.invoice_item_id == invoice_item.id,
                ArtisanAssigned.id == request_data.id
            )
            .first()
        )
        print("line73")
        # print(f"✅ Invoice ID: {artisan_assignment.invoice_id} | Invoice Item ID: {artisan_assignment.invoice_item_id} | artisan_id:{artisan_assignment.artisan_id}")
        if not artisan_assignment:
            return ErrorResponse(status_code=404, message="Artisan assignment not found")

        if artisan_assignment.status == request_data.status:
            return ErrorResponse(
                status_code=400,
                message="Artisan status already updated"
            )

        # 5. Update status
        # artisan_assignment.status = request.status
        # db.commit()
        # db.refresh(artisan_assignment)

        user_id = str(booking_obj.user_id)
        print("line",user_id)
        if not user_id:
            return ErrorResponse(status_code=404, message="User ID not found in booking")

        # 6. Optional push notification on ARRIVED
        # FOr Slide to ARRIVED Button
        try:
            if request_data.status == ArtisanAssignStatus.ONGOING:
                print("line85")
                start_ongoing(db, request_data, ArtisanAssigned, token, user_id)
                log_activity(
                    db=db,
                    title="Artisan Arrival",
                    description="Provider has arrived to the location",
                    reference_id=request_data.booking_id,
                    customer_id=booking_obj.user_id,
                    activity_type=ActivityType.ARTISAN_ARRIVAL,
                )

                return StandardResponse(
                status_code=200,
                message="Artisan status updated successfully",
                # data=booking
            )
            

            # For START Button
            elif request_data.status == ArtisanAssignStatus.STARTED:
                #check the booking_date equals to current date
                if booking_obj.booking_date != today:
                    return ErrorResponse(
                        status_code=400,
                        message="You can only start a service on the same day as the booking date."
                    )
                # Check if the artisan has any other ongoing service
                print("INSIDE STARTED")
                ongoing_service = (
                    db.query(ArtisanAssigned)
                    .filter(
                        ArtisanAssigned.artisan_id == artisan_assignment.artisan_id,
                        ArtisanAssigned.status.in_([
                            ArtisanAssignStatus.STARTED, 
                            ArtisanAssignStatus.ONGOING
                        ]),
                        ArtisanAssigned.id != artisan_assignment.id,
                        func.date(ArtisanAssigned.created_at) == today  # Exclude current
                    )
                    .first()
                )
                # print(ArtisanAssigned.created_at,today,"dateeee")
                if ongoing_service:
                    return ErrorResponse(
                        status_code=400,
                        message="You must complete your ongoing service before starting a new one."
                    )

                print("line95")
                start_service(db, request_data, ArtisanAssigned, token, user_id)
                booking_obj.status = BookingStatus.ONGOING
                db.commit()

                log_activity(
                    db=db,
                    title="Artisan Started",
                    description="Provider has started the service",
                    reference_id=request_data.booking_id,
                    customer_id=booking_obj.user_id,
                    activity_type=ActivityType.ARTISAN_START,
                )
                # db.query(ArtisanAssigned).filter(ArtisanAssigned.id == request_data.id).update({"status": ArtisanAssignStatus.STARTED})
                # booking = start_service(db, request_data, ArtisanAssigned, token)

            elif request_data.status == ArtisanAssignStatus.COMPLETED:
                if invoice.payment_status != InvoiceStatusEnum.PAID:
                    return ErrorResponse(status_code=409, message="Payment not completed for this service", error={'status': 'pending'})
            
                end_service(db, request_data, ArtisanAssigned, token, user_id)
                all_completed = db.query(ArtisanAssigned).filter(ArtisanAssigned.invoice_id == invoice.id, ArtisanAssigned.status != ArtisanAssignStatus.COMPLETED).count()
                print(all_completed, "all_completed")
                log_activity(
                    db=db,
                    title="Service Completed",
                    description="Provider has completed the service",
                    reference_id=request_data.booking_id,
                    customer_id=booking_obj.user_id,
                    activity_type=ActivityType.SERVICE_COMPLETED,
                )
                if all_completed == 0:
                    # Update booking status to COMPLETED
                    booking_obj.status = BookingStatus.COMPLETED
                    db.commit()
                    print("Booking status updated to COMPLETED")

                    if booking_obj.assigned_agent_id:
                        print("line190")
                        # send the fcm to AGENT
                        send_push_notification(
                            auth_token=token,
                            title="Service Completed",
                            message=f"Service has completed",
                            sender_id=str(booking_obj.assigned_agent_id),
                            type="agent",
                            user_id= str(booking_obj.assigned_agent_id),  # assigned agent id
                            fcm_request_type ="booking",
                            data={
                                "booking_id": str(booking_obj.id),
                                "booking_order_id": str(booking_obj.booking_order_id)
                            }
                        )

            return StandardResponse(
            status_code=200,
            message="Artisan status updated successfully",
            # data=booking
            )
        except Exception as e:
            db.rollback()  # Rollback in case of error
            return ErrorResponse(status_code=500, message="Error updating artisan status", error=str(e))
    except Exception as e:
        return ErrorResponse(status_code=500, message="Internal server error", error=str(e))




# @router.put("/status-update")
# async def update_booking_status(
#     request: UpdateBookingStatusRequest,
#     db: Session = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     try:
#         resp = await get_id_header(Authorization)
#         if resp.status_code != 200:
#             return ErrorResponse(status_code=401, message="Unauthorized")

#         booking_result = db.query(Booking).filter(Booking.id == request.id).first()

#         if not booking_result:
#             return ErrorResponse(status_code=404, message="Booking not found")

#         if booking_result.status == request.status:
#             return ErrorResponse(
#                 status_code=400, message="Booking status is already updated"
#             )

#         setattr(booking_result, "status", request.status)

#         db.commit()
#         db.refresh(booking_result)

#         if request.status == ArtisanAssignStatus.ARRIVED:
#             send_push_notification(
#                 auth_token=Authorization,
#                 title=f"Artisan Arrived - {booking_result.start_otp}",
#                 message=f"Start OTP:{booking_result.start_otp} - Artisan has arrived to your location!",
#                 sender_id=str(booking_result.user_id),
#                 type="user",
#                 data={
#                     "start_otp": booking_result.start_otp,
#                 },
#             )

#         if request.status == BookingStatus.STARTED:
#             send_push_notification(
#                 auth_token=Authorization,
#                 title="Service Started",
#                 message=f"Service has started",
#                 sender_id=str(booking_result.user_id),
#                 type="user",
#                 data={
#                     "end_otp": booking_result.end_otp,
#                 },
#             )

#         return StandardResponse(
#             status_code=200,
#             message="Artisan-arrival status updated successfully",
#             data=booking_result,
#         )

#     except Exception as e:
#         return ErrorResponse(status_code=500, message=f"Error", error=str(e))
