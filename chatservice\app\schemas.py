from uuid import UUID
from pydantic import BaseModel
from typing import Optional
from enum import Enum

class ConversationType(str, Enum):
    direct = "direct"
    support = "support"

class ConversationStatus(str, Enum):
    open = "open"
    closed = "closed"

class RoleType(str, Enum):
    customer = "customer"
    agent = "agent"

class ChatType(str, Enum):
    direct = "direct"
    support = "support"

class AgentCreate(BaseModel):
    name: str

class MessageCreate(BaseModel):
    conversation_id: UUID
    sender_id: UUID
    content: str
    chat_type: ChatType

class CustomerRequest(BaseModel):
    customer_id: UUID
    booking_id: Optional[UUID] = None

class chatinitiaterequest(BaseModel):
    customer_id: Optional[UUID] = None
    agent_id: Optional[UUID] = None
    artisan_id: Optional[UUID] = None
    booking_id: Optional[UUID] = None
    service_id: Optional[UUID] = None

class convesationrequest(BaseModel):
    conversation_id: UUID
    agent_id: Optional[UUID] = None
    customer_id: Optional[UUID] = None
    booking_id: Optional[UUID] = None