import asyncio
import logging
import tempfile
import os
from datetime import datetime
from typing import Dict, Any, Optional
import pandas as pd
from app.kafka_producer.producer import cognito_producer
from app.redis_config import redis_client
import uuid
import json

logger = logging.getLogger(__name__)

# Redis key prefix for task status
TASK_STATUS_PREFIX = "bulk_upload_task:"
TASK_LIST_KEY = "bulk_upload_tasks"


def _save_task_to_redis(task_id: str, task_data: Dict[str, Any]):
    """Save task data to Redis."""
    try:
        redis_key = f"{TASK_STATUS_PREFIX}{task_id}"
        redis_client.setex(redis_key, 86400, json.dumps(task_data))  # 24 hours TTL
        redis_client.sadd(TASK_LIST_KEY, task_id)
        logger.info(f"Saved task {task_id} to Redis")
    except Exception as e:
        logger.error(f"Error saving task {task_id} to Redis: {e}")


def _get_task_from_redis(task_id: str) -> Optional[Dict[str, Any]]:
    """Get task data from Redis."""
    try:
        redis_key = f"{TASK_STATUS_PREFIX}{task_id}"
        task_data = redis_client.get(redis_key)
        if task_data:
            return json.loads(task_data)
        return None
    except Exception as e:
        logger.error(f"Error getting task {task_id} from Redis: {e}")
        return None


def _update_task_in_redis(task_id: str, updates: Dict[str, Any]):
    """Update task data in Redis."""
    try:
        redis_key = f"{TASK_STATUS_PREFIX}{task_id}"
        current_data = redis_client.get(redis_key)
        if current_data:
            task_data = json.loads(current_data)
            task_data.update(updates)
            redis_client.setex(redis_key, 86400, json.dumps(task_data))  # 24 hours TTL
            logger.debug(f"Updated task {task_id} in Redis")
    except Exception as e:
        logger.error(f"Error updating task {task_id} in Redis: {e}")


def _get_all_task_ids() -> list:
    """Get all task IDs from Redis."""
    try:
        return list(redis_client.smembers(TASK_LIST_KEY))
    except Exception as e:
        logger.error(f"Error getting task IDs from Redis: {e}")
        return []


async def run_bulk_upload_task(file_path: str, token: str) -> str:
    """
    Start a bulk upload task and return the task ID.
    
    Args:
        file_path: Path to the uploaded Excel file
        token: Authorization token
        
    Returns:
        Task ID for tracking
    """
    task_id = str(uuid.uuid4())
    
    # Initialize task status
    initial_task_data = {
        "task_id": task_id,
        "status": "running",
        "progress": 0,
        "message": "Starting bulk upload process...",
        "total_rows": 0,
        "processed_rows": 0,
        "successful_creations": 0,
        "failed_creations": 0,
        "skipped_count": 0,
        "error_details": [],
        "skipped_details": [],
        "start_time": datetime.now().isoformat(),
        "end_time": None,
        "duration_seconds": None,
        "current_row": 0,
        "processing_stage": "initializing"
    }
    
    # Save to Redis
    _save_task_to_redis(task_id, initial_task_data)
    
    # Start the background task
    asyncio.create_task(_process_bulk_upload(task_id, file_path, token))
    
    return task_id


async def _process_bulk_upload(task_id: str, file_path: str, token: str):
    """
    Process the bulk upload in the background.
    
    Args:
        task_id: Unique task identifier
        file_path: Path to the Excel file
        token: Authorization token
    """
    start_time = datetime.now()
    
    try:
        # Update status to reading file
        _update_task_in_redis(task_id, {
            "processing_stage": "reading_file",
            "message": "Reading Excel file..."
        })
        
        # Read the Excel file
        try:
            df = pd.read_excel(file_path)
            total_rows = len(df)
            
            _update_task_in_redis(task_id, {
                "total_rows": total_rows,
                "processing_stage": "processing_rows",
                "message": f"Processing {total_rows} rows..."
            })
            
        except Exception as e:
            logger.error(f"Error reading Excel file for task {task_id}: {e}")
            _update_task_in_redis(task_id, {
                "status": "failed",
                "message": f"Error reading Excel file: {str(e)}",
                "end_time": datetime.now().isoformat(),
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            })
            return
        
        # Process each row
        successful_creations = 0
        failed_creations = 0
        skipped_count = 0
        error_details = []
        skipped_details = []
        
        for index, row in df.iterrows():
            current_row = index + 1
            
            # Update progress
            progress = int((current_row / total_rows) * 100)
            _update_task_in_redis(task_id, {
                "current_row": current_row,
                "processed_rows": current_row,
                "progress": progress,
                "message": f"Processing row {current_row}/{total_rows} ({progress}%)"
            })
            
            # Process single row
            result = await _process_single_row(row, token, current_row)
            
            if result["success"]:
                successful_creations += 1
            elif result["skipped"]:
                skipped_count += 1
                skipped_details.append(result["message"])
            else:
                failed_creations += 1
                error_details.append(result["message"])
            
            # Update counts
            _update_task_in_redis(task_id, {
                "successful_creations": successful_creations,
                "failed_creations": failed_creations,
                "skipped_count": skipped_count,
                "error_details": error_details,
                "skipped_details": skipped_details
            })
            
            # Small delay to prevent overwhelming the system
            await asyncio.sleep(0.01)
        
        # Finalize task status
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        _update_task_in_redis(task_id, {
            "status": "completed",
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
            "processing_stage": "completed",
            "message": f"Bulk upload completed. Success: {successful_creations}, Failed: {failed_creations}, Skipped: {skipped_count}"
        })
        
        # Clean up the temporary file
        try:
            os.remove(file_path)
        except Exception as e:
            logger.warning(f"Could not remove temporary file {file_path}: {e}")
            
    except Exception as e:
        logger.error(f"Error in bulk upload task {task_id}: {e}")
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        _update_task_in_redis(task_id, {
            "status": "failed",
            "message": f"Bulk upload failed: {str(e)}",
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
            "processing_stage": "failed"
        })


async def _process_single_row(row: pd.Series, token: str, row_number: int) -> Dict[str, Any]:
    """
    Process a single row from the Excel file.
    
    Args:
        row: Pandas Series containing row data
        token: Authorization token
        row_number: Current row number for logging
        
    Returns:
        Dictionary with processing result
    """
    try:
        # Extract and validate data
        reg_code = str(row["Trainee Reg. Code"]).strip()
        surname = str(row["Trainee Surname"]).strip()
        first_name = str(row["Trainee First Name"]).strip()
        other_names = (
            str(row["Other names of Trainee"]).strip()
            if pd.notna(row["Other names of Trainee"])
            else ""
        )
        trade_area = str(row["Trade Area(s)"]).strip()
        region = str(row["Region"]).strip()
        district = str(row["District/Town"]).strip()

        # Validate required fields
        if not all([reg_code, surname, first_name, trade_area, region, district]):
            return {
                "success": False,
                "skipped": True,
                "message": f"Row {row_number}: Missing required fields (reg_code, surname, first_name, trade_area, region, district)"
            }
            
        # Validate and format phone number
        phone_number_raw = row["Trainee Contact Number"]
        if pd.notna(phone_number_raw):
            phone_number = str(phone_number_raw).strip()
            # Remove any decimals that pandas might add
            if phone_number.endswith('.0'):
                phone_number = phone_number[:-2]
            # Basic phone number validation
            if not phone_number or len(phone_number) < 9:
                return {
                    "success": False,
                    "skipped": True,
                    "message": f"Row {row_number}: Invalid phone number: {phone_number}"
                }
        else:
            return {
                "success": False,
                "skipped": True,
                "message": f"Row {row_number}: Missing phone number"
            }
            
        # Process row to kafka topic
        data_dict = row.to_dict()
        data_dict["token"] = token
        
        await cognito_producer(data_dict)
        
        return {
            "success": True,
            "skipped": False,
            "message": f"Row {row_number}: Successfully sent to Kafka"
        }
        
    except Exception as e:
        return {
            "success": False,
            "skipped": False,
            "message": f"Row {row_number}: Error processing row - {str(e)}"
        }


def get_task_status(task_id: str) -> Dict[str, Any]:
    """
    Get the status of a specific task.
    
    Args:
        task_id: Task identifier
        
    Returns:
        Task status dictionary
    """
    task_data = _get_task_from_redis(task_id)
    if not task_data:
        return {
            "task_id": task_id,
            "status": "not_found",
            "message": "Task not found",
            "progress": 0,
            "total_rows": 0,
            "processed_rows": 0,
            "successful_creations": 0,
            "failed_creations": 0,
            "skipped_count": 0,
            "error_details": [],
            "skipped_details": [],
            "start_time": None,
            "end_time": None,
            "duration_seconds": None,
            "current_row": 0,
            "processing_stage": "not_found"
        }
    
    return task_data


def cleanup_completed_tasks(max_age_hours: int = 48):
    """
    Clean up completed or failed tasks older than specified hours.
    
    Args:
        max_age_hours: Maximum age in hours for tasks to be kept
    """
    task_ids = _get_all_task_ids()
    if not task_ids:
        return
    
    current_time = datetime.now()
    tasks_to_remove = []
    
    for task_id in task_ids:
        task_data = _get_task_from_redis(task_id)
        if task_data and task_data.get("status") in ["completed", "failed"]:
            start_time_str = task_data.get("start_time")
            if start_time_str:
                try:
                    start_time = datetime.fromisoformat(start_time_str)
                    age_hours = (current_time - start_time).total_seconds() / 3600
                    
                    if age_hours > max_age_hours:
                        tasks_to_remove.append(task_id)
                except ValueError:
                    # If start_time is not a valid ISO format, remove the task
                    tasks_to_remove.append(task_id)
    
    # Remove old tasks from Redis
    for task_id in tasks_to_remove:
        try:
            redis_key = f"{TASK_STATUS_PREFIX}{task_id}"
            redis_client.delete(redis_key)
            redis_client.srem(TASK_LIST_KEY, task_id)
            logger.info(f"Cleaned up old task: {task_id}")
        except Exception as e:
            logger.error(f"Error cleaning up task {task_id}: {e}")


def get_all_tasks() -> Dict[str, Dict[str, Any]]:
    """
    Get all active tasks.
    
    Returns:
        Dictionary of all tasks
    """
    task_ids = _get_all_task_ids()
    all_tasks = {}
    
    for task_id in task_ids:
        task_data = _get_task_from_redis(task_id)
        if task_data:
            all_tasks[task_id] = task_data
    
    return all_tasks 