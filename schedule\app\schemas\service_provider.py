from pydantic import BaseModel, UUID4, Field
from datetime import date, time    
from typing import Optional
from app.utils.enums import ExperienceRange


class ArtisanSearchRequest(BaseModel):
    service_id: str
    latitude: float
    longitude: float
    max_distance: float = 5  # Default max distance is 10 km
    start_time: time
    end_time: time
    booking_date: date
    sort_by: str = "rating"  # Default sort by rating
    page: int = 1
    limit: int = 10
    service_provider_id: Optional[UUID4] = None
    rating: Optional[float] = None
    experience_range: Optional[ExperienceRange] = None


class LiveArtisan(BaseModel):
    service_id: str
    latitude: float
    longitude: float
    max_distance: int = 10
    page: int = 1
    limit: int = 10
    # rating: Optional[float] = None
    # experience_range: Optional[ExperienceRange] = None
    
