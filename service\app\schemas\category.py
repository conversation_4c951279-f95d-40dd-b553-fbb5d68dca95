from pydantic import BaseModel, UUID4
from typing import Optional
from fastapi import Form

class CreateCategory:
    def __init__(
        self,
        name: Optional[str] = Form(None),
        parent_id: Optional[UUID4] = Form(None)
    ):
        self.name = name
        self.parent_id = parent_id

    def to_dict(self):
        return {
            "name": self.name,
            "parent_id": self.parent_id
            }

class CategoryResponse(BaseModel):
    id: UUID4
    banner: Optional[str] = None


class UpdateCategory:
    def __init__(
        self,
        name: Optional[str] = Form(None),
        parent_id: Optional[str] = Form(None)
    ):
        self.name = name
        self.parent_id = parent_id


class CreateSubCategory(BaseModel):
    def __init__(
        self,
        name: str = Form(),
        category_id: UUID4 = Form(),
    ):
        self.name = name
        self.category_id = category_id

class SubCategoryResponse(CreateSubCategory):
    id: UUID4

class UpdateSubCategory:
    def __init__(
        self,
        name: Optional[str] = Form(None),
        category_id: Optional[UUID4] = Form(None),
        banner: Optional[str] = Form(None),
    ):
        self.name = name
        self.category_id = category_id
        self.banner = banner