from pydantic import BaseModel, EmailStr, <PERSON>son, UUID4
from datetime import datetime, date
from typing import Optional, Union
from fastapi import Form
from pydantic.types import conint
from app.models_enum import PricingType, ServiceType, ServiceUnits

class CreateCategory:
    def __init__(
        self,
        name: Optional[str] = Form(None),
        parent_id: Optional[UUID4] = Form(None)
    ):
        self.name = name
        self.parent_id = parent_id

    def to_dict(self):
        return {
            "name": self.name,
            "parent_id": self.parent_id
            }

class CategoryResponse(BaseModel):
    id: UUID4
    banner: Optional[str] = None


class UpdateCategory:
    def __init__(
        self,
        name: Optional[str] = Form(None),
        parent_id: Optional[str] = Form(None)
    ):
        self.name = name
        self.parent_id = parent_id


class CreateSubCategory(BaseModel):
    def __init__(
        self,
        name: str = Form(),
        category_id: UUID4 = Form(),
    ):
        self.name = name
        self.category_id = category_id

class SubCategoryResponse(CreateSubCategory):
    id: UUID4

class UpdateSubCategory:
    def __init__(
        self,
        name: Optional[str] = Form(None),
        category_id: Optional[UUID4] = Form(None),
        banner: Optional[str] = Form(None),
    ):
        self.name = name
        self.category_id = category_id
        self.banner = banner


class CreateServices:
    def __init__(
        self,
        name: Optional[str] = Form(None),
        parent_id: Optional[UUID4] = Form(None),
        duration: Optional[int] = Form(0),
        price: Optional[float] = Form(0),
        description: Optional[str] = Form(None),
        service_code: Optional[str] = Form(None),
    ):
        self.name = name
        self.parent_id = parent_id
        self.duration = duration
        self.price = price
        self.description = description
        self.service_code = service_code

    def to_dict(self):
        return {
            "name": self.name,
            "parent_id": str(self.parent_id),
            "duration": self.duration,
            "price": self.price,
            "description": self.description,
            "service_code": self.service_code
            }


class ServicesResponse(BaseModel):
    id: UUID4
    name: str
    parent_id: UUID4
    banner: str
    duration: int

class UpdateServices:
    def __init__(
        self,
        name: Optional[str] = Form(None),
        parent_id: Optional[UUID4] = Form(None),
        duration: Optional[int] = Form(0),
        price: Optional[float] = Form(0),
        description: Optional[str] = Form(None),
        service_code: Optional[str] = Form(None),
    ):
        self.name = name
        self.parent_id = parent_id
        self.duration = duration
        self.price = price
        self.description = description
        self.service_code = service_code

class CreateServiceRateSheet(BaseModel):
    service_id: str
    artisan_id: str
    pricing_type: PricingType
    price_per_unit:Optional[float] = None
    service_type: ServiceType
    units:Optional[ServiceUnits] = None
    booking_fee: float
    minimum_booking_time: Optional[float] = None

    def to_dict(self):
        return {
            "service_id": self.service_id,
            "artisan_id": self.artisan_id,
            "pricing_type": self.pricing_type,
            "price_per_unit": self.price_per_unit,
            "service_type": self.service_type,
            "units": self.units,
            "booking_fee": self.booking_fee,
            "minimum_booking_time": self.minimum_booking_time
        }

class UpdateServiceRateSheet(BaseModel):
    pricing_type: Optional[PricingType] = None
    price_per_unit: Optional[float] = None
    service_type: Optional[ServiceType] = None
    units: Optional[ServiceUnits] = None
    booking_fee: Optional[float] = None
    minimum_booking_time: Optional[float] = None

    def to_dict(self):
        return {k: v for k, v in self.dict().items() if v is not None}

class ServiceRateSheetListRequest(BaseModel):
    service_id: Optional[str] = None
    artisan_id: Optional[str] = None
    pricing_type: Optional[str] = None
    service_type: Optional[str] = None
    units: Optional[str] = None
    q: Optional[str] = None
    skip: Optional[int] = 0
    limit: Optional[int] = 100

