import app.models as md

def db_get_phone_and_email_info(email, phone, db):

    email_info = db.query(md.ServiceProviders).filter(md.ServiceProviders.Business_Email == email).first()
    phn_info = db.query(md.ServiceProviders).filter(md.ServiceProviders.Business_Phone == phone).first()
    return email_info, phn_info

def db_get_service_provider_info(sp_id, db):

    sp_info = db.query(md.ServiceProviders).filter(md.ServiceProviders.id == sp_id).first()
    return sp_info

def db_get_service_providers(db):
    sp_info = db.query(md.ServiceProviders).all()
    return sp_info