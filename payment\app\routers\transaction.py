from datetime import datetime, timedelta
import http
import uuid
import os
import random
from sqlalchemy.ext.declarative import DeclarativeMeta
import json
from fastapi import APIRouter, Depends, HTTPException, Header, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_,cast
from typing import Optional, Annotated

from app.database import get_db
from app import models, schemas
from app.utils import (
    BookingStatus,
    check_datetime,
    create_record,

    update_record,
    delete_record,
    get_id_header,
    is_valid_uuid,
)
from app.notification import send_push_notification
import json
import requests
from app.config import settings

# from app.utils import TransactionTypeType, StatusType
# from app.models import Payment, Transaction
from app.utils import TransactionTypeType, StatusType, PaymentMethodType, get_surcharges, get_tax
from app.schemas import UpdateTransactionStatus

router = APIRouter()
from app.helper import StandardResponse, ErrorResponse
from app.models import Account, Booking, ServiceProvider, Transaction, Payment, Users
from sqlalchemy.sql import text

from app.models import Services, Category, Payment,UserProfiles,Invoice
import logging
from sqlalchemy import select, and_, select, join, outerjoin,or_
from sqlalchemy.orm import aliased
from sqlalchemy.orm.attributes import flag_modified
from app.utils import StatusType
from uuid import UUID
from app.auth import permission_checker
# Create aliases for Users and ServiceProvider tables
Users_to = aliased(Users)
ServiceProvider_to = aliased(ServiceProvider)

# Configure the logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class AlchemyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj.__class__, DeclarativeMeta):
            # Convert SQLAlchemy model to dictionary
            fields = {}
            for field in [
                x for x in dir(obj) if not x.startswith("_") and x != "metadata"
            ]:
                data = obj.__getattribute__(field)
                try:
                    json.dumps(data)
                    fields[field] = data
                except TypeError:
                    fields[field] = None
            return fields
        return json.JSONEncoder.default(self, obj)


# ================================
# Transaction Routes
# ================================
@router.post("/transaction-create")
async def create_transaction(
    request: schemas.CreateTransaction = Depends(),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Create a new Transaction record.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        request_dict = request.dict()
        print(request_dict)
        # payment_method = request.dict().get("payment_method")
        # if payment_method == "card":

        # print(response.text)

        new_transaction = await create_record(db, Transaction, request_dict)
        if isinstance(new_transaction, str):
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": new_transaction}
            )

        return StandardResponse(
            status_code=200,
            message="Transaction created successfully",
            data={"transaction_id": str(new_transaction.id)},
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/transaction-read/{id}")
async def read_transaction(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Fetch a single Transaction by UUID `id`.
    """
    try:
        # resp = await get_id_header(Authorization)
        # if resp.status_code != 200:
        #     return ErrorResponse(status_code=400, message={"error": resp.json()})

        trans_id = uuid.UUID(id)
        query = select(Transaction).filter(Transaction.id == trans_id)
        result = await db.execute(query)
        transaction_obj = result.scalars().first()

        if not transaction_obj:
            return ErrorResponse(status_code=404, message="Transaction not found")

        return StandardResponse(
            status_code=200,
            message="Transaction fetched successfully",
            data=transaction_obj.as_dict(),
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/transaction-list")
async def list_transactions(
    skip: int = 0,
    limit: int = 10,
    transaction_type: Optional[str] = None,
    payment_method: Optional[str] = None,
    status: Optional[str] = None,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker),
):
    """
    Retrieve a paginated list of Transactions.
    """
    try:
        filters = []

        if from_date:
            from_date_obj = datetime.strptime(from_date, "%Y-%m-%d")
            filters.append(Transaction.created_at >= from_date_obj)

        if to_date:
            to_date_obj = datetime.strptime(to_date, "%Y-%m-%d") + timedelta(days=1) - timedelta(seconds=1)
            filters.append(Transaction.created_at <= to_date_obj)

        if transaction_type and transaction_type.lower() not in {"null", ""}:
            try:
                filters.append(Transaction.transaction_type == TransactionTypeType(transaction_type.upper()))
            except ValueError:
                return ErrorResponse(
                    status_code=400,
                    message=f"Invalid transaction_type value: {transaction_type}.",
                )

        if payment_method and payment_method.lower() not in {"null", ""}:
            try:
                filters.append(Transaction.payment_method == PaymentMethodType(payment_method.upper()))
            except ValueError:
                return ErrorResponse(
                    status_code=400,
                    message=f"Invalid payment_method value: {payment_method}.",
                )

        if status and status.lower() not in {"null", ""}:
            try:
                filters.append(Transaction.status == StatusType(status.upper()))
            except ValueError:
                return ErrorResponse(
                    status_code=400,
                    message=f"Invalid status value: {status}.",
                )          

        query = (
            select(Transaction)
            .where(and_(*filters))
            .offset(skip)
            .limit(limit)
            .order_by(Transaction.created_at.desc())
        )

        result = await db.execute(query)
        transactions = result.scalars().all()

        count_query = select(func.count(Transaction.id)).where(and_(*filters))
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()

        processed_transactions = []

        for txn in transactions:
            data = txn.as_dict()

            # Defaults
            data["booking_id"] = None
            data["booking_order_id"] = None
            data["from_entity_name"] = None
            data["to_entity_name"] = None
            data["from_entity_phone"] = None
            data["to_entity_phone"] = None

           # Booking info via Payment → Invoice → Booking
            if txn.payment_id:
                payment = await db.get(Payment, txn.payment_id)
                if payment:
                    invoice = await db.get(Invoice, payment.invoice_id)
                    if invoice:
                        data["booking_id"] = invoice.booking_id
                        
                        # Fetch booking object to get booking_order_id
                        booking = await db.get(Booking, invoice.booking_id)
                        if booking:
                            data["booking_order_id"] = booking.booking_order_id

            # From entity info
            if txn.from_entity:
                from_user = await db.get(UserProfiles, txn.from_entity)
                if from_user:
                    full_name = f"{from_user.first_name or ''} {from_user.last_name or ''}".strip()
                    phone = f"{from_user.country_code or ''}{from_user.phone_number or ''}".strip()
                    data["from_entity_name"] = full_name if full_name else None
                    data["from_entity_phone"] = phone if phone else None

            # To entity info
            if txn.to_entity:
                to_user = await db.get(UserProfiles, txn.to_entity)
                if to_user:
                    full_name = f"{to_user.first_name or ''} {to_user.last_name or ''}".strip()
                    phone = f"{to_user.country_code or ''}{to_user.phone_number or ''}".strip()
                    data["to_entity_name"] = full_name if full_name else None
                    data["to_entity_phone"] = phone if phone else None

            processed_transactions.append(data)

        return StandardResponse(
            status_code=200,
            message="Transactions fetched successfully",
            data={"total": total_count, "data": processed_transactions},
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Internal error: {e}")



@router.put("/transaction-update/{id}")
async def update_transaction(
    id: str,
    request: schemas.UpdateTransaction = Depends(),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Update an existing Transaction.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        trans_id = uuid.UUID(id)
        query = select(Transaction).filter(Transaction.id == trans_id)
        result = await db.execute(query)
        get_transaction = result.scalars().first()

        if not get_transaction:
            return ErrorResponse(status_code=404, message="Transaction not found")

        res = await update_record(db, request, get_transaction)
        return StandardResponse(
            status_code=200, message="Transaction updated successfully", data=res
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.delete("/transaction-delete/{id}")
async def delete_transaction(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Delete a Transaction record by UUID `id`.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        trans_id = uuid.UUID(id)
        query = select(Transaction).filter(Transaction.id == trans_id)
        result = await db.execute(query)
        get_transaction = result.scalars().first()

        if not get_transaction:
            return ErrorResponse(status_code=404, message="Transaction not found")

        res = await delete_record(db, get_transaction)
        return StandardResponse(
            status_code=200, message="Transaction deleted successfully", data=res
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.put("/update-transaction-status")
async def update_transaction_status(
    request: UpdateTransactionStatus,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Update transaction status, gateway_status, and gateway_response in the Payment and Transaction tables.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        # Validate UUID
        if not is_valid_uuid(request.payment_id):
            return ErrorResponse(status_code=400, message="Invalid payment ID format")

        payment_id = uuid.UUID(request.payment_id)

        # Find the payment
        query = select(Payment).filter(Payment.id == payment_id)
        result = await db.execute(query)
        payment = result.scalars().first()

        if not payment:
            return ErrorResponse(status_code=404, message="Payment not found")

        # Update payment status if provided
        if request.status is not None:
            payment.status = request.status

        # Save payment changes
        await db.commit()
        await db.refresh(payment)

        # Now update the related transaction(s)
        if request.status is not None:
            # Find transactions related to this payment
            trans_query = select(Transaction).filter(
                Transaction.payment_id == str(payment_id),
                Transaction.from_entity == str(payment.artisan_id),
            )
            trans_result = await db.execute(trans_query)
            transactions = trans_result.scalars().all()

            # Update each matching transaction
            for transaction in transactions:
                transaction.status = request.status
                if request.gateway_status is not None:
                    transaction.gateway_status = request.gateway_status
                if request.gateway_response is not None:
                    transaction.gateway_response = request.gateway_response

            await db.commit()

        return StandardResponse(
            status_code=200,
            message="Payment and transaction status updated successfully",
            data=payment.as_dict(),
        )

    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message=f"Error: {e}")

@router.get("/transaction-status/{id}")
async def get_transaction_status(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Fetch a transaction status for given Payment `id`.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        payment_id = uuid.UUID(id)  # raises ValueError if invalid
        query = select(Transaction).filter(Transaction.payment_id == payment_id, Transaction.transaction_type == 'PAYMENT').order_by(Transaction.created_at.desc()).limit(1)
        result = await db.execute(query)
        trans_obj = result.scalars().first()

        if not trans_obj:
            return ErrorResponse(status_code=404, message="Transaction not found")
        
        status = trans_obj.status
        if status != 'INIT':
            payment_query = select(Payment).filter(Payment.id == payment_id)
            payment_result = await db.execute(payment_query)
            payment_obj = payment_result.scalars().first()
            payment_obj.status = status
            await db.commit()
            await db.refresh(payment_obj)


        return StandardResponse(
            status_code=200,
            message="Transaction fetched successfully",
            data=trans_obj.as_dict(),
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")
