from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError
from sqlalchemy.future import select
import logging
import random
import string
import time
from app.redis_config import redis_client

logger = logging.getLogger("crud_utils")

async def create_record(
    db: AsyncSession,
    model_class,
    payload: dict,
):
    """
    Create a new database record.

    Args:
        db (AsyncSession): The async database session.
        model_class: The SQLAlchemy model class.
        payload (dict): Dictionary of field values.

    Returns:
        model_instance OR str: The created object, or error message on failure.
    """
    try:
        obj = model_class(**payload)
        db.add(obj)
        await db.commit()
        await db.refresh(obj)
        return obj

    except IntegrityError as e:
        logger.error(f"Integrity error: {e}")
        await db.rollback()
        return str(e.orig)  # Returns constraint error message

    except Exception as e:
        logger.exception(f"Unexpected error while creating {model_class.__name__}")
        await db.rollback()
        return str(e)


async def update_record(db, model, obj_id, data_dict):
    await db.execute(
        model.__table__.update()
        .where(model.id == obj_id)
        .values(**data_dict)
    )
    await db.commit()
    result = await db.execute(select(model).where(model.id == obj_id))
    return result.scalars().first()

async def delete_record(db: AsyncSession, model, obj_id):
    result = await db.execute(select(model).where(model.id == obj_id))
    instance = result.scalars().first()
    if not instance:
        return None

    await db.delete(instance)
    await db.commit()


def generate_cognito_password(length=12):
    """
    Generates a Cognito-compliant random password using epoch time for uniqueness.
    """
    if length < 8:
        raise ValueError("Password length must be at least 8 characters.")

    # Get last 5 digits of the current epoch time
    epoch_part = str(int(time.time()))[-5:]

    # Define character groups
    uppercase = random.choice(string.ascii_uppercase)  # At least 1 uppercase
    lowercase = random.choice(string.ascii_lowercase)  # At least 1 lowercase
    digit = random.choice(string.digits)  # At least 1 digit
    special = random.choice("!@#$%^&*")  # At least 1 special character

    # Generate remaining random characters
    remaining_length = length - 5 - 4  # 5 from epoch + 4 required chars
    random_chars = "".join(
        random.choices(
            string.ascii_letters + string.digits + "!@#$%^&*", k=remaining_length
        )
    )

    # Combine all parts
    password = uppercase + lowercase + digit + special + random_chars + epoch_part

    # Shuffle the password to avoid predictable patterns
    password = "".join(random.sample(password, len(password)))

    return password