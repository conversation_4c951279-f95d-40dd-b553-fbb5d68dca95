import asyncio
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.kafka_consumer.consumer import broadcast_consumer, consume_broadcast_consumer
import logging

log = logging.getLogger("uvicorn")

app = FastAPI(
    title="API",
    description="FastAPI-based backend for services",
    version="1.0.0",
    docs_url="/broadcast/docs",           # Swagger UI
    redoc_url=None,                     # Disable ReDoc (optional)
    openapi_url="/broadcast/openapi.json"
)



# CORS Middleware (Allow all origins for frontend integration)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Change to specific frontend domain in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Start up event for FastAPI application."""
    log.info("Starting up...")
    kafka_conn = False
    while not kafka_conn: 
        try:
            await broadcast_consumer.start()
            kafka_conn = True
        except:
            pass
    log.info("Kafka Started...")
    asyncio.create_task(consume_broadcast_consumer())

@app.on_event("shutdown")
async def shutdown_event():
    """Shutdown event for FastAPI application."""
    log.info("Shutting down...")
    await broadcast_consumer.stop()


@app.get("/health")
def read_root():
    return {"message": "Service is Up and running"}
