from pydantic import BaseModel, Field
from typing import Optional, List
from uuid import UUID
from datetime import time
from app.models_enum import UserStatusType

class ArtisanDetailResponse(BaseModel):
    user_id: UUID
    skill: Optional[str]
    skill_level: Optional[str]
    experience: Optional[float] = 0.0
    about_us: Optional[str]
    reg_code: Optional[str]
    live_status: Optional[str]
    license: Optional[str]
    work_from_hrs: Optional[time]
    work_to_hrs: Optional[time]
    break_from_hrs: Optional[time]
    break_to_hrs: Optional[time]
    weekdays: Optional[List[int]]
    is_profile_complete: Optional[bool] = False
    is_confirmed: Optional[bool] = False

    class Config:
        orm_mode = True


class ServiceProviderStatusUpdate(BaseModel):
    artisan_id: Optional[str]
    live_status: str    # online/offline/live
    current_latitude: float = Field(..., ge=-90, le=90)  # Latitude must be between -90 and 90
    current_longitude: float = Field(..., ge=-180, le=180)  # Longitude must be between -180 and 180

class ArtisanListRequest(BaseModel):
    skip: int = 1
    limit: int = 10
    user_id: Optional[str] = None
    q: Optional[str] = None
    skill: Optional[str] = None
    status: Optional[UserStatusType] = None
    live_status: Optional[str] = None
    is_confirmed: Optional[bool] = None
    order_by: Optional[str] = "DESC"  # Direction
    sort_by: Optional[str] = "created_at"
