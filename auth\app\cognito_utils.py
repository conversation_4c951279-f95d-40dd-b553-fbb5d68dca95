import boto3
import hmac
import hashlib
import base64
from app.config import get_settings
import jwt


# ✅ Get settings
settings = get_settings()

# ✅ Create Cognito Client with Credentials
cognito_client = boto3.client(
    "cognito-idp",
    region_name=settings.COGNITO_REGION,
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,  # Use Client ID as access key
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,  # Use Client Secret as secret key
    # aws_access_key_id=settings.COGNITO_CLIENT_ID,  # Use Client ID as access key
    # aws_secret_access_key=settings.COGNITO_CLIENT_SECRET  # Use Client Secret as secret key
)


# ✅ Function to Generate Secret Hash for Cognito
def generate_secret_hash(username, client_id, client_secret):
    """
    Generates a secret hash required for AWS Cognito API calls.
    """
    message = username + client_id
    dig = hmac.new(client_secret.encode(), message.encode(), hashlib.sha256).digest()
    return base64.b64encode(dig).decode()


async def get_user_attributes(username: str):
    """
    Retrieves all user attributes from Cognito using an access token.
    
    Args:
        token: The Cognito access token
        
    Returns:
        Dictionary of user attributes
    """
    try:
        print(username, 'username')
        user_info = cognito_client.admin_get_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=username
        )
        
        # Convert attributes list to dictionary for easier access
        attributes = {}
        print(user_info, 'user_info')
        for attr in user_info.get('UserAttributes', []):
            attributes[attr['Name']] = attr['Value']
        
        # Add username to attributes
        attributes['username'] = user_info.get('Username')
        print(attributes, 'attributes')
        
        return attributes
        
    except Exception as e:
        # print(e)
        raise Exception(f"Cognito Error: {e}")

