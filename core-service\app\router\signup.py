from fastapi import APIRout<PERSON>, Depends, Header
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import delete
from sqlalchemy.orm import selectinload
from botocore.exceptions import ClientError
from uuid import UUID
from typing import Annotated
from app.auth import validate_token

from app.database import get_db
from app.models import (
    UserProfiles,
    Roles,
    ArtisanDetail,
    ServiceProviderServiceMapping,
    ServiceProviders,
)
from app.schemas import (
    SignupRequest,
    OtpVerifyRequest,
    RefreshRequest,
    ResendOtpRequest,
    CognitoGetUserRequest,
    AdminSignupRequest,
    ForgotPasswordRequest,
    ResetPasswordRequest,
    DeleteUser,
    TestSignupRequest,
)
from app.helper import StandardResponse, ErrorResponse
from app.utils import create_record
from app.models_enum import UserStatusType, ServiceProviderStatusType
from app.config import get_settings

from app.cognito_utils import (
    admin_get_user,
    cognito_admin_login,
    create_cognito_user,
    get_token_after_signup,
    password_login,
    resend_confirmation_code,
    add_user_to_group,
    cognito_sign_up,
    confirm_signup,
    cognito_renew_token,
    signup,
    update_cognito_attributes,
    generate_secret_hash,
    cognito_client,
    delete_cognito_user,
    generate_cognito_password,
)

router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/test-signup")
async def test_signup(request: TestSignupRequest, db: AsyncSession = Depends(get_db)):

    if (
        get_settings().TEST_USER_PASSWORD is None
        or len(get_settings().TEST_USER_PASSWORD) == 0
    ):
        return {"result": "Only local dev environment can do this operation."}

    cognito_resp = signup(
        phone_number=request.country_code + request.phone_number,
        password=get_settings().TEST_USER_PASSWORD,
        email=request.email,
    )
    auth_id = cognito_resp.get("User", {}).get("Username")
    role_query = await db.execute(select(Roles).where(Roles.role_name == request.role))
    role = role_query.scalars().first()
    if not role:
        return ErrorResponse(
            status_code=404, message=f"Role '{request.role}' not found"
        )

    user_obj = UserProfiles(
        phone_number=request.phone_number,
        auth_id=auth_id,
        status=UserStatusType.CREATED,
        role_id=role.id,
        country_code=request.country_code,
        is_test=True,
    )
    db.add(user_obj)
    await db.commit()
    await db.refresh(user_obj)
    return {"created_user": cognito_resp}


@router.post("/resend-signup-otp", response_model=StandardResponse)
async def resend_otp(request: ResendOtpRequest, db: AsyncSession = Depends(get_db)):
    phone_number = request.phone_number

    try:
        # Check if user exists in Cognito
        user_status = admin_get_user(phone_number)
        if user_status is None:
            return ErrorResponse(status_code=404, message="User not found in Cognito")

        # Check if user is already confirmed
        if user_status.get("UserStatus") == "CONFIRMED":
            return ErrorResponse(status_code=400, message="User already confirmed")

        # Resend OTP
        cognito_resp = resend_confirmation_code(phone_number)

        return StandardResponse(
            status_code=200,
            message="OTP resent successfully",
            data=cognito_resp,
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Error resending OTP",
            error=str(e),
        )


@router.post("/renew-token")
def get_new_access_token(request: RefreshRequest):
    try:
        return cognito_renew_token(
            refresh_token=request.refresh_token,
            role=request.role,
            phone_number=request.phone_number,
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Unexpected error occurred", error=str(e)
        )


@router.post("/cognito-get-user", response_model=StandardResponse)
async def cognito_get_user(
    request: CognitoGetUserRequest, db: AsyncSession = Depends(get_db)
):
    phone_number = request.phone_number
    role = request.role
    try:
        if not phone_number:
            return ErrorResponse(
                status_code=422,
                message="Phone number is required",
                error="missing_phone_number",
            )

        # Get user from Cognito
        user_status = admin_get_user(phone_number)
        if user_status is None:
            return ErrorResponse(
                status_code=404, message="User not found in Cognito", error="not_found"
            )

        # Check Cognito user status
        if user_status.get("UserStatus") != "CONFIRMED":
            return ErrorResponse(
                status_code=400, message="User not confirmed", error="not_confirmed"
            )

        if user_status.get("Enabled") is False:
            return ErrorResponse(
                status_code=403, message="User is disabled in Cognito", error="disabled"
            )

        # Now check local DB (user_profiles)
        query = select(UserProfiles).filter(UserProfiles.phone_number == phone_number)
        result = await db.execute(query)
        user = result.scalars().first()

        if not user:
            return ErrorResponse(
                status_code=404, message="User not found in database", error="not_found"
            )

        return StandardResponse(
            status_code=200, message="User found in Cognito and DB", data=user.status
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Error fetching user from Cognito", error=str(e)
        )


@router.post("/logout")
def logout(Authorization: str = Header(...)):
    """
    Logout user by calling Cognito's global_sign_out using access token
    """
    try:
        access_token = Authorization.replace("Bearer ", "")
        print("Global sign out called for token.")
        print("Access Token:", access_token)
        cognito_client.global_sign_out(AccessToken=access_token)
        try:

            response = cognito_client.get_user(AccessToken=access_token)
            print("Token is still active:", response)

        except cognito_client.exceptions.NotAuthorizedException:
            return ErrorResponse(
                status_code=200,
                message="Token has been revoked successfully after logout",
            )
        return StandardResponse(status_code=200, message="User logged out successfully")
    except Exception as e:
        return ErrorResponse(status_code=500, message="Logout failed", error=str(e))


@router.post("/signup-signin", response_model=StandardResponse)
async def signup_signin(request: SignupRequest, db: AsyncSession = Depends(get_db)):
    normal_number_without_country_code = request.phone_number
    country_code = request.country_code
    phone_number = country_code + normal_number_without_country_code
    role_name = request.role.value if request.role else None

    try:
        db_user_query = await db.execute(
            select(UserProfiles).where(
                UserProfiles.phone_number == normal_number_without_country_code
            )
        )
        db_user = db_user_query.scalars().first()

        cognito_user = admin_get_user(phone_number)

        # Signin
        if db_user and cognito_user:
            try:
                print("jjjjjjjjjjjjjjj", db_user.is_active)
                if cognito_user.get("Enabled") is False:
                    return ErrorResponse(
                        status_code=403,
                        message="User is disabled in Cognito",
                        error="DISABLED",
                    )
                if db_user.is_active == False:
                    return ErrorResponse(
                        status_code=403,
                        message="User is disabled in local database",
                        error="DISABLED",
                    )
                if (
                    get_settings().TEST_USER_PASSWORD is None
                    or len(get_settings().TEST_USER_PASSWORD) == 0
                ):
                    return ErrorResponse(
                        status_code=500,
                        message="Login failed",
                        error="Only local dev environment can do this operation.",
                    )
                if db_user.is_test == True:
                    return StandardResponse(
                        status_code=200,
                        message="Login successful - authentication initialized",
                        data={
                            "ChallengeName": "test-challenge",
                            "Session": "test-session",
                        },
                    )
                auth_response = cognito_client.initiate_auth(
                    AuthFlow="USER_AUTH",
                    ClientId=get_settings().COGNITO_CLIENT_ID,
                    AuthParameters={
                        "USERNAME": phone_number,
                        "PREFERRED_CHALLENGE": "SMS_OTP",
                        "SECRET_HASH": generate_secret_hash(
                            phone_number,
                            get_settings().CLIENT_ID,
                            get_settings().CLIENT_SECRET,
                        ),
                    },
                )
                return StandardResponse(
                    status_code=200,
                    message="Login successful - authentication initialized",
                    data=auth_response,
                )
            except Exception as e:
                return ErrorResponse(
                    status_code=500, message="Login failed", error=str(e)
                )

        # if cognito_user and not db_user:
        #     return ErrorResponse(
        #         status_code=404,
        #         message="User exists in Cognito but not in Database. Contact support.",
        #     )
        if cognito_user and not db_user:
            try:
                # Extract auth_id
                print("cognito_user found:", cognito_user)
                # auth_id = cognito_user.get("Username")
                auth_id = next(
                    attr["Value"]
                    for attr in cognito_user["UserAttributes"]
                    if attr["Name"] == "sub"
                )

                role_query = await db.execute(
                    select(Roles).where(Roles.role_name == role_name)
                )
                role = role_query.scalars().first()

                if not role:
                    return ErrorResponse(
                        status_code=404, message=f"Role '{role_name}' not found"
                    )

                user_obj = UserProfiles(
                    phone_number=normal_number_without_country_code,
                    auth_id=auth_id,
                    status=UserStatusType.CREATED,
                    role_id=role.id,
                    hash_password=None,
                    country_code=country_code,
                )
                db.add(user_obj)
                await db.commit()
                await db.refresh(user_obj)

                user_id = user_obj.id

                # Optional: update back Cognito with DB ID if not already there
                await update_cognito_attributes(
                    username=phone_number,
                    attributes={"custom:account_id": str(user_id)},
                    replace_all=False,
                )

                if role_name.upper() == "ARTISAN":
                    artisan_obj = ArtisanDetail(user_id=user_id)
                    db.add(artisan_obj)
                    await db.commit()
                if role_name.upper() == "BUSINESS_PROVIDER":
                    business_provider_obj = ServiceProviders(user_id=user_id)
                    db.add(business_provider_obj)
                    await db.commit()

                auth_response = cognito_client.initiate_auth(
                    AuthFlow="USER_AUTH",
                    ClientId=get_settings().COGNITO_CLIENT_ID,
                    AuthParameters={
                        "USERNAME": phone_number,
                        "PREFERRED_CHALLENGE": "SMS_OTP",
                        "SECRET_HASH": generate_secret_hash(
                            phone_number,
                            get_settings().CLIENT_ID,
                            get_settings().CLIENT_SECRET,
                        ),
                    },
                )

                return StandardResponse(
                    status_code=200,
                    message="User synced from Cognito and signed in",
                    data=auth_response,
                )

            except Exception as e:
                return ErrorResponse(
                    status_code=500,
                    message="User exists in Cognito but failed to sync or sign in",
                    error=str(e),
                )

        # signup
        if not cognito_user and not db_user:
            if not role_name:
                return ErrorResponse(
                    status_code=400,
                    message="Role is required for new users",
                )
            try:
                cognito_resp, encrypted_password = cognito_sign_up(phone_number)
                auth_id = cognito_resp.get("UserSub")
            except Exception as e:
                return ErrorResponse(
                    status_code=500, message="Error creating Cognito user", error=str(e)
                )

            role_query = await db.execute(
                select(Roles).where(Roles.role_name == role_name)
            )
            role = role_query.scalars().first()
            if not role:
                return ErrorResponse(
                    status_code=404, message=f"Role '{role_name}' not found"
                )

            user_obj = UserProfiles(
                phone_number=normal_number_without_country_code,
                auth_id=auth_id,
                status=UserStatusType.CREATED,
                role_id=role.id,
                hash_password=encrypted_password,
                country_code=country_code,
            )
            db.add(user_obj)
            await db.commit()
            await db.refresh(user_obj)

            user_id = user_obj.id

            try:
                await update_cognito_attributes(
                    username=phone_number,
                    attributes={"custom:account_id": str(user_id)},
                    replace_all=False,
                )

                if role_name.upper() == "ARTISAN":
                    artisan_obj = ArtisanDetail(user_id=user_id)
                    db.add(artisan_obj)
                    await db.commit()

                if role_name.upper() == "BUSINESS_PROVIDER":
                    business_provider_obj = ServiceProviders(user_id=user_id)
                    db.add(business_provider_obj)
                    await db.commit()

            except Exception as e:
                return ErrorResponse(
                    status_code=500,
                    message="User created but failed to update Cognito attributes",
                    error=str(e),
                )

            return StandardResponse(
                status_code=201,
                message="User created successfully",
                data={
                    "user_id": str(user_id),
                    "phone_number": phone_number,
                    "role": role_name,
                },
            )

    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Internal server error", error=str(e)
        )


@router.post("/otp-verify", response_model=StandardResponse)
async def otp_verify(request: OtpVerifyRequest, db: AsyncSession = Depends(get_db)):
    phone_number = request.country_code + request.phone_number
    confirmation_code = request.otp
    session = request.session

    try:
        user_status = admin_get_user(phone_number)

        if not user_status:
            return ErrorResponse(status_code=404, message="User not found in Cognito")

        # Login Flow
        if session:
            try:
                query = select(UserProfiles).filter(
                    UserProfiles.phone_number == request.phone_number
                )
                result = await db.execute(query)
                user_profile = result.scalars().first()

                if not user_profile:
                    return ErrorResponse(
                        status_code=404, message="User profile not found"
                    )

                if user_profile.is_test == False:
                    auth_response = cognito_client.respond_to_auth_challenge(
                        ChallengeName="SMS_OTP",
                        ClientId=get_settings().COGNITO_CLIENT_ID,
                        ChallengeResponses={
                            "USERNAME": phone_number,
                            "SMS_OTP_CODE": confirmation_code,
                            "SECRET_HASH": generate_secret_hash(
                                phone_number,
                                get_settings().COGNITO_CLIENT_ID,
                                get_settings().CLIENT_SECRET,
                            ),
                        },
                        Session=session,
                    )
                else:
                    if (
                        get_settings().TEST_USER_PASSWORD is None
                        or len(get_settings().TEST_USER_PASSWORD) == 0
                    ):
                        return ErrorResponse(
                            status_code=500,
                            message="Login OTP verification failed",
                            error="Only local dev environment can do this operation.",
                        )
                    auth_response = password_login(
                        identifier=phone_number,
                        password=get_settings().TEST_USER_PASSWORD,
                    )
                # Get user from DB using account_id from Cognito
                # account_id = next(
                #     (attr["Value"] for attr in user_status["UserAttributes"] if attr["Name"] == "custom:account_id"), None
                # )

                user_info = {
                    "user_id": str(user_profile.id),
                    "first_name": user_profile.first_name,
                    "last_name": user_profile.last_name,
                    "email": user_profile.email,
                    "phone_number": user_profile.phone_number,
                    "profile_image_url": user_profile.profile_image_url,
                    "role_id": str(user_profile.role_id),
                    "is_profile_complete": user_profile.is_profile_complete,
                }

                return StandardResponse(
                    status_code=200,
                    message="Login successful",
                    data={"token": auth_response, "user_info": user_info},
                )
            except Exception as e:
                return ErrorResponse(
                    status_code=500,
                    message="Login OTP verification failed",
                    error=str(e),
                )

        # Signup Flow
        if user_status.get("UserStatus") != "CONFIRMED":
            confirm_signup(phone_number, confirmation_code)

            account_id = next(
                (
                    attr["Value"]
                    for attr in user_status["UserAttributes"]
                    if attr["Name"] == "custom:account_id"
                ),
                None,
            )

            query = select(UserProfiles).filter(UserProfiles.id == account_id)
            result = await db.execute(query)
            user_profile = result.scalars().first()

            if not user_profile:
                return ErrorResponse(status_code=404, message="User profile not found")

            token_response = get_token_after_signup(
                phone_number, user_profile.hash_password
            )

            user_profile.status = UserStatusType.CREATED
            user_profile.hash_password = None
            await db.commit()
            await db.refresh(user_profile)

            user_info = {
                "user_id": str(user_profile.id),
                "first_name": user_profile.first_name,
                "last_name": user_profile.last_name,
                "email": user_profile.email,
                "phone_number": user_profile.phone_number,
                "profile_image_url": user_profile.profile_image_url,
                "role_id": str(user_profile.role_id),
                "is_profile_complete": user_profile.is_profile_complete,
            }

            return StandardResponse(
                status_code=200,
                message="Signup successful",
                data={"token": token_response, "user_info": user_info},
            )

        return ErrorResponse(
            status_code=400,
            message="User already confirmed. Please login instead.",
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500, message="OTP verification failed", error=str(e)
        )


@router.post("/superadmin/create_admin", response_model=StandardResponse)
async def signup_admin(
    request: AdminSignupRequest,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    print(Authorization)
    phone_number_full = request.country_code + request.phone_number
    try:
        if not Authorization or not Authorization.startswith("Bearer "):
            return ErrorResponse(
                status_code=401, message="Invalid Authorization header"
            )

        # token = Authorization.split(" ", 1)[1]

        # resp = await validate_token(token)

        # if "error" in resp:
        #     return ErrorResponse(status_code=401, message="Invalid token", error=resp["error"])

        # superadmin_id = resp.get("id")

        query = select(UserProfiles).where(
            UserProfiles.phone_number == request.phone_number
        )
        result = await db.execute(query)
        admin_exists = result.scalars().first()
        if admin_exists:
            return ErrorResponse(
                status_code=400, message="Admin or agent already exists in database"
            )

        email_query = select(UserProfiles).where(UserProfiles.email == request.email)
        email_result = await db.execute(email_query)
        if email_result.scalars().first():
            return ErrorResponse(
                status_code=400,
                message="Admin or agent already exists in database with this email",
            )

        try:
            cognito_resp = cognito_client.admin_create_user(
                UserPoolId=get_settings().COGNITO_USER_POOL_ID,
                Username=phone_number_full,
                UserAttributes=[
                    {"Name": "email", "Value": request.email},
                    {"Name": "phone_number", "Value": phone_number_full},
                    {"Name": "email_verified", "Value": "false"},
                    {"Name": "phone_number_verified", "Value": "true"},
                ],
                ForceAliasCreation=True,
                MessageAction="SUPPRESS",
            )
            cognito_client.admin_set_user_password(
                UserPoolId=get_settings().COGNITO_USER_POOL_ID,
                Username=phone_number_full,
                Password=generate_cognito_password(),
                Permanent=True,
            )
            print("Cognito user created successfully:", cognito_resp)
            # auth_id = cognito_resp.get("User", {}).get("Username")
            attributes = cognito_resp.get("User", {}).get("Attributes", [])
            auth_id = next(
                (attr["Value"] for attr in attributes if attr["Name"] == "sub"), None
            )

        except Exception as e:
            return ErrorResponse(
                status_code=500, message="Cognito Signup Failed", error=str(e)
            )

        role_query = await db.execute(
            select(Roles).where(Roles.role_name == request.role)
        )
        role = role_query.scalars().first()
        if not role:
            return ErrorResponse(
                status_code=404, message=f"Role '{request.role}' not found in DB"
            )

        admin_data = {
            "first_name": request.first_name,
            "last_name": request.last_name,
            "phone_number": request.phone_number,
            "country_code": request.country_code,
            "email": request.email,
            "auth_id": auth_id,
            "role_id": role.id,
            "status": UserStatusType.CREATED,
        }

        new_admin = await create_record(db, UserProfiles, admin_data)
        admin_id = str(new_admin.id)

        await update_cognito_attributes(
            cognito_resp["User"]["Username"], {"custom:account_id": admin_id}, False
        )

        return StandardResponse(
            status_code=201,
            message="Admin account created successfully",
            data={"admin_id": admin_id},
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Error creating admin", error=str(e)
        )


@router.delete("/admin/delete-user")
async def delete_user_by_phone(
    request: DeleteUser,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        if not Authorization or not Authorization.startswith("Bearer "):
            return ErrorResponse(
                status_code=401, message="Invalid Authorization header"
            )

        token = Authorization.split(" ", 1)[1]
        resp = await validate_token(token)

        if "error" in resp:
            return ErrorResponse(
                status_code=401, message="Invalid token", error=resp["error"]
            )

        full_phone_number = request.country_code + request.phone_number

        # Fetch user from DB
        query = (
            select(UserProfiles)
            .options(
                selectinload(UserProfiles.role),
                selectinload(UserProfiles.artisan_detail),
                selectinload(UserProfiles.addresses),
            )
            .where(
                UserProfiles.phone_number == request.phone_number,
                UserProfiles.country_code == request.country_code,
            )
        )
        result = await db.execute(query)
        user = result.scalar_one_or_none()

        if not user:
            return ErrorResponse(status_code=404, message="User not found in database")

        # Try deleting from Cognito
        try:
            await delete_cognito_user(full_phone_number)
        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            if error_code == "UserNotFoundException":
                pass  # Proceed with DB deletion
            else:
                return ErrorResponse(
                    status_code=500,
                    message=f"Cognito Error: {error_code}",
                    error=str(e),
                )
        except Exception as e:
            return ErrorResponse(
                status_code=500,
                message=f"Unexpected error deleting Cognito user: {str(e)}",
            )

        # Delete artisan_detail and service mappings if artisan
        if user.role and user.role.role_name.lower() == "artisan":
            if user.artisan_detail:
                await db.delete(user.artisan_detail)
            await db.execute(
                delete(ServiceProviderServiceMapping).where(
                    ServiceProviderServiceMapping.service_provider_id == user.id
                )
            )

        # Delete addresses
        for address in user.addresses or []:
            await db.delete(address)

        # Delete user
        await db.delete(user)
        await db.commit()

        return StandardResponse(
            status_code=200,
            message=f"User {full_phone_number} successfully deleted from Cognito (if existed) and database",
        )

    except Exception as e:
        import traceback

        print(traceback.format_exc())
        await db.rollback()
        return ErrorResponse(status_code=500, message=f"Error deleting user: {str(e)}")


@router.post("/forgot-password", response_model=StandardResponse)
async def forgot_password(
    request: ForgotPasswordRequest, db: AsyncSession = Depends(get_db)
):
    phone_number_full = request.country_code + request.phone_number

    query = select(UserProfiles).where(
        UserProfiles.phone_number == request.phone_number
    )
    result = await db.execute(query)
    user = result.scalar_one_or_none()

    if not user:
        return ErrorResponse(status_code=404, message="User not found")

    try:
        response = cognito_client.forgot_password(
            ClientId=get_settings().COGNITO_CLIENT_ID, Username=phone_number_full
        )
        return StandardResponse(
            status_code=200, message="OTP sent to registered phone/email"
        )
    except cognito_client.exceptions.UserNotFoundException:
        return ErrorResponse(status_code=404, message="User not found")
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Failed to send OTP", error=str(e)
        )


@router.post("/reset-password", response_model=StandardResponse)
async def reset_password(
    request: ResetPasswordRequest, db: AsyncSession = Depends(get_db)
):
    phone_number_full = request.country_code + request.phone_number
    query = select(UserProfiles).where(
        UserProfiles.phone_number == request.phone_number
    )
    result = await db.execute(query)
    user = result.scalar_one_or_none()

    if not user:
        return ErrorResponse(status_code=404, message="User not found")

    try:
        cognito_client.confirm_forgot_password(
            ClientId=get_settings().COGNITO_CLIENT_ID,
            Username=phone_number_full,
            ConfirmationCode=request.otp,
            Password=request.new_password,
        )
        return StandardResponse(status_code=200, message="Password reset successful")
    except cognito_client.exceptions.CodeMismatchException:
        return ErrorResponse(status_code=400, message="Invalid OTP")
    except cognito_client.exceptions.ExpiredCodeException:
        return ErrorResponse(status_code=400, message="OTP expired")
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Failed to reset password", error=str(e)
        )
