import uuid
from datetime import datetime
from app.kafka_producer.producer import invoice_producer
from app.schemas.artisan import ServiceProviderStatusUpdate
from sqlalchemy import extract
from sqlalchemy.orm import Session
from app.database import get_db_session
from app.utils.crud import create_record
from app.models import Booking, Cart, Account, ArtisanDetail, ServiceProviders
from app.utils.redis_config import redis_client

db = get_db_session()

SURCHARGES = 2.5
TAX = 17.5
CANCEL_THRESHOLD = 2
PENALTY_AMOUNT = 10


def get_surcharges(base_service_fee):
    return round(base_service_fee * SURCHARGES / 100, 2)

def get_tax(base_service_fee):
    return round(base_service_fee * TAX / 100, 2)

def get_booking_fee(base_service_fee, booking_fee_percentage):
    return round(base_service_fee * booking_fee_percentage / 100, 2)

def apply_penalty(db, penalty_user_type, penalty_user_id, penalty):
    if penalty_user_id and penalty_user_type.lower() == "user":
        acc = db.query(Account).filter(Account.user_id == penalty_user_id).first()
        acc.balance = acc.balance - penalty
        db.commit()
        db.refresh(acc)
    elif penalty_user_id and penalty_user_type.lower() == "artisan":
        acc = db.query(Account).filter(Account.user_id == penalty_user_id).first()
        acc.balance = acc.balance - penalty
        db.commit()
        db.refresh(acc)
    return

async def generate_booking_order_id(db: Session):
    """
    Generate a unique booking order ID in the format: REQ-MMYY-COUNT
    """
    try:
        prefix = "REQ"
        now = datetime.now()
        year_suffix = str(now.year)[-2:]
        month_suffix = f"{now.month:02d}"

        prefix_pattern = f"{prefix}-{month_suffix}{year_suffix}%"

        latest_booking = (
            db.query(Booking.id, Booking.booking_order_id)
            .filter(
                Booking.booking_order_id.like(prefix_pattern),
                extract('year', Booking.requested_time) == now.year,
                extract('month', Booking.requested_time) == now.month
            )
            .order_by(Booking.requested_time.desc())
            .first()
        )

        if latest_booking:
            latest_id, latest_order_id = latest_booking
            try:
                last_count = int(latest_order_id.split("-")[-1])
            except (ValueError, IndexError):
                last_count = 0
        else:
            latest_id = None
            last_count = 0

        new_count = last_count + 1
        new_order_id = f"{prefix}-{month_suffix}{year_suffix}-{new_count}"

        return new_order_id
    except Exception as e:
        print(f"Error generating booking order ID: {str(e)}")
        # Always return tuple
        fallback_id = f"{prefix}-{month_suffix}{year_suffix}-1"
        return fallback_id, None

def is_valid_uuid(val: str) -> bool:
    """Check if a string is a valid UUID"""
    try:
        uuid.UUID(val)
        return True
    except ValueError:
        return False


def check_file_exists(file):
    """Check if a file exists"""
    if file:
        return True
    return False

async def create_service_request(data):
    try:
        obj = create_record(db, Booking, data)
        if isinstance(obj, str):
            return 500
        #Create Invoice
        booking_id = str(obj.id)
        data["booking_id"] = booking_id
        # await invoice_producer(data)
        # return {"booking_id": booking_id, 'status': 200}
        return {"booking_obj": obj, 'status': 200}
    except Exception as e:
        print(e)
        return {'status': 500}


def get_cart_items(user_id):
    cart_items = []
    carts = db.query(Cart).filter(Cart.user_id == user_id, Cart.status == 'PENDING').all()
    print(carts, "carts")
    if carts:
        for cart in carts:
            cart_items.append(
                {
                    "service_id": str(cart.service_id),
                    "qty": cart.qty,
                    "description": cart.description,
                    "images": cart.attachments
                }
            )
        return cart_items
    else:
        return cart_items


def update_service_provider_status(db: Session, account_id: str, status_data: ServiceProviderStatusUpdate):
    print(account_id, ' Account ID')
    service_provider = db.query(ArtisanDetail).filter(ArtisanDetail.user_id == account_id).first()
    print(service_provider, ' Service provider')
    if not service_provider:
        return None  # Return None if not found

    service_provider.live_status = status_data.live_status
    service_provider.latitude = status_data.current_latitude
    service_provider.longitude = status_data.current_longitude
    db.commit()
    db.refresh(service_provider)
    
    return service_provider


def redis_update(key, data):
    redis_client.hset(key, mapping=data)


def get_sp_id(db: Session):
    """
    Get the Main service provider ID from the database.
    """
    sp_obj = db.query(ServiceProviders).first()
    if sp_obj:
        return str(sp_obj.id)
    return None
