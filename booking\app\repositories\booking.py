from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.models import Booking
from app.schemas.booking import (
    BookingCreate,
    ServiceRescheduleRequest,
    ServiceStart,
    ServiceEnd,
    ServiceReschedule,
)
from app.models_enum import BookingStatus
from app.models import BookingHistory


def create_booking(db: Session, booking_data: BookingCreate):
    booking = Booking(**booking_data.dict())
    db.add(booking)
    db.commit()
    db.refresh(booking)
    # Creating booking history
    create_booking_history(db, booking_obj=booking)
    return booking


def update_booking_status(db: Session, booking_id: str, status: BookingStatus):
    booking = db.query(Booking).filter(Booking.id == booking_id).first()
    if booking:
        booking.booking_status = status.value
        db.commit()
        db.refresh(booking)
    # Creating booking history
    create_booking_history(db, booking_obj=booking)
    return booking


def service_start(db: Session, data: ServiceStart):
    booking = db.query(Booking).filter(Booking.id == data.id).first()
    if booking:
        booking.status = BookingStatus.ONGOING
        booking.service_start = datetime.utcnow()
        booking.service_start_artisan_latitude = data.service_start_artisan_latitude
        booking.service_start_artisan_longitude = data.service_start_artisan_longitude
        db.commit()
        db.refresh(booking)
    # Creating booking history
    create_booking_history(db, booking_obj=booking)
    return booking


def service_end(db: Session, data: ServiceEnd):
    booking = db.query(Booking).filter(Booking.id == data.id).first()
    if booking:
        booking.status = BookingStatus.COMPLETED
        booking.service_end = datetime.utcnow()
        booking.service_end_artisan_latitude = data.service_end_artisan_latitude
        booking.service_end_artisan_longitude = data.service_end_artisan_longitude
        db.commit()
        db.refresh(booking)
    # Creating booking history
    create_booking_history(db, booking_obj=booking)
    return booking


def service_reschedule_request(
    db: Session, data: ServiceRescheduleRequest, service_duration: int
):
    booking = db.query(Booking).filter(Booking.id == data.id).first()
    if booking:
        booking.status = BookingStatus.PENDING
        booking.is_rescheduled = True
        booking_date = datetime.strptime(data.booking_date, "%Y-%m-%d").date()
        booking.booking_date = booking_date
        start_time = datetime.strptime(data.start_time, "%H:%M").time()
        end_time = datetime.strptime(data.end_time, "%H:%M").time()
        booking.start_time = start_time
        
        # Convert date and time to datetime before adding duration
        # start_datetime = datetime.combine(booking_date, start_time)
        # end_datetime = datetime.combine(booking_date, end_time)
        # end_datetime = start_datetime + timedelta(minutes=service_duration)
        booking.end_time = end_time
        
        db.commit()
        db.refresh(booking)
    # Creating booking history
    create_booking_history(db, booking_obj=booking)
    return booking


def service_reschedule(db: Session, data: ServiceReschedule):
    booking = db.query(Booking).filter(Booking.id == data.id).first()
    if booking:
        booking.status = BookingStatus.CONFIRMED
        db.commit()
        db.refresh(booking)
    # Creating booking history
    create_booking_history(db, booking_obj=booking)
    return booking


def create_booking_history(db, booking_obj=None):
    request_data = {
        "booking_id": booking_obj.id,
        "user_id": booking_obj.user_id,
        "artisan_id": booking_obj.artisan_id,
        "service_id": booking_obj.service_id,
        "status": booking_obj.status,
    }
    if request_data["status"] == BookingStatus.CANCELLED:
        request_data["reason"] = booking_obj.cancellation_reason
        # request_data["reason"] = booking_obj["cancellation_reason"]
    else:
        request_data["reason"] = ""
    bh_obj = BookingHistory(**request_data)
    db.add(bh_obj)
    db.commit()
    db.refresh(bh_obj)
