from sqlalchemy.orm import Session
from app.models.call_disposition import CallDis<PERSON>, CallDispositionCategory
from app.schemas.call_disposition import (
    CallDispositionCreate,
    CallDispositionUpdate,
    CallDispositionCategoryCreate,
    CallDispositionCategoryUpdate
)
from typing import List, Optional
from uuid import UUID
from fastapi import HTTPException

# CallDispositionCategory CRUD operations
def create_category(db: Session, category: CallDispositionCategoryCreate) -> CallDispositionCategory:
    db_category = CallDispositionCategory(**category.model_dump())
    try:
        db.add(db_category)
        db.commit()
        db.refresh(db_category)
        return db_category
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

def get_category(db: Session, category_id: UUID) -> CallDispositionCategory:
    category = db.query(CallDispositionCategory).filter(CallDispositionCategory.id == category_id).first()
    if not category:
        raise HTTPException(status_code=404, detail="Category not found")
    return category

def get_categories(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None
) -> List[CallDispositionCategory]:
    query = db.query(CallDispositionCategory)
    if is_active is not None:
        query = query.filter(CallDispositionCategory.is_active == is_active)
    return query.offset(skip).limit(limit).all()

def update_category(
    db: Session,
    category_id: UUID,
    category: CallDispositionCategoryUpdate
) -> CallDispositionCategory:
    db_category = get_category(db, category_id)
    update_data = category.model_dump(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_category, field, value)
    
    try:
        db.commit()
        db.refresh(db_category)
        return db_category
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

def delete_category(db: Session, category_id: UUID) -> bool:
    db_category = get_category(db, category_id)
    try:
        db.delete(db_category)
        db.commit()
        return True
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

# CallDisposition CRUD operations
def create_disposition(db: Session, disposition: CallDispositionCreate) -> CallDisposition:
    # Verify category exists
    get_category(db, disposition.category_id)
    
    db_disposition = CallDisposition(**disposition.model_dump())
    try:
        db.add(db_disposition)
        db.commit()
        db.refresh(db_disposition)
        return db_disposition
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

def get_disposition(db: Session, disposition_id: UUID) -> CallDisposition:
    disposition = db.query(CallDisposition).filter(CallDisposition.id == disposition_id).first()
    if not disposition:
        raise HTTPException(status_code=404, detail="Disposition not found")
    return disposition

def get_dispositions(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    category_id: Optional[UUID] = None,
    is_active: Optional[bool] = None
) -> List[CallDisposition]:
    query = db.query(CallDisposition)
    if category_id:
        query = query.filter(CallDisposition.category_id == category_id)
    if is_active is not None:
        query = query.filter(CallDisposition.is_active == is_active)
    return query.offset(skip).limit(limit).all()

def update_disposition(
    db: Session,
    disposition_id: UUID,
    disposition: CallDispositionUpdate
) -> CallDisposition:
    db_disposition = get_disposition(db, disposition_id)
    update_data = disposition.model_dump(exclude_unset=True)
    
    if 'category_id' in update_data:
        # Verify new category exists
        get_category(db, update_data['category_id'])
    
    for field, value in update_data.items():
        setattr(db_disposition, field, value)
    
    try:
        db.commit()
        db.refresh(db_disposition)
        return db_disposition
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

def delete_disposition(db: Session, disposition_id: UUID) -> bool:
    db_disposition = get_disposition(db, disposition_id)
    try:
        db.delete(db_disposition)
        db.commit()
        return True
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))