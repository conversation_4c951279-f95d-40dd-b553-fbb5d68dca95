from enum import Enum

class BookingStatus(str, Enum):
    REQUESTED = "REQUESTED"
    CONFIRMED = "CONFIRMED" #when artisan accepts booking
    AGENT_ASSIGNED = "AGENT_ASSIGNED"
    ARTISAN_ASSIGNED = "ARTISAN_ASSIGNED"
    ONGOING = "ONGOING" #when user starts service
    COMPLETED = "COMPLETED" #when user pays for service, should be update in webhook
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    CANCEL_REQUESTED = "CANCEL_REQUESTED"

class InvoiceItemStatus(str, Enum):
    PENDING = "PENDING"
    PAID = "PAID"
    CANCEL_REQUESTED = "CANCEL_REQUESTED"
    CANCELLED = "CANCELLED"

class PaymentType(str, Enum):
    CASH = "CASH"
    WALLET = "WALLET"
    CARD = "CARD"
    BANK = "BANK"

class UserType(str, Enum):
    ARTISAN = "ARTISAN"
    USER = "USER"
    AGENT = "AGENT"
    ADMIN= "ADMIN"
    SUPERADMIN= "SUPERADMIN"

class BookingCancellationStatus(str, Enum):
    # INITIATED = "initiated"
    PENDING = "pending"
    APPROVED = "approved"
    REFUND_INITIATED = "refund_initiated"
    REFUND_FAILED = "refund_failed"
    COMPLETED = "completed"
    REJECTED = "rejected"


class NotificationType(str, Enum):
    SMS = "sms"
    OTP = "otp"
    EMAIL = "email"
    PUSH = "push"

class PaymentMethodType(str, Enum):
    CARD = "CARD"
    WALLET = "WALLET"
    CASH = "CASH"
    BANK = "BANK"  # Using for artisan withdrawal

class StatusType(str, Enum):
    INIT = "INIT"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"


class CurrencyType(str, Enum):
    GHS = "GHS"
    EUR = "EUR"
    USD = "USD"
    AED = "AED"

class TransactionTypeType(str, Enum):
    PAYMENT = "PAYMENT"
    PAYOUT = "PAYOUT"
    REFUND = "REFUND"

class AccountStatusType(str, Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"


class AccountTypeType(str, Enum):
    APP = "APP"
    WALLET = "WALLET"
    BANK = "BANK"

class DeviceType(str, Enum):
    ANDROID = "android"
    IOS = "ios"

class Gender(str, Enum):
    MALE = "male"
    FEMALE = "female"

class ServiceProviderStatusType(str, Enum):
    CREATED = "created"
    APPROVED = "approved"
    REJECTED = "rejected"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"

# class UserStatusType(str, Enum):
#     APPROVED = "approved"
#     SUSPENDED = "suspended"
#     BLACKLIST = "blacklist"


class AdminRole(str, Enum):
    ADMIN = "admin"
    SUPERADMIN = "superadmin"
    AGENT = "agent"


class DisputeStatus(str, Enum):
    OPEN = "OPEN"
    IN_PROGRESS = "IN_PROGRESS"
    RESOLVED = "RESOLVED"

class DisputeType(str, Enum):
    BOOKING_ISSUES = "Booking Issues"
    PAYMENT_AND_REFUND = "Payment and Refund"
    SERVICE_ISSUES = "Service Issues"
    OTHERS = "Others"


class DisputePriority(str, Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class UserType(str, Enum):
    ARTISAN = "ARTISAN"
    USER = "USER"
    AGENT = "AGENT"
    ADMIN= "ADMIN"
    SUPERADMIN= "SUPERADMIN"

class ChargebackStatus(str, Enum):
    PENDING = "PENDING"
    REFUND_INITIATED = "REFUND_INITIATED"
    PROCESSED = "PROCESSED"

class IssuePriority(str, Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class IssueStatus(str, Enum) :
    OPEN = "OPEN"
    IN_PROGRESS = "IN_PROGRESS"
    RESOLVED = "RESOLVED"
class PricingType(str, Enum):
    FIXED = "FIXED"
    MEASURED = "MEASURED"
    CUSTOM = "CUSTOM"

class ServiceType(str, Enum):
    REGULAR = "REGULAR"
    PREMIUM = "PREMIUM"

class ServiceUnits(str, Enum):
    HOUR = "HOUR"
    MINUTE = "MINUTE"
    SQ_FT = "SQ/FT"
    METER = "METER"

class BusinessProviderStatus(str, Enum):
    CREATED = "created"
    APPROVED = "approved"
    REJECTED = "rejected"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"
    IN_REVIEW = "in_review"
    IN_PROGRESS = "in_progress"

class BusinessType(str, Enum):
    SOLE_PROPRIETOR = "sole_proprietor"
    LIMITED_LIABILITY = "limited_liability"
    INFORMAL_BUSINESS = "informal_business"


# class UserStatusType(str, Enum):
#     ACTIVE = "ACTIVE"
#     INACTIVE = "INACTIVE"
#     BLOCKED = "BLOCKED"

class InvoiceStatus(str, Enum):
    PENDING = "pending"
    PAID = "paid"
    FAILED = "failed"

class ServiceRequestType(str, Enum):
    INSTANT = "INSTANT"
    LATER = "LATER"

class UserStatusType(str, Enum):
    ACTIVE = "ACTIVE"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"
    INACTIVE = "INACTIVE"
    BLOCKED = "BLOCKED"
    CREATED = "CREATED"
 
class TaxType(str, Enum):
    STANDARD = "STANDARD"
    REDUCED = "REDUCED"
    ZERO = "ZERO"

class InvoiceStatusEnum(str, Enum):
    QUOTATION = "Quotation"
    ADVANCE_PAID = "AdvancePaid"
    PARTIAL_PAID = "PartialPaid"
    PAID = "Paid"
    REFUNDED = "Refunded"
    PARTIAL_REFUNDED = "PartialRefunded"

class CartStatus(str, Enum):
    PENDING = "PENDING"
    CHECKED_OUT = "CHECKED_OUT"
    
class ArtisanAssignStatus(str, Enum):
    ASSIGNED = "ASSIGNED"
    STARTED = "STARTED"
    ONGOING = "ONGOING"
    COMPLETED = "COMPLETED"
class CartStatus(str, Enum):
    PENDING = "PENDING"
    CHECKED_OUT = "CHECKED_OUT"

class KYCTypes(str, Enum):
    LICENCE = "license"
    CERTIFICATE = "certificate"
    GOVT_ID = "govt_id"
    POLICE_REPORT = "police_report"
    GUARANTEED_DOC = "guaranteed_doc"

class ValidationStatus(str, Enum):
    APPROVED = "approved"
    REJECTED = "rejected"
    PENDING = "pending"

class Gender(str, Enum):
    MALE = "male"
    FEMALE = "female"

class ConversationType(str, Enum):
    direct = "direct"
    support = "support"
    artisan="artisan"
    agent="agent"

class ConversationStatus(str, Enum):
    open = "open"
    closed = "closed"

class RoleType(str, Enum):
    user = "user"
    agent = "agent"
    artisan="artisan"

class ChatType(str, Enum):
    direct = "direct"
    support = "support"

class ActivityType(str, Enum):
    BOOKING_CREATED = "BOOKING_CREATED"
    SERVICE_FEE_PAID = "SERVICE_FEE_PAID"
    ARTISAN_ASSIGNED = "ARTISAN_ASSIGNED"
    ARTISAN_REASSIGNED = "ARTISAN_REASSIGNED"
    ADDITIONAL_SERVICE_ADDED = "ADDITIONAL_SERVICE_ADDED"
    QUOTE_GENERATED = "QUOTE_GENERATED"
    REASSIGN_ARTISAN = "REASSIGN_ARTISAN"
    ARTISAN_START = "ARTISAN_START"
    ARTISAN_ARRIVAL = "ARTISAN_ARRIVAL"
    SERVICE_COMPLETED = "SERVICE_COMPLETED"
    BOOKING_CANCELLATION_REQUESTED = "BOOKING_CANCELLATION_REQUESTED"
    BOOKING_CANCELLATION_APPROVED = "BOOKING_CANCELLATION_APPROVED"
    BOOKING_CANCELLATION_REJECTED = "BOOKING_CANCELLATION_REJECTED"