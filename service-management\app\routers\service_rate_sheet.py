import uuid
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Head<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import or_ , String
from sqlalchemy.future import select
from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Annotated, List, Optional
from app import schemas
from app.database import get_db
from app.utils import (
    create_record,
    delete_record,
    get_id_header,
    is_valid_uuid,
    update_record,
)
from app.models import ServiceRateSheet, Services, ServiceProvider
from app.helper import StandardResponse, ErrorResponse
from sqlalchemy.exc import SQLAlchemyError

router = APIRouter()

@router.post("/service-rate-sheet-create")
async def create_service_rate_sheet(
    request: schemas.CreateServiceRateSheet,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        # Validate service exists
        service_query = select(Services).filter(Services.id == uuid.UUID(request.service_id))
        service_result = await db.execute(service_query)
        service = service_result.scalars().first()
        if not service:
            return ErrorResponse(
                status_code=404,
                message="Service not found"
            )

        # Validate artisan exists
        artisan_query = select(ServiceProvider).filter(ServiceProvider.id == uuid.UUID(request.artisan_id))
        artisan_result = await db.execute(artisan_query)
        artisan = artisan_result.scalars().first()
        if not artisan:
            return ErrorResponse(
                status_code=404,
                message="Artisan not found"
            )

        request_dict = request.to_dict()
        new_rate_sheet = await create_record(db, ServiceRateSheet, request_dict)
        
        if isinstance(new_rate_sheet, str):
            return ErrorResponse(
                status_code=400,
                message=new_rate_sheet
            )
            
        return StandardResponse(
            status_code=200,
            message="Service rate sheet created successfully",
            data=new_rate_sheet
        )

    except SQLAlchemyError as e:
        return ErrorResponse(
            status_code=500,
            message="Service rate sheet creation failed",
            error=f"Database error: {str(e)}"
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Service rate sheet creation failed",
            error=f"Error: {e}"
        )

@router.get("/service-rate-sheet-read/{id}")
async def read_service_rate_sheet(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        query = select(ServiceRateSheet).filter(ServiceRateSheet.id == uuid.UUID(id))
        result = await db.execute(query)
        rate_sheet = result.scalars().first()
        
        if not rate_sheet:
            return ErrorResponse(
                status_code=404,
                message="Service rate sheet not found"
            )

        return StandardResponse(
            status_code=200,
            message="Service rate sheet read successfully",
            data=rate_sheet
        )
    except SQLAlchemyError as e:
        return ErrorResponse(
            status_code=500,
            message="Service rate sheet read failed",
            error=f"Database error: {str(e)}"
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Service rate sheet read failed",
            error=f"Error: {e}"
        )

@router.post("/service-rate-sheet-list")
async def list_service_rate_sheets(
    request: schemas.ServiceRateSheetListRequest,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        # Base query
        query = select(ServiceRateSheet)

        if request.q:
            query = query.filter(
                or_(
                    ServiceRateSheet.pricing_type.cast(String).ilike(f"%{request.q}%"),
                    ServiceRateSheet.service_type.cast(String).ilike(f"%{request.q}%"),
                    ServiceRateSheet.units.cast(String).ilike(f"%{request.q}%")
                )
            )

        if request.service_id:
            query = query.filter(ServiceRateSheet.service_id == uuid.UUID(request.service_id))
        if request.artisan_id:
            query = query.filter(ServiceRateSheet.artisan_id == uuid.UUID(request.artisan_id))
        if request.pricing_type:
            query = query.filter(ServiceRateSheet.pricing_type == request.pricing_type)
        if request.service_type:
            query = query.filter(ServiceRateSheet.service_type == request.service_type)
        if request.units:
            query = query.filter(ServiceRateSheet.units == request.units)

        # Get total count before pagination
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()

        # Calculate pagination using request directly
        adjusted_skip = (request.skip - 1) * request.limit if request.skip > 0 else 0
        total_pages = (total_count + request.limit - 1) // request.limit
        current_page = (request.skip // request.limit) + 1

        # Apply pagination and ordering
        paginated_query = (
            query.order_by(ServiceRateSheet.created_at.desc())
            .offset(adjusted_skip)
            .limit(request.limit)
        )

        # Execute final query
        result = await db.execute(paginated_query)
        rate_sheets = result.scalars().all()

        return StandardResponse(
            status_code=200,
            message="Service rate sheets retrieved successfully",
            data={
                "items": rate_sheets,
                "total": total_count,
                "page": current_page,
                "total_pages": total_pages,
                "limit": request.limit
            }
        )

    except SQLAlchemyError as e:
        return ErrorResponse(
            status_code=500,
            message="Service rate sheets retrieval failed",
            error=f"Database error: {str(e)}"
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Service rate sheets retrieval failed",
            error=f"Error: {e}"
        )

@router.put("/service-rate-sheet-update/{id}")
async def update_service_rate_sheet(
    id: str,
    data: schemas.UpdateServiceRateSheet,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        query = select(ServiceRateSheet).filter(ServiceRateSheet.id == uuid.UUID(id))
        result = await db.execute(query)
        rate_sheet = result.scalars().first()

        if not rate_sheet:
            return ErrorResponse(
                status_code=404,
                message="Service rate sheet not found"
            )

        # Update only the fields that are provided
        update_data = data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(rate_sheet, key, value)

        await db.commit()
        await db.refresh(rate_sheet)

        return StandardResponse(
            status_code=200,
            message="Service rate sheet updated successfully",
            data=rate_sheet
        )
    except SQLAlchemyError as e:
        return ErrorResponse(
            status_code=500,
            message="Service rate sheet update failed",
            error=f"Database error: {str(e)}"
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Service rate sheet update failed",
            error=f"Error: {e}"
        )

@router.delete("/service-rate-sheet-delete/{id}")
async def delete_service_rate_sheet(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        query = select(ServiceRateSheet).filter(ServiceRateSheet.id == uuid.UUID(id))
        result = await db.execute(query)
        rate_sheet = result.scalars().first()

        if not rate_sheet:
            return ErrorResponse(
                status_code=404,
                message="Service rate sheet not found"
            )

        await db.delete(rate_sheet)
        await db.commit()

        return StandardResponse(
            status_code=200,
            message="Service rate sheet deleted successfully"
        )
    
    except SQLAlchemyError as e:
        return ErrorResponse(
            status_code=500,
            message="Service rate sheet deletion failed",
            error=f"Database error: {str(e)}"
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Service rate sheet deletion failed",
            error=f"Error: {e}"
        ) 