import json
import logging
from app.kafka_consumer.config import create_cognito_consumer, create_db_consumer
from app.bulk_upload import process_cognito_creation, process_sp_creation

logger = logging.getLogger(__name__)

cognito_consumer = create_cognito_consumer()
db_consumer = create_db_consumer()
# delete_consumer = create_delete_consumer()


async def consume_cognito_consumer():
    logger.info("Starting cognito consumer...")
    try:
        async for msg in cognito_consumer:
            logger.info(f"Received message on cognito topic at offset {msg.offset}")
            try:
                data = msg.value.decode()
                data_dict = json.loads(data)
                result = await process_cognito_creation(data_dict)
                
                # Handle the result for status tracking
                if result and isinstance(result, dict):
                    if result.get("skipped"):
                        logger.info(f"Skipped: {result['message']}")
                    elif result.get("success"):
                        logger.info(f"Success: {result['message']}")
                    else:
                        logger.error(f"Failed: {result['message']}")
                elif result is None:
                    logger.error("process_cognito_creation returned None")
                    
            except json.JSONDecodeError as e:
                logger.error(f"Failed to decode JSON from Kafka message: {e}")
            except Exception as e:
                logger.error(f"Exception in cognito consumer: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
    except Exception as e:
        logger.error(f"Fatal error in cognito consumer: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")


async def consume_db_consumer():
    logger.info("Starting db consumer...")
    try:
        async for msg in db_consumer:
            logger.info(f"Received message on db topic at offset {msg.offset}")
            try:
                data = msg.value.decode()
                data_dict = json.loads(data)
                result = await process_sp_creation(data_dict)
                
                # Handle the result for status tracking
                if result and isinstance(result, dict):
                    if result.get("skipped"):
                        logger.info(f"Skipped: {result['message']}")
                    elif result.get("success"):
                        logger.info(f"Success: {result['message']}")
                    else:
                        logger.error(f"Failed: {result['message']}")
                elif result is None:
                    logger.error("process_sp_creation returned None")
                    
            except json.JSONDecodeError as e:
                logger.error(f"Failed to decode JSON from Kafka message: {e}")
            except Exception as e:
                logger.error(f"Exception in db consumer: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
    except Exception as e:
        logger.error(f"Fatal error in db consumer: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
