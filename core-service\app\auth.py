import os
import requests
from fastapi import Request
from app.helper import ErrorResponse
from fastapi.responses import JSONResponse
import jwt
from app.cognito_utils import get_user_attributes
import jwt

async def permission_checker(request: Request):
    token = request.headers.get("Authorization")
    if not token:
        return ErrorResponse(401, "Missing Token", "Authorization token required")

    try:
        auth_url = os.getenv("BE_AUTH_API_URL")  # e.g. http://auth-service:8000
        headers = {
            "Authorization": token,
            "X-Original-Path": request.url.path,
            "X-Original-Method": request.method
        }

        response = requests.post(f"{auth_url}/validate-token", headers=headers)
        resp_data = response.json()

        if response.status_code != 200:
            return JSONResponse(status_code=resp_data.get("status_code", 403), content=resp_data)

        return resp_data["data"]  # contains user_id, auth_id, role_id, role_name

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(500, "Permission service failed", str(e)).dict()
        )

async def validate_token(token: str) -> dict:
    """
    Simple JWT validator: decodes token and returns payload info without verifying signature.
    """
    if not token:
        return {"error": "Token is required"}

    # Check proper JWT format (3 parts separated by dots)
    if token.count('.') != 2:
        return {"error": "Invalid token format (must have 3 parts)"}

    try:
        # Decode token without signature verification
        payload = jwt.decode(token, options={"verify_signature": False}, algorithms=["HS256", "RS256"])

        return {
            "id": payload.get("sub"),
            "email": payload.get("email"),
            "role": payload.get("role"),
            "username": payload.get("username"),
            "claims": payload,
        }
    except jwt.DecodeError:
        return {"error": "Invalid or expired token"}
    except Exception as e:
        return {"error": f"Unexpected error: {str(e)}"}