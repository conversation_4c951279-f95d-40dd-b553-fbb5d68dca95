import uuid
from typing import Dict, <PERSON>, Any
from sqlalchemy.orm import Session
from app.models import SPRatings, Ratings
from sqlalchemy import func
import logging

def get_avg_rating_by_user(session: Session, user_id: str) -> float:
    """Get average rating for a user from ratings and SP ratings tables."""
    try:
        avg_rating = (
            session.query(func.avg(Ratings.rating))
            .join(SPRatings, SPRatings.ref_id == Ratings.id)
            .filter(SPRatings.user_id == user_id)
            .scalar()
        )
        return float(avg_rating) if avg_rating is not None else 0.0
    except Exception as e:
        # Log the exception in production
        logging.error(f"Error getting average rating for user {user_id}: {e}")
        return 0.0


def is_valid_uuid(value: Any) -> bool:
    """Check if a string is a valid UUID"""
    try:
        uuid.UUID(str(value))
        return True
    except (ValueError, TypeError):
        return False


def get_empty_user_details() -> Dict[str, Union[str, float]]:
    """Return empty user details object"""
    return {
        "id": "",
        "first_name": "",
        "last_name": "",
        "email": "",
        "phone_number": "",
        "profile_image_url": "",
        "country_code": "",
        "ratings": 0.0
    }


def get_empty_artisan_details() -> Dict[str, Union[str, float, int]]:
    """Return empty artisan details object"""
    return {
        "id": "",
        "first_name": "",
        "last_name": "",
        "email": "",
        "phone_number": "",
        "profile_image_url": "",
        "country_code": "",
        "ratings": 0.0,
        "job_count": 0
    }


def get_empty_invoice_details() -> Dict[str, Union[str, float]]:
    """Return empty invoice details object"""
    return {
        "id": "",
        "sp_id": "",
        "payment_method": "",
        "biller_id": "",
        "customer_id": "",
        "tax_percentage": 0.0,
        "tax_amount": 0.0,
        "platform_fee_percentage": 0.0,
        "platform_fee_amount": 0.0,
        "base_fee": 0.0,
        "discount_amount": 0.0,
        "booking_fee": 0.0,
        "total_amount": 0.0,
        "payment_status": "",
        "booking_id": "",
        "created_at": "",
        "updated_at": ""
    }


def get_empty_assigned_artisan_details() -> Dict[str, Union[str, float]]:
    """Return empty assigned artisan details object"""
    return {
        "id": "",
        "invoice_item_id": "",
        "invoice_id": "",
        "service_id": "",
        "artisan_id": "",
        "start_time": "",
        "end_time": "",
        # "start_otp": "",
        # "end_otp": "",
        "service_start": "",
        "service_end": "",
        "service_start_artisan_latitude": 0.0,
        "service_start_artisan_longitude": 0.0,
        "service_end_artisan_latitude": 0.0,
        "service_end_artisan_longitude": 0.0,
        "status": "",
        "created_at": "",
        "updated_at": ""
    }
    