"""mod device table

Revision ID: a4d77ecffc0c
Revises: edf2e98dec28
Create Date: 2025-06-06 10:20:23.580625

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a4d77ecffc0c'
down_revision: Union[str, None] = 'edf2e98dec28'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_notification_id', table_name='notification')
    op.drop_table('notification')
    op.drop_table('audit_logs')
    op.drop_index('ix_api_audit_logs_method', table_name='api_audit_logs')
    op.drop_index('ix_api_audit_logs_url', table_name='api_audit_logs')
    op.drop_table('api_audit_logs')
    op.create_unique_constraint(None, 'accounts', ['id'])
    op.alter_column('admin', 'reason',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.create_unique_constraint(None, 'admin', ['id'])
    op.add_column('booking_cancellation', sa.Column('refund_amount', sa.Float(), nullable=True))
    op.add_column('booking_cancellation', sa.Column('refund_needed', sa.Boolean(), nullable=True))
    op.add_column('booking_cancellation', sa.Column('reason_for_refund', sa.String(), nullable=True))
    op.add_column('booking_cancellation', sa.Column('is_auto_refund', sa.Boolean(), nullable=True))
    op.create_unique_constraint(None, 'booking_cancellation', ['id'])
    op.drop_column('booking_cancellation', 'penalty_user_type')
    op.drop_column('booking_cancellation', 'is_auto_cancel')
    op.create_unique_constraint(None, 'booking_cancellation_history', ['id'])
    op.add_column('booking_cancellation_reasons', sa.Column('refund_needed', sa.Boolean(), nullable=True))
    op.create_unique_constraint(None, 'booking_cancellation_reasons', ['id'])
    op.drop_column('booking_cancellation_reasons', 'is_notify_cancel')
    op.drop_column('booking_cancellation_reasons', 'is_negotiate_cancel')
    op.drop_column('booking_cancellation_reasons', 'apply_penalty_on')
    op.create_unique_constraint(None, 'booking_history', ['id'])
    op.alter_column('bookings', 'booking_order_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('bookings', 'tax',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False,
               existing_server_default=sa.text('17.5'))
    op.create_unique_constraint(None, 'bookings', ['id'])
    op.drop_column('bookings', 'booking_cancellation_reason_id')
    op.drop_column('bookings', 'service_fee')
    op.drop_column('bookings', 'name')
    op.drop_column('bookings', 'is_notify')
    op.drop_column('bookings', 'phone_number')
    op.drop_column('bookings', 'description_for_negotiation')
    op.create_unique_constraint(None, 'category', ['id'])
    op.create_unique_constraint(None, 'category_issues', ['id'])
    op.create_unique_constraint(None, 'device', ['id'])
    op.create_foreign_key(None, 'device', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.drop_column('device', 'info')
    op.create_unique_constraint(None, 'dispute_comments', ['id'])
    op.create_foreign_key(None, 'dispute_comments', 'disputes', ['dispute_id'], ['id'])
    op.create_unique_constraint(None, 'dispute_event_types', ['id'])
    op.create_unique_constraint(None, 'dispute_history', ['id'])
    op.create_foreign_key(None, 'dispute_history', 'disputes', ['dispute_id'], ['id'])
    op.alter_column('disputes', 'issue_category_id',
               existing_type=sa.UUID(),
               nullable=False)
    op.create_unique_constraint(None, 'disputes', ['id'])
    op.create_unique_constraint(None, 'issue_categories', ['id'])
    op.create_unique_constraint(None, 'issue_history', ['id'])
    op.create_unique_constraint(None, 'issue_types', ['id'])
    op.alter_column('issues', 'supporting_documents',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.create_unique_constraint(None, 'issues', ['id'])
    op.drop_column('issues', 'customer_name')
    op.create_unique_constraint(None, 'payments', ['id'])
    op.alter_column('service_provider', 'phone_number',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('service_provider', 'latitude',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
    op.alter_column('service_provider', 'longitude',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
    op.alter_column('service_provider', 'is_confirmed',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.create_unique_constraint(None, 'service_provider', ['id'])
    op.create_unique_constraint(None, 'service_provider_leave', ['id'])
    op.alter_column('service_provider_service_mapping', 'service_provider_id',
               existing_type=sa.UUID(),
               nullable=False)
    op.alter_column('service_provider_service_mapping', 'services_id',
               existing_type=sa.UUID(),
               nullable=False)
    op.create_unique_constraint(None, 'service_provider_service_mapping', ['id'])
    op.create_foreign_key(None, 'service_provider_service_mapping', 'service_provider', ['service_provider_id'], ['id'])
    op.drop_column('service_provider_service_mapping', 'sub_category_id')
    op.drop_column('service_provider_service_mapping', 'category_id')
    op.create_unique_constraint(None, 'services', ['id'])
    op.create_unique_constraint(None, 'subcategories_issues', ['id'])
    op.create_unique_constraint(None, 'transactions', ['id'])
    op.alter_column('users', 'phone_number',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('users', 'status',
               existing_type=postgresql.ENUM('APPROVED', 'SUSPENDED', 'BLACKLIST', name='userstatustype'),
               nullable=True,
               existing_server_default=sa.text("'APPROVED'::userstatustype"))
    op.alter_column('users', 'is_confirmed',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.create_unique_constraint(None, 'users', ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='unique')
    op.alter_column('users', 'is_confirmed',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('users', 'status',
               existing_type=postgresql.ENUM('APPROVED', 'SUSPENDED', 'BLACKLIST', name='userstatustype'),
               nullable=False,
               existing_server_default=sa.text("'APPROVED'::userstatustype"))
    op.alter_column('users', 'phone_number',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_constraint(None, 'transactions', type_='unique')
    op.drop_constraint(None, 'subcategories_issues', type_='unique')
    op.drop_constraint(None, 'services', type_='unique')
    op.add_column('service_provider_service_mapping', sa.Column('category_id', sa.UUID(), autoincrement=False, nullable=True))
    op.add_column('service_provider_service_mapping', sa.Column('sub_category_id', sa.UUID(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'service_provider_service_mapping', type_='foreignkey')
    op.drop_constraint(None, 'service_provider_service_mapping', type_='unique')
    op.alter_column('service_provider_service_mapping', 'services_id',
               existing_type=sa.UUID(),
               nullable=True)
    op.alter_column('service_provider_service_mapping', 'service_provider_id',
               existing_type=sa.UUID(),
               nullable=True)
    op.drop_constraint(None, 'service_provider_leave', type_='unique')
    op.drop_constraint(None, 'service_provider', type_='unique')
    op.alter_column('service_provider', 'is_confirmed',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('service_provider', 'longitude',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True)
    op.alter_column('service_provider', 'latitude',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True)
    op.alter_column('service_provider', 'phone_number',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_constraint(None, 'payments', type_='unique')
    op.add_column('issues', sa.Column('customer_name', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'issues', type_='unique')
    op.alter_column('issues', 'supporting_documents',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.drop_constraint(None, 'issue_types', type_='unique')
    op.drop_constraint(None, 'issue_history', type_='unique')
    op.drop_constraint(None, 'issue_categories', type_='unique')
    op.drop_constraint(None, 'disputes', type_='unique')
    op.alter_column('disputes', 'issue_category_id',
               existing_type=sa.UUID(),
               nullable=True)
    op.drop_constraint(None, 'dispute_history', type_='foreignkey')
    op.drop_constraint(None, 'dispute_history', type_='unique')
    op.drop_constraint(None, 'dispute_event_types', type_='unique')
    op.drop_constraint(None, 'dispute_comments', type_='foreignkey')
    op.drop_constraint(None, 'dispute_comments', type_='unique')
    op.add_column('device', sa.Column('info', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'device', type_='foreignkey')
    op.drop_constraint(None, 'device', type_='unique')
    op.drop_constraint(None, 'category_issues', type_='unique')
    op.drop_constraint(None, 'category', type_='unique')
    op.add_column('bookings', sa.Column('description_for_negotiation', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('bookings', sa.Column('phone_number', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('bookings', sa.Column('is_notify', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('bookings', sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('bookings', sa.Column('service_fee', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True))
    op.add_column('bookings', sa.Column('booking_cancellation_reason_id', sa.UUID(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'bookings', type_='unique')
    op.alter_column('bookings', 'tax',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True,
               existing_server_default=sa.text('17.5'))
    op.alter_column('bookings', 'booking_order_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.drop_constraint(None, 'booking_history', type_='unique')
    op.add_column('booking_cancellation_reasons', sa.Column('apply_penalty_on', postgresql.ENUM('ARTISAN', 'USER', 'AGENT', name='usertype'), autoincrement=False, nullable=True))
    op.add_column('booking_cancellation_reasons', sa.Column('is_negotiate_cancel', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('booking_cancellation_reasons', sa.Column('is_notify_cancel', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'booking_cancellation_reasons', type_='unique')
    op.drop_column('booking_cancellation_reasons', 'refund_needed')
    op.drop_constraint(None, 'booking_cancellation_history', type_='unique')
    op.add_column('booking_cancellation', sa.Column('is_auto_cancel', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('booking_cancellation', sa.Column('penalty_user_type', postgresql.ENUM('ARTISAN', 'USER', 'AGENT', name='usertype'), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'booking_cancellation', type_='unique')
    op.drop_column('booking_cancellation', 'is_auto_refund')
    op.drop_column('booking_cancellation', 'reason_for_refund')
    op.drop_column('booking_cancellation', 'refund_needed')
    op.drop_column('booking_cancellation', 'refund_amount')
    op.drop_constraint(None, 'admin', type_='unique')
    op.alter_column('admin', 'reason',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.drop_constraint(None, 'accounts', type_='unique')
    op.create_table('api_audit_logs',
    sa.Column('method', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('headers', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('query_params', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('body', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('response', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), server_default=sa.text('true'), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='api_audit_logs_pkey')
    )
    op.create_index('ix_api_audit_logs_url', 'api_audit_logs', ['url'], unique=False)
    op.create_index('ix_api_audit_logs_method', 'api_audit_logs', ['method'], unique=False)
    op.create_table('audit_logs',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('table_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('operation', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('record_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('changes', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('user_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='audit_logs_pkey')
    )
    op.create_table('notification',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('title', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('message', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('notification_type', postgresql.ENUM('SMS', 'OTP', 'EMAIL', 'PUSH', name='notificationtype'), autoincrement=False, nullable=False),
    sa.Column('phone_number', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('email', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('sender_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('send_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('data', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), server_default=sa.text('true'), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='notification_pkey')
    )
    op.create_index('ix_notification_id', 'notification', ['id'], unique=False)
    # ### end Alembic commands ###
