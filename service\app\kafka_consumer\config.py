from aiokafka import AIOKafkaConsumer
from app.config import get_settings


def create_service_request_consumer() -> AIOKafkaConsumer:
    return AIOKafkaConsumer(
        "service_request",
        bootstrap_servers=f"{get_settings().KAFKA_HOST}:{get_settings().KAFKA_PORT}",
        enable_auto_commit=False,
        group_id="service_request_group",
        auto_offset_reset="earliest",
    )


def create_invoice_consumer() -> AIOKafkaConsumer:
    return AIOKafkaConsumer(
        "invoice",
        bootstrap_servers=f"{get_settings().KAFKA_HOST}:{get_settings().KAFKA_PORT}",
        enable_auto_commit=False,
        group_id="invoice_group",
        auto_offset_reset="earliest",
    )
