import time
from celery_config import celery_app
from sqlalchemy.orm import Session
from app.database import SessionLocal
from app.models import Booking, BookingStatus
from app.repositories.booking import create_booking_history
from app.utils.notification import send_push_notification


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# @celery_app.task(name="app.tasks.send_artisan_reminder")
# def send_artisan_reminder(booking_id: str, artisan_id: str, token: str, data: dict):
#     """
#     Sends a reminder notification to the artisan after 3 minutes.
#     """
#     db: Session = next(get_db())
#     booking = db.query(Booking).filter(Booking.id == booking_id).first()
#     if not booking or booking.status != BookingStatus.PENDING:
#         db.close()
#         return
#     send_push_notification(
#         auth_token=token,
#         title="Reminder",
#         message=f"You have a pending booking request - {booking_id}",
#         sender_id=artisan_id,
#         type="service_provider",
#         data=data,
#     )


@celery_app.task
def cancel_booking_if_no_response(
    booking_id: str, artisan_id: str, user_id: str, token: str, data: dict
):
    """
    Cancels the booking if the artisan hasn't responded within 5 minutes.
    """
    try:
        db: Session = next(get_db())
        booking = db.query(Booking).filter(Booking.id == booking_id).first()
        if not booking or booking.status != BookingStatus.PENDING:
            db.close()

            return

        booking.status = BookingStatus.CANCELLED
        db.commit()
        create_booking_history(db, booking_obj=booking)

        # Notify both user and artisan
        send_push_notification(
            auth_token=token,
            title="Booking Cancelled",
            message=f"The artisan did not respond in time - {booking_id}",
            sender_id=user_id,
            type="user",
            data=data,
        )
        send_push_notification(
            auth_token=token,
            title="Booking Cancelled",
            message=f"Your booking request has been auto-cancelled - {booking_id}",
            sender_id=artisan_id,
            type="service_provider",
            data=data,
        )
    finally:
        db.close()


# @celery_app.task(name="app.tasks.wait_for_artisan_response")
# def wait_for_artisan_response(
#     booking_id: str, artisan_id: str, user_id: str, token: str, data: dict, db: Session
# ):
#     """
#     Waits for the artisan's response with two notification attempts.
#     If no response is given within 5 minutes, the booking is auto-cancelled.
#     """
#     print("Sending first notification")
#     time.sleep(180)  # Wait 3 minutes

#     booking = db.query(Booking).filter(Booking.id == booking_id).first()
#     if not booking or booking.status != BookingStatus.PENDING:
#         db.close()
#         return  # If already handled, exit

#     # print("Sending second notification")
#     # send_push_notification(auth_token=token, message="Reminder: Booking Request, Please respond to the booking request.", sender_id=artisan_id)
#     # send_push_notification(auth_token=token, title="Remainder", message=f"You have a new booking request - {booking_id} ", sender_id=artisan_id, type="service_provider",data=data)
#     # time.sleep(120)  # Wait 2 more minutes

#     booking = db.query(Booking).filter(Booking.id == booking_id).first()
#     if booking and booking.status == BookingStatus.PENDING:
#         booking.status = BookingStatus.CANCELLED
#         db.commit()
#         create_booking_history(db, booking_obj=booking)

#         print("Sending auto-cancelled notification")
#         # send_push_notification(auth_token=token, message="The artisan did not respond in time.", sender_id=user_id)
#         # send_push_notification(auth_token=token, message="Your booking request has been auto-cancelled.", sender_id=artisan_id)
#         # Notify both user and artisan
#         send_push_notification(
#             auth_token=token,
#             title="Booking Cancelled",
#             message=f"The artisan did not respond in time - {booking_id}",
#             sender_id=user_id,
#             type="user",
#             data=data,
#         )
#         send_push_notification(
#             auth_token=token,
#             title="Booking Cancelled",
#             message=f"Your booking request has been auto-cancelled - {booking_id}",
#             sender_id=artisan_id,
#             type="service_provider",
#             data=data,
#         )

#     db.close()
