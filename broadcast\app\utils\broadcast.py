from app.utils.notification import send_push_notification

async def process_broadcast(datas):
    try:
        print("Processing broadcast", datas)
        Authorization = datas['Authorization']
        booking_id = datas['booking_id']
        for data in datas['datas']:
            status = send_push_notification(
                auth_token=Authorization,
                title="New Booking Request",
                message=f"You have a new Booking Request - {booking_id}",
                sender_id=data['artisan_id'],
                type="service_provider",
                data={},
            )
            print(status, "notification response")
            return status
    except Exception as e:
        print(f"Error processing broadcast: {e}")