from aiokafka import AI<PERSON>afkaConsumer
from app.config import get_settings


def create_cognito_consumer() -> AIOKafkaConsumer:
    return AIOKafkaConsumer(
        "cognito",
        bootstrap_servers=f"{get_settings().KAFKA_HOST}:{get_settings().KAFKA_PORT}",
    )

def create_db_consumer() -> AIOKafkaConsumer:
    return AIOKafkaConsumer(
        "db",
        bootstrap_servers=f"{get_settings().KAFKA_HOST}:{get_settings().KAFKA_PORT}",
    )

def create_delete_consumer():
    return AIOKafkaConsumer(
        "delete",
        bootstrap_servers=f"{get_settings().KAFKA_HOST}:{get_settings().KAFKA_PORT}",
        # group_id="delete-group",
        # auto_offset_reset="earliest",
        # enable_auto_commit=True,
        # auto_commit_interval_ms=1000,
    )
