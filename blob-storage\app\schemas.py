from pydantic import BaseModel


class CustomBaseModel(BaseModel):
    class Config:
        from_attributes = True
        use_enum_values = True


class InitiateBlobUploadModel(CustomBaseModel):
    file_name: str
    bucket_name: str


class CheckFileStatusModel(CustomBaseModel):
    file_name: str
    bucket_name: str


class GeneratePresignedURLModel(CustomBaseModel):
    file_name: str
    content_type: str
    bucket_name: str


class CompleteBlobUploadModel(CustomBaseModel):
    file_name: str
    upload_id: str
    parts: list
    bucket_name: str


class DeleteFileStatusModel(CustomBaseModel):
    file_path: str
    # bucket_name: str
