from datetime import datetime
import os
import requests
from typing import Optional
from fastapi import H<PERSON><PERSON>Exception
from app.config import settings
import logging

from app.database import get_db_session
logging.basicConfig(level=logging.INFO)

async def get_user_details(user_id: str, auth_token: str) -> Optional[dict]:
    """Get user details from profile service"""
    try:
        response = requests.get(
            f"{settings.BE_PROFILE_API_URL}/user-read/{user_id}",
            headers={"Authorization": f'Bearer {auth_token}'}
        )
        if response.status_code == 200:
            return response.json().get("data")
        return None
    except Exception as e:
        print(f"Error fetching user details: {e}")
        return None

async def get_artisan_details(artisan_id: str, auth_token: str) -> Optional[dict]:
    """Get service provider details from profile service"""
    try:
        response = requests.get(
            f"{settings.BE_PROFILE_API_URL}/sp-read/{artisan_id}",
            headers={"Authorization": f'Bearer {auth_token}'}
        )
        if response.status_code == 200:
            return response.json().get("data")
        return None
    except Exception as e:
        print(f"Error fetching artisan details: {e}")
        return None

async def get_service_details(service_id: str, auth_token: str) -> Optional[dict]:
    """Get service details from service management"""
    try:
        response = requests.get(
            f"{settings.BE_SERVICE_API_URL}/services-read/{service_id}",
            headers={"Authorization": f'Bearer {auth_token}'}
        )
        if response.status_code == 200:
            return response.json().get("data")
        return None
    except Exception as e:
        print(f"Error fetching service details: {e}")
        return None 
    

