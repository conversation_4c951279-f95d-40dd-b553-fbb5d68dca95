from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from uuid import UUID

from app.database import get_db
from app.models import UserPreferences
from app.schemas import UserPreferencesCreate, UserPreferencesOut, UserPreferencesUpdate
from app.helper import StandardResponse, ErrorResponse
from app.utils import create_record, update_record, delete_record

router = APIRouter(prefix="/user-preferences", tags=["User Preferences"])


@router.post("/", response_model=StandardResponse)
async def create_user_preferences(
    payload: UserPreferencesCreate, 
    db: AsyncSession = Depends(get_db)
):
    try:
        # Check if user preferences already exist for this user
        existing = await db.execute(
            select(UserPreferences).where(UserPreferences.user_id == payload.user_id)
        )
        if existing.scalars().first():
            return ErrorResponse(
                status_code=400, 
                message="User preferences already exist for this user"
            )

        # Create new user preferences
        new_preferences = await create_record(db, UserPreferences, payload.dict())
        
        if isinstance(new_preferences, str):  # Error occurred
            return ErrorResponse(status_code=500, message=new_preferences)
        
        data = UserPreferencesOut.model_validate(new_preferences, from_attributes=True)
        return StandardResponse(
            status_code=201,
            data=data,
            message="User preferences created successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))


@router.get("/{user_id}", response_model=StandardResponse)
async def get_user_preferences(
    user_id: UUID, 
    db: AsyncSession = Depends(get_db)
):
    try:
        result = await db.execute(
            select(UserPreferences).where(UserPreferences.user_id == user_id)
        )
        preferences = result.scalars().first()

        if not preferences:
            return ErrorResponse(
                status_code=404, 
                message="User preferences not found"
            )

        data = UserPreferencesOut.model_validate(preferences, from_attributes=True)
        return StandardResponse(
            status_code=200,
            data=data,
            message="User preferences retrieved successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))


@router.get("/", response_model=StandardResponse)
async def get_all_user_preferences(
    db: AsyncSession = Depends(get_db)
):
    try:
        result = await db.execute(select(UserPreferences))
        preferences_list = result.scalars().all()

        data = [
            UserPreferencesOut.model_validate(prefs, from_attributes=True)
            for prefs in preferences_list
        ]
        
        return StandardResponse(
            status_code=200,
            data=data,
            message="All user preferences retrieved successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))


@router.put("/{preferences_id}", response_model=StandardResponse)
async def update_user_preferences(
    preferences_id: UUID,
    payload: UserPreferencesUpdate,
    db: AsyncSession = Depends(get_db)
):
    try:
        result = await db.execute(
            select(UserPreferences).where(UserPreferences.id == preferences_id)
        )
        preferences = result.scalars().first()

        if not preferences:
            return ErrorResponse(
                status_code=404, 
                message="User preferences not found"
            )

        # Update only provided fields
        update_data = payload.dict(exclude_unset=True)
        
        for key, value in update_data.items():
            setattr(preferences, key, value)

        await db.commit()
        await db.refresh(preferences)

        data = UserPreferencesOut.model_validate(preferences, from_attributes=True)
        return StandardResponse(
            status_code=200,
            data=data,
            message="User preferences updated successfully"
        )
    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message=str(e))


@router.delete("/{preferences_id}", response_model=StandardResponse)
async def delete_user_preferences(
    preferences_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    try:
        result = await db.execute(
            select(UserPreferences).where(UserPreferences.id == preferences_id)
        )
        preferences = result.scalars().first()

        if not preferences:
            return ErrorResponse(
                status_code=404, 
                message="User preferences not found"
            )

        await delete_record(db, UserPreferences, preferences_id)
        return StandardResponse(
            status_code=200,
            message="User preferences deleted successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))

