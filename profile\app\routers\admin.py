from datetime import datetime
import io
import re
import uuid
import json
from fastapi import (
    APIRouter,
    Depends,
    File,
    HTTPException,
    Header,
    UploadFile,
    Body,
    BackgroundTasks,
)
from fastapi.responses import JSONResponse
from app.helper import StandardResponse, ErrorResponse
from app.s3_upload import (
    upload_file_direct,
    s3_delete_file,
    S3_IMAGES_FOLDER,
    S3_DOCS_FOLDER,
)
from app.kafka_producer.producer import cognito_producer
from sqlalchemy import Time, cast, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

# from app.models import ServiceProvider, Admin, ServiceProviderServiceMapping, Users
from app.models import *
from app.database import get_db
from app.schemas import (
    AdminCreateServiceProvider,
    CreateAdmin,
    AdminSignupRequest,
    AdminLoginRequest, AgentApproval
)
from app.utils import (
    create_record,
    generate_cognito_password,
    get_id_header,
    ServiceProviderStatusType, 
    AgentStatusType,
    send_push_notification
)
from app.cognito_utils import (
    create_cognito_user,
    update_cognito_attributes,
    cognito_admin_login,
    delete_cognito_user,
    add_user_to_group,
    enable_cognito_user,
    disable_cognito_user,
)
from typing import Annotated, Optional, Dict, Any
import boto3
from app.config import get_settings
from app.hash import cognito_secret_hash
from app.file_uploader import upload_file
from app.bulk_upload import process_excel_file , process_delete_df
import os
import tempfile
from app.background_tasks import run_bulk_upload_task, get_task_status

from app.config import get_settings
from app import models
from geoalchemy2.functions import ST_Point
from sqlalchemy import or_, func
from sqlalchemy.types import String
from io import StringIO
import pandas as pd

router = APIRouter()


@router.post("/admin/create_serviceprovider")
async def create_service_provider(
    request: AdminCreateServiceProvider = Depends(),
    profile_pic: Optional[UploadFile] = File(None),
    license: Optional[UploadFile] = File(None),
    certificate: Optional[UploadFile] = File(None),
    govt_id: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        # ✅ Verify Admin's Identity
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized access")
        admin_resp_json = resp.json()
        admin_id = admin_resp_json.get("id")
        request_dict = request.__dict__
        services_list = request.__dict__.pop("services_list", None)

        # ✅ Validate required fields first
        required_fields = {
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'email': 'Email',
            'phone_number': 'Phone Number',
            'dob': 'Date of Birth'
        }

        missing_fields = []
        for field, label in required_fields.items():
            if not request_dict.get(field):
                missing_fields.append(label)

        if missing_fields:
            return ErrorResponse(
                status_code=400,
                message = f"The following fields are required:{','.join(missing_fields)}",
                error="Missing required fields"
            )

        # ✅ Validate phone number format
        phone_number = request_dict.get('phone_number', '')
        if not phone_number.startswith('+'):
            return ErrorResponse(
                status_code=400,
                message="Invalid phone number format",
                error="Phone number must start with '+' followed by country code"
            )

        # ✅ Validate email format
        email = request_dict.get('email', '')
        if not re.match(r"[^@]+@[^@]+\.[^@]+", email):
            return ErrorResponse(
                status_code=400,
                message="Invalid email format",
                error="Please provide a valid email address"
            )

        # ✅ Check if Service Provider Already Exists in DB
        query = select(ServiceProvider).filter(
            or_(
                ServiceProvider.email == request.email,
                ServiceProvider.phone_number == request.phone_number
            )
        )
        result = await db.execute(query)
        existing_sp = result.scalars().first()
        if existing_sp:
            if existing_sp.email == request.email:
                return ErrorResponse(
                    status_code=400,
                    message="Service Provider already exists",
                    error="A service provider with this email already exists"
                )
            else:
                return ErrorResponse(
                    status_code=400,
                    message="Service Provider already exists",
                    error="A service provider with this phone number already exists"
                )

        # ✅ Create Service Provider in Cognito
        try:
            auth_id = create_cognito_user(
                request.phone_number, generate_cognito_password(), request.email
            )
            add_user_to_group(auth_id.get("User", {}).get("Username", ""), "artisan")
        except Exception as e:
            error_message = str(e)
            if "UsernameExistsException" in error_message:
                return ErrorResponse(
                    status_code=400, 
                    message="Service Provider already exists",
                    error="A user with this phone number or email already exists"
                )
            return ErrorResponse(
                status_code=500, 
                message="Error creating Service Provider account", 
                error=str(e)
            )

        # ✅ Prepare Data for Database Insertion
        request_dict = request.__dict__
        auth_id = auth_id.get("User", {}).get("Username", "")
        request_dict["auth_id"] = auth_id
        request_dict["status"] = ServiceProviderStatusType.APPROVED

        # ✅ Upload Files if Provided
        file_fields = ["profile_pic", "license", "certificate", "govt_id"]
        for field in file_fields:
            file_obj = locals()[field]
            if file_obj:
                try:
                    if field == "profile_pic":
                        file_url = upload_file_direct(file_obj, path=S3_IMAGES_FOLDER)
                    else:
                        file_url = upload_file_direct(file_obj, path=S3_DOCS_FOLDER)
                    request_dict[field] = file_url["filename"]
                except Exception as e:
                    return ErrorResponse(
                        status_code=500,
                        message=f"File upload failed for {field}",
                        error=str(e),
                    )

        # ✅ Process Location and Working Hours
        try:
            if request.latitude and request.longitude:
                request_dict["location"] = ST_Point(request.longitude, request.latitude)

            time_fields = [
                "work_from_hrs",
                "work_to_hrs",
                "break_from_hrs",
                "break_to_hrs",
            ]
            for field in time_fields:
                value = getattr(request, field, None)
                if value and value.strip():  # Check if value exists and is not empty
                    try:
                        request_dict[field] = datetime.strptime(value, "%H:%M").time()
                    except ValueError:
                        return ErrorResponse(
                            status_code=400, 
                            message=f"Invalid time format for {field}. Expected format: HH:MM",
                            error=f"Could not parse time value: {value}"
                        )
                else:
                    request_dict[field] = None  # Set to None instead of empty string

            if request.dob:
                request_dict["dob"] = datetime.strptime(request.dob, "%Y-%m-%d").date()

            if request.weekdays:
                request.weekdays = list(
                    map(int, request.weekdays.split(","))
                )
                request_dict["weekdays"] = request.weekdays
        except Exception as e:
            return ErrorResponse(
                status_code=400, message="Invalid date/time format", error=str(e)
            )

        # ✅ Insert New Service Provider Record in Database
        try:
            new_sp = await create_record(db, ServiceProvider, request_dict)
            if isinstance(new_sp, str):
                return ErrorResponse(status_code=400, message=new_sp)
        except Exception as e:
            return ErrorResponse(
                status_code=500, message="Database insertion failed", error=str(e)
            )

        print(request_dict, ">> request_dict")
        print(new_sp)
        sp_id = new_sp.id

        if services_list:
            print(services_list, type(services_list), "service listtttt")
            # Removing existing services before adding new ones
            stmt = delete(ServiceProviderServiceMapping).where(
                ServiceProviderServiceMapping.service_provider_id == sp_id
            )
            await db.execute(stmt)
            await db.commit()
            # Adding new services
            # services_list_json = ast.literal_eval(services_list)
            # services_list_json = json.loads(services_list)
            uuid_pattern = (
                r"[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"
            )
            uuid_strings = re.findall(uuid_pattern, services_list)

            # Step 2: Convert to UUID objects for validation
            uuid_objects = [uuid.UUID(u) for u in uuid_strings]

            # Step 3: Convert UUID objects back to strings (if needed)
            services_list_json = [str(u) for u in uuid_objects]
            print(services_list_json, "services_list_json")
            for service_id in services_list_json:
                print(service_id, "service_id")
                val = {"service_provider_id": sp_id, "services_id": service_id}
                await create_record(db, ServiceProviderServiceMapping, val)

        return StandardResponse(
            status_code=200,
            message="Service Provider created successfully",
            data={"cognito_id": auth_id},
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Unexpected error occurred", error=str(e)
        )


@router.post("/superadmin/create_admin")
async def create_admin(
    request: CreateAdmin,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        print(request.__dict__, "request")
        # ✅ Verify Super Admin Identity
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            superadmin_resp_json = resp.json()
            superadmin_id = superadmin_resp_json.get("id")
        else:
            return ErrorResponse(status_code=401, message=resp.json())

        # ✅ Check if Admin Already Exists in DB
        query = select(Admin).filter(Admin.email == request.email)
        result = await db.execute(query)
        admin_obj = result.scalars().first()
        if admin_obj:
            raise HTTPException(status_code=400, detail="Admin already exists")

        # ✅ Create Admin in Cognito
        try:
            auth_id = create_cognito_user(
                request.phone_number, generate_cognito_password(), request.email
            )
            add_user_to_group(auth_id.get("User", {}).get("Username", ""), request.role)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

        # ✅ Create Admin in PostgreSQL
        request_dict = request.dict()
        print(request_dict, "request_dict")
        request_dict["auth_id"] = auth_id["User"]["Username"]
        request_dict["status"] = ServiceProviderStatusType.APPROVED
        new_admin = await create_record(db, Admin, request_dict)

        await update_cognito_attributes(
            auth_id["User"]["Username"], {"custom:account_id": str(new_admin.id)}, False
        )
        return StandardResponse(
            status_code=200,
            message=f"{request.role.capitalize()} created successfully",
            data={"cognito_id": auth_id},
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Error creating admin", error=str(e)
        )


@router.post("/admin/login")
async def admin_login(username: str, password: str, db: AsyncSession = Depends(get_db)):
    """
    Logs in an admin user directly with Cognito without requiring OTP verification.
    Also checks if the user is active in the database.

    Args:
        username: The username/email of the admin
        password: The admin's password

    Returns:
        Dictionary containing authentication tokens and expiration
    """
    try:
        # First check if user is active in our database
        # Check admin table
        query = select(Admin).filter(Admin.email == username, Admin.is_active == True)
        result = await db.execute(query)
        admin_obj = result.scalars().first()

        # If not found in admin table, check service provider table
        if not admin_obj:
            query = select(ServiceProvider).filter(
                ServiceProvider.email == username, ServiceProvider.is_active == True
            )
            result = await db.execute(query)
            sp_obj = result.scalars().first()

            if not sp_obj:
                # Also check users table for completeness
                query = select(Users).filter(
                    Users.email == username, Users.is_active == True
                )
                result = await db.execute(query)
                user_obj = result.scalars().first()

                if not user_obj:
                    return ErrorResponse(
                        status_code=401,
                        message="Account is inactive or does not exist",
                        error="INACTIVE_ACCOUNT",
                    )

        # If we get here, the account is active, proceed with Cognito login
        response = await cognito_admin_login(username, password)

        # Check if we need to handle a challenge
        if "ChallengeName" in response:
            if response["ChallengeName"] == "NEW_PASSWORD_REQUIRED":
                return ErrorResponse(
                    status_code=400,
                    message="User needs to set a new password. Please implement password reset flow.",
                    error="NEW_PASSWORD_REQUIRED",
                )
            else:
                return ErrorResponse(
                    status_code=400,
                    message=f"Unhandled challenge: {response['ChallengeName']}",
                    error=f"Unhandled challenge: {response['ChallengeName']}",
                )

        # Return tokens from successful authentication
        return StandardResponse(
            status_code=200,
            message="Admin logged in successfully",
            data={
                "id_token": response["AuthenticationResult"]["IdToken"],
                "access_token": response["AuthenticationResult"]["AccessToken"],
                "refresh_token": response["AuthenticationResult"]["RefreshToken"],
                "expires_in": response["AuthenticationResult"]["ExpiresIn"],
            },
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error logging in", error=str(e))


@router.delete("/admin/delete-user/{username}")
async def delete_user(
    username: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Deletes a user from Cognito and the database.

    Args:
        username: The username/email of the user to delete

    Returns:
        Success message
    """
    try:
        # Verify Admin Identity - modified to handle dictionary response
        resp = await get_id_header(Authorization)

        # Check if resp is a dictionary (direct response) or has status_code (Response object)
        if hasattr(resp, "status_code") and resp.status_code == 200:
            admin_resp_json = resp.json()
            admin_id = admin_resp_json.get("account_id")
        elif isinstance(resp, dict) and "id" in resp:
            # If resp is already a dictionary with id
            admin_id = resp.get("account_id")
        else:
            # Handle error case
            error_message = (
                resp if isinstance(resp, dict) else {"error": "Authentication failed"}
            )
            return ErrorResponse(status_code=401, message=error_message)

        await delete_cognito_user(username)

        query = select(ServiceProvider).filter(
            or_(
                ServiceProvider.email == username,
                ServiceProvider.phone_number == username,
            )
        )
        result = await db.execute(query)
        sp_obj = result.scalars().first()

        if sp_obj:
            await db.delete(sp_obj)
            await db.commit()
            return StandardResponse(
                status_code=200,
                message=f"Service provider {username} deleted successfully",
            )

        # If not a service provider, check if it's an admin
        query = select(Admin).filter(
            or_(
                Admin.email == username,
                Admin.phone_number == username,
            )
        )  # Changed to auth_id for consistency
        result = await db.execute(query)
        admin_obj = result.scalars().first()

        if admin_obj:
            await db.delete(admin_obj)
            await db.commit()
            return StandardResponse(
                status_code=200, message=f"Admin {username} deleted successfully"
            )

        # If not a service provider or admin, check if it's a regular user
        query = select(Users).filter(
            or_(
                Users.email == username,
                Users.phone_number == username,
            )
        )  # Changed to auth_id for consistency
        result = await db.execute(query)
        user_obj = result.scalars().first()

        if user_obj:
            await db.delete(user_obj)
            await db.commit()
            return StandardResponse(
                status_code=200, message=f"User {username} deleted successfully"
            )

        # If we got here, the user was deleted from Cognito but not found in our database
        return ErrorResponse(
            status_code=400,
            message=f"User {username} deleted from Cognito but not found in database",
        )

    except Exception as e:
        import traceback

        print(traceback.format_exc())  # Print full traceback for debugging
        return ErrorResponse(status_code=500, message=f"Error deleting user: {str(e)}")


@router.get("/admin-list")
async def list_admin(
    q: Optional[str] = None,
    skip: int = 0,
    limit: int = 10,
    pagination: bool = False,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            auth_id = resp_json.get("id")
            role = resp_json.get("user_role")
        else:
            return ErrorResponse(status_code=401, message="Unauthorized")

        # Total admins
        query = select(Admin)

        if role == "admin":
            # filter agent only for admin role
            query = query.filter(Admin.role == "agent")

        # Apply search filter
        if q:
            query = query.where(
                or_(
                    Admin.id.cast(String) == q,
                    Admin.phone_number.ilike(f"%{q}%"),
                    Admin.email.ilike(f"%{q}%"),
                    Admin.first_name.ilike(f"%{q}%"),
                    Admin.last_name.ilike(f"%{q}%"),
                )
            )

        # Count total matching records before pagination
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()
        if pagination == True:
            query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        admins = result.scalars().all()

        print(admins, total_count, "adminsssssss")

        return StandardResponse(
            status_code=200,
            message="Admin list fetched successfully",
            data={"total": total_count, "data": admins},
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Error fetching admin list", error=str(e)
        )


@router.post("/admin/bulk-upload-service-providers")
async def upload_service_providers(
    background_task: BackgroundTasks,
    excel_file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Upload an Excel file with service providers.
    This endpoint starts a background task and returns a task ID for tracking.
    """
    try:
        # Verify Admin's Identity
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized access")

        # Extract token from Authorization header
        token = Authorization.split(" ")[1]

        # Create a temporary file
        temp_dir = os.getenv("TEMP_DIR", "/tmp")
        os.makedirs(temp_dir, exist_ok=True)

        file_path = os.path.join(temp_dir, f"{uuid.uuid4()}_{excel_file.filename}")

        # Save the uploaded file
        with open(file_path, "wb") as f:
            content = await excel_file.read()
            f.write(content)

        # Start the background task and get task ID
        task_id = await run_bulk_upload_task(file_path, token)

        if not task_id:
            return ErrorResponse(
                status_code=500, message="Failed to start background task"
            )

        return StandardResponse(
            status_code=200,
            message="File uploaded and processing started",
            data={"task_id": task_id, "status": "processing", "file_path": file_path},
        )
    except Exception as e:
        import traceback

        print(traceback.format_exc())  # Print full traceback for debugging
        return ErrorResponse(status_code=500, message=f"Error uploading file: {str(e)}")


@router.get("/admin/bulk-upload/status/{task_id}")
async def check_upload_status(task_id: str):
    """
    Check the status of a bulk upload task.
    """
    status = get_task_status(task_id)

    if status["status"] == "not_found":
        raise HTTPException(status_code=404, detail="Task not found")

    return status


@router.put("/admin/soft-delete-user/{username}")
async def soft_delete_user(
    username: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Soft deletes a user by marking them as inactive in the database.
    The user remains in the database but cannot log in.

    Args:
        username: The username/email of the user to soft delete

    Returns:
        Success message
    """
    try:
        # Verify Admin Identity
        resp = await get_id_header(Authorization)

        # Check if resp is a dictionary or has status_code
        if hasattr(resp, "status_code") and resp.status_code == 200:
            admin_resp_json = resp.json()
            admin_id = admin_resp_json.get("account_id")
        elif isinstance(resp, dict) and "id" in resp:
            admin_id = resp.get("account_id")
        else:
            error_message = (
                resp if isinstance(resp, dict) else {"error": "Authentication failed"}
            )
            return ErrorResponse(status_code=401, message=error_message)

        # Check if user is a service provider
        query = select(ServiceProvider).filter(ServiceProvider.auth_id == username)
        result = await db.execute(query)
        sp_obj = result.scalars().first()

        if sp_obj:
            # Mark as inactive instead of deleting
            sp_obj.is_active = False
            await db.commit()
            return StandardResponse(
                status_code=200,
                message=f"Service provider {username} deactivated successfully",
            )

        # Check if user is an admin
        query = select(Admin).filter(Admin.auth_id == username)
        result = await db.execute(query)
        admin_obj = result.scalars().first()

        if admin_obj:
            # Mark as inactive instead of deleting
            admin_obj.is_active = False
            await db.commit()
            return StandardResponse(
                status_code=200, message=f"Admin {username} deactivated successfully"
            )

        # Check if user is a regular user
        query = select(Users).filter(Users.auth_id == username)
        result = await db.execute(query)
        user_obj = result.scalars().first()

        if user_obj:
            # Mark as inactive instead of deleting
            user_obj.is_active = False
            await db.commit()
            return StandardResponse(
                status_code=200, message=f"User {username} deactivated successfully"
            )

        # If we got here, the user was not found in our database
        return ErrorResponse(
            status_code=404, message=f"User {username} not found in database"
        )

    except Exception as e:
        import traceback

        print(traceback.format_exc())  # Print full traceback for debugging
        return ErrorResponse(
            status_code=500, message=f"Error deactivating user: {str(e)}"
        )


@router.put("/admin/reactivate-user/{username}")
async def reactivate_user(
    username: str,
    db: AsyncSession = Depends(get_db),
    # Authorization: Annotated[str, Header()] = None,
):
    """
    Reactivates a previously soft-deleted user.

    Args:
        username: The username/email of the user to reactivate

    Returns:
        Success message
    """
    try:
        # Verify Admin Identity
        # resp = await get_id_header(Authorization)

        # if hasattr(resp, "status_code") and resp.status_code == 200:
        #     admin_resp_json = resp.json()
        #     admin_id = admin_resp_json.get("account_id")
        # elif isinstance(resp, dict) and "id" in resp:
        #     admin_id = resp.get("account_id")
        # else:
        #     error_message = (
        #         resp if isinstance(resp, dict) else {"error": "Authentication failed"}
        #     )
        #     return ErrorResponse(status_code=401, message=error_message)

        # Check all three tables for the user
        user_found = False

        # Check service provider table
        query = select(ServiceProvider).filter(ServiceProvider.phone_number == username)
        result = await db.execute(query)
        sp_obj = result.scalars().first()

        if sp_obj:
            sp_obj.is_active = True
            enable_response = await enable_cognito_user(sp_obj.phone_number)
            if enable_response is None:
                return ErrorResponse(
                    status_code=400,
                    message="Failed to enable Cognito user, no user in cognito",
                )
            await db.commit()
            user_found = True
            user_type = "Service provider"

        # Check admin table
        if not user_found:
            query = select(Admin).filter(Admin.phone_number == username)
            result = await db.execute(query)
            admin_obj = result.scalars().first()

            if admin_obj:
                admin_obj.is_active = True
                enable_response = await enable_cognito_user(admin_obj.phone_number)
                if enable_response is None:
                    return ErrorResponse(
                        status_code=400,
                        message="Failed to enable Cognito user, no user in cognito",
                    )
                await db.commit()
                user_found = True
                user_type = "Admin"

        # Check users table
        if not user_found:
            query = select(Users).filter(Users.phone_number == username)
            result = await db.execute(query)
            user_obj = result.scalars().first()

            if user_obj:
                user_obj.is_active = True
                enable_response = await enable_cognito_user(user_obj.phone_number)
                if enable_response is None:
                    return ErrorResponse(
                        status_code=400,
                        message="Failed to enable Cognito user, no user in cognito",
                    )
                await db.commit()
                user_found = True
                user_type = "User"

        if user_found:
            return StandardResponse(
                status_code=200,
                message=f"{user_type} {username} reactivated successfully",
            )
        else:
            return ErrorResponse(
                status_code=404, message=f"User {username} not found in database"
            )

    except Exception as e:
        import traceback

        print(traceback.format_exc())
        return ErrorResponse(
            status_code=500, message=f"Error reactivating user: {str(e)}"
        )


@router.put("/admin/soft-delete-artisan/{username}")
async def soft_delete_artisan(
    username: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Soft deletes an artisan (service provider) by marking them as inactive in the database.
    The artisan remains in the database but cannot log in.

    Args:
        username: The username/email of the artisan to soft delete

    Returns:
        Success message
    """
    try:
        # Verify Admin Identity
        resp = await get_id_header(Authorization)

        # Check if resp is a dictionary or has status_code
        if hasattr(resp, "status_code") and resp.status_code == 200:
            admin_resp_json = resp.json()
            admin_id = admin_resp_json.get("account_id")
        elif isinstance(resp, dict) and "id" in resp:
            admin_id = resp.get("account_id")
        else:
            error_message = (
                resp if isinstance(resp, dict) else {"error": "Authentication failed"}
            )
            return ErrorResponse(status_code=401, message=error_message)

        # Check if user is a service provider
        query = select(ServiceProvider).filter(ServiceProvider.auth_id == username)
        result = await db.execute(query)
        sp_obj = result.scalars().first()

        if sp_obj:
            # Mark as inactive instead of deleting
            sp_obj.is_active = False
            await db.commit()
            return StandardResponse(
                status_code=200, message=f"Artisan {username} deactivated successfully"
            )
        else:
            return ErrorResponse(
                status_code=404, message=f"Artisan {username} not found in database"
            )

    except Exception as e:
        import traceback

        print(traceback.format_exc())  # Print full traceback for debugging
        return ErrorResponse(
            status_code=500, message=f"Error deactivating artisan: {str(e)}"
        )


@router.put("/admin/reactivate-artisan/{username}")
async def reactivate_artisan(
    username: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Reactivates a previously soft-deleted artisan (service provider).

    Args:
        username: The username/email of the artisan to reactivate

    Returns:
        Success message
    """
    try:
        # Verify Admin Identity
        resp = await get_id_header(Authorization)

        if hasattr(resp, "status_code") and resp.status_code == 200:
            admin_resp_json = resp.json()
            admin_id = admin_resp_json.get("account_id")
        elif isinstance(resp, dict) and "id" in resp:
            admin_id = resp.get("account_id")
        else:
            error_message = (
                resp if isinstance(resp, dict) else {"error": "Authentication failed"}
            )
            return ErrorResponse(status_code=401, message=error_message)

        # Check service provider table
        query = select(ServiceProvider).filter(ServiceProvider.auth_id == username)
        result = await db.execute(query)
        sp_obj = result.scalars().first()

        if sp_obj:
            sp_obj.is_active = True
            enable_response = await enable_cognito_user(sp_obj.phone_number)
            if enable_response is None:
                return ErrorResponse(
                    status_code=400,
                    message="Failed to enable Cognito user, no user in cognito",
                )
            await db.commit()
            return StandardResponse(
                status_code=200, message=f"Artisan {username} reactivated successfully"
            )
        else:
            return ErrorResponse(
                status_code=404, message=f"Artisan {username} not found in database"
            )

    except Exception as e:
        import traceback

        print(traceback.format_exc())
        return ErrorResponse(
            status_code=500, message=f"Error reactivating artisan: {str(e)}"
        )


@router.get("/admin-read")
async def read_admin(
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(status_code=401, message=resp.json())

        query = select(Admin).filter(Admin.id == account_id)
        result = await db.execute(query)
        admin_obj = result.scalars().first()

        if not admin_obj:
            return ErrorResponse(status_code=404, message="Admin not found")

        return StandardResponse(
            status_code=200, message="Admin read successfully", data=admin_obj
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error reading admin: {str(e)}")


@router.get("/admin-read/{id}")
async def read_admin_by_id(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            auth_id = resp_json.get("id")
        else:
            return ErrorResponse(status_code=401, message=resp.json())

        try:
            admin_id = uuid.UUID(id)
        except ValueError:
            return ErrorResponse(status_code=400, message="Invalid admin ID format")

        query = select(Admin).filter(Admin.id == admin_id)
        result = await db.execute(query)
        admin_obj = result.scalars().first()

        if not admin_obj:
            return ErrorResponse(
                status_code=404, message=f"Admin with ID {id} not found"
            )

        return StandardResponse(
            status_code=200, message="Admin read successfully", data=admin_obj
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error reading admin: {str(e)}")


async def process_df(df, token):
    df = df.fillna("")
    for index, row in df.iterrows():
        reg_code = str(row["Trainee Reg. Code"]).strip()
        surname = str(row["Trainee Surname"]).strip()
        first_name = str(row["Trainee First Name"]).strip()
        other_names = (
            str(row["Other names of Trainee"]).strip()
            if pd.notna(row["Other names of Trainee"])
            else ""
        )
        trade_area = str(row["Trade Area(s)"]).strip()
        region = str(row["Region"]).strip()
        district = str(row["District/Town"]).strip()

        # Validate required fields
        if not all([reg_code, surname, first_name, trade_area, region, district]):
            continue
        # Process each row to kafka topic
        data_dict = row.to_dict()
        data_dict["token"] = token
        await cognito_producer(data_dict)
    return


@router.post("/admin/bulk-upload-service-providers/v2")
async def upload_service_providers_v2(
    background_task: BackgroundTasks,
    excel_file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Upload an Excel file with service providers.
    This endpoint starts a background task and returns a task ID for tracking.
    """
    try:
        # Verify Admin's Identity
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized access")

        # Extract token from Authorization header
        token = Authorization.split(" ")[1]

        file_content = await excel_file.read()

        try:
            df = pd.read_excel(io.BytesIO(file_content))
            total_rows = len(df)
            required_columns = [
                "S/N",
                "Trainee Reg. Code",
                "Trade Area(s)",
                "Trainee First Name",
                "Trainee Surname",
                "Other names of Trainee",
                "Region",
                "District/Town",
                "Trainee Contact Number",
            ]
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return {
                    "success": False,
                    "message": f"Missing required columns: {', '.join(missing_columns)}",
                    "created": 0,
                    "total": total_rows,
                    "skipped": 0,
                    "errors": [
                        {"error": f"Missing columns: {', '.join(missing_columns)}"}
                    ],
                    "skipped_details": [],
                }
            background_task.add_task(process_df, df, token)
        except Exception as e:
            return ErrorResponse(
                status_code=400, message=f"Error reading Excel file: {str(e)}"
            )

        return StandardResponse(
            status_code=200, message="File uploaded and processing started"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error uploading file: {str(e)}")

@router.put("/agent-approval/{id}")
async def agent_approval(
    id: str,
    data: AgentApproval,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()

            # Verify agent exists
            query = select(Admin).filter(Admin.id == uuid.UUID(id))
            result = await db.execute(query)
            get_agent = result.scalars().first()
            
            if not get_agent:
                return ErrorResponse(
                    status_code=404,
                    message="Agent not found",
                )
            
            request = data.dict()

            # Handle blacklisting or suspending the agent
            if (
                data.status == AgentStatusType.BLACKLIST
                or data.status == AgentStatusType.SUSPENDED
            ):
                if get_agent.is_active:
                    setattr(get_agent, "is_active", False)
                    try:
                        disable_response = await disable_cognito_user(get_agent.phone_number)
                        if disable_response is None:
                            print(f"Warning: No Cognito user found for {get_agent.phone_number}")
                    except Exception as e:
                        print(f"Error disabling Cognito user: {str(e)}")
                        return ErrorResponse(
                            status_code=400,
                            message=f"Failed to disable Cognito user: {str(e)}",
                        )
                else:
                    return ErrorResponse(
                        status_code=400,
                        message="Agent already blacklisted or suspended",
                    )

            # Handle re-activating a suspended or blacklisted agent
            if (
                data.status == AgentStatusType.APPROVED
                and (get_agent.status == AgentStatusType.SUSPENDED or get_agent.status == AgentStatusType.BLACKLIST)
                and not get_agent.is_active
            ):
                setattr(get_agent, "is_active", True)
                try:
                    enable_response = await enable_cognito_user(get_agent.phone_number)
                    if enable_response is None:
                        print(f"Warning: No Cognito user found for {get_agent.phone_number}")
                except Exception as e:
                    print(f"Error enabling Cognito user: {str(e)}")
                    return ErrorResponse(
                        status_code=400,
                        message=f"Failed to enable Cognito user: {str(e)}",
                    )

            # Update agent status and reason
            setattr(get_agent, "status", data.status)
            if data.reason:
                setattr(get_agent, "reason", data.reason)
            await db.commit()
            await db.refresh(get_agent)

            # Send notification to agent
            try:
                if hasattr(get_agent, "notification_uuid") and get_agent.notification_uuid:
                    send_push_notification(
                        auth_token=Authorization,
                        title="Profile Status Updated",
                        message=f"Your profile has been {data.status}",
                        sender_id=get_agent.notification_uuid,
                        type="agent",
                    )
            except Exception as notification_error:
                # Log error but continue with success response
                print(f"Failed to send notification: {str(notification_error)}")

            return StandardResponse(
                status_code=200, 
                message=f"Agent status updated successfully to {data.status}"
            )
        else:
            return ErrorResponse(status_code=401, message="Unauthorized")
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return ErrorResponse(status_code=500, message=f"Error updating agent status: {str(e)}")


@router.post("/admin/bulk-delete-service-providers")
async def bulk_delete_service_providers(
    background_task: BackgroundTasks,
    excel_file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Upload an Excel file with service providers to delete.
    This endpoint starts a background task and returns a task ID for tracking.
    """
    try:
        # Verify Admin's Identity
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized access")

        # Extract token from Authorization header
        token = Authorization.split(" ")[1]

        file_content = await excel_file.read()

        try:
            df = pd.read_excel(io.BytesIO(file_content))
            total_rows = len(df)
            required_columns = [
                "Trainee Contact Number"
            ]
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return {
                    "success": False,
                    "message": f"Missing required columns: {', '.join(missing_columns)}",
                    "total": total_rows,
                    "deleted": 0,
                    "errors": [
                        {"error": f"Missing columns: {', '.join(missing_columns)}"}
                    ]
                }
            background_task.add_task(process_delete_df, df, token)
        except Exception as e:
            return ErrorResponse(
                status_code=400, message=f"Error reading Excel file: {str(e)}"
            )

        return StandardResponse(
            status_code=200, message="File uploaded and deletion process started"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error uploading file: {str(e)}")
