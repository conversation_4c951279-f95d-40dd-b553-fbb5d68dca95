"""service rate sheet colums update

Revision ID: 0f9e3a323467
Revises: c8e5d3c37914
Create Date: 2025-06-19 13:17:22.025655

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0f9e3a323467'
down_revision: Union[str, None] = 'c8e5d3c37914'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('service_rate_sheet', 'price_per_unit',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True)
    op.create_unique_constraint(None, 'service_rate_sheet', ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'service_rate_sheet', type_='unique')
    op.alter_column('service_rate_sheet', 'price_per_unit',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
    # ### end Alembic commands ###
