from pydantic import BaseModel, UUID4
from typing import Optional
from fastapi import Form
from app.models_enum import PricingType, PaymentType


# class CreateServiceRequest(BaseModel):
#     user_id: str
#     sp_id: Optional[str] = None
#     booking_date: str
#     preferred_arrival_time: str
#     user_latitude: float
#     user_longitude: float
#     user_address: str
#     request_type: Optional[str] = None
#     service_list: Optional[list] = None

class CreateServiceRequest(BaseModel):
    user_id: str
    sp_id: Optional[str] = None
    booking_date: str
    preferred_arrival_time: str
    user_latitude: float
    user_longitude: float
    user_address: str
    locality: Optional[str] = None
    request_type: Optional[str] = None
    address_id: Optional[UUID4] = None
    payment_type: Optional[PaymentType] = None


class AssignArtisanRequest(BaseModel):
    booking_id: str
    # service_id: str
    invoice_item_id: Optional[str] = None
    artisan_ids: list
    assign_type: Optional[str] = 'assign' # assign or reassign


class ArtisanSoftLockRequest(BaseModel):
    artisan_id: str
    is_lock: bool


class UpdateServiceRequest(BaseModel):
    assigned_agent_id: Optional[str] = None
    status: Optional[str] = None
    is_exit: Optional[bool] = None
    # If needed, add more fields for updating service request

class AddServiceCreate(BaseModel):
    booking_id: str
    service_id: str
    artisan_count: int
    description: Optional[str] = None
    artisan_ids: Optional[list] = None
