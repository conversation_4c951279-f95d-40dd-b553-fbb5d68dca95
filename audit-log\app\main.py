import logging
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from app.database import engine
# from app.models import audit_log
from app.routes import audit_log as audit_log_router

log = logging.getLogger("uvicorn")


# Add models to create tables
# audit_log.Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="API",
    description="FastAPI-based backend for audit log service",
    version="1.0.0"
)

# CORS Middleware (Allow all origins for frontend integration)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Change to specific frontend domain in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
def health_check():
    return {"message": "Service is Up and running"}


# Registering Routes
app.include_router(audit_log_router.router)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8011)
