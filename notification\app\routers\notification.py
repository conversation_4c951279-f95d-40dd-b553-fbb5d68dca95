from datetime import datetime
from typing import Annotated
from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException, <PERSON><PERSON>
import pytz
from app import schemas
from app.database import get_db
from app.utils import create_record, get_id_header
from app.kafka_producer.producer import notification_producer
from sqlalchemy.ext.asyncio import AsyncSession
from app.celery.task import send_delayed_notification
from app.config import get_settings
from app.models import Notification
router = APIRouter()


@router.post("/send_notification")
async def schedule_notification(
    request: schemas.NotificationRequest,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        print(request.__dict__, "request")
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            email = resp_json.get("email")
        else:
            return {"error": resp.json()}

        if get_settings().ENVIRONMENT == "development":
            result = await create_record(db, Notification, request.model_dump())
            return {"data": result}

        notification_data = request.model_dump()
        if request.send_at:

            send_at_utc = datetime.fromisoformat(str(request.send_at)).replace(
                tzinfo=pytz.utc
            )
            current_time = datetime.now(pytz.utc)

            if send_at_utc < current_time:
                raise HTTPException(status_code=404, detail=f"Only future dates")

            delay_seconds = (send_at_utc - current_time).total_seconds()
            print("delay", delay_seconds)
            result = send_delayed_notification.apply_async(
                [notification_data], countdown=delay_seconds, queue="notification_queue"
            )
            print(f"Scheduled Task ID: {result.id}")

        else:
            await notification_producer(notification_data)
        
        result = await create_record(db, Notification, request.model_dump())
        return {"data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error: {e}")
