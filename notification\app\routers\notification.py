from datetime import datetime
from typing import Annotated, List, Optional
from fastapi import APIRout<PERSON>, Depends, HTTPException, Header
import pytz
import uuid
from app import schemas
from app.database import get_db
from app.utils import create_record, get_id_header
from app.kafka_producer.producer import notification_producer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from app.celery.task import send_delayed_notification
from app.config import get_settings
from app.models import Notification
from app.auth import permission_checker
from fastapi import Request
from fastapi.responses import J<PERSON>NResponse
from app.helper import ErrorResponse, StandardResponse
 
router = APIRouter() 


@router.post("/send_notification")
async def schedule_notification(
    # req: Request,
    request: schemas.NotificationRequest,
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker), 
    # Authorization: Annotated[str, Header()] = None,
):
    try:
        if isinstance(user, JSONResponse):
            return user
        
        # Authorization = req.headers.get("Authorization")
        # if not Authorization:
        #     return ErrorResponse(status_code=401, message="Authorization header is required")
            
        #Get authenticated user info
        # account_id = user.get("user_id")
        # role= user.get("role_name")
        # if not account_id:
        #     return ErrorResponse(status_code=401, message="User not authenticated")
 
        # print(request.__dict__, "request")
        # resp = await get_id_header(Authorization)
        # if resp.status_code == 200:
        #     resp_json = resp.json()
        #     account_id = resp_json.get("account_id")
        #     email = resp_json.get("email")
        # else:
        #     return {"error": resp.json()}
 
        if get_settings().ENVIRONMENT == "development":
            result = await create_record(db, Notification, request.model_dump())
            return {"data": result}
        
        notification_data = request.model_dump()
        if request.send_at:
 
            send_at_utc = datetime.fromisoformat(str(request.send_at)).replace(
                tzinfo=pytz.utc
            )
            current_time = datetime.now(pytz.utc)
    
            if send_at_utc < current_time:
                raise HTTPException(status_code=404, detail=f"Only future dates")
 
            delay_seconds = (send_at_utc - current_time).total_seconds()

            print("delay", delay_seconds)
            result = send_delayed_notification.apply_async(
                [notification_data], countdown=delay_seconds, queue="notification_queue"
            )
            print(f"Scheduled Task ID: {result.id}")

        else:
            await notification_producer(notification_data)
        result = await create_record(db, Notification, request.model_dump())
        return {"data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error: {e}")




@router.get("/notifications-list")
async def get_all_notifications(
    skip: int = 1,
    limit: int = 10,
    user_id: Optional[str] = None,
    notification_type: Optional[str] = None,
    fcm_request_type: Optional[str] = None,
    is_read: Optional[bool] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker), 
):
    """
    Get all notifications from the database with pagination and filtering.
    Returns a list of notifications ordered by creation date (newest first).
    """
    try:
        if isinstance(user, JSONResponse):
            return user
        
        # Build query with filters
        query = select(Notification).filter(Notification.is_active == True)
        count_query = select(func.count()).select_from(Notification).filter(Notification.is_active == True)

        # Apply filters
        if user_id:
            query = query.filter(Notification.user_id == uuid.UUID(user_id))
            count_query = count_query.filter(Notification.user_id == uuid.UUID(user_id))

        if notification_type:
            query = query.filter(Notification.notification_type == notification_type)
            count_query = count_query.filter(Notification.notification_type == notification_type)

        if is_read is not None:
            query = query.filter(Notification.is_read == is_read)
            count_query = count_query.filter(Notification.is_read == is_read)

        if fcm_request_type:
            query = query.filter(Notification.fcm_request_type == fcm_request_type)
            count_query = count_query.filter(Notification.fcm_request_type == fcm_request_type)
        
        if start_date or end_date:
            start_date_parsed = None
            end_date_parsed = None  
            if start_date:
                start_date_parsed = datetime.strptime(start_date, "%Y-%m-%d").date()
            if end_date:
                end_date_parsed = datetime.strptime(end_date, "%Y-%m-%d").date()

            # Apply date filters
            if start_date_parsed and end_date_parsed:
                # Both dates provided - filter for date range
                query = query.filter(
                    and_(
                        func.date(Notification.created_at) >= start_date_parsed,
                        func.date(Notification.created_at) <= end_date_parsed
                    )
                )
                count_query = count_query.filter(
                    and_(
                        func.date(Notification.created_at) >= start_date_parsed,
                        func.date(Notification.created_at) <= end_date_parsed
                    )
                )


        # Order by creation date (newest first)
        query = query.order_by(Notification.created_at.desc())
        
        # Apply pagination
        adjusted_skip = (skip - 1) * limit
        query = query.offset(adjusted_skip).limit(limit)
        
        # Execute queries
        result = await db.execute(query)
        notifications = result.scalars().all()

        # Get total count
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()
        
        # Convert to response models with explicit field mapping
        notification_responses = []
        for notification in notifications:
            notification_response = {
                "id": str(notification.id),
                "title": notification.title,
                "message": notification.message,
                "notification_type": notification.notification_type,
                # "phone_number": notification.phone_number,
                # "email": notification.email,
                # "sender_id": notification.sender_id,
                # "send_at": notification.send_at.isoformat() if notification.send_at else None,
                "data": notification.data,
                "fcm_request_type": notification.fcm_request_type,
                "user_id": str(notification.user_id) if notification.user_id else None,
                "is_read": notification.is_read if notification.is_read is not None else False,
                "created_at": notification.created_at.isoformat() if notification.created_at else None,
                # "updated_at": notification.updated_at.isoformat() if notification.updated_at else None,
                # "is_active": notification.is_active
            }
            notification_responses.append(notification_response)
        
        response_data = {
            "notifications": notification_responses,
            "pagination": {
                "page": skip,
                "limit": limit,
                "total": total_count
            }
        }
        return StandardResponse(
            status_code=200,
            message="Notification list retrival successfully",
            data=response_data
        )
    except Exception as e:
        raise HTTPException(status_code=500,detail=f"Error: {e}")



@router.put("/update_notification/{notification_id}")
async def update_notification_read(
    notification_id: str,
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker),
):
    """
    Update notification read status to True.
    Marks a specific notification as read based on notification ID.
    """
    try:
        if isinstance(user, JSONResponse):
            return user
        
        # Check if notification exists
        query = select(Notification).filter(Notification.id == uuid.UUID(notification_id))
        result = await db.execute(query)
        notification = result.scalar_one_or_none()
        
        if not notification:
            return ErrorResponse(
                status_code=404, 
                message="Notification not found"
            )
        
        # Update the notification to mark as read
        notification.is_read = True
        notification.updated_at = datetime.now()
        
        # Commit the changes
        await db.commit()
        await db.refresh(notification)
        
        # Prepare response
        notification_response = {
            "id": str(notification.id),
            # "title": notification.title,
            # "message": notification.message,
            # "notification_type": notification.notification_type,
            # "phone_number": notification.phone_number,
            # "email": notification.email,
            # "send_at": notification.send_at.isoformat() if notification.send_at else None,
            # "data": notification.data,
            # "fcm_request_type": notification.fcm_request_type,
            "user_id": str(notification.user_id) if notification.user_id else None,
            "is_read": notification.is_read,
            # "created_at": notification.created_at.isoformat() if notification.created_at else None,
            "updated_at": notification.updated_at.isoformat() if notification.updated_at else None,
        }
        
        return StandardResponse(
            status_code=200,
            message="Notification marked as read successfully",
            data=notification_response
        )
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Error: {e}")

