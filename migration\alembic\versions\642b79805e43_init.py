"""init

Revision ID: 642b79805e43
Revises: 
Create Date: 2025-06-05 16:44:23.160797

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import geoalchemy2

# revision identifiers, used by Alembic.
revision: str = '642b79805e43'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.execute('CREATE EXTENSION IF NOT EXISTS postgis')
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('accounts',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('balance', sa.Float(), nullable=False),
    sa.Column('currency', postgresql.ENUM('GHS', 'EUR', 'USD', 'AED', name='currencytype'), nullable=False),
    sa.Column('account_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='accountstatustype'), nullable=False),
    sa.Column('account_type', postgresql.ENUM('USER', 'AGENT', 'BUSINESS', name='accounttypetype'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('admin',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('auth_id', sa.String(), nullable=True),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('gender', sa.Enum('MALE', 'FEMALE', name='gender'), nullable=True),
    sa.Column('role', sa.Enum('ADMIN', 'SUPERADMIN', 'AGENT', name='adminrole'), nullable=True),
    sa.Column('status', sa.Enum('CREATED', 'APPROVED', 'REJECTED', 'SUSPENDED', 'BLACKLIST', name='serviceproviderstatustype'), server_default='CREATED', nullable=True),
    sa.Column('reason', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_index(op.f('ix_admin_email'), 'admin', ['email'], unique=True)
    op.create_index(op.f('ix_admin_phone_number'), 'admin', ['phone_number'], unique=True)
    op.create_table('booking_cancellation_reasons',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('reason', sa.String(), nullable=False),
    sa.Column('user_type', sa.Enum('ARTISAN', 'USER', 'AGENT', name='usertype'), nullable=True),
    sa.Column('penalty_needed', sa.Boolean(), nullable=True),
    sa.Column('refund_needed', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('bookings',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('booking_order_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('artisan_id', sa.UUID(), nullable=False),
    sa.Column('service_id', sa.UUID(), nullable=False),
    sa.Column('booking_date', sa.Date(), nullable=False),
    sa.Column('start_time', sa.Time(), nullable=False),
    sa.Column('end_time', sa.Time(), nullable=False),
    sa.Column('start_otp', sa.String(), nullable=False),
    sa.Column('end_otp', sa.String(), nullable=False),
    sa.Column('service_start', sa.DateTime(), nullable=True),
    sa.Column('service_end', sa.DateTime(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('reason', sa.String(), nullable=True),
    sa.Column('user_latitude', sa.Float(), nullable=False),
    sa.Column('user_longitude', sa.Float(), nullable=False),
    sa.Column('service_start_artisan_latitude', sa.Float(), nullable=True),
    sa.Column('service_start_artisan_longitude', sa.Float(), nullable=True),
    sa.Column('service_end_artisan_latitude', sa.Float(), nullable=True),
    sa.Column('service_end_artisan_longitude', sa.Float(), nullable=True),
    sa.Column('user_address', sa.String(), nullable=False),
    sa.Column('is_rescheduled', sa.Boolean(), nullable=True),
    sa.Column('payment_type', sa.Enum('CASH', 'WALLET', 'CARD', name='paymenttype'), nullable=True),
    sa.Column('payment_id', sa.UUID(), nullable=True),
    sa.Column('tax', sa.Float(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'CONFIRMED', 'STARTED', 'ARRIVED', 'ONGOING', 'COMPLETED', 'CANCELLED', 'REJECTED', name='bookingstatus'), nullable=True),
    sa.Column('requested_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.Column('base_service_fee', sa.Float(), nullable=True),
    sa.Column('surcharges', sa.Float(), nullable=True),
    sa.Column('artisan_rating', sa.Integer(), nullable=True),
    sa.Column('artisan_rating_tags', sa.String(), nullable=True),
    sa.Column('artisan_feedback', sa.Text(), nullable=True),
    sa.Column('customer_rating', sa.Integer(), nullable=True),
    sa.Column('customer_rating_tags', sa.String(), nullable=True),
    sa.Column('customer_feedback', sa.Text(), nullable=True),
    sa.Column('cancellation_reason', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('booking_order_id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('category',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('parent_id', sa.UUID(), nullable=True),
    sa.Column('banner', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('category_issues',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('default_priority', sa.Enum('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='issuepriority'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code'),
    sa.UniqueConstraint('id')
    )
    op.create_table('dispute_event_types',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code'),
    sa.UniqueConstraint('id')
    )
    op.create_table('issue_categories',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('user_type', sa.Enum('ARTISAN', 'USER', 'AGENT', name='usertype'), nullable=True),
    sa.Column('default_priority', sa.Enum('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='disputepriority'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('payments',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('artisan_id', sa.UUID(), nullable=False),
    sa.Column('service_request_id', sa.UUID(), nullable=False),
    sa.Column('payment_method', postgresql.ENUM('CARD', 'WALLET', 'CASH', 'BANK', name='paymentmethodtype'), nullable=False),
    sa.Column('base_service_fee', sa.Float(), nullable=False),
    sa.Column('surcharges', sa.Float(), nullable=False),
    sa.Column('tax', sa.Float(), nullable=False),
    sa.Column('status', postgresql.ENUM('INIT', 'SUCCESS', 'FAILED', name='statustype'), nullable=False),
    sa.Column('payment_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('payment_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('currency', postgresql.ENUM('GHS', 'EUR', 'USD', 'AED', name='currencytype'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('service_provider',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('auth_id', sa.String(), nullable=True),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('gender', sa.Enum('MALE', 'FEMALE', name='gender'), nullable=True),
    sa.Column('dob', sa.Date(), nullable=True),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('primary_location', sa.String(), nullable=True),
    sa.Column('skill', sa.String(), nullable=True),
    sa.Column('skill_level', sa.String(), nullable=True),
    sa.Column('experience', sa.Float(), nullable=True),
    sa.Column('about_us', sa.Text(), nullable=True),
    sa.Column('reg_code', sa.String(), nullable=True),
    sa.Column('rating', sa.Integer(), nullable=True),
    sa.Column('status', sa.Enum('CREATED', 'APPROVED', 'REJECTED', 'SUSPENDED', 'BLACKLIST', name='serviceproviderstatustype'), server_default='CREATED', nullable=True),
    sa.Column('live_status', sa.String(), nullable=True),
    sa.Column('profile_pic', sa.String(), nullable=True),
    sa.Column('license', sa.String(), nullable=True),
    sa.Column('certificate', sa.String(), nullable=True),
    sa.Column('govt_id', sa.String(), nullable=True),
    sa.Column('police_report', sa.String(), nullable=True),
    sa.Column('guaranteed_doc', sa.String(), nullable=True),
    sa.Column('bank_acc_holder_name', sa.String(), nullable=True),
    sa.Column('bank_name', sa.String(), nullable=True),
    sa.Column('bank_acc_no', sa.String(), nullable=True),
    sa.Column('bank_branch_code', sa.String(), nullable=True),
    sa.Column('bank_swift_code', sa.String(), nullable=True),
    sa.Column('bank_acc_type', sa.String(), nullable=True),
    sa.Column('work_from_hrs', sa.Time(), nullable=True),
    sa.Column('work_to_hrs', sa.Time(), nullable=True),
    sa.Column('break_from_hrs', sa.Time(), nullable=True),
    sa.Column('break_to_hrs', sa.Time(), nullable=True),
    sa.Column('weekdays', sa.ARRAY(sa.Integer()), nullable=True),
    sa.Column('latitude', sa.Float(), nullable=False),
    sa.Column('longitude', sa.Float(), nullable=False),
    sa.Column('notification_uuid', sa.String(), nullable=True),
    sa.Column('reason', sa.String(), nullable=True),
    sa.Column('location', geoalchemy2.types.Geography(geometry_type='POINT', srid=4326, from_text='ST_GeogFromText', name='geography'), nullable=True),
    sa.Column('is_profile_complete', sa.Boolean(), server_default='FALSE', nullable=False),
    sa.Column('is_confirmed', sa.Boolean(), server_default='FALSE', nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    # op.create_index('idx_service_provider_location', 'service_provider', ['location'], unique=False, postgresql_using='gist')
    op.create_index(op.f('ix_service_provider_email'), 'service_provider', ['email'], unique=True)
    op.create_index(op.f('ix_service_provider_phone_number'), 'service_provider', ['phone_number'], unique=True)
    op.create_table('users',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('auth_id', sa.String(), nullable=True),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('primary_location', sa.String(), nullable=True),
    sa.Column('status', sa.Enum('APPROVED', 'SUSPENDED', 'BLACKLIST', name='userstatustype'), server_default='APPROVED', nullable=True),
    sa.Column('reason', sa.String(), nullable=True),
    sa.Column('rating', sa.Integer(), nullable=True),
    sa.Column('profile_pic', sa.String(), nullable=True),
    sa.Column('locations', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('notification_uuid', sa.String(), nullable=True),
    sa.Column('is_profile_complete', sa.Boolean(), server_default='FALSE', nullable=False),
    sa.Column('is_confirmed', sa.Boolean(), server_default='FALSE', nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_phone_number'), 'users', ['phone_number'], unique=True)
    op.create_table('booking_cancellation',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('artisan_id', sa.UUID(), nullable=False),
    sa.Column('agent_id', sa.UUID(), nullable=True),
    sa.Column('booking_id', sa.UUID(), nullable=False),
    sa.Column('transaction_id', sa.UUID(), nullable=True),
    sa.Column('payment_id', sa.UUID(), nullable=True),
    sa.Column('cancellation_reason_id', sa.UUID(), nullable=True),
    sa.Column('user_type', sa.Enum('ARTISAN', 'USER', 'AGENT', name='usertype'), nullable=True),
    sa.Column('status', sa.Enum('INITIATED', 'PENDING', 'APPROVED', 'COMPLETED', name='bookingcancellationstatus'), nullable=True),
    sa.Column('assigned_agent_id', sa.UUID(), nullable=True),
    sa.Column('payment_method', sa.String(), nullable=True),
    sa.Column('booking_amount', sa.Float(), nullable=True),
    sa.Column('refund_amount', sa.Float(), nullable=True),
    sa.Column('penalty_user_id', sa.UUID(), nullable=True),
    sa.Column('penalty', sa.Float(), nullable=True),
    sa.Column('penalty_needed', sa.Boolean(), nullable=True),
    sa.Column('refund_needed', sa.Boolean(), nullable=True),
    sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('attachments', sa.String(), nullable=True),
    sa.Column('reason_for_refund', sa.String(), nullable=True),
    sa.Column('reason_for_penalty', sa.String(), nullable=True),
    sa.Column('is_auto_refund', sa.Boolean(), nullable=True),
    sa.Column('comments', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.ForeignKeyConstraint(['booking_id'], ['bookings.id'], ),
    sa.ForeignKeyConstraint(['cancellation_reason_id'], ['booking_cancellation_reasons.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('booking_history',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('booking_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('artisan_id', sa.UUID(), nullable=False),
    sa.Column('service_id', sa.UUID(), nullable=False),
    sa.Column('reason', sa.String(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'CONFIRMED', 'STARTED', 'ARRIVED', 'ONGOING', 'COMPLETED', 'CANCELLED', 'REJECTED', name='bookingstatus'), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['booking_id'], ['bookings.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('device',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('service_provider_id', sa.UUID(), nullable=True),
    sa.Column('type', sa.Enum('ANDROID', 'IOS', name='devicetype'), nullable=True),
    sa.Column('notification_token', sa.String(), nullable=False),
    sa.Column('info', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.ForeignKeyConstraint(['service_provider_id'], ['service_provider.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_index(op.f('ix_device_notification_token'), 'device', ['notification_token'], unique=False)
    op.create_table('disputes',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('case_number', sa.String(), nullable=False),
    sa.Column('booking_id', sa.UUID(), nullable=False),
    sa.Column('customer_id', sa.UUID(), nullable=False),
    sa.Column('customer_name', sa.String(), nullable=True),
    sa.Column('merchant_id', sa.UUID(), nullable=False),
    sa.Column('merchant_name', sa.String(), nullable=True),
    sa.Column('dispute_type', sa.Enum('BOOKING_ISSUES', 'PAYMENT_AND_REFUND', 'SERVICE_ISSUES', 'OTHERS', name='disputetype'), nullable=False),
    sa.Column('dispute_sub_type', sa.String(), nullable=False),
    sa.Column('status', sa.Enum('OPEN', 'IN_PROGRESS', 'RESOLVED', name='disputestatus'), nullable=True),
    sa.Column('priority', sa.Enum('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='disputepriority'), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('supporting_documents', sa.String(), nullable=True),
    sa.Column('user_type', sa.Enum('ARTISAN', 'USER', 'AGENT', name='usertype'), nullable=False),
    sa.Column('assigned_to', sa.String(), nullable=True),
    sa.Column('transaction_id', sa.String(), nullable=True),
    sa.Column('transaction_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('transaction_amount', sa.Float(), nullable=True),
    sa.Column('disputed_amount', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('resolution_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('resolution_notes', sa.Text(), nullable=True),
    sa.Column('refund_amount', sa.Float(), nullable=True),
    sa.Column('chargeback_status', sa.Enum('PENDING', 'REFUND_INITIATED', 'PROCESSED', name='chargebackstatus'), nullable=True),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('issue_category_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['issue_category_id'], ['issue_categories.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('case_number'),
    sa.UniqueConstraint('id')
    )
    op.create_table('issue_types',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('category_id', sa.UUID(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['issue_categories.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('service_provider_leave',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('service_provider_id', sa.UUID(), nullable=False),
    sa.Column('leave_date', sa.Date(), nullable=False),
    sa.Column('reason', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.ForeignKeyConstraint(['service_provider_id'], ['service_provider.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id'),
    sa.UniqueConstraint('service_provider_id', 'leave_date', name='unique_leave_date')
    )
    op.create_table('service_provider_service_mapping',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('service_provider_id', sa.UUID(), nullable=False),
    sa.Column('services_id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.ForeignKeyConstraint(['service_provider_id'], ['service_provider.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('services',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('parent_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('banner', sa.String(), nullable=True),
    sa.Column('duration', sa.Integer(), nullable=True),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_multiple', sa.Boolean(), server_default='FALSE', nullable=True),
    sa.Column('service_code', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.ForeignKeyConstraint(['parent_id'], ['category.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id'),
    sa.UniqueConstraint('service_code')
    )
    op.create_table('subcategories_issues',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('category_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['category_issues.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('transactions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('payment_id', sa.UUID(), nullable=True),
    sa.Column('from_entity', sa.String(), nullable=False),
    sa.Column('to_entity', sa.String(), nullable=False),
    sa.Column('transaction_type', postgresql.ENUM('PAYMENT', 'PAYOUT', 'REFUND', name='transactiontypetype'), nullable=False),
    sa.Column('payment_method', postgresql.ENUM('CARD', 'WALLET', 'CASH', 'BANK', name='paymentmethodtype'), nullable=False),
    sa.Column('gateway_response', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('gateway_status', sa.String(), nullable=True),
    sa.Column('status', postgresql.ENUM('INIT', 'SUCCESS', 'FAILED', name='statustype'), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('transaction_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('transaction_id', sa.String(), nullable=True),
    sa.Column('cancellation_id', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.ForeignKeyConstraint(['payment_id'], ['payments.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('booking_cancellation_history',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('booking_cancellation_id', sa.UUID(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('user_name', sa.String(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.ForeignKeyConstraint(['booking_cancellation_id'], ['booking_cancellation.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('dispute_comments',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('dispute_id', sa.UUID(), nullable=False),
    sa.Column('author_id', sa.UUID(), nullable=False),
    sa.Column('author_name', sa.String(), nullable=False),
    sa.Column('comment', sa.Text(), nullable=False),
    sa.Column('date', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['dispute_id'], ['disputes.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('dispute_history',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('dispute_id', sa.UUID(), nullable=False),
    sa.Column('event_code', sa.String(), nullable=False),
    sa.Column('user', sa.String(), nullable=False),
    sa.Column('date', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['dispute_id'], ['disputes.id'], ),
    sa.ForeignKeyConstraint(['event_code'], ['dispute_event_types.code'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('issues',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('customer_id', sa.UUID(), nullable=False),
    sa.Column('case_number', sa.String(), nullable=False),
    sa.Column('category_id', sa.UUID(), nullable=False),
    sa.Column('subcategory_id', sa.UUID(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('supporting_documents', sa.String(), nullable=True),
    sa.Column('assigned_to', sa.UUID(), nullable=True),
    sa.Column('status', sa.Enum('OPEN', 'IN_PROGRESS', 'RESOLVED', name='issuestatus'), nullable=False),
    sa.Column('priority', sa.Enum('LOW', 'MEDIUM', 'HIGH', 'CRITICAL', name='issuepriority'), nullable=True),
    sa.Column('user_type', sa.Enum('ARTISAN', 'USER', 'AGENT', name='usertype'), nullable=False),
    sa.Column('resolution_notes', sa.Text(), nullable=True),
    sa.Column('resolution_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['category_issues.id'], ),
    sa.ForeignKeyConstraint(['subcategory_id'], ['subcategories_issues.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('case_number'),
    sa.UniqueConstraint('id')
    )
    op.create_table('issue_comments',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('issue_id', sa.UUID(), nullable=True),
    sa.Column('author_id', sa.UUID(), nullable=True),
    sa.Column('author_name', sa.String(), nullable=True),
    sa.Column('comment', sa.String(), nullable=False),
    sa.Column('date', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['issue_id'], ['issues.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('issue_history',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('issue_id', sa.UUID(), nullable=False),
    sa.Column('event_code', sa.String(), nullable=False),
    sa.Column('event_name', sa.String(), nullable=True),
    sa.Column('user', sa.String(), nullable=False),
    sa.Column('date', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['issue_id'], ['issues.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('issue_history')
    op.drop_table('issue_comments')
    op.drop_table('issues')
    op.drop_table('dispute_history')
    op.drop_table('dispute_comments')
    op.drop_table('booking_cancellation_history')
    op.drop_table('transactions')
    op.drop_table('subcategories_issues')
    op.drop_table('services')
    op.drop_table('service_provider_service_mapping')
    op.drop_table('service_provider_leave')
    op.drop_table('issue_types')
    op.drop_table('disputes')
    op.drop_index(op.f('ix_device_notification_token'), table_name='device')
    op.drop_table('device')
    op.drop_table('booking_history')
    op.drop_table('booking_cancellation')
    op.drop_index(op.f('ix_users_phone_number'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_service_provider_phone_number'), table_name='service_provider')
    op.drop_index(op.f('ix_service_provider_email'), table_name='service_provider')
    op.drop_index('idx_service_provider_location', table_name='service_provider', postgresql_using='gist')
    op.drop_table('service_provider')
    op.drop_table('payments')
    op.drop_table('issue_categories')
    op.drop_table('dispute_event_types')
    op.drop_table('category_issues')
    op.drop_table('category')
    op.drop_table('bookings')
    op.drop_table('booking_cancellation_reasons')
    op.drop_index(op.f('ix_admin_phone_number'), table_name='admin')
    op.drop_index(op.f('ix_admin_email'), table_name='admin')
    op.drop_table('admin')
    op.drop_table('accounts')
    # ### end Alembic commands ###
