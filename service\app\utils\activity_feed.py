from app.models import ActivityLog
from app.models_enum import ActivityType
from sqlalchemy.orm import Session

def log_activity(
    db: Session,
    title: str,
    description: str,
    reference_id: str,
    customer_id: str,
    activity_type: ActivityType,
):
    activity = ActivityLog(
        title=title,
        description=description,
        reference_id=reference_id,
        customer_id=customer_id,
        activity_type=activity_type,
    )
    db.add(activity)
    db.commit()