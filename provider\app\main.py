import uvicorn
from dotenv import load_dotenv
load_dotenv()
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
# import app.models as md
import app.database as database
from app.database import Base
from app.service_providers.views import router as sp_apis
from app.sp_branches.views import router as spb_apis
from app.sp_users.views import router as user_apis
from app.sp_ratings.views import router as rating_apis
from app.kyc_details.views import router as kyc_apis
import logging
log = logging.getLogger("uvicorn")

Base.metadata.create_all(bind=database.engine)

app = FastAPI(
    title="API",
    description="FastAPI-based backend for booking",
    version="1.0.0",
    docs_url="/provider-service/docs",           # Swagger UI
    redoc_url=None,                     # Disable ReDoc (optional)
    openapi_url="/provider-service/openapi.json"
)

# Add the list of routes we want to allow
origins = ["*"]

app.add_middleware(
    middleware_class=CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
def read_root():
    return {"message": "Provider Service is Up and running"}


app.include_router(sp_apis, prefix="/sp")
app.include_router(spb_apis, prefix="/sp")
app.include_router(user_apis,prefix="/sp")
app.include_router(rating_apis, prefix="/sp")
app.include_router(kyc_apis, prefix="/sp")

if __name__ == "__main__":
    uvicorn.run("main:app", reload=True, port=8000)