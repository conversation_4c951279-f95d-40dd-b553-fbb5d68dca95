from contextlib import asynccontextmanager
from typing import Annotated, Optional
from fastapi import FastAP<PERSON>, HTTPException, Depends, Header
from fastapi.middleware.cors import CORSMiddleware
# from app.config import get_settings
import json
import os
import requests
# from app.schemas import Body
from app.database import get_db, create_tables
from sqlalchemy.orm import Session
from app import models
from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.routers import category, services ,service_rate_sheet
from app.audit_log import log_requests



app = FastAPI(debug=True)

app.middleware("http")(log_requests)


# List of allowed origins (the frontend URLs that are allowed to access the backend)
allowed_orgins = [os.getenv("LOCALHOST_URL"),os.getenv("WEBAPP_URL"),os.getenv("UAT_WEBAPP_URL")]
origins = allowed_orgins
 
# Add CORSMiddleware to the application
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # Allows specified origins
    allow_credentials=True,  # Allows cookies to be included in requests
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

@app.get("/__health")
def read_root():
    return {"status": "online"}


app.include_router(category.router)
# app.include_router(sub_category.router)
app.include_router(services.router)
app.include_router(service_rate_sheet.router)


@app.on_event("startup")
async def startup():
    await create_tables()