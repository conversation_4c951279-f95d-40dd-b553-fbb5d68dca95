# Configuration File - Nginx Server Configs
# This is a read-only file, do not try to modify it.
master_process on;

worker_processes auto;

# main configuration snippet starts

# main configuration snippet ends

error_log logs/error.log warn;
pid logs/nginx.pid;

worker_rlimit_nofile 20480;

events {
    accept_mutex off;
    worker_connections 10620;
}

worker_rlimit_core  16G;

worker_shutdown_timeout 240s;

env APISIX_PROFILE;
env PATH; # for searching external plugin runner's binary

# reserved environment variables for configuration
env APISIX_DEPLOYMENT_ETCD_HOST;


thread_pool grpc-client-nginx-module threads=1;

lua {
}




http {
    # put extra_lua_path in front of the builtin path
    # so user can override the source code
    lua_package_path  "$prefix/deps/share/lua/5.1/?.lua;$prefix/deps/share/lua/5.1/?/init.lua;/usr/local/apisix/?.lua;/usr/local/apisix/?/init.lua;;/usr/local/apisix/?.lua;/usr/local/apisix/deps/share/lua/5.1/?/init.lua;./?.lua;/usr/local/openresty/luajit/share/luajit-2.1/?.lua;/usr/local/share/lua/5.1/?.lua;/usr/local/share/lua/5.1/?/init.lua;/usr/local/openresty/luajit/share/lua/5.1/?.lua;/usr/local/openresty/luajit/share/lua/5.1/?/init.lua;;";
    lua_package_cpath "$prefix/deps/lib64/lua/5.1/?.so;$prefix/deps/lib/lua/5.1/?.so;;./?.so;/usr/local/lib/lua/5.1/?.so;/usr/local/openresty/luajit/lib/lua/5.1/?.so;/usr/local/lib/lua/5.1/loadall.so;";

    lua_max_pending_timers 16384;
    lua_max_running_timers 4096;

    lua_shared_dict internal-status 10m;
    lua_shared_dict upstream-healthcheck 10m;
    lua_shared_dict worker-events 10m;
    lua_shared_dict lrucache-lock 10m;
    lua_shared_dict balancer-ewma 10m;
    lua_shared_dict balancer-ewma-locks 10m;
    lua_shared_dict balancer-ewma-last-touched-at 10m;
    lua_shared_dict etcd-cluster-health-check 10m; # etcd health check

    # for discovery shared dict



    lua_shared_dict plugin-ai-rate-limiting 10m;

    lua_shared_dict plugin-ai-rate-limiting-reset-header 10m;




    lua_shared_dict prometheus-metrics 10m;



    # for openid-connect and authz-keycloak plugin
    lua_shared_dict discovery 1m; # cache for discovery metadata documents

    # for openid-connect plugin
    lua_shared_dict jwks 1m; # cache for JWKs
    lua_shared_dict introspection 10m; # cache for JWT verification results






    # for custom shared dict


    lua_ssl_verify_depth 5;
    ssl_session_timeout 86400;

    underscores_in_headers on;

    lua_socket_log_errors off;

    resolver 127.0.0.11 ipv6=on;
    resolver_timeout 5;

    lua_http10_buffering off;

    lua_regex_match_limit 100000;
    lua_regex_cache_max_entries 8192;

    log_format main escape=default '$remote_addr - $remote_user [$time_local] $http_host "$request" $status $body_bytes_sent $request_time "$http_referer" "$http_user_agent" $upstream_addr $upstream_status $upstream_response_time "$upstream_scheme://$upstream_host$upstream_uri"';
    uninitialized_variable_warn off;

    access_log logs/access.log main buffer=16384 flush=3;
    open_file_cache  max=1000 inactive=60;
    client_max_body_size 0;
    keepalive_timeout 60s;
    client_header_timeout 60s;
    client_body_timeout 60s;
    send_timeout 10s;
    variables_hash_max_size 2048;

    server_tokens off;

    include mime.types;
    charset utf-8;

    real_ip_header X-Real-IP;

    real_ip_recursive off;

    set_real_ip_from 127.0.0.1;
    set_real_ip_from unix:;

    lua_ssl_trusted_certificate /etc/ssl/certs/ca-certificates.crt;
    # http configuration snippet starts
    
    # http configuration snippet ends

    upstream apisix_backend {
        server *******;

        keepalive 320;
        keepalive_requests 1000;
        keepalive_timeout 60s;
        # we put the static configuration above so that we can override it in the Lua code

        balancer_by_lua_block {
            apisix.http_balancer_phase()
        }
    }


    apisix_delay_client_max_body_check on;
    apisix_mirror_on_demand on;


    init_by_lua_block {
        require "resty.core"
        apisix = require("apisix")

        local dns_resolver = { "127.0.0.11", }
        local args = {
            dns_resolver = dns_resolver,
        }
        apisix.http_init(args)

        -- set apisix_lua_home into constants module
        -- it may be used by plugins to determine the work path of apisix
        local constants = require("apisix.constants")
        constants.apisix_lua_home = "/usr/local/apisix"
    }

    init_worker_by_lua_block {
        apisix.http_init_worker()
    }

    exit_worker_by_lua_block {
        apisix.http_exit_worker()
    }

    # the server block for lua-resty-events
    server {
        listen unix:/usr/local/apisix/logs/worker_events.sock;
        access_log off;
        location / {
            content_by_lua_block {
                require("resty.events.compat").run()
            }
        }
    }

    server {
        listen 127.0.0.1:9090;

        access_log off;

        location / {
            content_by_lua_block {
                apisix.http_control()
            }
        }
    }

    server {
            listen 127.0.0.1:9091 enable_process=privileged_agent;

        access_log off;

        location / {
            content_by_lua_block {
                local prometheus = require("apisix.plugins.prometheus.exporter")
                prometheus.export_metrics()
            }
        }

        location = /apisix/nginx_status {
            allow *********/24;
            deny all;
            stub_status;
        }
    }

    server {
        listen 0.0.0.0:9180;
        log_not_found off;

        # admin configuration snippet starts
        
        # admin configuration snippet ends

        set $upstream_scheme             'http';
        set $upstream_host               $http_host;
        set $upstream_uri                '';

        location /apisix/admin {
                allow *********/24;
                deny all;

            content_by_lua_block {
                apisix.http_admin()
            }
        }
    }



    server {
        http2 on;
        listen 0.0.0.0:9080 default_server reuseport;
        listen [::]:9080 default_server reuseport;
        listen 0.0.0.0:9443 ssl default_server reuseport;
        listen [::]:9443 ssl default_server reuseport;

        server_name _;

        ssl_certificate      cert/ssl_PLACE_HOLDER.crt;
        ssl_certificate_key  cert/ssl_PLACE_HOLDER.key;
        ssl_session_cache    shared:SSL:20m;
        ssl_session_timeout 10m;

        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers on;
        ssl_session_tickets off;

        proxy_ssl_trusted_certificate /etc/ssl/certs/ca-certificates.crt;

        # opentelemetry_set_ngx_var starts
        # opentelemetry_set_ngx_var ends

        # zipkin_set_ngx_var starts
        # zipkin_set_ngx_var ends

        # http server configuration snippet starts
        
        # http server configuration snippet ends

        location = /apisix/nginx_status {
            allow *********/24;
            deny all;
            access_log off;
            stub_status;
        }

        ssl_client_hello_by_lua_block {
            apisix.http_ssl_client_hello_phase()
        }

        ssl_certificate_by_lua_block {
            apisix.http_ssl_phase()
        }

        proxy_ssl_name $upstream_host;
        proxy_ssl_server_name on;

        location / {
            set $upstream_mirror_host        '';
            set $upstream_mirror_uri         '';
            set $upstream_upgrade            '';
            set $upstream_connection         '';

            set $upstream_scheme             'http';
            set $upstream_host               $http_host;
            set $upstream_uri                '';
            set $ctx_ref                     '';


            # http server location configuration snippet starts
            
            # http server location configuration snippet ends


            access_by_lua_block {
                apisix.http_access_phase()
            }

            proxy_http_version 1.1;
            proxy_set_header   Host              $upstream_host;
            proxy_set_header   Upgrade           $upstream_upgrade;
            proxy_set_header   Connection        $upstream_connection;
            proxy_set_header   X-Real-IP         $remote_addr;
            proxy_pass_header  Date;

            ### the following x-forwarded-* headers is to send to upstream server

            set $var_x_forwarded_proto      $scheme;
            set $var_x_forwarded_host       $host;
            set $var_x_forwarded_port       $server_port;

            proxy_set_header   X-Forwarded-For      $proxy_add_x_forwarded_for;
            proxy_set_header   X-Forwarded-Proto    $var_x_forwarded_proto;
            proxy_set_header   X-Forwarded-Host     $var_x_forwarded_host;
            proxy_set_header   X-Forwarded-Port     $var_x_forwarded_port;


            proxy_pass      $upstream_scheme://apisix_backend$upstream_uri;


            header_filter_by_lua_block {
                apisix.http_header_filter_phase()
            }

            body_filter_by_lua_block {
                apisix.http_body_filter_phase()
            }

            log_by_lua_block {
                apisix.http_log_phase()
            }
        }

        location @grpc_pass {

            access_by_lua_block {
                apisix.grpc_access_phase()
            }

            # For servers which obey the standard, when `:authority` is missing,
            # `host` will be used instead. When used with apisix-runtime, we can do
            # better by setting `:authority` directly
            grpc_set_header   ":authority" $upstream_host;
            grpc_set_header   Content-Type application/grpc;
            grpc_set_header   TE trailers;
            grpc_socket_keepalive on;
            grpc_pass         $upstream_scheme://apisix_backend;


            header_filter_by_lua_block {
                apisix.http_header_filter_phase()
            }

            body_filter_by_lua_block {
                apisix.http_body_filter_phase()
            }

            log_by_lua_block {
                apisix.http_log_phase()
            }
        }



    }

    # http end configuration snippet starts
    
    # http end configuration snippet ends
}
