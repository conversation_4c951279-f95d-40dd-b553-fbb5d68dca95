from uuid import UUID
from fastapi import Depends, APIRouter, Form, File, UploadFile
import app.sp_ratings.schemas as sc
import app.models as md
from sqlalchemy.orm import Session
# from fastapi.responses import JSONResponse
from app.database import get_db
from app.response_models import (
    StandardResponse,
    StandardResponseWithoutSerialize,
    ErrorResponse
)
from app.sp_ratings.db_query import (
    db_get_sp_rating,
    db_get_sp_rating_info
)
from app.auth import permission_checker
from fastapi.responses import JSONResponse

router = APIRouter(tags=["SP_Rating"])

@router.post("/sp-rating-create")
def create_sp_rating(payload: sc.SPRatings, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        new_data = md.SPRatings(
            rating_type=payload.rating_type,
            ref_id=payload.ref_id,
            rating_info=payload.rating_info
        )
        db.add(new_data)
        db.commit()
        return StandardResponse(status=True, status_code=200, message="service provider rating created sucessfully")
    except Exception as e:
        db.rollback()
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")

@router.put("/sp-rating-update/{spr_id}")
def update_sp_branche(spr_id: UUID, payload: sc.SPRatingsUpdate, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        spr_info = db_get_sp_rating_info(spr_id, db)
        if not spr_info:
            return StandardResponse(status=False, status_code=400, message="service provider rating id not found")
        if payload.rating_type:
            spr_info.rating_type = payload.rating_type
        if payload.ref_id:
            spr_info.ref_id = payload.ref_id
        if payload.rating_info:
            spr_info.rating_info = payload.rating_info
        db.commit()
        return StandardResponse(status=True, status_code=200, message="service provider rating details updated sucessfully")
    except Exception as e:
        db.rollback()
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")
    

@router.get("/get-sp-rating/{spr_id}")
def sp_branch(spr_id: UUID, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        spr_info = db_get_sp_rating_info(spr_id, db)
        if not spr_info:
            return StandardResponse(status=False, status_code=400, message="service provider rating id not found")
        res = {
            "spr_id": spr_info.id,
            "rating_type": spr_info.rating_type,
            "ref_id": spr_info.ref_id,
            "rating_info": spr_info.rating_info
        }
        return StandardResponse(status=True, status_code=200, data={"result": res}, message="service provider rating details fetched sucessfully")
    except Exception as e:
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")
    

@router.get("/get-sp-ratings-list")
def ratings(page_no:int, page_size:int, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        spr_infos= db_get_sp_rating(db)
        if not spr_infos:
            return StandardResponse(status=False, status_code=400, message="service provider ratings not found")
        total_rec = len(spr_infos)
        skip = (page_no - 1) * page_size
        limit = page_no * page_size
        result = [
            {
                "spr_id": spr_info.id,
                "rating_type": spr_info.rating_type,
                "ref_id": spr_info.ref_id,
                "rating_info": spr_info.rating_info
            }
            for spr_info in spr_infos[skip:limit]
        ]
        return StandardResponse(status=True, status_code=200, data={"result": result, "page_no": page_no, "page_size": page_size, "total_records": total_rec}, message="service provider rating details fetched sucessfully")
    except Exception as e:
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")
    
@router.delete("/delete-sp_rating/{spr_id}")
def delete_spr(spr_id: UUID, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        spr_info = db_get_sp_rating_info(spr_id, db)
        if not spr_info:
            return StandardResponse(status=False, status_code=400, message="service provider rating id not found")
        db.delete(spr_info)
        db.commit()
        return StandardResponse(status=True, status_code=200, message="service provider branch deleted sucessfully")
    except Exception as e:
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")