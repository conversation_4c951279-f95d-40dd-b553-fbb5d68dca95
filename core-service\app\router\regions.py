from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import IntegrityError
from uuid import UUID
from typing import List

from app.database import get_db
from app.models import Region
from app.schemas import RegionCreate, RegionUpdate, RegionOut
from app.helper import StandardResponse, ErrorResponse
from app.utils import create_record, update_record, delete_record

router = APIRouter(tags=["Regions"])


@router.post("/region", response_model=StandardResponse)
async def create_region(payload: RegionCreate, db: AsyncSession = Depends(get_db)):
    """Create a new region"""
    try:
        # Check for duplicate region name
        existing = await db.execute(select(Region).where(Region.name == payload.name))
        if existing.scalars().first():
            return ErrorResponse(status_code=400, message="Region with this name already exists")

        # Check for duplicate short_name
        existing_short = await db.execute(select(Region).where(Region.short_name == payload.short_name))
        if existing_short.scalars().first():
            return ErrorResponse(status_code=400, message="Region with this short name already exists")

        # Create new region
        new_region = await create_record(db, Region, payload.dict())
        
        if isinstance(new_region, str):  # Error occurred
            return ErrorResponse(status_code=500, message=new_region)

        data = RegionOut.model_validate(new_region, from_attributes=True)
        return StandardResponse(
            status_code=201,
            data=data,
            message="Region created successfully"
        )
    except IntegrityError as e:
        await db.rollback()
        if "regions_name_key" in str(e):
            return ErrorResponse(status_code=400, message="Region name must be unique")
        return ErrorResponse(status_code=400, message="Integrity constraint violated")
    except Exception as e:
        return ErrorResponse(status_code=500, message="creating region failed", error=str(e))


@router.get("/region", response_model=StandardResponse)
async def get_all_regions(db: AsyncSession = Depends(get_db)):
    """Get all regions"""
    try:
        result = await db.execute(select(Region).where(Region.is_active == True))
        regions = result.scalars().all()

        data = [RegionOut.model_validate(region, from_attributes=True) for region in regions]
        return StandardResponse(
            status_code=200, 
            data=data, 
            message="Regions retrieved successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="getting regions failed", error=str(e))


@router.get("/region/{region_id}", response_model=StandardResponse)
async def get_region_by_id(region_id: UUID, db: AsyncSession = Depends(get_db)):
    """Get a specific region by ID"""
    try:
        result = await db.execute(
            select(Region).where(Region.id == region_id, Region.is_active == True)
        )
        region = result.scalars().first()

        if not region:
            return ErrorResponse(status_code=404, message="Region not found")

        data = RegionOut.model_validate(region, from_attributes=True)
        return StandardResponse(
            status_code=200,
            data=data,
            message="Region retrieved successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="getting region by id failed", error=str(e))


@router.put("/region/{region_id}", response_model=StandardResponse)
async def update_region(region_id: UUID, payload: RegionUpdate, db: AsyncSession = Depends(get_db)):
    """Update a region"""
    try:
        # Check if region exists
        result = await db.execute(
            select(Region).where(Region.id == region_id, Region.is_active == True)
        )
        region = result.scalars().first()

        if not region:
            return ErrorResponse(status_code=404, message="Region not found")

        # Check for duplicate name if updating name
        if payload.name and payload.name != region.name:
            existing = await db.execute(
                select(Region).where(Region.name == payload.name, Region.id != region_id)
            )
            if existing.scalars().first():
                return ErrorResponse(status_code=400, message="Region with this name already exists")

        # Check for duplicate short_name if updating short_name
        if payload.short_name and payload.short_name != region.short_name:
            existing_short = await db.execute(
                select(Region).where(Region.short_name == payload.short_name, Region.id != region_id)
            )
            if existing_short.scalars().first():
                return ErrorResponse(status_code=400, message="Region with this short name already exists")

        # Update the region with provided fields
        update_data = payload.dict(exclude_unset=True)
        if update_data:
            updated_region = await update_record(db, Region, region_id, update_data)
            data = RegionOut.model_validate(updated_region, from_attributes=True)
            return StandardResponse(
                status_code=200,
                data=data,
                message="Region updated successfully"
            )
        else:
            return ErrorResponse(status_code=400, message="No fields provided to update")

    except IntegrityError as e:
        await db.rollback()
        if "regions_name_key" in str(e):
            return ErrorResponse(status_code=400, message="Region name must be unique")
        return ErrorResponse(status_code=400, message="Integrity constraint violated")
    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message="updating region failed", error=str(e))


@router.delete("/region/{region_id}", response_model=StandardResponse)
async def delete_region(region_id: UUID, db: AsyncSession = Depends(get_db)):

    try:
        result = await db.execute(
            select(Region).where(Region.id == region_id, Region.is_active == True)
        )
        region = result.scalars().first()

        if not region:
            return ErrorResponse(status_code=404, message="Region not found")

        await delete_record(db, Region, region_id)

        return StandardResponse(
            status_code=200,
            message="Region deleted successfully"
        )
    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message="deleting region failed", error=str(e))