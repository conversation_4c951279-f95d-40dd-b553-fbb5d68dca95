from uuid import UUID
from pydantic import BaseModel, Field
from typing import Optional

class ServiceProvidersBranch(BaseModel):
    sp_id : UUID
    branch_location: str
    address: str
    phone: str
    is_main: bool
    Region :str
    

class BranchUpdate(BaseModel):
    # sp_id : Optional[int]
    branch_location: Optional[str]
    address: Optional[str]
    phone: Optional[str]
    is_main: Optional[bool]