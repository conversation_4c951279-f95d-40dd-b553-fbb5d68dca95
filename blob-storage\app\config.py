import logging
import os
from functools import lru_cache
from pydantic_settings import BaseSettings

log = logging.getLogger("uvicorn")


class Settings(BaseSettings):
    """Class for storing settings."""

    S3_ACCESS_KEY_ID: str = os.getenv("S3_ACCESS_KEY_ID")
    S3_SECRET_ACCESS_KEY: str = os.getenv("S3_SECRET_ACCESS_KEY")
    S3_REGION: str = os.getenv("S3_REGION")
    S3_IMAGES_FOLDER: str = os.getenv("S3_IMAGES_FOLDER")
    S3_IMAGES_FOLDER: str = os.getenv("S3_IMAGES_FOLDER")
    S3_DOCS_FOLDER: str = os.getenv("S3_DOCS_FOLDER")
    S3_BUCKET: str = os.getenv("S3_BUCKET")


@lru_cache()
def get_settings() -> BaseSettings:
    """Get application settings usually stored as environment variables.

    Returns:
        Settings: Application settings.
    """
    log.info("Loading config settings from the environment...")
    return Settings()
