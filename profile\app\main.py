from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import os
import logging
import asyncio
from app.database import create_tables
from app.routers import users, service_provider, device, admin, signup
from app.bulk_update_groups import router as bulk_update_router
# Commented out Kafka imports since core-service handles this
# from app.kafka_producer.config import kafka_producer_config
# from app.kafka_consumer.consumer import cognito_consumer,db_consumer, consume_cognito_consumer, consume_db_consumer, delete_consumer, consume_delete_consumer
# from app.event import shutdown_event, startup_event
from app.audit_log import log_requests

log = logging.getLogger("uvicorn")


# @asynccontextmanager
# async def lifespan(app: FastAPI):
#     await create_tables()
#     await startup_event()
#     # autogenerate_and_upgrade()
#     yield
#     await shutdown_event()

# app = FastAPI(lifespan=lifespan,debug=True)
app = FastAPI(debug=True)

app.middleware("http")(log_requests)


# List of allowed origins (the frontend URLs that are allowed to access the backend)
allowed_orgins = [os.getenv("LOCALHOST_URL"), os.getenv("WEBAPP_URL"), os.getenv("UAT_WEBAPP_URL")]
origins = allowed_orgins

# Add CORSMiddleware to the application
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # Allows specified origins
    allow_credentials=True,  # Allows cookies to be included in requests
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)


@app.get("/__health")
def read_root():
    return {"status": "online"}


app.include_router(users.router)
app.include_router(service_provider.router)
app.include_router(admin.router)
app.include_router(device.router)
app.include_router(
    bulk_update_router,
    prefix="/admin",
    tags=["admin"]
)
app.include_router(signup.router)
# app.include_router(business.router)


@app.on_event("startup")
async def startup_event():
    await create_tables()
    log.info("Profile service started without Kafka consumers (handled by core-service)")
    # Commented out Kafka startup since core-service handles bulk upload processing
    # """Start up event for FastAPI application."""
    # log.info("Starting up...")
    # kafka_conn = False
    # while not kafka_conn: 
    #     try:
    #         await kafka_producer_config.start()
    #         await cognito_consumer.start()
    #         await db_consumer.start()
    #         await delete_consumer.start()
    #         kafka_conn = True
    #     except:
    #         pass
    # log.info("Kafka Started...")
    # asyncio.create_task(consume_cognito_consumer())
    # asyncio.create_task(consume_db_consumer())
    # asyncio.create_task(consume_delete_consumer())

@app.on_event("shutdown")
async def shutdown_event():
    log.info("Profile service shutting down...")
    # Commented out Kafka shutdown since core-service handles this
    # """Shutdown event for FastAPI application."""
    # log.info("Shutting down...")
    # await cognito_consumer.stop()
    # await db_consumer.stop()
    # await delete_consumer.stop()
    # await kafka_producer_config.stop()
