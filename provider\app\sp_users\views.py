from uuid import UUID
from app.models import KycDetails, SPUsers, Roles, Permissions, Region, SPBranches, Services
from app.database import get_db
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.future import select
from sqlalchemy import func
from app.sp_users.schemas import SPManagerCreateForm, RolesEnum, SPManagerUpdateForm
from fastapi import Depends, APIRouter, Query
from app.helper import StandardResponse, ErrorResponse
from app.auth import permission_checker
from app.image_validator import validate_image
from app.s3_upload import upload_file_direct
from app.cognito_utils import(
    create_cognito_user
)
from fastapi.responses import JSONResponse
from app.models_enum import KYCTypes, UserStatusType, ValidationStatus


router = APIRouter(tags=["SP_Users"])

@router.post("/business/users-create")
def create_sp_users(
    payload: SPManagerCreateForm = Depends(),
    db: Session = Depends(get_db),
    # user=Depends(permission_checker),
):
    try:
        # if isinstance(user, JSONResponse):
        #     return user

        govtid = payload.govtid
        certification = payload.certification
        # if payload.role_name != RolesEnum.BUSINESS_MANAGER:
        #     return ErrorResponse(status_code=403, message="Permission denied, you can create Business Managers only")

        # Check phone number
        phone_check = db.execute(
            select(SPUsers).where(SPUsers.phone_number == payload.phone_number)
        )
        if phone_check.scalars().first():
            return ErrorResponse(status_code=400, message="User already exists with this phone number")

        # Check email
        email_check = db.execute(
            select(SPUsers).where(SPUsers.email == payload.email)
        )
        if email_check.scalars().first():
            return ErrorResponse(status_code=400, message="User already exists with this email")

        # Check role
        role_query = db.execute(select(Roles).where(Roles.role_name == payload.role_name))
        role = role_query.scalars().first()
        print(role, 'roleeeeeeeeeeeeeeee')
        if not role:
            return ErrorResponse(status_code=404, message=f"Role '{payload.role_name}' not found")
        # Check branch
        branch = db.execute(select(SPBranches).where(SPBranches.id == payload.sp_branch_id))
        branch_obj = branch.scalars().first()
        if not branch_obj:
            return ErrorResponse(status_code=400, message=f"Branch with id {payload.sp_branch_id} does not exist")

        # Check service
        if payload.service_id:
            service = db.execute(select(Services).where(Services.id == payload.service_id))
            service_obj = service.scalars().first()
            if not service_obj:
                return ErrorResponse(status_code=400, message=f"Service with id {payload.service_id} does not exist")
            
        # Check region
        region = db.execute(select(Region).where(Region.id == payload.region_id))
        region_obj = region.scalars().first()
        if not region_obj:
            return ErrorResponse(status_code=400, message=f"Region with id {payload.region_id} does not exist")

        # Create Cognito user
        # auth_id = create_cognito_user(email=payload.email, phone_number=payload.country_code + payload.phone_number)
        auth_id = "97c4eb71-0dff-4bf9-9de5-4990067e55f2"
        if not auth_id:
            return ErrorResponse(
                status_code=500,
                message="Failed to create Cognito user",
                error="Cognito returned no sub ID"
            )
        
        # Handle profile pic upload (example: just storing filename)
        image_url = None
        if payload.profile_pic:
            validate_image(payload.profile_pic)
            image_url = upload_file_direct(payload.profile_pic, path="JC_SP_USERS_PROFILE")
            image_url = image_url.get('filename')

        # Create SP user in DB
        new_user = SPUsers(
            email=payload.email,
            country_code=payload.country_code,
            phone_number=payload.phone_number,
            auth_id=auth_id,
            first_name=payload.first_name,
            last_name=payload.last_name,
            region_id=payload.region_id,
            role_id=role.id,
            SP_ID=branch_obj.SP_ID,
            SPB_ID=payload.sp_branch_id,
            profile_image_url=image_url,
            service_id = payload.service_id
        )

        db.add(new_user)
        db.commit()
        db.refresh(new_user)

        user_id = new_user.id

        # kyc upload for personals
        kyc_files = {
                KYCTypes.CERTIFICATE: certification,
                KYCTypes.GOVT_ID: govtid,
        }
        for kyc_type, file in kyc_files.items():
            if file:
                upload_result = upload_file_direct(file, path=f"users/kyc/{kyc_type.value}")
                file_key = upload_result.get("filename")

                if not file_key:
                    return ErrorResponse(status_code=400, message=f"Failed to upload {kyc_type.value} document")

                # file_url = BASE_BLOB_ACCESS_URL + file_key

                existing_kyc = db.query(KycDetails).filter(
                    KycDetails.user_id == user_id,
                    KycDetails.KYC_type == kyc_type
                ).first()

                if existing_kyc:
                    existing_kyc.document = file_key
                    existing_kyc.validation_status = ValidationStatus.PENDING
                else:
                    new_kyc = KycDetails(
                        user_id=user_id,
                        KYC_type=kyc_type,
                        document=file_key,
                        validation_status=ValidationStatus.PENDING
                    )
                    db.add(new_kyc)

        # Save permissions if provided
        if payload.permissions:
            for policy_name, actions in payload.permissions.dict(exclude_none=True).items():
                user_permission = Permissions(
                    user_id=user_id,
                    policy_name=policy_name,
                    permission=actions,
                    role_id=None,
                )
                db.add(user_permission)

            db.commit()

        return StandardResponse(
            status_code=201,
            data={"user_id": str(new_user.id)},
            message="User created successfully",
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            error=str(e),
            message="Exception occurred. Unable to process your request, please try again",
        )

@router.put("/business/users-update/{id}")
def update_sp_manager(
    id: UUID,
    payload: SPManagerUpdateForm = Depends(),
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    try:
        # Get user
        sp_user = db.execute(select(SPUsers).where(SPUsers.id == id)).scalars().first()
        if not sp_user:
            return ErrorResponse(status_code=404, message=f"User with id {id} not found")

        # Update basic info
        if payload.first_name:
            sp_user.first_name = payload.first_name
        if payload.last_name:
            sp_user.last_name = payload.last_name
        if payload.status:
            sp_user.status = payload.status
        # if payload.email:
        #     # Check email conflict
        #     email_check = db.execute(
        #         select(SPUsers).where(SPUsers.id != payload.user_id)
        #     ).scalars().first()
        #     if email_check:
        #         return ErrorResponse(status_code=400, message="Another user already exists with this email")
        #     sp_user.email = payload.email
        # if payload.phone_number:
        #     phone_check = db.execute(
        #         select(SPUsers).where(SPUsers.phone_number == payload.phone_number, SPUsers.id != payload.user_id)
        #     ).scalars().first()
        #     if phone_check:
        #         return ErrorResponse(status_code=400, message="Another user already exists with this phone number")
        #     sp_user.phone_number = payload.phone_number
        #     if payload.country_code:
        #         sp_user.country_code = payload.country_code

        # Branch validation
        if payload.sp_branch_id:
            branch = db.execute(select(SPBranches).where(SPBranches.id == payload.sp_branch_id)).scalars().first()
            if not branch:
                return ErrorResponse(status_code=400, message=f"Branch {payload.sp_branch_id} does not exist")
            sp_user.SPB_ID = payload.sp_branch_id

        # Region validation
        if payload.region_id:
            region = db.execute(select(Region).where(Region.id == payload.region_id)).scalars().first()
            if not region:
                return ErrorResponse(status_code=400, message=f"Region {payload.region_id} does not exist")
            sp_user.region_id = payload.region_id

        # Profile pic update
        if payload.profile_pic:
            validate_image(payload.profile_pic)
            image_url = upload_file_direct(payload.profile_pic, path="JC_SP_USERS_PROFILE")
            sp_user.profile_image_url = image_url.get('filename')

        # Permissions update (replace old with new if passed)
        if payload.permissions:
            db.query(Permissions).filter(Permissions.user_id == payload.user_id).delete()
            for policy_name, actions in payload.permissions.dict(exclude_none=True).items():
                user_permission = Permissions(
                    user_id=payload.user_id,
                    policy_name=policy_name,
                    permission=actions,
                    role_id=None,
                )
                db.add(user_permission)

        db.commit()
        db.refresh(sp_user)

        return StandardResponse(
            status_code=200,
            data={"user_id": str(sp_user.id)},
            message="Service provider manager updated successfully",
        )

    except Exception as e:
        db.rollback()
        return ErrorResponse(
            status_code=500,
            error=str(e),
            message="Exception occurred. Unable to process your request, please try again",
        )

@router.get("/business/users-list")
def list_sp_users(
    limit: int = Query(10, ge=1),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
):
    try:
        # Count total records
        total_query = db.execute(select(func.count(SPUsers.id)))
        total_records = total_query.scalar()

        # Fetch paginated records
        query = (
            db.execute(
                select(
                    SPUsers.id,
                    SPUsers.first_name,
                    SPUsers.last_name,
                    SPUsers.email,
                    SPUsers.phone_number,
                    SPUsers.country_code,
                    SPUsers.status,
                )
                .offset(offset)
                .limit(limit)
            )
        )
        users = query.all()

        data = [
            {
                "id": user.id,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "email": user.email,
                "phone_number": user.phone_number,
                "country_code": user.country_code,
                "status": user.status,
                
            }
            for user in users
        ]

        return StandardResponse(
            status_code=200,
            message="SP Users fetched successfully",
            data={
                "total_records": total_records,
                "limit": limit,
                "offset": offset,
                "users": data,
            },
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Failed to fetch SP Users",
            error=str(e),
        )

@router.get("/business/users-details/{id}", response_model=StandardResponse)
def get_sp_user_details(id: UUID, db: Session = Depends(get_db)):
    try:
        # Fetch user
        user = db.execute(
            select(SPUsers)
            .where(SPUsers.id == id)
        ).scalars().first()

        if not user:
            return ErrorResponse(status_code=404, message=f"SP User with id {id} not found")

        # Fetch role
        role = db.execute(
            select(Roles).where(Roles.id == user.role_id)
        ).scalars().first()

        # Fetch branch
        branch = db.execute(
            select(SPBranches).where(SPBranches.id == user.SPB_ID)
        ).scalars().first()

        # Fetch region
        region = db.execute(
            select(Region).where(Region.id == user.region_id)
        ).scalars().first()

        # Fetch service
        service = db.execute(
            select(Services).where(Services.id == user.service_id)
        ).scalars().first()

        # Fetch permissions
        permissions = db.execute(
            select(Permissions).where(Permissions.user_id == user.id)
        ).scalars().all()

        # Fetch KYC details
        kyc_details = db.execute(
            select(KycDetails).where(KycDetails.user_id == user.id)
        ).scalars().all()

        response_data = {
            "id": str(user.id),
            "first_name": user.first_name,
            "last_name": user.last_name,
            "email": user.email,
            "phone_number": user.phone_number,
            "status": user.status,
            "created_at": user.created_at,
            "profile_image_url": user.profile_image_url,
            "role": {
                "id": str(role.id) if role else None,
                "name": role.role_name if role else None,
            },
            "branch": {
                "id": str(branch.id) if branch else None,
                "name": branch.Branch_Location if branch else None,
            },
            "region": {
                "id": str(region.id) if region else None,
                "name": region.name if region else None,
            },
            "service": {
                "id": str(service.id) if service else None,
                "name": service.name if service else None,
            },
            "permissions": [
                {
                    "id": str(p.id),
                    "policy_name": p.policy_name,
                    "permission": p.permission
                } for p in permissions
            ],
            "kyc_details": [
                {
                    "id": str(k.id),
                    "kyc_type": k.KYC_type,
                    "document": k.document,
                    "validation_status": k.validation_status,
                    "validation_comments": k.validation_comments,
                    "created_at": k.created_at
                } for k in kyc_details
            ]
        }

        return StandardResponse(
            status_code=200,
            message="SP User details fetched successfully",
            data=response_data
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message="Failed to fetch SP User details", error=str(e))
