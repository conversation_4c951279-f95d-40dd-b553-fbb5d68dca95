from enum import Enum


class BookingStatus(str, Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed" #when artisan accepts booking
    STARTED = "started"
    ARRIVED = "arrived" #when artisan arrives at user location
    ACCEPTED = "accepted" #when user accepts booking
    ONGOING = "ongoing" #when user starts service
    ENDED = "ended" #when artisan ends service
    COMPLETED = "completed" #when user pays for service, should be update in webhook
    CANCELLED = "cancelled"
    REJECTED = "rejected"

class PaymentType(str, Enum):
    CASH = "CASH"
    WALLET = "WALLET"
    CARD = "CARD"

class UserType(str, Enum):
    ARTISAN = "ARTISAN"
    USER = "USER"
    AGENT = "AGENT"

class Gender(str, Enum):
    MALE = "male"
    FEMALE = "female"


class ServiceProviderStatusType(str, Enum):
    CREATED = "created"
    APPROVED = "approved"
    REJECTED = "rejected"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"


class UserStatusType(str, Enum):
    APPROVED = "approved"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"


class DeviceType(str, Enum):
    ANDROID = "android"
    IOS = "ios"

class BookingCancellationStatus(str, Enum):
    INITIATED = "initiated"
    PENDING = "pending"     # Cancellation accepted by agent
    # APPROVED = "approved"
    COMPLETED = "completed" # Once penalty is applied or agent approves the cancellation
    

class ExperienceRange(str, Enum):
    LESS_THAN_5 = "less_than_5"
    MORE_THAN_5 = "more_than_5"
    MORE_THAN_10 = "more_than_10"
