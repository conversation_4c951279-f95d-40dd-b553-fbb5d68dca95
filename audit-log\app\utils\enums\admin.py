from enum import Enum


class Gender(str, Enum):
    MALE = "male"
    FEMALE = "female"


class AdminRole(str, Enum):
    ADMIN = "admin"
    SUPERADMIN = "superadmin"
    AGENT = "agent"


class ServiceProviderStatusType(str, Enum):
    CREATED = "created"
    APPROVED = "approved"
    REJECTED = "rejected"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"


class UserStatusType(str, Enum):
    APPROVED = "approved"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"