import requests
from app.config import get_settings

s3_upload_url = get_settings().BE_BLOB_API_URL
S3_IMAGES_FOLDER = get_settings().S3_IMAGES_FOLDER
S3_DOCS_FOLDER = get_settings().S3_DOCS_FOLDER

def upload_file_direct(file, path):
    try:
        files = {"file": (file.filename, file.file, file.content_type)}
        data = {'path': path}
        response = requests.post(s3_upload_url+'/upload-file-direct', files=files, data=data)
        return response.json()
    except Exception as e:
        return {"message": f"Error: {e}"}
    

def s3_delete_file(file_name):
    data = {
        "file_path": file_name,
    }
    response = requests.delete(s3_upload_url+'/delete-file', json=data)
    return response.json()