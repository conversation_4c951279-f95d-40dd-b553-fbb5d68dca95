from pydantic import BaseModel, UUID4, Field
from typing import Optional, Any
from datetime import datetime
from fastapi import Form
from app.utils import StatusType, PaymentMethodType, CurrencyType
# -------------------------------------------
# CreatePayment schema
# -------------------------------------------
class CreatePayment(BaseModel):
    """
    Schema for creating a Payment using multipart/form-data.
    Similar approach to CreateCategory / CreateServices, etc.
    """
    user_id: UUID4
    artisan_id: UUID4
    service_request_id: UUID4
    payment_method: PaymentMethodType = "CASH" # e.g. "CARD", "WALLET", or "CASH"
    base_service_fee: float
    surcharges: float
    currency: Optional[CurrencyType] = "USD"       # e.g. "GHS" or "EUR"
    # payment_date: Optional[datetime] = datetime.now()
    # status: Optional[StatusType]      # e.g. "INIT", "SUCCESS", "FAILED"


# -------------------------------------------
# CreateTransaction schema
# -------------------------------------------
class CreateTransaction(BaseModel):
    """
    Schema for creating a Transaction using multipart/form-data.
    Similar to CreatePayment or CreateAccount.
    """
    payment_id: UUID4
    from_entity: str
    to_entity: str
    transaction_type: str  # e.g. "PAYMENT", "PAYOUT"
    status: str          # e.g. "INIT", "SUCCESS", "FAILED"
    amount: float

# -------------------------------------------
# TransactionResponse schema
# -------------------------------------------
class TransactionResponse(BaseModel):
    """
    Pydantic model for returning Transaction details in a JSON response.
    Typically used in 'GET' operations.
    """
    id: UUID4
    payment_id: UUID4
    from_entity: str
    to_entity: str
    transaction_type: str  # e.g. "PAYMENT", "PAYOUT"
    status: str            # e.g. "INIT", "SUCCESS", "FAILED"
    amount: float



# -------------------------------------------
# UpdateTransaction schema
# -------------------------------------------
class UpdateTransaction(BaseModel):
    """
    Schema for partially updating an existing Transaction record.
    Fields are optional (None) so we can selectively update them.
    """
    payment_id: Optional[UUID4] = None
    from_entity: Optional[str] = None
    to_entity: Optional[str] = None
    transaction_type: Optional[str] = None  # e.g. "PAYMENT", "PAYOUT"
    status: Optional[str] = None            # e.g. "INIT", "SUCCESS", "FAILED"
    amount: Optional[float] = None

# -------------------------------------------
# PaymentResponse schema
# -------------------------------------------
class PaymentResponse(BaseModel):
    """
    Pydantic model for returning payment details in a JSON response.
    Typically used in 'GET' operations.
    """
    id: UUID4
    user_id: UUID4
    artisan_id: UUID4
    service_request_id: UUID4
    payment_method: str
    base_service_fee: float
    surcharges: float
    currency: str
    status: str
    payment_date: Optional[datetime]



# -------------------------------------------
# UpdatePayment schema
# -------------------------------------------
class UpdatePayment(BaseModel):
    """
    Schema for updating Payment data via multipart/form-data.
    Fields are optional (None) so we can selectively update them.
    """
    user_id: Optional[UUID4] = None
    artisan_id: Optional[UUID4] = None
    service_request_id: Optional[UUID4] = None
    payment_method: Optional[str] = None
    base_service_fee: Optional[float] = None
    surcharges: Optional[float] = None
    currency: Optional[str] = None
    status: Optional[str] = None
    payment_date: Optional[datetime] = None

# -------------------------------------------
# CreateAccount schema
# -------------------------------------------
class CreateAccount(BaseModel):
    """
    Schema for creating an Account record via multipart/form-data (Form).
    Similar pattern to your CreateCategory or CreatePayment classes.
    """
    user_id: UUID4
    balance: float
    currency: str
    account_details: Optional[Any] = None
    status: str
    account_type: str

# -------------------------------------------
# AccountResponse schema
# -------------------------------------------
class AccountResponse(BaseModel):
    """
    Pydantic model for returning an Account record as JSON.
    Used in GET endpoints or response models.
    """
    id: UUID4
    user_id: UUID4
    balance: float
    currency: str
    account_details: Optional[Any] = None  # Can be dict or string
    status: str
    account_type: str



# -------------------------------------------
# UpdateAccount schema
# -------------------------------------------
class UpdateAccount(BaseModel):
    """
    Schema for partially updating an existing Account record.
    All fields are optional to support partial updates.
    """
    user_id: Optional[UUID4] = None
    balance: Optional[float] = None
    currency: Optional[str] = None
    account_details: Optional[Any] = None
    status: Optional[str] = None
    account_type: Optional[str] = None

# -------------------------------------------
# UpdateTransactionStatus schema
# -------------------------------------------
class UpdateTransactionStatus(BaseModel):
    """
    Schema for updating transaction status, gateway status, and gateway response.
    All fields are optional to support partial updates.
    """
    payment_id: str
    gateway_status: Optional[str] = None
    status: Optional[StatusType] = None
    gateway_response: Optional[dict] = None


class RequestPayment(BaseModel):
    amount: Optional[float] = None
    payment_mode: Optional[str] = "wallet"

class RefundPayment(BaseModel):
    amount: Optional[float] = None
    user_id: Optional[str] = None
    payment_method: Optional[str] = 'wallet'    # bank or wallet
    p_order_id: Optional[str] = None
    payment_id: Optional[str] = None
    cancellation_id: Optional[str] = None


# Update account details schema
class UpdateContactInfoRequest(BaseModel):
    account_id: UUID4
    email: Optional[str] = Field(None, description="New email address")
    mobile: Optional[str] = Field(None, description="New mobile number")

class UpdateContactInfoResponse(BaseModel):
    account_id: UUID4
    email: Optional[str]
    mobile: Optional[str]
