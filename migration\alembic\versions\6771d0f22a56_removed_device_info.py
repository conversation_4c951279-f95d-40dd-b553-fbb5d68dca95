"""removed device info

Revision ID: 6771d0f22a56
Revises: 642b79805e43
Create Date: 2025-06-05 16:51:43.226819

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '6771d0f22a56'
down_revision: Union[str, None] = '642b79805e43'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'accounts', ['id'])
    op.create_unique_constraint(None, 'admin', ['id'])
    op.create_unique_constraint(None, 'booking_cancellation', ['id'])
    op.create_unique_constraint(None, 'booking_cancellation_history', ['id'])
    op.create_unique_constraint(None, 'booking_cancellation_reasons', ['id'])
    op.create_unique_constraint(None, 'booking_history', ['id'])
    op.create_unique_constraint(None, 'bookings', ['id'])
    op.create_unique_constraint(None, 'category', ['id'])
    op.create_unique_constraint(None, 'category_issues', ['id'])
    op.create_unique_constraint(None, 'device', ['id'])
    op.drop_column('device', 'info')
    op.create_unique_constraint(None, 'dispute_comments', ['id'])
    op.create_unique_constraint(None, 'dispute_event_types', ['id'])
    op.create_unique_constraint(None, 'dispute_history', ['id'])
    op.create_unique_constraint(None, 'disputes', ['id'])
    op.create_unique_constraint(None, 'issue_categories', ['id'])
    op.create_unique_constraint(None, 'issue_history', ['id'])
    op.create_unique_constraint(None, 'issue_types', ['id'])
    op.create_unique_constraint(None, 'issues', ['id'])
    op.create_unique_constraint(None, 'payments', ['id'])
    op.create_unique_constraint(None, 'service_provider', ['id'])
    op.create_unique_constraint(None, 'service_provider_leave', ['id'])
    op.create_unique_constraint(None, 'service_provider_service_mapping', ['id'])
    op.create_unique_constraint(None, 'services', ['id'])
    op.create_unique_constraint(None, 'subcategories_issues', ['id'])
    op.create_unique_constraint(None, 'transactions', ['id'])
    op.create_unique_constraint(None, 'users', ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='unique')
    op.drop_constraint(None, 'transactions', type_='unique')
    op.drop_constraint(None, 'subcategories_issues', type_='unique')
    op.drop_constraint(None, 'services', type_='unique')
    op.drop_constraint(None, 'service_provider_service_mapping', type_='unique')
    op.drop_constraint(None, 'service_provider_leave', type_='unique')
    op.drop_constraint(None, 'service_provider', type_='unique')
    op.drop_constraint(None, 'payments', type_='unique')
    op.drop_constraint(None, 'issues', type_='unique')
    op.drop_constraint(None, 'issue_types', type_='unique')
    op.drop_constraint(None, 'issue_history', type_='unique')
    op.drop_constraint(None, 'issue_categories', type_='unique')
    op.drop_constraint(None, 'disputes', type_='unique')
    op.drop_constraint(None, 'dispute_history', type_='unique')
    op.drop_constraint(None, 'dispute_event_types', type_='unique')
    op.drop_constraint(None, 'dispute_comments', type_='unique')
    op.add_column('device', sa.Column('info', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'device', type_='unique')
    op.drop_constraint(None, 'category_issues', type_='unique')
    op.drop_constraint(None, 'category', type_='unique')
    op.drop_constraint(None, 'bookings', type_='unique')
    op.drop_constraint(None, 'booking_history', type_='unique')
    op.drop_constraint(None, 'booking_cancellation_reasons', type_='unique')
    op.drop_constraint(None, 'booking_cancellation_history', type_='unique')
    op.drop_constraint(None, 'booking_cancellation', type_='unique')
    op.drop_constraint(None, 'admin', type_='unique')
    op.drop_constraint(None, 'accounts', type_='unique')
    # ### end Alembic commands ###
