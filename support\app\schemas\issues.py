from pydantic import BaseModel, EmailStr, <PERSON><PERSON>, UUID4, Past<PERSON><PERSON>, <PERSON>, validator
from datetime import datetime, date, time
from typing import Optional, Union, List
from fastapi import Form
from pydantic.types import conint
# from app.utils import CognitoRole, DeviceType, Gender, ServiceProviderStatusType , AgentStatusType
import json
# from app.enum import NotificationType
from uuid import UUID
from app.utils.enums import IssuePriority, IssueStatus, UserType



# Category and Subcategory Schemas
class SubCategoryCreate(BaseModel):
    name: str
    description: Optional[str] = None


class IssueCategoryCreate(BaseModel):
    name: str
    code: str
    description: Optional[str] = None
    default_priority: IssuePriority
    subcategories: Optional[List[SubCategoryCreate]] = None


class SubCategoryResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class IssueCategoryResponse(BaseModel):
    id: str
    name: str
    code: str
    description: Optional[str]
    default_priority: IssuePriority
    created_at: datetime
    updated_at: Optional[datetime]
    subcategories: List[SubCategoryResponse]

    class Config:
        from_attributes = True

class IssueCategoryUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    default_priority: Optional[IssuePriority] = None
    subcategories: Optional[List[SubCategoryCreate]] = None


class SubCategoryUpdate(BaseModel):
    category_id: Optional[UUID] = None
    name: Optional[str] = None
    description: Optional[str] = None

class IssueCreateForm:
    def __init__(
        self,
        customer_id: Optional[str] = Form(None),
        # customer_name: Optional[str] = Form(None),
        category_id: str = Form(...),
        subcategory_id: Optional[str] = Form(None),
        description: str = Form(...),
        assigned_to: Optional[str] = Form(None),
        status: str = Form("OPEN"),
        priority: Optional[str] = Form(None),
        user_type: str = Form(...),
        resolution_notes: Optional[str] = Form(None),
        resolution_date: Optional[datetime] = Form(None)
    ):
        self.customer_id = customer_id
        # self.customer_name = customer_name
        self.category_id = category_id
        self.subcategory_id = subcategory_id
        self.description = description
        self.assigned_to = assigned_to
        self.status = status
        self.priority = priority
        self.user_type = user_type
        self.resolution_notes = resolution_notes
        self.resolution_date = resolution_date

class IssueUpdate(BaseModel):
    description: Optional[str] = Field(None, description="Description of the issue")
    assigned_to: Optional[UUID] = Field(None, description="ID of the user assigned to handle the issue")
    status: Optional[IssueStatus] = Field(None, description="Current status of the issue")
    priority: Optional[IssuePriority] = Field(None, description="Priority level of the issue")
    resolution_notes: Optional[str] = Field(None, description="Notes regarding the resolution of the issue")
    resolution_date: Optional[datetime] = Field(None, description="Date when the issue was resolved")


class IssuesCommentCreate(BaseModel):
    issue_id: UUID4
    comment: str
    status: Optional[IssueStatus] = None
    resolution_notes: Optional[str] = None


class IssueCommentResponse(BaseModel):
    id: UUID4
    author_id: UUID4
    author_name: str
    comment: str
    date: datetime
    
    class Config:
        orm_mode = True


class IssueCommentResponse(BaseModel):
    id: UUID4
    author_id: UUID4
    author_name: str
    comment: str
    date: datetime
    
    class Config:
        orm_mode = True



class IssueHistoryResponse(BaseModel):
    id: UUID4
    event_code: str
    event_name: Optional[str] = None  # We'll populate this from the event_type relationship
    user: str
    date: datetime
    
    class Config:
        orm_mode = True

class IssuesListRequest(BaseModel):
    """
    Request model for listing issues with filters and pagination
    """
    q: Optional[str] = None
    customer_id: Optional[str] = None
    status: Optional[str] = None
    category_id: Optional[str] = None
    priority: Optional[IssuePriority] = None
    user_type: Optional[str] = None
    assigned_to: Optional[str] = None
    sub_category: Optional[str] = None
    limit: int = 10
    skip: int = 0