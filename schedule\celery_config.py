from celery import Celery
from app.config import get_settings

celery_app = Celery(
    "schedule",
    broker=f"redis://{get_settings().REDIS_HOST}:{get_settings().REDIS_PORT}/{get_settings().REDIS_DB}",
    backend=f"redis://{get_settings().REDIS_HOST}:{get_settings().REDIS_PORT}/{get_settings().REDIS_DB}",
    # include=["app.tasks"],
)
celery_app.conf.task_default_queue = "schedule_queue"
celery_app.autodiscover_tasks(["app"])
celery_app.conf.broker_connection_retry_on_startup = True

import app.tasks