from datetime import datetime, timed<PERSON><PERSON>
from typing import Annotated
import uuid
import asyncio
from app.utils.notification import send_push_notification
from sqlalchemy.orm import Session
from typing import List, Optional, Annotated
from fastapi import APIRouter, Depends, HTTPException, Header
from app.models import Booking, ServiceProvider, Users
from app.database import get_db
from app.schemas.helper import StandardResponse, ErrorResponse
from app.schemas.booking import ArtisanRating, RatingRequest, SortOrder
from app.utils.auth import get_id_header
from app.utils.enums import BookingStatus
import random
from sqlalchemy import text, func, desc, Numeric
from app.repositories.booking import create_booking_history

router = APIRouter()


@router.post("/artisan_rating")
async def artisan_rating(
    request: ArtisanRating,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    resp = await get_id_header(Authorization)
    if resp.status_code != 200:
        return ErrorResponse(status_code=401, message="Unauthorized")

    account_id = resp.json().get("account_id")

    try:
        user_type = request.user_type
        booking_obj = db.query(Booking).filter(Booking.id == request.booking_id).first()
        if not booking_obj:
            return ErrorResponse(status_code=404, message="Booking not found")

        if user_type == "customer":
            booking_obj.artisan_rating = request.artisan_rating
            booking_obj.artisan_rating_tags = request.artisan_rating_tags
            booking_obj.artisan_feedback = request.artisan_feedback
            db.commit()
            db.refresh(booking_obj)
            send_push_notification(
                auth_token=Authorization,
                title=f"You have been rated: {request.artisan_rating} stars",
                message=request.artisan_feedback,
                type="service_provider",
                sender_id=booking_obj.artisan_id,
            )
        elif user_type == "artisan":
            booking_obj.customer_rating = request.customer_rating
            booking_obj.customer_rating_tags = request.customer_rating_tags
            booking_obj.customer_feedback = request.customer_feedback
            db.commit()
            db.refresh(booking_obj)
            send_push_notification(
                auth_token=Authorization,
                title=f"You have been rated: {request.customer_rating} stars",
                message=request.customer_feedback,
                type="user",
                sender_id=booking_obj.user_id,
            )
        # return {'data': 'Rating submitted successfully'}
        return StandardResponse(
            status_code=200, message="Rating submitted successfully"
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Rating submitted successfully", error=str(e)
        )


@router.post("/ratings")
async def get_ratings(
    data: RatingRequest,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Get ratings and reviews based on user type:
    - If user_id is provided: Returns all ratings given to artisans by this user
    - If artisan_id is provided: Returns all ratings received by this artisan from users
    - Pagination is supported with page and limit parameters
    - Sorting is supported with sort parameter (high_to_low or low_to_high)
    """
    resp = await get_id_header(Authorization)
    if resp.status_code != 200:
        return ErrorResponse(status_code=401, message="Unauthorized")

    account_id = resp.json().get("account_id")

    try:
        query = db.query(Booking)

        if data.user_id:
            rating_column = Booking.customer_rating
            query = query.filter(Booking.user_id == uuid.UUID(data.user_id))
            query = query.filter(rating_column.isnot(None))
        elif data.artisan_id:
            rating_column = Booking.artisan_rating
            query = query.filter(Booking.artisan_id == uuid.UUID(data.artisan_id))
            query = query.filter(rating_column.isnot(None))
        else:
            return ErrorResponse(
                status_code=400, message="Either user_id or artisan_id must be provided"
            )

        # Calculate overall average from DB
        if data.user_id:
            avg_rating_query = db.query(func.avg(Booking.customer_rating))\
                .filter(Booking.user_id == uuid.UUID(data.user_id))\
                .filter(Booking.customer_rating.isnot(None))
        else:
            avg_rating_query = db.query(func.avg(Booking.artisan_rating))\
                .filter(Booking.artisan_id == uuid.UUID(data.artisan_id))\
                .filter(Booking.artisan_rating.isnot(None))

        avg_rating = avg_rating_query.scalar() or 0
        avg_rating = round(avg_rating, 2)

        # Apply sorting if specified
        if data.sort:
            if data.sort == SortOrder.HIGH_TO_LOW:
                query = query.order_by(rating_column.desc())
            else:  # LOW_TO_HIGH
                query = query.order_by(rating_column.asc())
        else:
            # Default sorting by requested_time if no sort specified
            query = query.order_by(Booking.requested_time.desc())

        # Pagination and result
        total_count = query.count()
        offset = (data.page - 1) * data.limit if data.page > 0 else 0
        bookings = query.offset(offset).limit(data.limit).all()

        # Star rating counts (1 to 5)
        star_counts_query = db.query(
            rating_column.label("rating"), func.count().label("count")
        ).filter(rating_column.isnot(None))

        if data.user_id:
            star_counts_query = star_counts_query.filter(
                Booking.user_id == uuid.UUID(data.user_id)
            )
        else:
            star_counts_query = star_counts_query.filter(
                Booking.artisan_id == uuid.UUID(data.artisan_id)
            )

        star_counts_query = star_counts_query.group_by(rating_column)
        star_counts = star_counts_query.all()

        # Convert to dict with default 0 for missing ratings
        star_counts_dict = {i: 0 for i in range(1, 6)}
        for row in star_counts:
            star_counts_dict[int(row.rating)] = row.count
        
        result = []
        for booking in bookings:
            if data.user_id:
                artisan = (
                    db.query(ServiceProvider)
                    .filter(ServiceProvider.id == booking.artisan_id)
                    .first()
                )

                artisan_name = (
                    f"{artisan.first_name or ''} {artisan.last_name or ''}".strip()
                    if artisan
                    else ""
                )
                artisan_profile_pic = artisan.profile_pic if artisan else None

                result.append(
                    {
                        "booking_id": booking.id,
                        "artisan_id": booking.artisan_id,
                        "artisan_name": artisan_name,
                        "artisan_profile_pic": artisan_profile_pic,
                        "service_id": booking.service_id,
                        "booking_date": booking.booking_date,
                        "rating": booking.customer_rating,
                        "rating_tags": booking.customer_rating_tags,
                        "feedback": booking.customer_feedback,
                        "created_at": booking.created_at
                    }
                )
            else:
                user = db.query(Users).filter(Users.id == booking.user_id).first()

                user_name = (
                    f"{user.first_name or ''} {user.last_name or ''}".strip()
                    if user
                    else ""
                )
                user_profile_pic = user.profile_pic if user else None

                result.append(
                    {
                        "booking_id": booking.id,
                        "user_id": booking.user_id,
                        "user_name": user_name,
                        "user_profile_pic": user_profile_pic,
                        "service_id": booking.service_id,
                        "booking_date": booking.booking_date,
                        "rating": booking.artisan_rating,
                        "rating_tags": booking.artisan_rating_tags,
                        "feedback": booking.artisan_feedback,
                        "created_at": booking.created_at
                    }
                )

        return StandardResponse(
            status_code=200,
            message="Ratings retrieved successfully",
            data={
                "ratings": result,
                "star_counts": star_counts_dict,
                "avg_rating": avg_rating, 
                "pagination": {
                    "total": total_count,
                    "page": data.page,
                    "limit": data.limit,
                    "pages": (total_count + data.limit - 1) // data.limit,
                },
            },
        )
    except ValueError:
        return ErrorResponse(status_code=400, message="Invalid UUID format")
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Failed to retrieve ratings", error=str(e)
        )


@router.post("/average-rating")
async def get_average_rating(
    data: RatingRequest,
    db: Session = Depends(get_db),
    # Authorization: Annotated[str, Header()] = None,
):
    """
    Get average rating and total number of ratings for an artisan.
    """
    # resp = await get_id_header(Authorization)
    # if resp.status_code != 200:
    #     return ErrorResponse(status_code=401, message="Unauthorized")

    artisan_id = data.artisan_id
    user_id = data.user_id
    return get_avg_rating(db, artisan_id, user_id)


def get_avg_rating(
    db: Session, artisan_id: Optional[str] = None, user_id: Optional[str] = None
):
    try:
        if artisan_id:
            query_result = (
                db.query(
                    Booking.artisan_id,
                    func.count(Booking.artisan_rating).label("total_ratings"),
                    func.round(func.avg(Booking.artisan_rating).cast(Numeric), 2).label(
                        "average_rating"
                    ),
                )
                .filter(
                    Booking.artisan_id == uuid.UUID(artisan_id),
                    Booking.artisan_rating.isnot(None),
                )
                .group_by(Booking.artisan_id)
                .first()
            )

            if not query_result:
                # return StandardResponse(
                #     status_code=200,
                #     message="No ratings found for this artisan",
                #     data={
                #         "artisan_id": artisan_id,
                #         "total_ratings": 0,
                #         "average_rating": 0.0
                #     }
                # )
                data = {
                    "artisan_id": artisan_id,
                    "total_ratings": 0,
                    "average_rating": 0.0,
                }
                return data["average_rating"]

            # return StandardResponse(
            #     status_code=200,
            #     message="Average rating retrieved successfully",
            #     data={
            #         "artisan_id": str(query_result.artisan_id),
            #         "total_ratings": query_result.total_ratings,
            #         "average_rating": float(query_result.average_rating) if query_result.average_rating else 0.0
            #     }
            # )
            data = {
                "artisan_id": str(query_result.artisan_id),
                "total_ratings": query_result.total_ratings,
                "average_rating": (
                    float(query_result.average_rating)
                    if query_result.average_rating
                    else 0.0
                ),
            }
            return data["average_rating"]
        elif user_id:
            query_result = (
                db.query(
                    Booking.user_id,
                    func.count(Booking.customer_rating).label("total_ratings"),
                    func.round(
                        func.avg(Booking.customer_rating).cast(Numeric), 2
                    ).label("average_rating"),
                )
                .filter(
                    Booking.user_id == uuid.UUID(user_id),
                    Booking.customer_rating.isnot(None),
                )
                .group_by(Booking.user_id)
                .first()
            )

            if not query_result:
                # return StandardResponse(
                #     status_code=200,
                #     message="No ratings found for this user",
                #     data={
                #         "user_id": user_id,
                #         "total_ratings": 0,
                #         "average_rating": 0.0
                #     }
                # )
                data = {"user_id": user_id, "total_ratings": 0, "average_rating": 0.0}
                return data["average_rating"]

            # return StandardResponse(
            #     status_code=200,
            #     message="Average rating retrieved successfully",
            #     data={
            #         "user_id": str(query_result.user_id),
            #         "total_ratings": query_result.total_ratings,
            #         "average_rating": float(query_result.average_rating) if query_result.average_rating else 0.0
            #     }
            # )
            data = {
                "user_id": str(query_result.user_id),
                "total_ratings": query_result.total_ratings,
                "average_rating": (
                    float(query_result.average_rating)
                    if query_result.average_rating
                    else 0.0
                ),
            }
            return data["average_rating"]
        return ErrorResponse(
            status_code=400, message="Artisan ID or User ID is required"
        )
    except ValueError:
        return ErrorResponse(status_code=400, message="Invalid UUID format")
    except Exception as e:
        return ErrorResponse(
            status_code=500, message=f"Failed to retrieve average rating: {str(e)}"
        )
