import app.models as md

def db_get_phone_info(phone, db):

    phn_info = db.query(md.SPBranches).filter(md.SPBranches.Phone == phone).first()
    return phn_info

def db_get_service_provider_info(sp_id, db):

    sp_info = db.query(md.ServiceProviders).filter(md.ServiceProviders.id == sp_id).first()
    return sp_info

def db_get_sp_branch_info(spb_id, db):
    spb_info = db.query(md.SPBranches).filter(md.SPBranches.id == spb_id).first()
    return spb_info

def db_get_sp_branchs(db):
    spb_info = db.query(md.SPBranches).all()
    return spb_info