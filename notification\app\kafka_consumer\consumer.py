import json
from app.kafka_consumer.config import create_vector_consumer
from app.service import send_notification


vector_consumer = create_vector_consumer()


async def consume_vector_process():
    async for msg in vector_consumer:
        data = msg.value.decode()
        data_dict = json.loads(data)
        try:
            await send_notification(data_dict)
        except BaseException as err:
            print(f"Exception caused due to: {err}")
