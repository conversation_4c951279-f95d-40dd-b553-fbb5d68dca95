from enum import Enum

class PaymentMethodType(str, Enum):
    CARD = "CARD"
    WALLET = "WALLET"
    CASH = "CASH"
    BANK = "BANK"

class StatusType(str, Enum):
    INIT = "INIT"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

class CurrencyType(str, Enum):
    GHS = "GHS"
    EUR = "EUR"
    USD = "USD"
    AED = "AED"

class TransactionTypeType(str, Enum):
    PAYMENT = "PAYMENT"
    PAYOUT = "PAYOUT"
    REFUND = "REFUND"

class AccountStatusType(str, Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"

class AccountTypeType(str, Enum):
    USER = "USER"
    AGENT = "ARTISAN"
    BUSINESS = "BUSINESS"