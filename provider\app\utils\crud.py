from sqlalchemy.orm import Session
from typing import Any, Dict, Optional

def create_record(db: Session, model, request_dict: Dict[str, Any]):
    """Create a new record in the database"""
    print('calling create record functionnnnnnnn')
    try:
        obj = model(**request_dict)
        db.add(obj)
        db.commit()
        db.refresh(obj)
        return obj
    except Exception as e:
        print(e, 'eeeeeeeeeeeeee')
        db.rollback()
        error_message = f"Database error: {str(e)}"
        print(error_message, 'errrrrrrrrrrrrrrrrrrrrrrrrrrr')
        # raise HTTPException(status_code=500, detail=error_message)
        return error_message