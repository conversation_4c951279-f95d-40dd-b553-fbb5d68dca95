import os
import requests
from typing import Optional
from app.config import settings
from app.database import get_db_session
from app.models import Services, Booking
from app.utils.helper import get_tax, get_booking_fee, get_surcharges
from app.utils.helper import get_sp_id

db = get_db_session()

async def create_invoice(data):
    """Creates invoice and invoice items with correct calculations"""
    try:
        service_list = data.get("service_list")
        items = []
        tax_percentage = 17.5
        platform_fee_percentage = 2.5
        booking_fee_percentage = 20

        total_base_fee = 0
        total_tax = 0
        total_platform_fee = 0
        total_booking_fee = 0
        total_discount = 0

        for service in service_list:
            service_obj = db.query(Services).filter(Services.id == service.get("service_id")).first()
            if not service_obj:
                continue

            base_fee = service_obj.price or 0
            booking_fee = get_booking_fee(base_fee, service_obj.booking_fee_percentage or booking_fee_percentage)
            platform_fee = get_surcharges(base_fee)  # Replace with helper or inline if needed
            tax = get_tax(base_fee)

            total_amount = base_fee + booking_fee + platform_fee + tax

            items.append({
                "service_id": service.get("service_id"),
                "quantity": 1,
                "price": base_fee,
                "description": service.get("description"),
                "tax_percentage": tax_percentage,
                "tax_amount": tax,
                "platform_fee_percentage": platform_fee_percentage,
                "platform_fee_amount": platform_fee,
                "booking_fee": booking_fee,
                "booking_fee_percentage": booking_fee_percentage,
                "discount_amount": 0,
                "total_amount": total_amount,
                "pending_amount": total_amount,
                "is_agent_created": False
            })

            # Totals
            total_base_fee += base_fee
            total_booking_fee += booking_fee
            total_platform_fee += platform_fee
            total_tax += tax

        total_invoice_amount = total_base_fee + total_booking_fee + total_platform_fee + total_tax

        payload = {
            "sp_id": get_sp_id(db),
            "payment_method": data.get("payment_type"),
            "biller_id": "",
            "customer_id": data.get("user_id"),
            "tax_percentage": tax_percentage,
            "tax_amount": total_tax,
            "platform_fee_percentage": platform_fee_percentage,
            "platform_fee_amount": total_platform_fee,
            "booking_fee": total_booking_fee,
            "booking_fee_percentage": booking_fee_percentage,
            "discount_amount": total_discount,
            "total_amount": total_invoice_amount,
            "payment_status": "Quotation",
            "booking_id": data.get("booking_id"),
            "items": items
        }

        base_url = os.getenv("BE_PAYMENT_API_URL", "localhost:8005")
        url = f"{base_url}/invoices/create-invoice"
        response = requests.post(url, json=payload)
        print(response.json(), "invoice creation response")

        if response.status_code == 200:
            db.query(Booking).filter(
                Booking.id == data.get("booking_id")
            ).update(
                {"invoice_id": response.json().get("data").get("invoice_id")},
                synchronize_session=False
            )
            db.commit()
            return 200

        return 500

    except Exception as e:
        print(f"Error creating invoice: {e}")
        return 500