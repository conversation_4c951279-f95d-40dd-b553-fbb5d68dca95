from typing import Optional
import boto3
import hmac
import hashlib
import base64
from app.config import get_settings
import secrets
import string
from app.utils import generate_cognito_password
from app.helper import ErrorResponse
from app.cognito_password_encrypt import decrypt_password, encrypt_password


# ✅ Get settings
settings = get_settings()

# ✅ Create Cognito Client with Credentials
cognito_client = boto3.client(
    "cognito-idp",
    region_name=settings.COGNITO_REGION,
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,  # Use Client ID as access key
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,  # Use Client Secret as secret key
    # aws_access_key_id=settings.COGNITO_CLIENT_ID,  # Use Client ID as access key
    # aws_secret_access_key=settings.COGNITO_CLIENT_SECRET  # Use Client Secret as secret key
)

print(get_settings().COGNITO_USER_POOL_ID, get_settings().COGNITO_CLIENT_ID, "settings")


# ✅ Function to Generate Secret Hash for Cognito
def generate_secret_hash(username, client_id, client_secret):
    """
    Generates a secret hash required for AWS Cognito API calls.
    """
    message = username + client_id
    dig = hmac.new(client_secret.encode(), message.encode(), hashlib.sha256).digest()
    return base64.b64encode(dig).decode()


# ✅ Function to Create a User in Cognito
# def create_cognito_user(email: str, phone_number: str, password: str):
#     """
#     Creates a user in AWS Cognito and returns the Cognito ID (sub).
#     """
#     secret_hash = generate_secret_hash(
#         email, get_settings().COGNITO_CLIENT_ID, get_settings().COGNITO_CLIENT_SECRET
#     )
#     print("PN: ", phone_number)
#     cognito_params = {
#         "ClientId": get_settings().COGNITO_CLIENT_ID,
#         "SecretHash": secret_hash,
#         "Username": email,
#         "Password": password,  # Temporary password
#         "UserAttributes": [
#             {"Name": "email", "Value": email},
#             {"Name": "phone_number", "Value": phone_number},
#         ],
#     }

#     try:
#         cognito_response = cognito_client.sign_up(**cognito_params)
#         return cognito_response["UserSub"]  # Return Cognito user ID
#     except Exception as e:
#         raise Exception(f"Cognito Error: {e}")


def create_cognito_user(
    phone_number: str,
    temporary_password: str,
    email: Optional[str] = None,
):
    """
    Creates a user in AWS Cognito **without sending SMS/Email** using `admin_create_user()`.
    """
    try:

        response = cognito_client.admin_create_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=phone_number,
            UserAttributes=[
                {"Name": "email", "Value": email},
                {"Name": "phone_number", "Value": phone_number},
                {"Name": "email_verified", "Value": "false"},  # ✅ Now allowed
                {"Name": "phone_number_verified", "Value": "true"},  # ✅ Now allowed
            ],
            ForceAliasCreation=True,
            TemporaryPassword=temporary_password,
            # MessageAction="SUPPRESS",  # ✅ Prevents Cognito from sending verification emails/SMS
        )
        set_password_response = cognito_client.admin_set_user_password(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=phone_number,
            Password="Hello@123",
            Permanent=True,  # This makes the password permanent, without requiring a password change on first login
        )
        return response

    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


async def update_cognito_attributes(
    username: str, attributes: dict, replace_all: bool = False
):
    """
    Updates Cognito user attributes using admin privileges.
    Can be used for both regular users and admins.

    Args:
        username: The username/email of the user
        attributes: Dictionary of attributes to update
                   (e.g., {"custom:local_user_id": "123"} or {"custom:local_admin_id": "456"})
        replace_all: If True, replaces all attributes. If False, only updates/adds specified attributes
    """
    try:
        if not replace_all:
            user_info = cognito_client.admin_get_user(
                UserPoolId=get_settings().COGNITO_USER_POOL_ID, Username=username
            )

            existing_attributes = {
                attr["Name"]: attr["Value"]
                for attr in user_info.get("UserAttributes", [])
                if attr["Name"]
                != "sub"  # Exclude the 'sub' attribute as it's immutable
            }

            merged_attributes = {**existing_attributes, **attributes}
        else:
            merged_attributes = attributes

        # Ensure 'sub' is not in the attributes to update
        if "sub" in merged_attributes:
            del merged_attributes["sub"]

        user_attributes = [
            {"Name": key, "Value": value} for key, value in merged_attributes.items()
        ]

        response = cognito_client.admin_update_user_attributes(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=username,
            UserAttributes=user_attributes,
        )
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


# async def add_custom_attributes():
#     try:
#         response = cognito_client.add_custom_attributes(
#             UserPoolId=get_settings().COGNITO_USER_POOL_ID,
#             CustomAttributes=[
#                 {
#                     'Name': 'user_id',
#                     'AttributeDataType': 'String',
#                     'DeveloperOnlyAttribute': False,
#                     'Mutable': True,
#                     'Required': False,
#                     'StringAttributeConstraints': {
#                         'MinLength': '1',
#                         'MaxLength': '255'
#                     }
#                 },
#             ]
#         )
#         return response
#     except Exception as e:
#         raise Exception(f"Cognito Error: {e}")


async def cognito_admin_login(username: str, password: str):
    try:

        auth_parameters = {"USERNAME": username, "PASSWORD": password}

        # If client secret is configured, add secret hash
        if get_settings().COGNITO_CLIENT_SECRET:
            from app.hash import cognito_secret_hash

            secret_hash = cognito_secret_hash(username)
            auth_parameters["SECRET_HASH"] = secret_hash

        response = cognito_client.initiate_auth(
            ClientId=get_settings().COGNITO_CLIENT_ID,
            AuthFlow="USER_PASSWORD_AUTH",
            AuthParameters=auth_parameters,
        )
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


async def delete_cognito_user(username: str):
    """
    Deletes a user from AWS Cognito.

    Args:
        username: The username/email of the user to delete

    Returns:
        Response from Cognito API
    """
    try:
        response = cognito_client.admin_delete_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID, Username=username
        )
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def add_user_to_group(username: str, group_name: str):
    """
    Add a user to a Cognito user group.

    Args:
        username: The Cognito username (UUID)
        group_name: The name of the group to add the user to

    Returns:
        The response from Cognito
    """
    try:
        client = boto3.client(
            "cognito-idp",
            region_name=get_settings().AWS_REGION,
            aws_access_key_id=get_settings().AWS_ACCESS_KEY_ID,
            aws_secret_access_key=get_settings().AWS_SECRET_ACCESS_KEY,
        )

        response = client.admin_add_user_to_group(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=username,
            GroupName=group_name,
        )

        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def cognito_sign_up(phonenumber):
    try:
        password = generate_cognito_password()
        encrypted_password = encrypt_password(password)
        response = cognito_client.sign_up(
            ClientId=get_settings().CLIENT_ID,
            Username=phonenumber,
            UserAttributes=[
                {"Name": "email", "Value": ""},
                {"Name": "phone_number", "Value": phonenumber},
            ],
            Password=password,
            SecretHash=generate_secret_hash(
                phonenumber, get_settings().CLIENT_ID, get_settings().CLIENT_SECRET
            ),
        )
        print(response, "respppppppppppppppp")
        return response, encrypted_password
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def cognito_renew_token(refresh_token, role):
    try:
        if role == "user":
            client_id = "6q4vfso22gqhj84m1hc2ca7sue"
        else:
            client_id = "aq3cgcvkthr2hk808suldlb7u"

        response = cognito_client.initiate_auth(
            AuthFlow="REFRESH_TOKEN_AUTH",
            ClientId=client_id,
            AuthParameters={"REFRESH_TOKEN": refresh_token},
        )
        print(response, "respppppppppppppppp")
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def confirm_signup(phonenumber, confirmation_code):
    try:
        response = cognito_client.confirm_sign_up(
            ClientId=get_settings().CLIENT_ID,
            Username=phonenumber,
            ConfirmationCode=confirmation_code,
            SecretHash=generate_secret_hash(
                phonenumber, get_settings().CLIENT_ID, get_settings().CLIENT_SECRET
            ),
        )
        print(response, "respppppppppppppppp")
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def resend_confirmation_code(phonenumber):
    try:
        response = cognito_client.resend_confirmation_code(
            ClientId=get_settings().CLIENT_ID,
            Username=phonenumber,
            SecretHash=generate_secret_hash(
                phonenumber, get_settings().CLIENT_ID, get_settings().CLIENT_SECRET
            ),
        )
        print(response, "respppppppppppppppp")
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def get_access_token(phone_number, otp):
    try:
        auth_response = cognito_client.initiate_auth(
            AuthFlow="CUSTOM_AUTH",
            AuthParameters={
                "USERNAME": phone_number,
                "SECRET_HASH": generate_secret_hash(
                    phone_number, get_settings().CLIENT_ID, get_settings().CLIENT_SECRET
                ),
            },
            ClientId=get_settings().CLIENT_ID,
        )

        print(auth_response, "auth_response")

        session = auth_response["Session"]

        token_response = cognito_client.respond_to_auth_challenge(
            ClientId=get_settings().CLIENT_ID,
            ChallengeName="CUSTOM_CHALLENGE",
            Session=session,
            ChallengeResponses={"USERNAME": phone_number, "ANSWER": otp},
        )
        print(token_response, "token_response")
        return token_response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def get_token_after_signup(phonenumber, encrypted_password):
    try:
        password = decrypt_password(encrypted_password)
        auth_response = cognito_client.initiate_auth(
            ClientId=get_settings().CLIENT_ID,
            AuthFlow="USER_PASSWORD_AUTH",
            AuthParameters={
                "USERNAME": phonenumber,
                "PASSWORD": password,
                "SECRET_HASH": generate_secret_hash(
                    phonenumber, get_settings().CLIENT_ID, get_settings().CLIENT_SECRET
                ),
            },
        )
        # auth_response = cognito_client.initiate_auth(
        #         AuthFlow="USER_SRP_AUTH",
        #         ClientId=get_settings().COGNITO_CLIENT_ID,
        #         AuthParameters={"USERNAME": phonenumber}
        #     )
        print(auth_response, "token responseeeeeeeeeeee")
        return auth_response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def admin_get_user(phonenumber):
    try:
        response = cognito_client.admin_get_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=phonenumber,
        )
        print(response, "respppppppppppppppp")
        return response
    except Exception as e:
        if (
            hasattr(e, "response")
            and e.response.get("Error", {}).get("Code") == "UserNotFoundException"
        ):
            return None
        raise Exception(f"Cognito Error: {str(e)}")


async def disable_cognito_user(username: str):
    """
    Deletes a user from AWS Cognito.

    Args:
        username: The username/email of the user to delete

    Returns:
        Response from Cognito API
    """
    try:
        cognito_entry = admin_get_user(username)
        print(cognito_entry, "cognitttttto")
        if cognito_entry is None:
            print("noppeeee")
            return None
        response = cognito_client.admin_disable_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID, Username=username
        )
        print("dissssabled")
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


async def enable_cognito_user(username: str):
    """
    Deletes a user from AWS Cognito.

    Args:
        username: The username/email of the user to delete

    Returns:
        Response from Cognito API
    """
    try:
        cognito_entry = admin_get_user(username)
        if cognito_entry is None:
            print("noppeeee")
            return None
        response = cognito_client.admin_enable_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID, Username=username
        )
        print("enabledabled")
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")
