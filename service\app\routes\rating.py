from fastapi import APIRouter, Depends, Header,HTTPException, Query
from app.database import get_db
from app.schemas.rating import RatingRequest,RatingSummaryResponse,ReviewItem , RatingRequestagent
from sqlalchemy.orm import Session
from app.utils.auth import get_id_header
from typing import Optional
from app.utils.crud import get_record_by_id, create_record, delete_record, update_record
from typing import Annotated,Dict
from app.schemas.helper import StandardResponse, ErrorResponse
from app.models import Ratings,SPRatings,UserProfiles,Services,InvoiceItem, Booking,ArtisanAssigned
from app.utils.auth import permission_checker
from fastapi.responses import JSONResponse
from collections import defaultdict

router = APIRouter(tags=["Ratings"])


@router.post("/create-rating")
async def create_rating(request: RatingRequest, db: Session = Depends(get_db)):
    try:
        # user_type = request.user_type
        # Step 1: Validate IDs
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user
        
        booking_obj = db.query(Booking).filter(Booking.id == request.booking_id).first()
        if not booking_obj:
            return ErrorResponse(
            status_code=500, message="Booking not Found"
        )

        user = db.query(UserProfiles).filter(UserProfiles.id == booking_obj.user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        if request.user_type == 'USER':
            ratings_val = {
                    "rating": request.rating,
                    "rating_tags": request.rating_tags,
                    "feedback": request.feedback,
                    "user_type": request.user_type,
                }
            rating = create_record(db, Ratings, ratings_val)

            spratings_val = {
                "user_id": booking_obj.user_id,
                "booking_id": booking_obj.id,
                # "service_id": request.service_id,
                # "invoice_item_id": request.invoice_item_id,
                "ref_id":rating.id
                
            }
            sp_rating = create_record(db, SPRatings, spratings_val)
        elif request.user_type == 'ARTISAN':
            artisan_ids = db.query(ArtisanAssigned).filter(ArtisanAssigned.invoice_id == booking_obj.invoice_id).all()
            for artisan_id in artisan_ids:
                print("line31")
                ratings_val = {
                    "rating": request.rating,
                    "rating_tags": request.rating_tags,
                    "feedback": request.feedback,
                    "user_type": request.user_type,
                }
                rating = create_record(db, Ratings, ratings_val)

                spratings_val = {
                    "user_id": artisan_id.artisan_id,
                    "booking_id": booking_obj.id,
                    # "service_id": request.service_id,
                    # "invoice_item_id": request.invoice_item_id,
                    "ref_id":rating.id
                    
                }
                sp_rating = create_record(db, SPRatings, spratings_val)
        
        # return {'data': 'Rating submitted successfully'}
        return StandardResponse(
            status_code=200, message="Rating submitted successfully", data={
                "rating_id": rating.id,
            }
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Rating submitted Failed", error=str(e)
        )
    
@router.get("/ratings/list-feedback", response_model=RatingSummaryResponse)
def get_rating_summary(
    skip: int = Query(1, description="Page number (1-based)"),
    limit: int = Query(10, description="Number of items per page"),
    db: Session = Depends(get_db), 
    user=Depends(permission_checker)
):

    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user

        user_id = user.get("user_id") if isinstance(user, dict) else None
        user_data = get_record_by_id(db, UserProfiles, user_id)
        print(user_id, 'user_id in get_rating_summary')

        # Get ref_ids of ratings associated with this user
        ref_ids = (db.query(SPRatings.ref_id)
            .filter(SPRatings.user_id == user_id)
            .distinct()
            .all()
        )
        ref_ids = [r[0] for r in ref_ids]  # extract ids

        if not ref_ids:
            return ErrorResponse(status_code=400, message="No ratings found for this user")

        # Get all ratings for average and distribution calculation
        all_ratings = (
            db.query(Ratings)
            .filter(Ratings.id.in_(ref_ids))
            .order_by(Ratings.created_at.desc())
            .all()
        )

        total = len(all_ratings)
        if total == 0:
            return {
                "average_rating": 0.0,
                "total_reviews": 0,
                "rating_distribution": {5: 0, 4: 0, 3: 0, 2: 0, 1: 0},
                "reviews": []
            }

        # Calculate average using all ratings
        average_rating = round(sum(r.rating for r in all_ratings) / total, 1)

        # Rating distribution using all ratings
        distribution: Dict[int, int] = defaultdict(int)
        for r in all_ratings:
            rounded = int(round(r.rating))
            distribution[rounded] += 1

        final_dist = {i: distribution.get(i, 0) for i in range(1, 6)}

        # Apply pagination for reviews only
        adjusted_skip = (skip - 1) * limit if skip > 0 else 0
        paginated_ratings = (
            db.query(Ratings)
            .filter(Ratings.id.in_(ref_ids))
            .order_by(Ratings.created_at.desc())
            .offset(adjusted_skip)
            .limit(limit)
            .all()
        )

        # Build reviews list from paginated results
        review_list = [
            ReviewItem(
                user_id=str(user_id),
                first_name=user_data.first_name if user else "Unknown",
                last_name=user_data.last_name if user else "",
                profile_picture=user_data.profile_image_url if user else "",
                rating=r.rating,
                time_ago=r.created_at.strftime("%Y-%m-%d"),
                feedback=r.feedback or ""
            )
            for r in paginated_ratings
        ]

        return {
            "average_rating": average_rating,
            "total_reviews": total,
            "rating_distribution": dict(sorted(final_dist.items(), reverse=True)),
            "reviews": review_list,
            "pagination": {
                "skip": skip,
                "limit": limit,
                "total": total  # add the total number of reviews
            }
        }

    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Failed to fetch ratings", error=str(e))


@router.post("/create-rating-agent")
async def create_rating_agent(
    request: RatingRequestagent,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user
        
        user = db.query(UserProfiles).filter(UserProfiles.id == request.user_id).first()
        if not user:
            return ErrorResponse(status_code=404, message="Agent not found")
    
        # Step 1: Create rating record
        ratings_val = {
            "rating": request.rating,
            "user_type": request.user_type,
        }
        rating = create_record(db, Ratings, ratings_val)
        
        if isinstance(rating, str):
            return ErrorResponse(status_code=500, message=f"Failed to create rating: {rating}")

        # Step 2: Create SP rating record with optional fields
        spratings_val = {
            "user_id": request.user_id,
            "ref_id": rating.id,
        }
        
        # Add optional fields only if they are provided
        if request.dispute_id:
            spratings_val["dispute_id"] = request.dispute_id
        if request.issue_id:
            spratings_val["issue_id"] = request.issue_id
        
        sp_rating = create_record(db, SPRatings, spratings_val)
        
        if isinstance(sp_rating, str):
            return ErrorResponse(status_code=500, message=f"Failed to create SP rating: {sp_rating}")
        
        return StandardResponse(
            status_code=200,
            message="Rating submitted successfully",
            data={
                "rating_id": rating.id,
            }
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Rating submission failed",
            error=str(e)
        )