import uuid
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, Header
from app.s3_upload import upload_file_direct, s3_delete_file, S3_IMAGES_FOLDER, S3_DOCS_FOLDER
from sqlalchemy.orm import Session
from sqlalchemy import or_
from pathlib import Path
from app import schemas
from app.database import get_db
from app.utils import (
    create_record,
    delete_record,
    get_id_header,
    is_valid_uuid,
    update_record,
    check_file_exists,
)
from typing import Optional
from app.hash import cognito_secret_hash
from sqlalchemy.future import select
from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Annotated, List
from app.file_uploader import upload_file, delete_file
from app.config import get_settings
from sqlalchemy.types import String
# from hash import generate_salt, hash_password
from app.models import Services
from app.helper import StandardResponse, ErrorResponse

router = APIRouter()


@router.post("/services-create")
async def create_services(
    request: schemas.CreateServices = Depends(),
    banner: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            email = resp_json.get("email")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        request_dict = request.to_dict()
        print(request_dict, "request_dict")

        if banner:
            # Calling S3 upload function
            profile_url = upload_file_direct(banner, path=S3_IMAGES_FOLDER)
            request_dict["banner"] = profile_url['filename']

        new_services = await create_record(db, Services, request_dict)
        if isinstance(new_services, str):
            return ErrorResponse(
                status_code=400,
                message=new_services
            )
        return StandardResponse(
            status_code=200,
            message="Services created successfully",
            data={"data": "Services created successfully"}
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )


@router.get("/services-read/{id}")
async def read_services(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        query = select(Services).filter(Services.id == uuid.UUID(id))
        result = await db.execute(query)
        services_obj = result.scalars().first()
        return StandardResponse(
            status_code=200,
            message="Services read successfully",
            data=services_obj
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )

@router.get("/services-list")
async def list_services(
    parent_id: Optional[str] = None,
    q: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        print(resp, "resp")
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        # Total services
        query = select(Services)

        if q:
            query = query.filter(
                or_(
                    Services.id.cast(String) == q,
                    Services.name.ilike(f"%{q}%"),
                )
            )

        if parent_id:
            query = query.filter(Services.parent_id == uuid.UUID(parent_id))
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        services = result.scalars().all()

        # Total count
        count_query = select(func.count()).select_from(Services)
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()

        return StandardResponse(
            status_code=200,
            message="Services read successfully",
            data={"total": total_count, "services_list": services}
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )


@router.put("/services-update/{id}")
async def update_services(
    id: str,
    data: schemas.UpdateServices = Depends(),
    banner: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        query = select(Services).filter(Services.id == uuid.UUID(id))
        result = await db.execute(query)
        get_services = result.scalars().first()

        if banner:
            if get_services.banner:
                s3_delete_file(get_services.banner)
            # Calling S3 upload function
            profile_url = upload_file_direct(banner, path=S3_IMAGES_FOLDER)
            setattr(get_services, "banner", profile_url['filename'])

        res = await update_record(db, data, get_services)
        return StandardResponse(
            status_code=200,
            message="Services updated successfully",
            data=res
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )


@router.delete("/services-delete/{id}")
async def delete_services(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        query = select(Services).filter(Services.id == uuid.UUID(id))
        result = await db.execute(query)
        get_services = result.scalars().first()

        if get_services is None:
            return ErrorResponse(
                status_code=404,
                message="services not found"
            )

        if get_services.banner:
            s3_delete_file(get_services.banner)

        res = await delete_record(db, get_services)
        return StandardResponse(
            status_code=200,
            message="Services deleted successfully",
            data=res
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )

@router.get("/services-list-simple")
async def list_services_simple(
    parent_id: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Get a simplified list of services with minimal data to avoid recursion issues.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return {
                "status_code": 401,
                "message": resp.json()
            }

        # Build query
        query = select(Services)
        
        # Filter by parent_id if provided
        if parent_id:
            query = query.filter(Services.parent_id == uuid.UUID(parent_id))
        
        # Execute query
        result = await db.execute(query)
        services = result.scalars().all()
        
        # Create a simplified list with only the data we need
        simple_services = []
        for service in services:
            simple_services.append({
                "id": str(service.id),
                "name": service.name
            })
        
        return {
            "status_code": 200,
            "message": "Services fetched successfully",
            "data": {
                "total": len(simple_services),
                "data": simple_services
            }
        }
    except Exception as e:
        return {
            "status_code": 500,
            "message": f"Error fetching services: {str(e)}",
            "error": str(e)
        }
