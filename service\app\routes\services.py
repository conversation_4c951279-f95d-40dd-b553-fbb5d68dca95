import uuid
from fastapi import APIRouter, Depends, File, UploadFile, Header
from app.utils.s3_upload import upload_file_direct, s3_delete_file, S3_IMAGES_FOLDER, S3_DOCS_FOLDER
from app.utils.fts import full_text_search
from sqlalchemy.orm import Session
from sqlalchemy import or_
from app.schemas import service as schemas
from app.database import get_db
from app.utils.crud import get_record_by_id, create_record, delete_record, update_record
from app.utils.auth import get_id_header
from typing import Optional
from sqlalchemy.future import select
from sqlalchemy import func
from typing import Annotated
from sqlalchemy.types import String
from app.models import Services
from app.schemas.helper import StandardResponse, ErrorResponse
from app.utils.auth import permission_checker
from fastapi.responses import JSONResponse

router = APIRouter(tags=["Services"])


@router.post("/services-create")
async def create_services(
    request: schemas.CreateServices = Depends(),
    banner: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        request_dict = request.to_dict()
        print(request_dict, "request_dict")

        if banner:
            # Calling S3 upload function
            profile_url = upload_file_direct(banner, path=S3_IMAGES_FOLDER)
            request_dict["banner"] = profile_url['filename']

        new_services = create_record(db, Services, request_dict)
        if isinstance(new_services, str):
            return ErrorResponse(
                status_code=400,
                message=new_services
            )
        return StandardResponse(
            status_code=200,
            message="Services created successfully",
            data={"data": "Services created successfully"}
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )


@router.get("/services-read/{id}")
async def read_services(
    id: str,
    db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        services_obj = get_record_by_id(db, Services, uuid.UUID(id))
        if isinstance(services_obj, str):
            return ErrorResponse(status_code=400, message=services_obj)
        return StandardResponse(
            status_code=200,
            message="Services read successfully",
            data=services_obj
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )

@router.get("/services-list")
async def list_services(
    parent_id: Optional[str] = None,
    q: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    #user=Depends(permission_checker)
    ):
    try:
        #if isinstance(user, JSONResponse):  # Permission failed
         #   return user
        # Total services
        query = select(Services)

        if q:
            services = full_text_search(db, q, skip, limit)
            print(services, "res")
            total_count = len(services)
        elif parent_id:
            query = query.filter(Services.parent_id == uuid.UUID(parent_id))

        if not q:
            query = query.offset(skip).limit(limit)
            result = db.execute(query)
            services = result.scalars().all()

            # Total count
            count_query = select(func.count()).select_from(Services)
            total_result = db.execute(count_query)
            total_count = total_result.scalar()


        return StandardResponse(
            status_code=200,
            message="Services read successfully",
            data={"total": total_count, "services_list": services}
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )


@router.put("/services-update/{id}")
async def update_services(
    id: str,
    data: schemas.UpdateServices = Depends(),
    banner: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        get_services = get_record_by_id(db, Services, uuid.UUID(id))
        if isinstance(get_services, str):
            return ErrorResponse(status_code=400, message=get_services)

        if banner:
            if get_services.banner:
                s3_delete_file(get_services.banner)
            # Calling S3 upload function
            profile_url = upload_file_direct(banner, path=S3_IMAGES_FOLDER)
            setattr(get_services, "banner", profile_url['filename'])

        res = update_record(db, data, get_services)
        if isinstance(res, str):
            return ErrorResponse(status_code=400, message=res)
        return StandardResponse(
            status_code=200,
            message="Services updated successfully",
            data=res
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )


@router.delete("/services-delete/{id}")
async def delete_services(
    id: str,
    db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        get_services = get_record_by_id(db, Services, uuid.UUID(id))
        if isinstance(get_services, str):
            return ErrorResponse(status_code=400, message=get_services)

        if get_services is None:
            return ErrorResponse(
                status_code=404,
                message="services not found"
            )

        if get_services.banner:
            s3_delete_file(get_services.banner)

        res = delete_record(db, get_services)
        return StandardResponse(
            status_code=200,
            message="Services deleted successfully",
            data=res
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )

@router.get("/services-list-simple")
async def list_services_simple(
    parent_id: Optional[str] = None,
    db: Session = Depends(get_db),user=Depends(permission_checker)):
    """
    Get a simplified list of services with minimal data to avoid recursion issues.
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        # Build query
        query = select(Services)
        
        # Filter by parent_id if provided
        if parent_id:
            query = query.filter(Services.parent_id == uuid.UUID(parent_id))
        
        # Execute query
        result = db.execute(query)
        services = result.scalars().all()
        
        # Create a simplified list with only the data we need
        simple_services = []
        for service in services:
            simple_services.append({
                "id": str(service.id),
                "name": service.name
            })
        
        return {
            "status_code": 200,
            "message": "Services fetched successfully",
            "data": {
                "total": len(simple_services),
                "data": simple_services
            }
        }
    except Exception as e:
        return {
            "status_code": 500,
            "message": f"Error fetching services: {str(e)}",
            "error": str(e)
        }
