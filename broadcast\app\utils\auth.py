import os
import requests

async def get_id_header(Authorization):
    if not Authorization:
        return {'error': "Token required"}
    try:
        jwt_token = Authorization.split(" ")[1]
        baseurl = os.getenv('BE_AUTH_API_URL')
        response = requests.post(f"{baseurl}/validate-token/", json={'token': jwt_token})
        return response
    except Exception as e:
        return {'error': f"Error: {e}"}