import pandas as pd
import uuid
import re
from typing import List, Dict, Any, Optional
from app.kafka_producer.producer import db_producer
from app.database import get_db_session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import delete
from app.models import Services, Category, UserProfiles, ArtisanDetail, Address, ServiceProviderServiceMapping, Roles

from app.models_enum import UserStatusType
from app.utils import generate_cognito_password

from app.cognito_utils import (
    admin_get_user,
    create_cognito_user,
    update_cognito_attributes,
    add_user_to_group,
)
from geoalchemy2.functions import ST_Point
import logging
from datetime import time
import os
import requests

logger = logging.getLogger(__name__)

# Cache for geocoding results to avoid repeated API calls
_geocoding_cache = {}


async def check_user_in_database(phone_number: str, db: AsyncSession) -> Optional[UserProfiles]:
    """
    Check if a user already exists in the database by phone number.
    
    Args:
        phone_number: The phone number to check
        db: Database session
        
    Returns:
        UserProfiles object if found, None otherwise
    """
    try:
        # Parse phone number to match database format
        if phone_number.startswith("+"):
            phone_without_plus = phone_number[1:]
            if phone_without_plus.startswith("233"):
                phone_only = phone_without_plus[3:]
            else:
                phone_only = phone_without_plus
        else:
            phone_only = phone_number
            
        logger.info(f"Checking database for user with phone: {phone_only}")
        
        # Query UserProfiles table by phone number
        query = select(UserProfiles).where(UserProfiles.phone_number == phone_only)
        result = await db.execute(query)
        user = result.scalars().first()
        
        if user:
            logger.info(f"Found existing user in database: {user.id} with phone: {phone_only}")
            return user
        else:
            logger.info(f"No user found in database with phone: {phone_only}")
            return None
            
    except Exception as e:
        logger.error(f"Error checking user in database: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None




async def get_artisan_role_id(db: AsyncSession) -> Optional[str]:
    """
    Get the artisan role ID from the database.
    
    Returns:
        Role ID for artisan role or None if not found
    """
    try:
        query = select(Roles).where(Roles.role_name == "artisan")
        result = await db.execute(query)
        role = result.scalars().first()
        return str(role.id) if role else None
    except Exception as e:
        logger.error(f"Error getting artisan role: {e}")
        return None


async def create_artisan_user(
    db: AsyncSession, 
    user_data: dict, 
    artisan_data: dict, 
    address_data: dict,
    services: list,
    auth_id: str
) -> Optional[UserProfiles]:
    """
    Create a complete artisan user with UserProfiles, ArtisanDetail, Address, and service mappings.
    
    Args:
        db: Database session
        user_data: Data for UserProfiles table
        artisan_data: Data for ArtisanDetail table  
        address_data: Data for Address table
        services: List of services to map
        auth_id: Cognito auth ID for updating attributes
        
    Returns:
        Created UserProfiles object or None if error
    """
    try:
        logger.info(f"Creating artisan user with data: {user_data}")
        
        # 1. Create UserProfiles record
        user_obj = UserProfiles(**user_data)
        db.add(user_obj)
        await db.flush()
        logger.info(f"✅ Created UserProfiles with ID: {user_obj.id}")
        
        # 2. Create ArtisanDetail record
        artisan_data["user_id"] = user_obj.id
        logger.info(f"Creating ArtisanDetail with data: {artisan_data}")
        artisan_obj = ArtisanDetail(**artisan_data)
        db.add(artisan_obj)
        await db.flush()
        logger.info(f"✅ Created ArtisanDetail for user: {user_obj.id}")
        
        # 3. Create Address record
        address_data["user_id"] = user_obj.id
        logger.info(f"Creating Address with data: {address_data}")
        address_obj = Address(**address_data)
        db.add(address_obj)
        await db.flush()
        logger.info(f"✅ Created Address for user: {user_obj.id}")
        
        # 4. Create service mappings
        if len(services) == 0:
            logger.info("No services to map - user will be created without service mappings")
        else:
            logger.info(f"Creating {len(services)} service mappings")
        for i, service in enumerate(services):
            logger.info(f"Creating service mapping {i+1}: {service}")
            try:
                service_mapping = ServiceProviderServiceMapping(
                    id=uuid.uuid4(),
                    service_provider_id=user_obj.id,  # Now points to UserProfiles.id
                    services_id=uuid.UUID(service["id"]),
                )
                db.add(service_mapping)
                await db.flush()
                logger.info(f"✅ Created service mapping {i+1} for user: {user_obj.id}")
            except Exception as e:
                logger.error(f"❌ Error creating service mapping {i+1}: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
        
        # 5. Update Cognito with account ID
        logger.info(f"Updating Cognito attributes for user: {auth_id}")
        await update_cognito_attributes(
            auth_id, {"custom:account_id": str(user_obj.id)}, False
        )
        logger.info(f"✅ Updated Cognito attributes for user: {user_obj.id}")
        
        return user_obj
        
    except Exception as e:
        logger.error(f"❌ Error creating artisan user: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None


async def get_category_by_name(db: AsyncSession, category_name: str):
    """
    Query the category table directly to find a category by name (case-insensitive).
    """
    try:
        logger.info(f"🔍 Looking for category matching trade area: '{category_name}'")
        
        # Query all categories from database
        query = select(Category).where(Category.is_active == True)
        result = await db.execute(query)
        categories = result.scalars().all()
        
        logger.info(f"📋 Found {len(categories)} active categories in database")
        if len(categories) == 0:
            logger.error("❌ No active categories found in database!")
            return None
            
        # Show all available categories for debugging
        logger.info("📋 Available categories:")
        for cat in categories:
            logger.info(f"  - {cat.name} (ID: {cat.id})")
        
        # Clean and normalize the input category name
        input_name = category_name.lower().strip()
        input_name = re.sub(r'\s+', '', input_name)  # Remove all whitespace
        input_name = re.sub(r'[^a-z0-9]', '', input_name)  # Remove special characters
        
        logger.info(f"🔍 Normalized search term: '{input_name}'")

        # First try exact match
        for category in categories:
            cat_name = str(category.name).lower().strip()
            cat_name = re.sub(r'\s+', '', cat_name)  # Remove all whitespace
            cat_name = re.sub(r'[^a-z0-9]', '', cat_name)  # Remove special characters
                        
            if cat_name == input_name:
                logger.info(f"✅ EXACT match found: '{category.name}' for trade area '{category_name}'")
                return category

        # Second try: Look for categories that contain the trade area term
        for category in categories:
            cat_name = str(category.name).lower().strip()
            cat_name = re.sub(r'\s+', '', cat_name)  # Remove all whitespace
            cat_name = re.sub(r'[^a-z0-9]', '', cat_name)  # Remove special characters
            
            # Check if trade area is contained in category name (more restrictive)
            if input_name in cat_name:
                logger.info(f"✅ PARTIAL match found: '{category.name}' contains trade area '{category_name}'")
                return category

        # No match found
        logger.warning(f"⚠️ No good match found for trade area: '{category_name}'")
        logger.info("❌ Returning None to avoid incorrect category assignment")
        return None
        
    except Exception as e:
        logger.error(f"❌ Error getting category by name: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None


async def get_services_by_category_id(db: AsyncSession, category_id: str):
    """
    Get all services that belong to a specific category directly from database.
    """
    try:
        logger.info(f"🔍 Looking for services with category_id: {category_id}")
        
        # Handle None category_id
        if not category_id:
            logger.warning("⚠️ No category_id provided, returning empty services list")
            return []
        
        # First, let's check if this category exists
        category_query = select(Category).where(Category.id == uuid.UUID(category_id))
        category_result = await db.execute(category_query)
        category = category_result.scalars().first()
        
        if not category:
            logger.error(f"❌ Category with ID {category_id} not found!")
            return []
        
        logger.info(f"✅ Found category: {category.name} (ID: {category_id})")
        
        # Query services from database by category ID
        query = select(Services).where(
            Services.parent_id == uuid.UUID(category_id),
            Services.is_active == True
        )
        result = await db.execute(query)
        services = result.scalars().all()
        
        logger.info(f"🔍 Found {len(services)} services for category '{category.name}'")
        
        if len(services) == 0:
            # Let's check if there are ANY services in the database
            all_services_query = select(Services).where(Services.is_active == True)
            all_services_result = await db.execute(all_services_query)
            all_services = all_services_result.scalars().all()
            logger.warning(f"⚠️ No services found for category '{category.name}'. Total services in database: {len(all_services)}")
            
            if len(all_services) > 0:
                logger.info("📋 Available services in database:")
                for service in all_services[:5]:  # Show first 5 services
                    logger.info(f"  - {service.name} (parent_id: {service.parent_id})")
        
        # Convert to list of dictionaries for compatibility
        services_list = []
        for service in services:
            services_list.append({
                "id": str(service.id),
                "name": service.name  # Add name for debugging
            })
            logger.info(f"✅ Service found: {service.name} (ID: {service.id})")
        
        return services_list
    except Exception as e:
        logger.error(f"❌ Error getting services by category ID: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return []


def geocode_location(district: str, region: str) -> tuple:
    """
    Use Google Maps Geocoding API to get latitude and longitude for a location.

    Args:
        district: The district or town
        region: The region or province

    Returns:
        Tuple of (latitude, longitude)
    """
    try:
        # Get Google Maps API key from environment
        api_key = os.getenv("GOOGLE_API_KEY")

        if not api_key:
            logger.warning("Google Maps API key not found in environment variables")
            return (0.0, 0.0)  # Default coordinates if API key is missing

        # Format the address for geocoding
        address = f"{district}, {region}"

        # Check if the result is in the cache
        if address in _geocoding_cache:
            logger.info(f"Using cached geocoding result for: {address}")
            return _geocoding_cache[address]

        # Make request to Google Maps Geocoding API
        url = f"https://maps.googleapis.com/maps/api/geocode/json?address={address}&key={api_key}"
        response = requests.get(url)

        if response.status_code == 200:
            data = response.json()

            if data["status"] == "OK" and len(data["results"]) > 0:
                location = data["results"][0]["geometry"]["location"]
                _geocoding_cache[address] = (location["lat"], location["lng"])
                return _geocoding_cache[address]

        logger.warning(f"Failed to geocode address: {address}")
        _geocoding_cache[address] = (0.0, 0.0) # Cache a default failure
        return _geocoding_cache[address]

    except Exception as e:
        logger.error(f"Error geocoding location: {e}")
        _geocoding_cache[address] = (0.0, 0.0) # Cache a default failure
        return _geocoding_cache[address]


def format_phone_number(phone_number: str, country_code: str = "233") -> str:
    """
    Format a phone number to ensure it has the proper country code.

    Args:
        phone_number: The raw phone number from the Excel file
        country_code: The country ISD code (default: 233 for Ghana)

    Returns:
        Properly formatted phone number with country code
    """
    # Handle empty or None phone numbers
    if not phone_number:
        return ""
        
    phone_number = str(phone_number).strip()
    
    # If it already has a plus sign, it's likely already formatted
    if phone_number.startswith("+"):
        return phone_number

    # Remove any non-digit characters after checking for plus
    phone_number = re.sub(r"\D", "", phone_number)

    # If it starts with the country code without plus, add the plus
    if phone_number.startswith(country_code):
        return f"+{phone_number}"

    # If it starts with a leading zero, remove it and add country code
    if phone_number.startswith("0"):
        return f"+{country_code}{phone_number[1:]}"

    # Otherwise, just add the country code
    return f"+{country_code}{phone_number}"



async def process_cognito_creation(data):
    """
    Process individual row data to create Cognito user and send to database queue.
    
    Args:
        data: Dictionary containing row data from Excel file
        
    Returns:
        None
    """
    db = await get_db_session()
    try:
        token = data.pop("token")
        logger.info(f"Processing Cognito creation for user: {data.get('Trainee Contact Number')}")
        
        # Check if latitude and longitude columns exist
        if data.get("Latitude") and data.get("Longitude"):
            try:
                latitude = float(data["Latitude"])
                longitude = float(data["Longitude"])
                logger.info(f"Using provided coordinates: {latitude}, {longitude}")
            except (ValueError, TypeError):
                # If coordinates in Excel are invalid, geocode them
                logger.warning(f"Invalid coordinates provided, geocoding location: {data['District/Town']}, {data['Region']}")
                latitude, longitude = geocode_location(data['District/Town'], data['Region'])
        else:
            logger.info(f"No coordinates provided, geocoding location: {data['District/Town']}, {data['Region']}")
            latitude, longitude = geocode_location(data['District/Town'], data['Region'])
        
        try:
            # Extract data from row
            reg_code = str(data["Trainee Reg. Code"]).strip()
            surname = str(data["Trainee Surname"]).strip()
            first_name = str(data["Trainee First Name"]).strip()
            other_names = str(data["Other names of Trainee"]).strip() if pd.notna(data["Other names of Trainee"]) else ""
            trade_area = str(data["Trade Area(s)"]).strip()
            region = str(data["Region"]).strip()
            district = str(data["District/Town"]).strip()

            # Validate required fields
            if not all([reg_code, surname, first_name, trade_area, region, district]):
                logger.error(f"Missing required fields for user: {data.get('Trainee Contact Number')}")
                return None

            # Format phone number - handle both string and numeric inputs
            phone_number_raw = data["Trainee Contact Number"]
            if pd.notna(phone_number_raw):
                phone_number = str(phone_number_raw).strip()
                # Remove any decimals that pandas might add
                if phone_number.endswith('.0'):
                    phone_number = phone_number[:-2]
                phone_number = format_phone_number(phone_number)
            else:
                logger.error(f"Missing phone number for user")
                return None
            logger.info(f"Formatted phone number: {phone_number}")

            # Find category by trade area name
            category = await get_category_by_name(db, trade_area)
            logger.info(f"🔍 Category search result: {category.name if category else 'None'} for trade area: {trade_area}")

            # Flexible validation: Continue with default category if not found
            if not category:
                logger.warning(f"⚠️ No suitable category found for trade area: '{trade_area}'. Continuing with empty services list.")
                logger.info("💡 User will be created without service mappings. Services can be added later.")
                # Set a default category_id or None to indicate no services
                category_id = None
            else:
                logger.info(f"✅ Using category: '{category.name}' (ID: {category.id}) for trade area: '{trade_area}'")
                category_id = str(category.id)

            # Create service provider in Cognito
            try:
                # Check if user already exists
                cognito_exists_check = admin_get_user(phone_number)
                
                if cognito_exists_check is not None:
                    logger.warning(f"User already exists in Cognito: {phone_number}")
                    
                    # Get the auth_id from existing Cognito user
                    auth_id = cognito_exists_check.get("Username", "")
                    if not auth_id:
                        logger.error(f"No auth_id found for existing Cognito user: {phone_number}")
                        return {
                            "success": False,
                            "skipped": True,
                            "message": f"User exists in Cognito but no auth_id found: {phone_number}"
                        }
                    
                    logger.info(f"Found existing Cognito user with auth_id: {auth_id}")
                    
                    # Check if user already exists in database
                    existing_user = await check_user_in_database(phone_number, db)
                    if existing_user:
                        logger.info(f"User already exists in both Cognito and database: {phone_number}")
                        return {
                            "success": True,
                            "skipped": True,
                            "message": f"User already exists in system: {phone_number}"
                        }
                    
                    # User exists in Cognito but not in database - create database record
                    logger.info(f"User exists in Cognito but not in database. Creating database record for: {phone_number}")
                    
                    # Process existing cognito user to kafka topic for database creation
                    sp_data = {
                        'first_name': first_name,
                        'surname': surname,
                        'other_names': other_names,
                        'phone_number': phone_number,
                        'auth_id': auth_id,
                        'latitude': latitude,
                        'longitude': longitude,
                        'reg_code': reg_code,
                        'trade_area': trade_area,
                        'region': region,
                        'district': district,
                        'token': token,
                        'category_id': category_id,
                        'notification_token':"",  # Assuming notification token is not provided in the data
                    }
                    
                    logger.info(f"Sending existing Cognito user to database queue for: {phone_number}")
                    await db_producer(sp_data)
                    logger.info(f"Successfully queued existing user for database creation: {phone_number}")
                    
                    return {
                        "success": True,
                        "skipped": False,
                        "message": f"Existing Cognito user queued for database creation: {phone_number}"
                    }

                # Create new Cognito user
                logger.info(f"Creating Cognito user for: {phone_number}")
                auth_response = create_cognito_user(
                    phone_number, generate_cognito_password(), ""
                )
                
                if not auth_response or "User" not in auth_response:
                    logger.error(f"Failed to create Cognito user: {phone_number}")
                    return None
                    
                auth_id = auth_response.get("User", {}).get("Username", "")
                if not auth_id:
                    logger.error(f"No auth_id returned from Cognito for: {phone_number}")
                    return None
                    
                logger.info(f"Successfully created Cognito user with auth_id: {auth_id}")
                
                # Add user to the "artisan" group in Cognito
                # add_user_to_group(auth_id, "artisan")
                # logger.info(f"Added user to artisan group: {phone_number}")

                # Process cognito user to kafka topic for database creation
                sp_data = {
                    'first_name': first_name,
                    'surname': surname,
                    'other_names': other_names,
                    'phone_number': phone_number,
                    'auth_id': auth_id,
                    'latitude': latitude,
                    'longitude': longitude,
                    'reg_code': reg_code,
                    'trade_area': trade_area,
                    'region': region,
                    'district': district,
                    'token': token,
                    'category_id': category_id,
                    'notification_token':"",  # Assuming notification token is not provided in the data
                }
                
                logger.info(f"Sending to database queue for: {phone_number}")
                await db_producer(sp_data)
                logger.info(f"Successfully queued for database creation: {phone_number}")
                
            except Exception as e:
                logger.error(f"Error creating Cognito user for {phone_number}: {str(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return None
                
        except Exception as e:
            logger.error(f"Error processing row data for {data.get('Trainee Contact Number')}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
            
    except Exception as e:
        logger.error(f"Error in process_cognito_creation: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None
    finally:
        await db.close()
            

async def process_sp_creation(data):
    db = await get_db_session()
    try:
        token = data.pop("token")
        category_id = data.pop("category_id")
        
        # Check if user already exists in database
        existing_user = await check_user_in_database(data['phone_number'], db)
        if existing_user:
            logger.warning(f"User already exists in database: {data['phone_number']}")
            return {
                "success": True,
                "skipped": True,
                "message": f"User already exists in database: {data['phone_number']}"
            }
        
        # Get artisan role ID
        artisan_role_id = await get_artisan_role_id(db)
        if not artisan_role_id:
            logger.error(f"Artisan role not found in database for user: {data['phone_number']}")
            return None
        
        # Set default working hours (9 AM to 7 PM)
        work_from_hrs = time(9, 0)  # 9:00 AM
        work_to_hrs = time(19, 0)  # 7:00 PM

        # Set default weekdays (1-7, Monday to Sunday)
        weekdays = [1, 2, 3, 4, 5, 6, 7]

        # Parse phone number correctly
        phone_number = data["phone_number"]
        if phone_number.startswith("+"):
            # Extract country code and phone number properly
            # For "+233203467279" -> country_code="233", phone="203467279"
            phone_without_plus = phone_number[1:]  # Remove the "+"
            country_code = "+233"  # Default for Ghana with +
            # Remove country code from phone number
            if phone_without_plus.startswith("233"):
                phone_only = phone_without_plus[3:]  # Remove "233"
            else:
                phone_only = phone_without_plus
        else:
            country_code = "+233"
            phone_only = phone_number

        # Prepare UserProfiles data
        user_data = {
            "auth_id": data["auth_id"] if "auth_id" in data else "",
            "first_name": data["first_name"] if "first_name" in data else "",
            "last_name": data["surname"] if "surname" in data else "",
            "country_code": country_code,  # Store with + prefix
            "phone_number": phone_only,    
            "email": None,
            "gender":None,  # Default value, can be updated later
            "location": f"{data['district']}, {data['region']}",
            "status": UserStatusType.APPROVED,
            "notification_token": data["notification_token"] if "notification_token" in data else "",
            "is_profile_complete": True,
            "role_id": uuid.UUID(artisan_role_id),
            
        }

        # Prepare ArtisanDetail data
        artisan_data = {
            "skill": data['trade_area'],
            "skill_level": "Trainee",
            "reg_code": data['reg_code'],
            "work_from_hrs": work_from_hrs,
            "work_to_hrs": work_to_hrs,
            "weekdays": weekdays,
            "experience": 0.0,  # Default
            "is_confirmed": True,  # Since status was APPROVED
        }

        # Prepare Address data
        address_data = {
            "name": "Primary Address",
            "locality": f"{data['district']}, {data['region']}",
            "details": {
                "latitude": data['latitude'],
                "longitude": data['longitude'],
                "district": data['district'],
                "region": data['region'],
                "coordinates": f"POINT({data['longitude']} {data['latitude']})"
            },
            "is_primary": True,
        }

        # Get services for the category
        if category_id:
            services = await get_services_by_category_id(db, category_id)
            logger.info(f"Found {len(services)} services for category")
        else:
            # No category found, use empty services list
            services = []
            logger.info("No category found, using empty services list")

        # Create complete artisan user
        user_obj = await create_artisan_user(
            db, user_data, artisan_data, address_data, services, data["auth_id"]
        )
        
        if not user_obj:
            logger.error(f"Failed to create artisan user: {data['phone_number']}")
            return None

        logger.info(f"Successfully created artisan user with ID: {user_obj.id} for phone: {data['phone_number']}")
        
        # Commit all changes
        await db.commit()
    except Exception as e:
        logger.error(f"Error processing {data['phone_number']}: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        await db.rollback()
    finally:
        await db.close()
