import json
from app.routes.artisan_rating import get_avg_rating_by_user
from app.utils.helper import get_available_artisans_query
from app.models import ArtisanAssigned
from sqlalchemy.orm import aliased
from sqlalchemy.sql import exists
from fastapi import Depends, APIRouter, Header # HTTPException
from sqlalchemy.orm import Session
from datetime import datetime, timedelta # timedelta
from app.models import ServiceProvider, ArtisanDetail, UserProfiles, Services
from app.schemas.service_provider import LiveArtisan
from app.models import Booking
from app.database import get_db
from typing import Annotated
from geoalchemy2.functions import ST_DWithin, ST_MakePoint, ST_Point
from sqlalchemy import and_, or_, func, cast, Integer
from typing import List
from app.schemas.helper import StandardResponse, ErrorResponse
from app.schemas.service_provider import ArtisanSearchRequest
from app.utils.google_maps import get_distance
from sqlalchemy import not_
from app.utils.auth import get_id_header
from datetime import date
from app.redis_config import redis_client
# from app.utils.auth import permission_checker
from fastapi.responses import JSONResponse


router = APIRouter(prefix="/search_artisan", tags=["Search Artisan"])

# For Live location
@router.post("/search_live_artisan", response_model=StandardResponse)
async def search_live_artisa_v2(
    request: LiveArtisan,
    db: Session = Depends(get_db)
):
    print(request.__dict__, "request")
    """
    Search for available artisans near the given location using Redis.
    - Filters by service ID
    - Checks for active status
    - Filters out artisans with active bookings
    """
    try:
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user
        service_id = request.service_id

        service_obj = db.query(Services).filter(Services.id == service_id).first()
        service_name = service_obj.name if service_obj else "Unnamed Service"

        service_geo_key = f"geo:service:{request.service_id}"

        current_weekday = date.today().isoweekday()

        print(current_weekday, "current_weekday")


        # Get artisans within the max_distance using GEOSEARCH
        artisan_ids = redis_client.georadius(
            service_geo_key,
            longitude=request.longitude,
            latitude=request.latitude,
            radius=request.max_distance,  # Convert km to meters
            unit="km",
        )

        if not artisan_ids:
            return StandardResponse(
                status_code=200, message="No artisans found", data=[]
            )

        # Fetch artisan details from Redis
        pipeline = redis_client.pipeline()
        for artisan_id in artisan_ids:
            pipeline.hgetall(f"artisan:{artisan_id}")
        artisan_data_list = pipeline.execute()

        print(artisan_data_list, "artisan_data_list")

        redis_artisan_dict = {}
        for i, artisan_data in enumerate(artisan_data_list):
            if artisan_data:  # Ensure the data isn't empty
                artisan_data['booking_id'] = None
                artisan_data['booking_order_id'] = None
                artisan_id = artisan_ids[i]
                artisan_lock_key = f"artisan_lock:{artisan_id}"
                artisan_data['is_locked'] = True if redis_client.exists(artisan_lock_key) else False
                # Check artisan have any ongoing bookings
                booking_exists = db.query(ArtisanAssigned).filter(ArtisanAssigned.artisan_id == artisan_id, ArtisanAssigned.status.in_(["ASSIGNED","STARTED","ONGOING"])).first()
                if booking_exists and artisan_data.get('status') == "online":
                    artisan_data['status'] = "busy"
                    try:
                        booking_obj = db.query(Booking).filter(Booking.id == booking_exists.invoice_id).first()
                        artisan_data['booking_id'] = booking_obj.id
                        artisan_data['booking_order_id'] = booking_obj.booking_order_id
                    except:
                        pass

                weekdays = artisan_data.get("weekdays", [])
                print(weekdays, "weekdays")
                try:
                    if weekdays:
                        weekdays = json.loads(weekdays)
                        if current_weekday not in weekdays:
                            continue
                except:
                    continue
                artisan_data["id"] = str(artisan_id)
                redis_artisan_dict[str(artisan_id)] = (artisan_data)

        print(redis_artisan_dict, "redis_artisan_dict")
        # Fetch artisans from Postgres
        artisans = (db.query(ArtisanDetail, UserProfiles).join(UserProfiles, ArtisanDetail.user_id == UserProfiles.id).filter(ArtisanDetail.user_id.in_(artisan_ids)).all())
        print(artisans, "artisans")
        active_artisans = []
        for artisan, userprofile in artisans:
            try:
                artisan_redis_data = redis_artisan_dict[str(userprofile.id)]
                # Calculate actual driving distance
                origin = (request.latitude, request.longitude)
                destination = (artisan_redis_data['live_latitude'], artisan_redis_data['live_longitude'])
                actual_distance, actual_duration = get_distance(origin, destination)
                if actual_distance == None:
                    print(f"We cant travel to this location {origin}, -- {destination}")
                    continue

                # Calculate average rating
                avg_rating = get_avg_rating_by_user(db, str(userprofile.id))
                print(avg_rating, "avg_rating")

                redis_artisan_dict[str(userprofile.id)].update({
                        "service_name" : service_name,
                        "profile_pic" : userprofile.profile_image_url,
                        "first_name" : userprofile.first_name,
                        "last_name" : userprofile.last_name,
                        "rating" : avg_rating,
                        "distance" : actual_distance['text'],
                        "time" : actual_duration['text']
                    })
                active_artisans.append(redis_artisan_dict[str(userprofile.id)])
            except:
                print(f"Error processing artisan {userprofile.id}: {artisan_redis_data}")
                continue

        print(active_artisans, "active artisans")

        if not active_artisans:
            return StandardResponse(
                status_code=200, message="No available artisans found", data=[]
            )

        start_index = (request.page - 1) * request.limit
        end_index = start_index + request.limit
        print(start_index, end_index, "start_index, end_index")

        # Slice the list for the current page
        paginated_artisans = active_artisans[start_index:end_index]

        print(paginated_artisans, "paginated_artisans")

        return StandardResponse(
            status_code=200, message="Neraby artisans found", data=paginated_artisans
        )
    except Exception as e:
        print(f"Error in search_live_artisan: {e}")
        return ErrorResponse(status_code=500, message="Neraby artisans not found", error=str(e))
    

# For Base location
# @router.post("/search_artisan")
# async def search_artisan(request: ArtisanSearchRequest, db: Session = Depends(get_db)):
#     """Search for artisans based on service, location, availability, and existing bookings"""
#     try:
#         # if isinstance(user, JSONResponse):  # Permission failed
#         #     return user
#         # Convert string times to time objects
#         start_time = request.start_time
#         end_time = request.end_time
#         booking_date = request.booking_date
#         req_lat = request.latitude
#         req_long = request.longitude
#         print(request.__dict__, "request")

#         # Get current weekday (1 = Monday, 7 = Sunday)
#         current_weekday = booking_date.isoweekday()

#         # Sorting logic
#         sort_column = ServiceProvider.rating.desc()  # Default sorting by rating
#         if request.sort_by == "distance":
#             sort_column = func.ST_Distance(
#                 ST_Point(ServiceProvider.longitude, ServiceProvider.latitude),
#                 ST_MakePoint(request.longitude, request.latitude),
#             ).asc()
#         elif request.sort_by == "availability":
#             sort_column = ServiceProvider.work_from_hrs.asc()

#         request_data = {
#             'booking_date': booking_date,
#             'start_time': start_time,
#             'end_time': end_time,
#             'req_lat': req_lat,
#             'req_long': req_long,
#             'service_id': request.service_id,
#             'max_distance': request.max_distance,
#             'service_provider_id': request.service_provider_id,
#             'current_weekday': current_weekday,
#         }
#         artisans_query = get_available_artisans_query(db, request_data)

#         # Apply sorting and pagination
#         artisans_query = artisans_query.order_by(sort_column).offset((request.page - 1) * request.limit).limit(request.limit)

#         artisans = artisans_query.all()
#         print(artisans, "artisans")

#         # 3. Additional distance check using Google Maps Distance Matrix API
#         filtered_artisans = []

#         for artisan in artisans:
#             # Calculate actual driving distance
#             origin = (request.latitude, request.longitude)
#             destination = (artisan.latitude, artisan.longitude)

#             actual_distance = get_distance(origin, destination)
#             if actual_distance == None:
#                 print(f"We cant travel to this location {origin}, -- {destination}")
#                 continue

#             # print(actual_distance, artisan.ServiceProvider.__dict__, "actual_dist")

#             # Calculate average rating
#             avg_rating = get_avg_rating(db, artisan_id=str(artisan.id))

#             # Only include artisans within 100 km driving distance
#             if actual_distance <= 100:
#                 filtered_artisans.append(
#                     {
#                         "artisan_id": artisan.id,
#                         "profile_pic": artisan.profile_pic,
#                         "first_name": artisan.first_name,
#                         "last_name": artisan.last_name,
#                         "latitude": artisan.latitude,
#                         "longitude": artisan.longitude,
#                         "service_id": request.service_id,
#                         "skill": artisan.skill,
#                         "experience": artisan.experience,
#                         "about_us": artisan.about_us,
#                         "country_code": artisan.country_code,
#                         "rating": avg_rating,
#                         "distance_km": round(actual_distance, 2),  # Use actual driving distance
#                     }
#                 )
#         print("checkpoint", filtered_artisans)
#         # Apply pagination after filtering
#         start_idx = (request.page - 1) * request.limit
#         end_idx = start_idx + request.limit
#         paginated_artisans = filtered_artisans[start_idx:end_idx]

#         return StandardResponse(
#             status_code=200, message="Matching artisans found", data=filtered_artisans
#         )
#     except Exception as e:
#         return ErrorResponse(status_code=500, message="Matching artisans not found", error=str(e))
    

