import json
from app.routes.artisan_rating import get_avg_rating
from app.utils.helper import get_available_artisan_ids, get_conflict_artisan_ids
from sqlalchemy.orm import aliased
from sqlalchemy.sql import exists
from fastapi import Depends, APIRouter, Header # HTTPException
from sqlalchemy.orm import Session
from datetime import datetime, timedelta # timedelta
from app.models import (
    ServiceProvider,
    ServiceProviderServiceMapping,
    ServiceProviderLeave,
)
from app.schemas.service_provider import LiveArtisan
from app.models import Booking
from app.database import get_db
from typing import Annotated
from geoalchemy2.functions import ST_DWithin, ST_MakePoint, ST_Point
from sqlalchemy import and_, or_, func, cast, Integer
from typing import List
from app.schemas.helper import StandardResponse, ErrorResponse
from app.schemas.service_provider import ArtisanSearchRequest
from app.utils.google_maps import get_distance
from sqlalchemy import not_
from app.utils.auth import get_id_header
from datetime import date
from app.redis_config import redis_client


router = APIRouter(prefix="/search_artisan", tags=["Search Artisan"])


@router.post(
    "/search_artisan",
    #   response_model=StandardResponse
)
async def search_artisan(request: ArtisanSearchRequest, db: Session = Depends(get_db), Authorization: Annotated[str, Header()] = None,):
    """Search for artisans based on service, location, availability, and existing bookings"""
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")
        # Convert string times to time objects
        start_time = request.start_time
        end_time = request.end_time
        booking_date = request.booking_date
        req_lat = request.latitude
        req_long = request.longitude
        min_rating = 0  # Default rating filter
        print(request.__dict__, "request")

        buffer = timedelta(hours=1)

        # Calculate buffer time (1 hour before & after)
        buffer_start_time = (
            # 1 hour buffer commented
            # datetime.combine(booking_date, start_time) - timedelta(hours=1)
            datetime.combine(booking_date, start_time)
        ).time()
        buffer_end_time = (
            # datetime.combine(booking_date, end_time) + timedelta(hours=1)
            datetime.combine(booking_date, end_time)
        ).time()

        # Get current weekday (1 = Monday, 7 = Sunday)
        current_weekday = booking_date.isoweekday()

        # Sorting logic
        sort_column = ServiceProvider.rating.desc()  # Default sorting by rating
        if request.sort_by == "distance":
            sort_column = func.ST_Distance(
                ST_Point(ServiceProvider.longitude, ServiceProvider.latitude),
                ST_MakePoint(request.longitude, request.latitude),
            ).asc()
        elif request.sort_by == "availability":
            sort_column = ServiceProvider.work_from_hrs.asc()

        # Alias the Booking table for filtering booked artisans
        BookingAlias = aliased(Booking)

        # artisan_id_from_booking = None
        # if request.booking_id:
        #     booking_record = db.query(Booking).filter(Booking.id == request.booking_id).first()
        #     if not booking_record:
        #         return ErrorResponse(status_code=404, message="Booking not found")
        #     artisan_id_from_booking = booking_record.artisan_id


        # Experience range filter conditions
        experience_filter = None
        if request.experience_range is not None:
            if request.experience_range == "less_than_5":
                experience_filter = ServiceProvider.experience < 5
            elif request.experience_range == "more_than_5":
                experience_filter = ServiceProvider.experience >= 5
            elif request.experience_range == "more_than_10":
                experience_filter = ServiceProvider.experience >= 10


        request_data = {
            'booking_date': booking_date,
            'start_time': start_time,
            'end_time': end_time,
            'req_lat': req_lat,
            'req_long': req_long,
            'service_id': request.service_id,
            'max_distance': request.max_distance,
            'service_provider_id': request.service_provider_id,
            'rating': request.rating,
            'experience_range': request.experience_range,
            'current_weekday': current_weekday,
        }
        artisans_query = get_available_artisan_ids(db, request_data)
        # print(artisans_query, "xxxxxxxxxxxxx")
        # print(hello)

        # conflict_subquery = get_conflict_artisan_ids(db, start_time, end_time)
        # print(conflict_subquery, 'conflict_subquery')

        # Query for available artisans
        # artisans_query = (
        #     db.query(
        #         ServiceProvider,
        #         func.ST_Distance(
        #             ST_Point(ServiceProvider.longitude, ServiceProvider.latitude),
        #             ST_MakePoint(request.longitude, request.latitude),
        #         ).label("distance_km"),
        #     )
        #     .join(
        #         ServiceProviderServiceMapping,
        #         ServiceProvider.id == ServiceProviderServiceMapping.service_provider_id,
        #     )
        #     .filter(
        #         ServiceProviderServiceMapping.services_id
        #         == request.service_id,  # Filter by service
        #         ServiceProvider.status == "approved",
        #         # ServiceProvider.rating >= min_rating,
        #         # ServiceProvider.status == "online",  # Only online artisans
        #         ST_DWithin(
        #             ST_Point(ServiceProvider.longitude, ServiceProvider.latitude),
        #             ST_MakePoint(request.longitude, request.latitude),
        #             request.max_distance * 1000,  # Convert km to meters
        #         ),
        #         cast(current_weekday, Integer)
        #         == func.any(ServiceProvider.weekdays),  # Check weekday availability
        #         and_(
        #             ServiceProvider.work_from_hrs <= start_time,
        #             ServiceProvider.work_to_hrs >= end_time,
        #         ),
        #         and_(ServiceProvider.latitude != 0, ServiceProvider.longitude != 0),
        #         # 1. Check that start time is not within break hours
        #         or_(
        #             ServiceProvider.break_from_hrs.is_(None),  # If no break is set
        #             and_(
        #                 not_(
        #                     and_(
        #                         start_time >= ServiceProvider.break_from_hrs,
        #                         start_time < ServiceProvider.break_to_hrs,
        #                     )
        #                 ),
        #                 # not_(and_(
        #                 #     end_time > ServiceProvider.break_from_hrs,
        #                 #     end_time <= ServiceProvider.break_to_hrs
        #                 # )),
        #                 # not_(and_(
        #                 #     start_time <= ServiceProvider.break_from_hrs,
        #                 #     end_time >= ServiceProvider.break_to_hrs
        #                 # ))
        #             ),
        #         ),
        #         # Check for leaves
        #         ~exists().where(
        #             and_(
        #                 ServiceProviderLeave.service_provider_id == ServiceProvider.id,
        #                 ServiceProviderLeave.leave_date == booking_date,
        #             )
        #         ),
        #         *( [ServiceProvider.id == request.service_provider_id] if request.service_provider_id else [] ),
        #         # 2. Enhanced booking conflict check with buffer
        #         # ~exists().where((BookingAlias.start_time - buffer) < end_time,
        #         #     (BookingAlias.end_time + buffer) > start_time,
        #         # )
        #     )
        # )

        # print(artisans_query, "artisans_query")

        # Apply buffer logic
        # conflict_subquery = get_conflict_artisan_ids(db, start_time, end_time)
        # available_artisans = (db.query(ServiceProvider)
        #         .filter(~Artisan.id.in_(select(conflict_subquery.c.artisan_id)))
        #         .all()
        #     )

        # Add experience filter if specified
        if experience_filter is not None:
            artisans_query = artisans_query.filter(experience_filter)

        # Apply sorting and pagination
        artisans_query = artisans_query.order_by(sort_column).offset((request.page - 1) * request.limit).limit(request.limit)

        artisans = artisans_query.all()

        # 3. Additional distance check using Google Maps Distance Matrix API
        filtered_artisans = []

        for artisan in artisans:
            # Calculate actual driving distance
            origin = (request.latitude, request.longitude)
            destination = (
                artisan.ServiceProvider.latitude,
                artisan.ServiceProvider.longitude,
            )

            actual_distance = get_distance(origin, destination)
            if actual_distance == None:
                print(f"We cant travel to this location {origin}, -- {destination}")
                continue

            print(actual_distance, artisan.ServiceProvider.__dict__, "actual_dist")

            # Calculate average rating
            avg_rating = get_avg_rating(db, artisan_id=str(artisan.ServiceProvider.id))

            # Apply rating filter if specified
            if request.rating is not None and request.rating != 0:  # Only apply filter if rating is not 0
                if request.rating == 5:
                    if avg_rating != 5.0:
                        continue
                else:
                    if not (float(request.rating) <= avg_rating < float(request.rating + 1)):
                        continue

            # Only include artisans within 100 km driving distance
            if actual_distance <= 100:
                filtered_artisans.append(
                    {
                        "artisan_id": artisan.ServiceProvider.id,
                        # "auth_id": artisan.ServiceProvider.auth_id,
                        "profile_pic": artisan.ServiceProvider.profile_pic,
                        "first_name": artisan.ServiceProvider.first_name,
                        "last_name": artisan.ServiceProvider.last_name,
                        "latitude": artisan.ServiceProvider.latitude,
                        "longitude": artisan.ServiceProvider.longitude,
                        "service_id": request.service_id,
                        "skill": artisan.ServiceProvider.skill,
                        "experience": artisan.ServiceProvider.experience,
                        "about_us": artisan.ServiceProvider.about_us,
                        "country_code": artisan.ServiceProvider.country_code,
                        "rating": avg_rating,
                        "radius_km": round(
                            artisan.distance_km / 1000, 2
                        ),  # Use actual driving distance
                        "distance_km": round(
                            actual_distance, 2
                        ),  # Use actual driving distance
                    }
                )
        print("checkpoint", filtered_artisans)
        # Apply pagination after filtering
        start_idx = (request.page - 1) * request.limit
        end_idx = start_idx + request.limit
        paginated_artisans = filtered_artisans[start_idx:end_idx]

        return StandardResponse(
            status_code=200, message="Matching artisans found", data=filtered_artisans
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Matching artisans not found", error=str(e))


@router.post("/search_live_artisan", response_model=StandardResponse)
async def search_live_artisan(
    request: LiveArtisan,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    print(request.__dict__, "request")
    """
    Search for available artisans near the given location using Redis.
    - Filters by service ID
    - Checks for active status
    - Filters out artisans with active bookings
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")

        account_id = resp.json().get("account_id")

        service_geo_key = f"geo:service:{request.service_id}"

        current_weekday = date.today().isoweekday()

        print(current_weekday, "current_weekday")


        # Get artisans within the max_distance using GEOSEARCH
        artisan_ids = redis_client.georadius(
            service_geo_key,
            longitude=request.longitude,
            latitude=request.latitude,
            radius=request.max_distance,  # Convert km to meters
            unit="km",
        )

        if not artisan_ids:
            return StandardResponse(
                status_code=200, message="No artisans found", data=[]
            )

        # Fetch artisan details from Redis
        pipeline = redis_client.pipeline()
        for artisan_id in artisan_ids:
            pipeline.hgetall(f"artisan:{artisan_id}")
        artisan_data_list = pipeline.execute()

        print(artisan_data_list, "artisan_data_list")

        redis_artisan_dict = {}
        for i, artisan_data in enumerate(artisan_data_list):
            if artisan_data:  # Ensure the data isn't empty
                weekdays = artisan_data.get("weekdays", [])
                print(weekdays, "weekdays")
                try:
                    if weekdays:
                        weekdays = json.loads(weekdays)
                        if current_weekday not in weekdays:
                            continue
                except:
                    continue
                artisan_id = artisan_ids[i]
                artisan_data["id"] = str(artisan_id)  # Ensure id is string for matching
                redis_artisan_dict[str(artisan_id)] = (
                    artisan_data  # Store in dictionary
                )

        # Fetch artisans from Postgres
        artisans = (
            db.query(ServiceProvider)
            .filter(
                ServiceProvider.id.in_(artisan_ids),
                ServiceProvider.status == "approved"  # Add approved status filter
            )
            .all()
        )

        # Convert Postgres objects to a dictionary with id as key
        def serialize_artisan(artisan):
            origin = (request.latitude, request.longitude)
            destination = (
                artisan.latitude,
                artisan.longitude,
            )

            # actual_distance = get_distance(origin, destination)
            return {
                "artisan_id": str(artisan.id),  # Convert UUID to string for consistency
                # "auth_id": str(artisan.auth_id),
                "profile_pic": artisan.profile_pic,
                "first_name": artisan.first_name,
                "last_name": artisan.last_name,
                "latitude": artisan.latitude,
                "longitude": artisan.longitude,
                "experience": artisan.experience,
                "about_us": artisan.about_us,
                "country_code": artisan.country_code,
                # "service_id": artisan.service_id,
                "skill": artisan.skill,
                "rating": get_avg_rating(
                    db, artisan_id=str(artisan.id)
                ),
                # "radius_km": round(
                #     artisan.distance_km / 1000, 2
                # ),  # Use actual driving distance
                # "distance_km": round(actual_distance, 2),
                # "current_latitude": float(artisan.current_latitude),
                # "current_longitude": float(artisan.current_longitude),
            }

        postgres_artisan_dict = {
            str(artisan.id): serialize_artisan(artisan) for artisan in artisans
        }
        # Merge Redis data with Postgres data
        final_filtered_artisans = []
        for artisan_id, artisan_db_data in postgres_artisan_dict.items():
            if artisan_id in redis_artisan_dict:
                # Preserve Redis `current_latitude` and `current_longitude`
                current_lat = redis_artisan_dict[artisan_id].get("current_latitude")
                current_lng = redis_artisan_dict[artisan_id].get("current_longitude")

                # Merge DB details into Redis but keep `current_latitude` and `current_longitude`
                redis_artisan_dict[artisan_id].update(artisan_db_data)

                # Restore Redis latitude/longitude
                redis_artisan_dict[artisan_id]["current_latitude"] = current_lat
                redis_artisan_dict[artisan_id]["current_longitude"] = current_lng
                final_filtered_artisans.append(redis_artisan_dict[artisan_id])
            # else:
            #     # If not in Redis, just add DB data (without current_lat/lng)
            #     redis_artisan_dict[artisan_id] = artisan_db_data
        print(final_filtered_artisans, "final_filtered_artisans")
        # Convert the merged dictionary back into a list
        # merged_list = list(redis_artisan_dict.values())

        # print(merged_list, "merged_list")
        # Filter active artisans & extract relevant details
        active_artisans = final_filtered_artisans
        # for artisan_id, artisan_data in zip(artisan_ids, artisan_data_list):
        #     if not artisan_data or artisan_data["status"] != "online":
        #         continue
        #     print(artisan_data)
        #     active_artisans.append(
        #         {
        #             "auth_id": artisan_id,
        #             "current_latitude": float(artisan_data.get("current_latitude", 0)),
        #             "current_longitude": float(
        #                 artisan_data.get("current_longitude", 0)
        #             ),
        #         }
        #     )

        if not active_artisans:
            return StandardResponse(
                status_code=200, message="No available artisans found", data=[]
            )
        print(active_artisans, "active artisans")
        # Get active bookings for today
        today = date.today()
        booked_artisans = (
            db.query(Booking.artisan_id).filter(Booking.booking_date == today, Booking.status.in_(["pending", "confirmed", "started", "arrived", "ongoing"])).all()
        )
        booked_artisan_ids = {str(artisan.artisan_id) for artisan in booked_artisans}

        # Filter out booked artisans
        final_artisans = [
            artisan
            for artisan in active_artisans
            if artisan["artisan_id"] not in booked_artisan_ids
        ]

        # Apply rating filter if specified
        if request.rating is not None and request.rating != 0:
            final_artisans = [
                artisan for artisan in final_artisans
                if (request.rating == 5 and artisan["rating"] == 5.0) or
                   (request.rating != 5 and float(request.rating) <= artisan["rating"] < float(request.rating + 1))
            ]

        # Apply experience range filter if specified
        if request.experience_range is not None:
            if request.experience_range == "less_than_5":
                final_artisans = [artisan for artisan in final_artisans if artisan["experience"] < 5]
            elif request.experience_range == "more_than_5":
                final_artisans = [artisan for artisan in final_artisans if artisan["experience"] >= 5]
            elif request.experience_range == "more_than_10":
                final_artisans = [artisan for artisan in final_artisans if artisan["experience"] >= 10]

        print(final_artisans, "final_artisans")

        # Pagination logic: calculate the starting and ending indices
        start_index = (request.page - 1) * request.limit
        end_index = start_index + request.limit
        print(start_index, end_index, "start_index, end_index")

        # Slice the list for the current page
        paginated_artisans = final_artisans[start_index:end_index]

        print(paginated_artisans, "paginated_artisans")

        return StandardResponse(
            status_code=200, message="Matching artisans found", data=paginated_artisans
        )
    except Exception as e:
        print(f"Error in search_live_artisan: {e}")
        return ErrorResponse(status_code=500, message="Matching artisans not found", error=str(e))

