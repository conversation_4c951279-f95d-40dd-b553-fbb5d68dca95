import json
from app.kafka_consumer.config import create_service_request_consumer, create_invoice_consumer
from app.utils.helper import create_service_request
from app.service_calls.invoice import create_invoice
from app.database import get_db_session
from app.models import Cart
from app.models_enum import CartStatus

db = get_db_session()


service_request_consumer = create_service_request_consumer()
invoice_consumer = create_invoice_consumer()

async def consume_service_request_consumer():
    async for msg in service_request_consumer:
        print(f"Got msg at offset {msg.offset}")
        data = msg.value.decode()
        data_dict = json.loads(data)
        try:
            resp = await create_service_request(data_dict)
            if resp['status'] == 200:
                await service_request_consumer.commit()
                print(f"Committed offset {msg.offset}")
                # db = session_local
                db.query(Cart).filter(Cart.status == CartStatus.PENDING, Cart.user_id == data_dict['user_id']).update({Cart.status: CartStatus.CHECKED_OUT},synchronize_session=False)
                db.commit()
            else:
                print(f"Failed to process message at offset {msg.offset}")
        except BaseException as err:
            print(f"Exception caused due to consume_service_request: {err}")
            print(f"Error: {err} – Not committing offset {msg.offset}")


async def consume_invoice_consumer():
    async for msg in invoice_consumer:
        print(f"Got msg at offset {msg.offset}")
        data = msg.value.decode()
        data_dict = json.loads(data)
        try:
            status = await create_invoice(data_dict)
            if status == 200:
                await invoice_consumer.commit()
                print(f"Committed offset {msg.offset}")
            else:
                print(f"Failed to process message at offset {msg.offset}")
        except BaseException as err:
            print(f"Exception caused due to: {err}")
            print(f"Error: {err} – Not committing offset {msg.offset}")
