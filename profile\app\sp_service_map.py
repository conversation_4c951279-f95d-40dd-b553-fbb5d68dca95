import uuid

service_ids = [
    "1b75c173-51cf-4b00-9248-367f03fa8034",
    "1e4a02a9-5d1e-406c-a52a-4fcf9f1cd953",
    "1ebfe089-9fc7-4f14-a248-8efbde8100d7",
    "9029c831-a036-4c1d-8e5f-63ca2d13ed79",
    "9393310d-b41c-4182-8cf9-bcf2948500fe",
    "9f9f4d82-6781-4e90-8e76-951cdb4ea36a",
]

service_provider_ids = [
    "12750e0e-52b8-4938-a28c-de86fcbc90e2",
    "2827e5f0-2efb-4c4f-bcdd-8924fc7282da",
    "2a8b9088-d9a4-47bf-aed9-a24549b7b27f",
    "3e74f88c-9598-480e-bf2b-fd73bf629fc1",
    "58e356d3-b172-48ee-b8f9-1f5b8350ba43",
    "6c8da359-e6c1-43f5-a8a3-4260ea2240d2",
    "757088e2-fcf2-4742-9d72-d41c8f8765fa",
    "8c2c88a5-9b1c-4078-a93d-14ac9c60a586",
    "99d3e466-4177-4bc1-8f80-4f499ffd09b8",
    "9d523cdc-5d0c-4967-9873-5e9ff88574e4",
    "a6611c7f-beda-4785-af2e-23da669331e5",
    "c061702b-8af6-4027-8208-4a2588fe7913",
]

sql_statements = []
for provider in service_provider_ids:
    for service in service_ids:
        mapping_id = uuid.uuid4()
        sql_statements.append(
            f"('{mapping_id}', '{provider}', '{service}')"
        )

sql_query = f"""
INSERT INTO service_provider_service_mapping (id, service_provider_id, services_id)
VALUES {', '.join(sql_statements)};
"""

print(sql_query)
