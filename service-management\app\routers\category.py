import uuid
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, Header
from app.s3_upload import upload_file_direct, s3_delete_file, S3_IMAGES_FOLDER, S3_DOCS_FOLDER
from sqlalchemy.orm import Session
from sqlalchemy import or_
from pathlib import Path
from app import schemas
from app.database import get_db
from app.utils import create_record, delete_record, get_id_header, is_valid_uuid, update_record
from typing import Optional
from app.hash import cognito_secret_hash
from sqlalchemy.future import select
from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Annotated, List
from app.file_uploader import delete_file, upload_file
from app.config import get_settings
from sqlalchemy.orm import selectinload
from app.helper import StandardResponse, ErrorResponse, StandardResponseWithoutSerialize
from app.models import Category
import json
# from hash import generate_salt, hash_password

router = APIRouter()

@router.post("/category-create")
async def create_category(
    request: schemas.CreateCategory = Depends(),
    banner: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            email = resp_json.get("email")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        request_dict = request.to_dict()

        if banner:
            # Calling S3 upload function
            profile_url = upload_file_direct(banner, path=S3_IMAGES_FOLDER)
            print(profile_url, "profile_url")
            request_dict["banner"] = profile_url['filename']
        
        new_category = await create_record(db, Category, request_dict)
        if isinstance(new_category, str):
            return ErrorResponse(
                status_code=400,
                message=new_category
            )
        return StandardResponse(
            status_code=200,
            message="Category created successfully",
            data={'data': 'Category created successfully'}
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )


@router.get("/category-read/{id}")
async def read_category(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        query = select(Category).filter(Category.id == uuid.UUID(id))
        result = await db.execute(query)
        category_obj = result.scalars().first()
        return StandardResponse(
            status_code=200,
            message="Category read successfully",
            data=category_obj
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )
    

async def get_categories_with_services(db, category_id):
    query = select(Category).filter(Category.parent_id == category_id)
    # query = select(Category).filter(Category.parent_id == category_id).options(selectinload(Category.services))
    result = await db.execute(query)
    categories = result.scalars().all()
    # return StandardResponse(
    #     status_code=200,
    #     message="Category read successfully",
    #     data=categories
    # )
    return categories

@router.get("/category-list")
async def list_category(
    q: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )
        
        # Total category
        query = select(Category).filter(Category.parent_id == None, Category.is_active == True)
        # query = select(Category).filter(Category.is_active == True)
        result = await db.execute(query)
        categories = result.scalars().all()

        lst = []
        for category_obj in categories:
            category_data = category_obj.__dict__
            sub_services = await get_categories_with_services(db, category_data['id'])
            category_data['sub_category'] = sub_services
            lst.append(category_data)

        count_query = select(func.count()).select_from(Category)
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()
        print(lst)
        return StandardResponse(
            status_code=200,
            message="Category read successfully",
            data={"total": total_count, "category": lst}
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )
    


@router.put("/category-update/{id}")
async def update_category(
    id: str,
    data: schemas.UpdateCategory = Depends(),
    banner: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        print(data.parent_id, "parent_id")
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )
        
        query = select(Category).filter(Category.id == uuid.UUID(id))
        result = await db.execute(query)
        get_category = result.scalars().first()
        print(get_category)
        if banner:
            if get_category.banner:
                s3_delete_file(get_category.banner)
            # Calling S3 upload function
            profile_url = upload_file_direct(banner, path=S3_IMAGES_FOLDER)
            setattr(get_category, "banner", profile_url['filename'])

        res = await update_record(db, data, get_category)
        return StandardResponse(
            status_code=200,
            message="Category updated successfully",
            data=res
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )
    
@router.delete("/category-delete/{id}")
async def delete_category(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )
        
        query = select(Category).filter(Category.id == uuid.UUID(id))
        result = await db.execute(query)
        get_category = result.scalars().first()

        if get_category is None:
            return ErrorResponse(
                status_code=404,
                message="Category not found"
            )
        
        if get_category.banner:
            s3_delete_file(get_category.banner)
        
        res = await delete_record(db, get_category)
        return StandardResponse(
            status_code=200,
            message="Category deleted successfully",
            data=res
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )

@router.get("/category-list-simple")
async def list_category_simple(
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Get a simplified list of categories with minimal data to avoid recursion issues.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )
        
        # Get categories with minimal data
        query = select(Category).filter(Category.is_active == True)
        result = await db.execute(query)
        categories = result.scalars().all()

        # Create a simplified list with only the data we need
        simple_categories = []
        for category in categories:
            simple_categories.append({
                "id": str(category.id),
                "name": category.name
            })

        return {
            "status_code": 200,
            "message": "Categories fetched successfully",
            "data": {
                "total": len(simple_categories),
                "data": simple_categories
            }
        }
    except Exception as e:
        return {
            "status_code": 500,
            "message": f"Error: {e}",
            "error": str(e)
        }
