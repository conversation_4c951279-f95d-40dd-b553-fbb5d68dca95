import uuid
import random
from fastapi import APIR<PERSON>er, Depends, Header
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_
from typing import Optional, Annotated

from app.database import get_db
from app import models, schemas
from app.utils import (
    BookingStatus,
    check_datetime,
    create_record,
    get_gtipay_transaction_detail,
    update_payment_rec,
    update_record,
    delete_record,
    get_id_header,
    is_valid_uuid,
)
from app.notification import send_push_notification
import json
import requests
from app.config import settings

# from app.utils import TransactionTypeType, StatusType
# from app.models import Payment, Transaction
from app.utils import TransactionTypeType, StatusType, PaymentMethodType, get_surcharges, get_tax
from app.schemas import RefundPayment, RequestPayment, UpdateTransactionStatus

router = APIRouter()
from app.helper import StandardResponse, ErrorResponse
from app.models import Account, Booking, ServiceProvider, Transaction, Payment, Users
from sqlalchemy.sql import text

from app.models import Services, Category, Payment
import logging
from sqlalchemy import select, and_, select, join, outerjoin,or_
from sqlalchemy.orm import aliased
from sqlalchemy.orm.attributes import flag_modified
from app.utils import StatusType
from uuid import UUID

router = APIRouter()


@router.post("/refund-payment")
async def refund_payment(
    request: RefundPayment,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Update transaction status, gateway_status, and gateway_response in the Payment and Transaction tables.
    """
    try:
        request_amount = request.amount
        user_id = request.user_id
        payment_method = request.payment_method
        p_order_id = request.p_order_id
        payment_id = request.payment_id
        is_amount_refunded = False
        cancellation_id = request.cancellation_id

        print(payment_method, 'payment_method')
        print(payment_id, 'payment_id')
        print(request_amount, 'request_amount')

        resp = await get_id_header(Authorization)
        print(resp.json())
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        account_id = uuid.UUID(user_id)
        u_query = select(Account).filter(Account.user_id == account_id)
        u_result = await db.execute(u_query)
        u_account_obj = u_result.scalars().first()
        u_account_details = u_account_obj.account_details
        if u_account_obj is None:
            return ErrorResponse(status_code=404, message="Account not found")
        u_account_balance = u_account_obj.balance
        new_dict = u_account_obj

        if payment_method == "wallet":
            # Authenticate with the wallet API
            login_url = f"{settings.WALLET_API_URL}/Authentication/Login"
            login_payload = json.dumps(
                {
                    "username": settings.WALLET_USERNAME,
                    "password": settings.WALLET_PASSWORD,
                }
            )
            login_headers = {"accept": "*/*", "Content-Type": "application/json"}
            login_response = requests.request(
                "POST", login_url, headers=login_headers, data=login_payload
            )
            auth_token = json.loads(login_response.text)["token"]
            print("auth_token", auth_token)
            if auth_token:
                transact_id = str(random.randint(10**13, 10**14 - 1))
                # Creating Transaction Entry
                a_transaction = Transaction(
                    payment_id=payment_id,
                    from_entity=str(account_id),
                    payment_method=payment_method.upper(),
                    to_entity=str(account_id),
                    transaction_type=TransactionTypeType.REFUND,
                    status=StatusType.INIT,
                    amount=request_amount,
                    transaction_id=transact_id,
                    cancellation_id=cancellation_id
                )
                u_record = await create_record(db, Transaction, a_transaction.as_dict())

                receive_money_payload = json.dumps(
                    {
                        "accountName": u_account_details["walletName"],
                        "accountNumber": u_account_details["walletNumber"],
                        "amount": request_amount,
                        "channel": "MNO",
                        "institutionCode": u_account_details["walletCode"],
                        "transactionId": transact_id,
                        "creditNaration": "JOBCONNECTZ REFUND",
                        "currency": "GHS",
                        "currencyAmount": 0.00,
                        "originCountryCode": "",
                        "senderName": "jobconnectz",
                    }
                )
                receive_money_url = (
                    f"{settings.WALLET_API_URL}/SendMoney/SendMoneyService"
                )
                receive_money_headers = {
                    "accept": "text/plain",
                    "Authorization": f"Bearer {auth_token}",  # Use the token from the previous response
                    "Content-Type": "application/json",
                }
                receive_money_response = requests.post(
                    receive_money_url,
                    headers=receive_money_headers,
                    data=receive_money_payload,
                )
                print(receive_money_response.json(), "receive_money_response")
                receive_money_resp = receive_money_response.json()

                if receive_money_resp["statusCode"] in ["200", "202"]:
                    is_amount_refunded = True
                    # Updating Account Balance after payment refund through wallet
                    setattr(
                        u_account_obj, "balance", u_account_balance + request_amount
                    )
                    await db.commit()
                    await db.refresh(u_account_obj)
                else:
                    is_amount_refunded = False
            else:
                return ErrorResponse(
                    status_code=400,
                    message="ERROR",
                    error={"error": "Wallet authentication failed"},
                )
        elif payment_method == "card":
            # Getting transaction details from GTI API
            trans_resp = get_gtipay_transaction_detail(p_order_id)
            print(trans_resp, "trans respppp")
            if trans_resp.get("status") == "success":
                transact_id = trans_resp["data"]["transaction_history"][-1]["transaction_id"]
            else:
                return ErrorResponse(
                    status_code=400, message="ERROR", error={"error": str(trans_resp)}
                )
            
            # Creating Transaction Entry
            a_transaction = Transaction(
                payment_id=payment_id,
                from_entity=str(account_id),
                payment_method=payment_method.upper(),
                to_entity=str(account_id),
                transaction_type=TransactionTypeType.REFUND,
                status=StatusType.INIT,
                amount=request_amount,
                transaction_id=p_order_id,
            )
            u_record = await create_record(db, Transaction, a_transaction.as_dict())

            
            url = f"{settings.CARD_API_URL}/open/orders/transaction"
            receive_money_payload = json.dumps(
                {
                    "action": "REFUND",
                    "transaction_id": transact_id,
                    "amount": {"currencyCode": "GHS", "value": request_amount},
                    "reason": "jobconnectz refund",
                }
            )
            print(receive_money_payload, "receive_money_payload")
            # print(x)

            headers = {
                "merchant-key": settings.CARD_MERCHANT_KEY,
                "merchant-secret": settings.CARD_MERCHANT_SECRET,
                "Content-Type": "application/json",
                "Cookie": "AWSALB=8JFTC8y88vPc/fSN6IXX+pa8VVP3meHjNy1KdAi0FZKpLgeNlmJ+rEvky4Ho+hgTHDytrBWiSPWtRHOQMwqX1ZTfVz+RlkwVPg52gtFLiyRyU1iEDJU2Yf82Lq7K; AWSALBCORS=8JFTC8y88vPc/fSN6IXX+pa8VVP3meHjNy1KdAi0FZKpLgeNlmJ+rEvky4Ho+hgTHDytrBWiSPWtRHOQMwqX1ZTfVz+RlkwVPg52gtFLiyRyU1iEDJU2Yf82Lq7K",
            }
            response = requests.request(
                "POST", url, headers=headers, data=receive_money_payload
            )
            print(response.text, "response")
            receive_money_resp = response.json()
            # print(resp_json, "resp_json")
            if receive_money_resp["status"].upper() == "SUCCESS":
                is_amount_refunded = True
            else:
                is_amount_refunded = False
        else:
            return ErrorResponse(status_code=400, message="Invalid payment method")

        if is_amount_refunded:
            pass
            # a_transaction = Transaction(
            #     payment_id=payment_id,
            #     from_entity=str(account_id),
            #     payment_method=payment_method.upper(),
            #     to_entity=str(account_id),
            #     transaction_type=TransactionTypeType.REFUND,
            #     status=StatusType.SUCCESS,
            #     amount=request_amount,
            #     gateway_response=receive_money_resp,
            #     transaction_id=transact_id if payment_method == "wallet" else p_order_id,
            # )
            # # Creating Transaction Entry
            # u_record = await create_record(db, Transaction, a_transaction.as_dict())
        else:
            # a_transaction = Transaction(
            #     payment_id=payment_id,
            #     from_entity=str(account_id),
            #     payment_method=payment_method.upper(),
            #     to_entity=str(account_id),
            #     transaction_type=TransactionTypeType.REFUND,
            #     status=StatusType.FAILED,
            #     amount=request_amount,
            #     gateway_response=receive_money_resp,
            #     transaction_id=transact_id if payment_method == "wallet" else p_order_id,
            # )
            # # Creating Transaction Entry
            # u_record = await create_record(db, Transaction, a_transaction.as_dict())
            return ErrorResponse(
                status_code=500,
                message="ERROR",
                error={"error": receive_money_resp.get("statusDesc", "")},
            )
        return StandardResponse(
            status_code=200,
            message="Refund completed successfully",
            data="Refund completed successfully",
        )
    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message=f"Error: {e}")