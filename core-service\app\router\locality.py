from fastapi import APIRouter
from app.helper import StandardResponse, ErrorResponse
from app.redis_config import redis_client

router = APIRouter(prefix="/locality", tags=["Locality"])

@router.get("/get-localities")
async def get_localities():
    """
    Returns all localities stored in the Redis SET.
    """
    try:
        table_name = 'locality'
        localities = redis_client.smembers(table_name)
        # data = [loc.decode() for loc in localities]
        return StandardResponse(status_code=200, message='Locality retrieved', data=localities)
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))
    

def add_locality(table_name: str, locality: str):
    """
    Adds a unique locality to the Redis SET for the given table_name.
    """
    added = redis_client.sadd(table_name, locality.lower())
    if added == 1:
        return {"message": f"{locality} added to {table_name}"}
    else:
        return {"message": f"{locality} already exists in {table_name}"}