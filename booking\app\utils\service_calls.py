from datetime import datetime
import os
import requests
from typing import Optional
from fastapi import H<PERSON><PERSON>Exception
from app.config import settings
import logging

from app.database import get_db_session
from app.models import BookingCancellation
from app.utils.booking_cancellation_history import create_booking_cancellation_history, status_dict



logger = logging.getLogger("booking_cancellation")
logging.basicConfig(level=logging.INFO)

async def get_user_details(user_id: str, auth_token: str) -> Optional[dict]:
    """Get user details from profile service"""
    try:
        response = requests.get(
            f"{settings.BE_PROFILE_API_URL}/user-read/{user_id}",
            headers={"Authorization": f'Bearer {auth_token}'}
        )
        if response.status_code == 200:
            return response.json().get("data")
        return None
    except Exception as e:
        print(f"Error fetching user details: {e}")
        return None

async def get_artisan_details(artisan_id: str, auth_token: str) -> Optional[dict]:
    """Get service provider details from profile service"""
    try:
        response = requests.get(
            f"{settings.BE_PROFILE_API_URL}/sp-read/{artisan_id}",
            headers={"Authorization": f'Bearer {auth_token}'}
        )
        if response.status_code == 200:
            return response.json().get("data")
        return None
    except Exception as e:
        print(f"Error fetching artisan details: {e}")
        return None

async def get_service_details(service_id: str, auth_token: str) -> Optional[dict]:
    """Get service details from service management"""
    try:
        response = requests.get(
            f"{settings.BE_SERVICE_API_URL}/services-read/{service_id}",
            headers={"Authorization": f'Bearer {auth_token}'}
        )
        if response.status_code == 200:
            return response.json().get("data")
        return None
    except Exception as e:
        print(f"Error fetching service details: {e}")
        return None 
    


def process_refund(data: dict):
    logger.info("Processing refund")
    """Process refund for booking cancellation"""
    db = get_db_session()
    try:
        bch_user_id = data['bch_user_id']
        bc_id = data['bc_id']
        bc_obj = db.query(BookingCancellation).filter(BookingCancellation.id == data['bc_id']).first()
        bc_status = bc_obj.status

        if bc_status == "approved":
            # Calling payment service to refund user
            payload = {
                "user_id": str(data['user_id']),
                "amount": data['booking_amount'],
                "payment_method": data['payment_method'].lower(),
                "p_order_id": data['p_order_id'],
                "payment_id": str(data['payment_id']),
                "cancellation_id": str(bc_id)
            }
            print(f"Payload for refund: {payload}")
            payment_service_url = os.getenv("BE_PAYMENT_API_URL", "http://payment-service:8005")
            resp = requests.post(
                url=f"{payment_service_url}/refund-payment",
                json=payload,
                headers={"Authorization": data['Authorization']}
            )
            if resp.status_code != 200:
                logger.error(f"Refund process failed for booking cancellation id: {bc_id}. Error: {resp.json()}")
                # Create history entry
                create_booking_cancellation_history(db, {"booking_cancellation_id": bc_id, "description": status_dict["refund_failed"], "user_name": data['user_name'], "user_id": bch_user_id})
                return
            
            bc_obj.status = "completed"
            bc_obj.resolved_at = datetime.now()
            db.commit()
            db.refresh(bc_obj)
            logger.info("Refund processed successfully, Booking cancellation status updated to completed")
            # Create history entry
            create_booking_cancellation_history(db, {"booking_cancellation_id": bc_id, "description": status_dict["refund_done"], "user_name": data['user_name'], "user_id": bch_user_id})
            create_booking_cancellation_history(db, {"booking_cancellation_id": bc_id, "description": status_dict["resolved"], "user_name": data['user_name'], "user_id": bch_user_id})
        else:
            logger.info(f"Booking cancellation status is not APPROVED, cannot process refund for booking cancellation id: {bc_id}")
    except Exception as e:
        logger.error(f"Error processing refund for booking cancellation id: {bc_id}: {e}")
