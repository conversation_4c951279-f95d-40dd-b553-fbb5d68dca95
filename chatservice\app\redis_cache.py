import redis
import json

class RedisCache:
    def __init__(self):
        self.client = redis.Redis(host="redis", port=6379, db=0, decode_responses=True)
        self.queue_key = "waiting_customers"

    def enqueue_customer(self, customer_id: str):
        self.client.rpush(self.queue_key, str(customer_id))

    def dequeue_customer(self):
        return self.client.lpop(self.queue_key)

    def queue_length(self):
        return self.client.llen(self.queue_key)

    def mark_agent_available(self, agent_id: int):
        self.client.set(f"agent:{str(agent_id)}:available", "1")

    def mark_agent_unavailable(self, agent_id: int):
        self.client.set(f"agent:{str(agent_id)}:available", "0")

    def remove_customer_from_redis(self,user_id: str):
        self.client.lrem(self.queue_key, 0, str(user_id))

    def store_offline_message(self, user_id: int, message: dict):

        key = f"offline_messages:{user_id}"
        self.client.rpush(key, json.dumps(message))

    def fetch_offline_messages(self, user_id: int):

        key = f"offline_messages:{user_id}"
        messages = []

        while True:
            msg_json = self.client.lpop(key)
            if not msg_json:
                break
            try:
                messages.append(json.loads(msg_json))
            except json.JSONDecodeError:
                continue

        return messages