# plugins:
#   openid-connect:
#     bearer_only: true
#     client_id: ${CLIENT_ID}
#     client_secret: ${CLIENT_SECRET}
#     discovery: ${DISCOVERY_URL}
#     scope: openid profile
#     realm: jobconnectz
#     access_token_in_authorization_header: true
#     set_access_token_header: true
#     set_id_token_header: true
#     set_userinfo_header: true
#     introspection_endpoint_auth_method: "none"
#     ssl_verify: false


plugins:
  openid-connect:
    bearer_only: true
    client_id: 5g7jhua3cm4hjktg5ghl8v6ouv
    client_secret: 169a4nkcueg2vncj733905e0mkdsupducf0bjqkcnc2s45gotaan
    discovery: https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/openid-configuration
    scope: openid profile
    realm: jobconnectz
    access_token_in_authorization_header: true
    set_access_token_header: true
    set_id_token_header: true
    set_userinfo_header: true
    introspection_endpoint_auth_method: "none"
    ssl_verify: false
