import requests
import json
import os
from app.config import settings

async def initiate_refund(payload, token):
    """Initiate refund for booking cancellation"""
    try:
        print(payload, "refund payload")
        url = f"{os.getenv('BE_PAYMENT_API_URL', 'http://payment-service:8005')}/refund-payment"
        response = requests.post(url, json=payload, headers={"Authorization": f"Bearer {token}"})
        print(response.json(), "refund response")
        if response.status_code == 200:
            return response.json().get("data")
        return None
    except Exception as e:
        print(f"Error initiating refund: {e}")
        return None