from sqlalchemy import create_engine, inspect, text
from sqlalchemy.orm import sessionmaker, declarative_base
from psycopg2 import connect
from psycopg2.errors import DuplicateDatabase
from app.config import settings
# from shared.models.base import Base
Base = declarative_base()

# Create engine without database name (connect to PostgreSQL server)
DB_URL_WITHOUT_DB = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"

# Create engine for the actual database
DATABASE_URL = settings.DATABASE_URL
engine = create_engine(
            DATABASE_URL,
            pool_size=80,
            max_overflow=0
        )

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_database():
    """Ensure the PostgreSQL database exists before the app starts."""
    try:
        # Connect to PostgreSQL (without database name)
        conn = connect(
            dbname=settings.POSTGRES_DB,
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD,
            host=settings.POSTGRES_HOST,
            port=settings.POSTGRES_PORT
        )
        conn.autocommit = True
        cursor = conn.cursor()

        # Check if database exists
        cursor.execute(f"SELECT 1 FROM pg_database WHERE datname = '{settings.POSTGRES_DB}';")
        if not cursor.fetchone():
            print(f"⚠️ Database `{settings.POSTGRES_DB}` not found. Creating it now...")
            cursor.execute(f"CREATE DATABASE {settings.POSTGRES_DB};")
            print(f"✅ Database `{settings.POSTGRES_DB}` created successfully.")
        
        cursor.close()
        conn.close()
    except DuplicateDatabase:
        print(f"✅ Database `{settings.POSTGRES_DB}` already exists. Skipping creation.")

def enable_postgis():
    """Enable PostGIS extension in PostgreSQL."""
    with engine.connect() as conn:
        conn.execute(text("CREATE EXTENSION IF NOT EXISTS postgis;"))
        conn.commit()
        print("✅ PostGIS extension enabled successfully.")

def check_and_alter_table():
    """Check if the `break_from_hrs` column exists, and add it if missing."""
    with engine.connect() as conn:
        inspector = inspect(engine)
        columns = [col["name"] for col in inspector.get_columns("service_provider")]

        if "break_from_hrs" not in columns:
            print("⚠️ Column `break_from_hrs` is missing! Altering table...")
            conn.execute(text("ALTER TABLE service_provider ADD COLUMN break_from_hrs TIME;"))
            print("✅ Column `break_from_hrs` added successfully.")

        if "break_to_hrs" not in columns:
            print("⚠️ Column `break_to_hrs` is missing! Altering table...")
            conn.execute(text("ALTER TABLE service_provider ADD COLUMN break_to_hrs TIME;"))
            print("✅ Column `break_to_hrs` added successfully.")

def init_db():
    """Initialize the database and create tables if they do not exist."""
    pass
    # create_database()  # Ensure database exists
    # enable_postgis()
    # Base.metadata.create_all(bind=engine)  # Create tables if they don’t exist
    # booking_cancellation.Base.metadata.create_all(bind=engine)  
    # check_and_alter_table()  # Check and alter missing columns dynamically

# Dependency function to get a database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_db_session():
    """Get an async database session for use in non-endpoint functions"""
    db = SessionLocal()
    try:
        return db
    except Exception as e:
        db.close()
        raise e