from app.database import get_db
from app.models import DisputeEventTypeModel
import uuid

def seed_dispute_event_types():
    db = next(get_db())
    
    # Clear existing event types
    db.query(DisputeEventTypeModel).delete()
    db.commit()
    
    # Define event types
    event_types = [
        {
            "code": "DISPUTE_CREATED",
            "name": "Dispute Created",
            "description": "A new dispute has been created"
        },
        {
            "code": "ASSIGNED_TO_SYSTEM",
            "name": "Assigned to System",
            "description": "The dispute has been assigned to the system for initial processing"
        },
        {
            "code": "ASSIGNED_TO_AGENT",
            "name": "Assigned to Agent",
            "description": "The dispute has been assigned to an agent for resolution"
        },
        {
            "code": "STATUS_CHANGED_TO_OPEN",
            "name": "Status Changed to Open",
            "description": "The dispute status has been changed to Open"
        },
        {
            "code": "STATUS_CHANGED_TO_IN_PROGRESS",
            "name": "Status Changed to In Progress",
            "description": "The dispute status has been changed to In Progress"
        },
        {
            "code": "STATUS_CHANGED_TO_RESOLVED",
            "name": "Status Changed to Resolved",
            "description": "The dispute status has been changed to Resolved"
        },
        {
            "code": "COMMENT_ADDED",
            "name": "Comment Added",
            "description": "A comment has been added to the dispute"
        },
        {
            "code": "DOCUMENT_ADDED",
            "name": "Document Added",
            "description": "A document has been added to the dispute"
        },
        {
            "code": "MERCHANT_CONTACTED",
            "name": "Merchant Contacted",
            "description": "The merchant has been contacted about the dispute"
        },
        {
            "code": "MERCHANT_RESPONSE_RECEIVED",
            "name": "Merchant Response Received",
            "description": "A response has been received from the merchant"
        },
        {
            "code": "PAYMENT_GATEWAY_INVESTIGATION",
            "name": "Payment Gateway Investigation",
            "description": "The payment gateway is investigating the transaction"
        },
        {
            "code": "REFUND_INITIATED",
            "name": "Refund Initiated",
            "description": "A refund has been initiated for the dispute"
        },
        {
            "code": "REFUND_COMPLETED",
            "name": "Refund Completed",
            "description": "The refund has been completed"
        },
        {
            "code": "DISPUTE_CLOSED",
            "name": "Dispute Closed",
            "description": "The dispute has been closed"
        }
    ]
    
    # Insert event types
    for event_type in event_types:
        event_type_obj = DisputeEventTypeModel(
            id=uuid.uuid4(),
            code=event_type["code"],
            name=event_type["name"],
            description=event_type["description"]
        )
        db.add(event_type_obj)
    
    db.commit()
    print("Dispute event types seeded successfully")

if __name__ == "__main__":
    seed_dispute_event_types() 