import io
import tempfile
from datetime import datetime, time
from typing import Optional, Annotated
import uuid
import pandas as pd
import re
from fastapi import (
    APIRouter,
    Depends,
    File,
    UploadFile,
    BackgroundTasks,
    Request,
    Header
)
from fastapi.responses import JSONResponse
from app.helper import StandardResponse, ErrorResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from app.database import get_db
from app.models import UserProfiles, ArtisanDetail, Address, ServiceProviderServiceMapping, Category, Services, Roles
from app.auth import permission_checker
from app.cognito_utils import admin_get_user, create_cognito_user, add_user_to_group, update_cognito_attributes
from app.utils import generate_cognito_password
from app.kafka_producer.producer import cognito_producer
from app.background_tasks import (
    run_bulk_upload_task,
    get_task_status,
    cleanup_completed_tasks,
    get_all_tasks
)
import os
import requests

router = APIRouter()


@router.post("/admin/bulk-upload-service-providers/v2")
async def upload_service_providers_v2(
    excel_file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
    authorization: str = Header(None, alias="Authorization")
    # user=Depends(permission_checker)
):
    """
    Upload an Excel file with service providers.
    This endpoint starts a background task and returns a task ID for tracking.
    """
    try:
        # Verify Admin's Identity
        # if isinstance(user, JSONResponse):
        #     return user
        
        # Authorization = req.headers.get("Authorization")
        if not authorization:
            return ErrorResponse(status_code=401, message="Authorization header is required")
        # Extract token from Authorization header
        token = authorization.split(" ")[1]

        # Save uploaded file to temporary location
        file_content = await excel_file.read()
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # Validate Excel file structure
            df = pd.read_excel(temp_file_path)
            total_rows = len(df)
            required_columns = [
                "S/N",
                "Trainee Reg. Code",
                "Trade Area(s)",
                "Trainee First Name",
                "Trainee Surname",
                "Other names of Trainee",
                "Region",
                "District/Town",
                "Trainee Contact Number",
            ]
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                # Clean up temporary file
                os.unlink(temp_file_path)
                return {
                    "success": False,
                    "message": f"Missing required columns: {', '.join(missing_columns)}",
                    "created": 0,
                    "total": total_rows,
                    "skipped": 0,
                    "errors": [
                        {"error": f"Missing columns: {', '.join(missing_columns)}"}
                    ],
                    "skipped_details": [],
                }
            
            # Start background task with status tracking
            task_id = await run_bulk_upload_task(temp_file_path, token)
            
            return {
                "success": True,
                "message": "Bulk upload task started successfully",
                "task_id": task_id,
                "total_rows": total_rows,
                "status": "running"
            }
            
        except Exception as e:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass
            return ErrorResponse(
                status_code=400, message=f"Error reading Excel file: {str(e)}"
            )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error uploading file: {str(e)}")



@router.get("/admin/bulk-upload/status/{task_id}")
async def check_upload_status(task_id: str):
    """
    Check the status of a bulk upload task.
    
    Args:
        task_id: Task identifier
        
    Returns:
        Task status information
    """
    status = get_task_status(task_id)
    return {
        "success": True,
        "task_status": status
    }


@router.get("/admin/bulk-upload/tasks")
async def list_all_tasks():
    """
    Get all active bulk upload tasks.
    
    Returns:
        List of all tasks
    """
    tasks = get_all_tasks()
    return {
        "success": True,
        "tasks": tasks
    }




@router.delete("/admin/bulk-upload/cleanup")
async def cleanup_old_tasks(max_age_hours: int = 48):
    """
    Clean up old completed or failed tasks.
    
    Args:
        max_age_hours: Maximum age in hours for tasks to be kept
        
    Returns:
        Cleanup result
    """
    cleanup_completed_tasks(max_age_hours)
    return {
        "success": True,
        "message": f"Cleanup completed for tasks older than {max_age_hours} hours"
    }