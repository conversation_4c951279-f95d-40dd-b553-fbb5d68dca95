import logging
import os
from functools import lru_cache
from pydantic_settings import BaseSettings

log = logging.getLogger("uvicorn")


class Settings(BaseSettings):
    """Class for storing settings."""

    POSTGRES_DB: str = os.getenv("POSTGRES_DB")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD")
    POSTGRES_HOST: str = os.getenv("POSTGRES_HOST")
    POSTGRES_PORT: int = os.getenv("POSTGRES_PORT")
    PGADMIN_DEFAULT_EMAIL: str = os.getenv("PGADMIN_DEFAULT_EMAIL")
    PGADMIN_DEFAULT_PASSWORD: str = os.getenv("PGADMIN_DEFAULT_PASSWORD")
    KEYCLOAK_URL: str = os.getenv("KEYCLOAK_API_URL", "")
    KEYCLOAK_REALM: str = os.getenv("KEYCLOAK_REALM", "")
    COGNITO_REGION:str = os.getenv("COGNITO_REGION", "") 
    CLIENT_ID : str = os.getenv("CLIENT_ID", "")
    CLIENT_SECRET : str = os.getenv("CLIENT_SECRET", "")
    USER_POOL_ID : str = os.getenv("USER_POOL_ID", "")
    COGNITO_ISSUER : str = os.getenv("COGNITO_ISSUER", "")
    
    COGNITO_CLIENT_ID : str = os.getenv("COGNITO_CLIENT_ID", "")
    COGNITO_CLIENT_SECRET : str = os.getenv("COGNITO_CLIENT_SECRET", "")
    COGNITO_REGION : str = os.getenv("COGNITO_REGION", "")
    COGNITO_USER_POOL_ID : str = os.getenv("COGNITO_USER_POOL_ID", "")

    AWS_ACCESS_KEY_ID : str = os.getenv("AWS_ACCESS_KEY_ID", "")
    AWS_SECRET_ACCESS_KEY : str = os.getenv("AWS_SECRET_ACCESS_KEY", "")
    AWS_REGION : str = os.getenv("AWS_REGION", "")


@lru_cache()
def get_settings() -> BaseSettings:
    """Get application settings usually stored as environment variables.

    Returns:
        Settings: Application settings.
    """
    log.info("Loading config settings from the environment...")
    return Settings()
