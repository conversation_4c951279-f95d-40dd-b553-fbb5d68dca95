# import logging
# import os

# # Ensure log directory exists
# os.makedirs("log", exist_ok=True)
# logger = logging.getLogger("agenntservice")
# logger.setLevel(logging.INFO)

# if not logger.handlers:
#     file_handler = logging.FileHandler("log/agenntservice.log")
#     formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(name)s - %(message)s")
#     file_handler.setFormatter(formatter)
#     logger.addHandler(file_handler)
