import os
import uuid
import traceback
import sys
import logging
from datetime import datetime
from typing import Optional, Annotated
from uuid import UUID
from io import BytesIO

from pydantic import EmailStr
from fastapi import (
    APIRouter, Depends, File, Form, UploadFile, Header
)
import sqlalchemy as sa
from fastapi.responses import JSONResponse, StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError
from sqlalchemy import select, or_, and_, func


from app.database import get_db
from app.config import settings
from app.models import UserProfiles, Roles, Address, KycDetails, ArtisanDetail
from app.models_enum import UserStatusType
from app.auth import permission_checker, validate_token
from app.utils import (
    create_record,
    update_record,
    delete_record,
)
from app.schemas import UserProfileOut, UserListRequest, ServiceProviderApproval
from app.s3_service import upload_file_direct, validate_image, s3_delete_file
from app.helper import StandardResponse, ErrorResponse
from app.notification import send_push_notification
from fastapi import Request
from fastapi.encoders import jsonable_encoder
import pandas as pd
# Setup logger
logger = logging.getLogger("user_profile_log")
logging.basicConfig(level=logging.INFO)

# Router
router = APIRouter(prefix="/user-profiles", tags=["User Profiles"])

@router.post("/create-user", response_model=StandardResponse)
async def create_user_profile(
    first_name: Optional[str] = Form(None),
    last_name: Optional[str] = Form(None),
    phone_number: Optional[str] = Form(None),
    email: Optional[EmailStr] = Form(None),
    status: Optional[UserStatusType] = Form(UserStatusType.ACTIVE),
    profile_image: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
):
    try:
        logger.info("Creating user profile")

        if email or phone_number:
            query = select(UserProfiles).where(
                (UserProfiles.email == email) | (UserProfiles.phone_number == phone_number)
            )
            result = await db.execute(query)
            existing = result.scalars().first()
            if existing:
                if existing.email == email:
                    return ErrorResponse(status_code=400, message="Email already exists.")
                if existing.phone_number == phone_number:
                    return ErrorResponse(status_code=400, message="Phone number already exists.")

        image_url = None
        if profile_image:
            validate_image(profile_image)
            image_url = upload_file_direct(profile_image, path="JC_USER_PROFILE")
            image_url = image_url.get('filename')

        request_dict = {
            "first_name": first_name,
            "last_name": last_name,
            "phone_number": phone_number,
            "email": email,
            "status": status,
            "profile_image_url": image_url,
        }

        new_user = await create_record(db, UserProfiles, request_dict)

        if isinstance(new_user, str):
            return ErrorResponse(status_code=400, message=new_user)

        return StandardResponse(
            status_code=201,
            data=UserProfileOut.model_validate(new_user),
            message="User profile created successfully"
        )

    except Exception as e:
        logger.exception("Error while creating user profile")
        return ErrorResponse(status_code=500, message=str(e))

@router.get("/", response_model=StandardResponse)
async def get_user_full_details(
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user

        account_id = user.get("user_id") if isinstance(user, dict) else None

        if not account_id:
            return ErrorResponse(status_code=400, message="User ID is missing")

        query = (
            select(UserProfiles, Roles, Address)
            .outerjoin(Roles, UserProfiles.role_id == Roles.id)
            .outerjoin(Address, UserProfiles.id == Address.user_id)
            .where(UserProfiles.id == account_id)
        )

        result = await db.execute(query)
        records = result.all()

        if not records:
            return ErrorResponse(status_code=404, message="User not found")

        user_obj, role, _ = records[0]

        # Collect addresses
        addresses = []
        primary_address = None
        for _, _, address in records:
            if address:
                address_data = {
                    "address_id": str(address.id),
                    "name": address.name,
                    "details": address.details,
                    "locality": address.locality,
                    "is_primary": address.is_primary,
                }
                addresses.append(address_data)
                if address.is_primary:
                    primary_address = address_data

        user_data = {
            "user_id": str(user_obj.id),
            "first_name": user_obj.first_name,
            "last_name": user_obj.last_name,
            "email": user_obj.email,
            "phone_number": user_obj.phone_number,
            "country_code": user_obj.country_code,
            "status": user_obj.status.value,
            "profile_image_url": user_obj.profile_image_url,
            "is_profile_complete": user_obj.is_profile_complete,
        }

        role_data = {
            "role_id": str(role.id) if role else None,
            "role_name": role.role_name if role else None
        }

        response_data = {
            "user_basic_info": user_data,
            "role_info": role_data,
            "primary_address": primary_address,
            "all_addresses": addresses,
        }

        # Artisan and KYC Details
        if role and role.role_name == "artisan":
            artisan = (
                await db.execute(
                    select(ArtisanDetail).where(ArtisanDetail.user_id == account_id)
                )
            ).scalars().first()

            if artisan:
                response_data["artisan_details"] = {
                    "artisan_id": str(artisan.id),
                    "skill": artisan.skill,
                    "skill_level": artisan.skill_level,
                    "experience": artisan.experience,
                    "about_us": artisan.about_us,
                    "reg_code": artisan.reg_code,
                    "live_status": artisan.live_status,
                    "license": artisan.license,
                    "work_from_hrs": str(artisan.work_from_hrs) if artisan.work_from_hrs else None,
                    "work_to_hrs": str(artisan.work_to_hrs) if artisan.work_to_hrs else None,
                    "break_from_hrs": str(artisan.break_from_hrs) if artisan.break_from_hrs else None,
                    "break_to_hrs": str(artisan.break_to_hrs) if artisan.break_to_hrs else None,
                    "weekdays": artisan.weekdays,
                    "is_confirmed": artisan.is_confirmed,
                }

            kyc_list = (
                await db.execute(
                    select(KycDetails)
                    .where(KycDetails.user_id == account_id)
                    .order_by(KycDetails.created_at.desc())
                )
            ).scalars().all()

            response_data["kyc_details"] = [
                {
                    "kyc_id": str(k.id),
                    "kyc_type": k.KYC_type,
                    "document": k.document,
                    "validation_status": k.validation_status,
                    "validation_comments": k.validation_comments,
                    "created_at": k.created_at
                }
                for k in kyc_list
            ]

        return StandardResponse(
            status_code=200,
            message="User details fetched successfully",
            data=response_data
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Failed to fetch user details",
            error=str(e)
        )

@router.put("/update", response_model=StandardResponse)
async def update_user_profile(
    req: Request,
    user_id: Optional[str] = Form(None),
    first_name: Optional[str] = Form(None),
    last_name: Optional[str] = Form(None),
    email: Optional[str] = Form(None),
    notification_token: Optional[str] = Form(None),
    status: Optional[UserStatusType] = Form(None),
    profile_image: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker),
):
    try:
        if isinstance(user, JSONResponse):
            return user
        
        if not user_id:
            user_id = user.get("user_id") if isinstance(user, dict) else None
        
        Authorization = req.headers.get("Authorization")
        if not Authorization:
            return ErrorResponse(status_code=401, message="Authorization header is required")

        if not user_id:
            return ErrorResponse(status_code=400, message="User ID is missing")


        result = await db.execute(select(UserProfiles).where(UserProfiles.id == user_id))
        user_obj = result.scalars().first()

        if not user_obj:
            return ErrorResponse(status_code=404, message="User not found")

        update_data = {}

        # Check mandatory fields
        # missing_fields = []
        # if not first_name:
        #     missing_fields.append("first_name")
        # if not last_name:
        #     missing_fields.append("last_name")
        # if not email:
        #     missing_fields.append("email")

        # if missing_fields:
        #     return ErrorResponse(
        #         status_code=400,
        #         message=f"Missing fields: {', '.join(missing_fields)}"
        #     )

        # Update fields
        # update_data["first_name"] = first_name
        # update_data["last_name"] = last_name
        # update_data["email"] = email
        # update_data["is_profile_complete"] = True  # Automatically set to True since all fields provided

        if notification_token is not None:
            update_data["notification_token"] = notification_token
        if status is not None:
            update_data["status"] = status
        if first_name:
            update_data["first_name"] = first_name
        if last_name:
            update_data["last_name"] = last_name
        if email:
            update_data["email"] = email
        if first_name and last_name and email:
            update_data["is_profile_complete"] = True
        if status == UserStatusType.APPROVED:
            result = await db.execute(select(KycDetails).where(KycDetails.user_id == user_id))
            Kyc_details = result.scalars().all()
            if Kyc_details:
                for kyc in Kyc_details:
                    kyc.validation_status = "approved"
                    await db.commit()

        # Handle profile image
        if profile_image:
            validate_image(profile_image)
            image_url = upload_file_direct(profile_image, path="JC_USER_PROFILE")

            update_data["profile_image_url"] = image_url.get('filename')
            print("iamge_urlllllllllll", image_url)

        updated_user = await update_record(db, UserProfiles, user_id, update_data)

        if status == UserStatusType.APPROVED:
            send_push_notification(
                auth_token=Authorization,
                title="Profile Approved",
                message=f"Your profile has been approved successfully!",
                sender_id=str(user_id),
                type="user",
                data={
                    "user_id": str(user_id),
                    "request_type": "user_profile"
                }
            )
        if status == UserStatusType.REJECTED:
            send_push_notification(
                auth_token=Authorization,
                title="Profile Rejected",
                message=f"Your profile update has been rejected",
                sender_id=str(user_id),
                type="user",
                data={
                    "user_id": str(user_id),
                    "request_type": "user_profile"
                }
            )

        return StandardResponse(
            status_code=200,
            data=UserProfileOut.model_validate(updated_user),
            message="User profile updated successfully"
        )

    except Exception as e:
        logger.exception("Error updating user profile")
        return ErrorResponse(status_code=500, message=str(e))
    
# @router.delete("/{user_id}", response_model=StandardResponse) # soft and hard delete need to implement
# async def delete_user(user_id: UUID, db: AsyncSession = Depends(get_db)):
#     try:
#         result = await db.execute(select(UserProfiles).where(UserProfiles.id == user_id))
#         user = result.scalars().first()

#         if not user:
#             return ErrorResponse(status_code=404, message="User not found")

#         # Delete image from S3 if it exists
#         if user.profile_image_url:
#             s3_key = user.profile_image_url.split(f"{settings.S3_BUCKET}/")[-1]
#             try:
#                 s3_delete_file(s3_key)
#             except Exception as e:
#                 logger.warning(f"Could not delete image from S3: {e}")

#         # Delete DB record
#         await delete_record(db, UserProfiles, user_id)

#         return StandardResponse(status_code=200, message="User deleted successfully")

#     except Exception as e:
#         logger.exception("Error deleting user")
#         return ErrorResponse(status_code=500, message=str(e))

@router.post("/users-list", response_model=StandardResponse)
async def list_users(
    request: UserListRequest,
    db: AsyncSession = Depends(get_db),
):
    try:
        users_data = []
        
        if request.user_id:
            query = select(UserProfiles, Roles).outerjoin(Roles, UserProfiles.role_id == Roles.id).where(UserProfiles.id == request.user_id)
            result = await db.execute(query)
            record = result.first()

            if not record:
                return ErrorResponse(status_code=404, message="User not found")

            user, role = record

            user_data = {
                "user_id": str(user.id),
                "first_name": user.first_name,
                "last_name": user.last_name,
                "email": user.email,
                "phone_number": user.phone_number,
                "status": user.status.value,
                "profile_image_url": user.profile_image_url,
                "role": {
                    "role_id": str(role.id) if role else None,
                    "role_name": role.role_name if role else None
                }
            }

            # Artisan Details
            if role and role.role_name == "artisan":
                artisan_result = await db.execute(select(ArtisanDetail).where(ArtisanDetail.user_id == user.id))
                artisan = artisan_result.scalars().first()
                if artisan:
                    user_data["artisan_details"] = {
                        "artisan_id": str(artisan.id),
                        "skill": artisan.skill,
                        "skill_level": artisan.skill_level,
                        "experience": artisan.experience,
                        "about_us": artisan.about_us,
                        "reg_code": artisan.reg_code,
                        "live_status": artisan.live_status,
                        "license": artisan.license,
                        "work_from_hrs": str(artisan.work_from_hrs) if artisan.work_from_hrs else None,
                        "work_to_hrs": str(artisan.work_to_hrs) if artisan.work_to_hrs else None,
                        "break_from_hrs": str(artisan.break_from_hrs) if artisan.break_from_hrs else None,
                        "break_to_hrs": str(artisan.break_to_hrs) if artisan.break_to_hrs else None,
                        "weekdays": artisan.weekdays,
                        "is_confirmed": artisan.is_confirmed,
                    }
                kyc_result = await db.execute(
                    select(KycDetails).where(KycDetails.user_id == user.id).order_by(KycDetails.created_at.desc())
                )
                kyc_list = kyc_result.scalars().all()
                user_data["kyc_details"] = [{
                    "kyc_id": str(k.id),
                    "kyc_type": k.KYC_type,
                    "document": k.document,
                    "validation_status": k.validation_status,
                    "validation_comments": k.validation_comments,
                    "created_at": k.created_at
                } for k in kyc_list]

            users_data.append(user_data)

            return StandardResponse(status_code=200, message="User fetched successfully", data={"users": users_data, "pagination": {}})

        # If no user_id → normal filter list
        query = select(UserProfiles, Roles).outerjoin(Roles, UserProfiles.role_id == Roles.id)
        filters = []

        if request.role_name:
            filters.append(Roles.role_name == request.role_name)
        if request.status:
            filters.append(UserProfiles.status == request.status)
        if request.q:
            filters.append(or_(
                UserProfiles.first_name.ilike(f"%{request.q}%"),
                UserProfiles.last_name.ilike(f"%{request.q}%"),
                UserProfiles.email.ilike(f"%{request.q}%"),
                UserProfiles.phone_number.ilike(f"%{request.q}%")
            ))

        if filters:
            query = query.where(and_(*filters))

        count_query = select(func.count()).select_from(
            select(UserProfiles.id)
            .outerjoin(Roles, UserProfiles.role_id == Roles.id)
            .where(and_(*filters) if filters else True)
            .subquery()
        )
        total_count = (await db.execute(count_query)).scalar()

        offset = (request.skip - 1) * request.limit if request.skip > 0 else 0
        query = query.offset(offset).limit(request.limit)

        records = (await db.execute(query)).all()

        for user, role in records:
            user_data = {
                "user_id": str(user.id),
                "first_name": user.first_name,
                "last_name": user.last_name,
                "email": user.email,
                "phone_number": user.phone_number,
                "status": user.status.value,
                "profile_image_url": user.profile_image_url,
                "role": {
                    "role_id": str(role.id) if role else None,
                    "role_name": role.role_name if role else None
                }
            }

            if role and role.role_name == "artisan":
                artisan_result = await db.execute(select(ArtisanDetail).where(ArtisanDetail.user_id == user.id))
                artisan = artisan_result.scalars().first()
                if artisan:
                    user_data["artisan_details"] = {
                        "artisan_id": str(artisan.id),
                        "skill": artisan.skill,
                        "skill_level": artisan.skill_level,
                        "experience": artisan.experience,
                        "about_us": artisan.about_us,
                        "reg_code": artisan.reg_code,
                        "live_status": artisan.live_status,
                        "license": artisan.license,
                        "work_from_hrs": str(artisan.work_from_hrs) if artisan.work_from_hrs else None,
                        "work_to_hrs": str(artisan.work_to_hrs) if artisan.work_to_hrs else None,
                        "break_from_hrs": str(artisan.break_from_hrs) if artisan.break_from_hrs else None,
                        "break_to_hrs": str(artisan.break_to_hrs) if artisan.break_to_hrs else None,
                        "weekdays": artisan.weekdays,
                        "is_confirmed": artisan.is_confirmed,
                    }
                kyc_result = await db.execute(select(KycDetails).where(KycDetails.user_id == user.id).order_by(KycDetails.created_at.desc()))
                kyc_list = kyc_result.scalars().all()
                user_data["kyc_details"] = [{
                    "kyc_id": str(k.id),
                    "kyc_type": k.KYC_type,
                    "document": k.document,
                    "validation_status": k.validation_status,
                    "validation_comments": k.validation_comments,
                    "created_at": k.created_at
                } for k in kyc_list]

            users_data.append(user_data)

        return StandardResponse(
            status_code=200,
            message="User list fetched successfully",
            data={
                "users": users_data,
                "pagination": {
                    "total": total_count,
                    "page": request.skip,
                    "limit": request.limit,
                    "pages": (total_count + request.limit - 1) // request.limit,
                }
            }
        )
    except Exception as e:
        logger.exception("Failed to fetch users")
        return ErrorResponse(status_code=500, message="Failed to fetch users", error=str(e))

@router.get("/sp-export")
async def export_sp(
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        # Select only users with 'Artisan' role
        selected_columns = [
            UserProfiles.first_name,
            UserProfiles.last_name,
            UserProfiles.email,
            UserProfiles.gender,
            UserProfiles.phone_number,
            UserProfiles.location,
            UserProfiles.status,
            UserProfiles.created_at
        ]

        # Join roles and filter artisan users
        query = (
            select(*selected_columns)
            .select_from(UserProfiles)
            .join(Roles, UserProfiles.role_id == Roles.id)
            .where(Roles.role_name == 'artisan')
            .order_by(UserProfiles.created_at.desc())
        )

        # Execute
        result = await db.execute(query)
        artisans = result.mappings().all()

        # Convert to JSON
        json_data = jsonable_encoder(artisans)

        # Convert to DataFrame
        df = pd.DataFrame(json_data)

        # Rename columns
        df = df.rename(columns={
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'email': 'Email',
            'gender': 'Gender',
            'phone_number': 'Phone Number',
            'location': 'Location',
            'status': 'Status',
            'created_at': 'Created At'
        })

        # Format date
        df['Created At'] = pd.to_datetime(df['Created At']).dt.strftime('%Y-%m-%d %H:%M:%S')

        # Excel output
        output = BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, sheet_name='Artisans')
            workbook = writer.book
            worksheet = writer.sheets['Artisans']
            header_format = workbook.add_format({
                'bold': True, 'text_wrap': True, 'valign': 'top',
                'fg_color': '#D7E4BC', 'border': 1
            })

            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)

            for idx, col in enumerate(df):
                max_len = max(df[col].astype(str).map(len).max(), len(col)) + 1
                worksheet.set_column(idx, idx, max_len)

        output.seek(0)

        return StreamingResponse(
            output,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": "attachment; filename=artisans_export.xlsx"
            }
        )

    except Exception as e:
        tb = traceback.extract_tb(sys.exc_info()[2])[-1]
        logger.error(
            f"Unexpected error: {str(e)} | File: {tb.filename}, Line: {tb.lineno}, "
            f"Function: {tb.name}, Code: {tb.line}",
            exc_info=True
        )
        return ErrorResponse(status_code=500, message="Service providers export failed", error=f"Error: {e}")

@router.delete("/soft-delete")
async def soft_delete(
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user

        account_id = user.get("user_id") if isinstance(user, dict) else None

        if not account_id:
            return ErrorResponse(status_code=400, message="User ID is missing")
        # return uuid(account_id)

        # Soft delete user
        query = select(UserProfiles).where(UserProfiles.id == account_id)
        result = await db.execute(query)
        user_obj = result.scalars().first()
        if not user_obj:
            return ErrorResponse(status_code=404, message="User not found")

        user_obj.is_active = False
        await db.commit()

        return StandardResponse(
            status_code=200,
            message="User deleted successfully"
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Failed to soft delete user",
            error=str(e)
        )

# @router.put("/sp-approval/{id}")
# async def sp_approval(
#     id: str,
#     data: ServiceProviderApproval,
#     Authorization: Annotated[str, Header()] = None
# ):
#     try:
#         if not Authorization or not Authorization.startswith("Bearer "):
#             return ErrorResponse(status_code=401, message="Invalid Authorization header")

#         token = Authorization.split(" ", 1)[1]

#         resp = await validate_token(token) 

#         if "error" in resp:
#             return ErrorResponse(status_code=401, message="Invalid token", error=resp["error"])

#     except Exception as e:
#         return ErrorResponse(status_code=500, message="Error creating admin", error=str(e))
