# Use the official Python image as the base image
FROM 813714097634.dkr.ecr.ap-south-1.amazonaws.com/dev_local_python:3.10-slim

# Set the working directory in the container
WORKDIR /app

# set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# install system dependencies
# RUN apt-get update && \
#     apt-get install -y --no-install-recommends \
#     gcc \
#     libpq-dev gcc kafkacat && \
#     apt-get clean && \
#     rm -rf /var/lib/apt/lists/*

# Copy the requirements file into the container
COPY ./requirements.txt .

# Copy the FastAPI application code into the container

# Install FastAPI and any other required packages
RUN pip install -r requirements.txt

COPY . .

# Expose the FastAPI application port
EXPOSE 8000

# Define the command to start the FastAPI application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]