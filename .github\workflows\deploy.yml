# name: Deploy to EC2

# on:
#   push:
#     branches:
#       - devlopment  # Trigger only when pushing to the development branch

# jobs:
#   deploy:
#     runs-on: ubuntu-latest

#     steps:
#       - name: Checkout Repository
#         uses: actions/checkout@v4

#       - name: Set up SSH Key
#         run: |
#           mkdir -p ~/.ssh
#           echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/deploy_key
#           chmod 600 ~/.ssh/deploy_key
#           ssh-keyscan -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts

#       - name: Deploy to EC2
#         run: |
#           ssh -i ~/.ssh/deploy_key ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} << 'EOF'
#             cd /home/<USER>/GTI-JobConnectz-backend  # Go to project directory
#             git pull origin devlopment  # Pull latest code
#             docker-compose down  # Stop running containers
#             docker-compose up -d --build  # Start containers in detached mode
#             docker system prune -f  # Clean up unused Docker resources
#           EOF
