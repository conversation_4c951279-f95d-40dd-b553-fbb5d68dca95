{"version": 3, "timeout": "3600s", "swagger": {"url": "/swagger"}, "port": 8080, "extra_config": {"github.com/devopsfaith/krakend/proxy": {"debug": true}, "websocket": {"input_headers": ["<PERSON><PERSON>", "Authorization"], "connect_event": true, "disconnect_event": true, "read_buffer_size": 4096, "write_buffer_size": 4096, "message_buffer_size": 4096, "max_message_size": 3200000, "write_wait": "10s", "pong_wait": "60s", "ping_period": "54s", "max_retries": 0, "backoff_strategy": "exponential"}, "router": {"return_error_msg": true}, "security/cors": {"allow_origins": ["http://localhost:3000", "https://portal.jobconnectz.com", "https://uat-portal.jobconnectz.com"], "allow_methods": ["GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS"], "expose_headers": ["Content-Length", "Content-Type"], "allow_headers": ["Accept", "Authorization", "Accept-Language", "Accept-Encoding", "Access-Control-Request-Headers", "Access-Control-Request-Method", "Origin", "Content-Type", "Cache-Control", "Pragma", "Host", "User-Agent", "Connection", "<PERSON><PERSON><PERSON>", "Sec-Fetch-Dest", "Sec-Fetch-Mode", "Sec-Fetch-Site", "ngrok-skip-browser-warning"], "max_age": "12h", "allow_credentials": false, "debug": true}}, "endpoints": [{"endpoint": "/health", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true}}, "backend": [{"url_pattern": "/health", "encoding": "no-op", "method": "GET", "host": ["http://auth-service:8000"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/user-signup", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/user-signup", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/user-read", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/user-read", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/user-read/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/user-read/{id}", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/user-list", "input_headers": ["Authorization"], "input_query_strings": ["q", "limit", "skip"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/user-list", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/user-update", "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/user-update", "encoding": "no-op", "method": "PUT", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/user-delete", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/user-delete", "encoding": "no-op", "method": "DELETE", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/sp-signup", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/sp-signup", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/sp-read", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/sp-read", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/sp-read/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/sp-read/{id}", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/sp-list", "input_headers": ["Authorization"], "input_query_strings": ["q", "limit", "skip", "sort", "status"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/sp-list", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/sp-update", "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/sp-update", "encoding": "no-op", "method": "PUT", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/sp-approval/{id}", "input_query_strings": ["id"], "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/sp-approval/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/user-approval/{id}", "input_query_strings": ["id"], "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/user-approval/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/sp-delete", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/sp-delete", "encoding": "no-op", "method": "DELETE", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/create-device", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/create-device", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/device-read/{id}", "input_query_strings": ["id"], "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/device-read/{id}", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/device-list", "input_query_strings": ["q", "limit", "skip"], "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/device-list", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/device-update", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/device-update", "encoding": "no-op", "method": "PUT", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/device-delete/{id}", "input_query_strings": ["id"], "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/device-delete/{id}", "encoding": "no-op", "method": "DELETE", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/send_notification", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true}}, "backend": [{"url_pattern": "/send_notification", "encoding": "no-op", "method": "POST", "host": ["http://notification-service:8002"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/notification", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true}}, "backend": [{"url_pattern": "/notification", "encoding": "no-op", "method": "GET", "host": ["http://notification-service:8002"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/payment-create", "input_headers": ["Authorization", "Content-Type"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/payment-create", "encoding": "no-op", "method": "POST", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/payment-read/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/payment-read/{id}", "encoding": "no-op", "method": "GET", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/payment-list", "input_headers": ["Authorization"], "input_query_strings": ["skip", "limit"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/payment-list", "encoding": "no-op", "method": "GET", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/payment-update/{id}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/payment-update/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/payment-delete/{id}", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/payment-delete/{id}", "encoding": "no-op", "method": "DELETE", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/transaction-create", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/transaction-create", "encoding": "no-op", "method": "POST", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/transaction-read/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/transaction-read/{id}", "encoding": "no-op", "method": "GET", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/transaction-list", "input_headers": ["Authorization"], "input_query_strings": ["skip", "limit", "from_date", "to_date", "transaction_type", "payment_method", "status"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/transaction-list", "encoding": "no-op", "method": "GET", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/request-payment", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/request-payment", "encoding": "no-op", "method": "POST", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/update-transaction-status", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/update-transaction-status", "encoding": "no-op", "method": "PUT", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/transaction-details", "input_headers": ["Authorization"], "input_query_strings": ["p_order_id"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/transaction-details", "encoding": "no-op", "method": "GET", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/account-create", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/account-create", "encoding": "no-op", "method": "POST", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/account-read", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/account-read", "encoding": "no-op", "method": "GET", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/account-update", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/account-update", "encoding": "no-op", "method": "PUT", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/category-create", "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/category-create", "encoding": "no-op", "method": "POST", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/category-read/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/category-read/{id}", "encoding": "no-op", "method": "GET", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/category-list", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/category-list", "encoding": "no-op", "method": "GET", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/category-update/{id}", "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/category-update/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/category-delete/{id}", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/category-delete/{id}", "encoding": "no-op", "method": "DELETE", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/subcategory-create", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/subcategory-create", "encoding": "no-op", "method": "POST", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/subcategory-read/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/subcategory-read/{id}", "encoding": "no-op", "method": "GET", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/subcategory-list", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/subcategory-list", "encoding": "no-op", "method": "GET", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/subcategory-update/{id}", "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/subcategory-update/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/subcategory-delete/{id}", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/subcategory-delete/{id}", "encoding": "no-op", "method": "DELETE", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/services-create", "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/services-create", "encoding": "no-op", "method": "POST", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/services-read/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/services-read/{id}", "encoding": "no-op", "method": "GET", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/services-list", "input_query_strings": ["limit", "skip", "parent_id", "q"], "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/services-list", "encoding": "no-op", "method": "GET", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/services-update/{id}", "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/services-update/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/services-delete/{id}", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/services-delete/{id}", "encoding": "no-op", "method": "DELETE", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/service_provider/leave", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/service_provider/leave", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/service_provider/leaves", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/service_provider/leaves", "encoding": "no-op", "method": "GET", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/service_provider/leave/{leave_id}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/service_provider/leave/{leave_id}", "encoding": "no-op", "method": "PUT", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/service_provider/leave/{leave_id}", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/service_provider/leave/{leave_id}", "encoding": "no-op", "method": "DELETE", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/request", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/request", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/response", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/response", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/status-update", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/status-update", "encoding": "no-op", "method": "PUT", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/start", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/start", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/end", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/end", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/reschedule-request", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/reschedule-request", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/reschedule-confirm", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/reschedule-confirm", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/history", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/history", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/latest_booking", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/latest_booking", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/{id}", "encoding": "no-op", "method": "GET", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/search_artisan/search_artisan", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/search_artisan/search_artisan", "encoding": "no-op", "method": "POST", "host": ["http://schedule-service:8012"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/search_artisan/search_live_artisan", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/search_artisan/search_live_artisan", "encoding": "no-op", "method": "POST", "host": ["http://schedule-service:8012"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/service_provider/status", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/service_provider/status", "encoding": "no-op", "method": "PUT", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin/create_serviceprovider", "input_headers": ["Authorization", "Content-Type", "Accept-Language"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/admin/create_serviceprovider", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/superadmin/create_admin", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/superadmin/create_admin", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin-list", "input_query_strings": ["q", "limit", "skip", "pagination"], "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/admin-list", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/create-issue", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/create-issue", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/read-issue/{id}", "input_query_strings": ["id"], "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/read-issue/{id}", "encoding": "no-op", "method": "GET", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/list-issues", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/list-issues", "encoding": "no-op", "method": "GET", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/update-issue", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/update-issue", "encoding": "no-op", "method": "PUT", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/delete-issue/{id}", "input_query_strings": ["id"], "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/delete-issue/{id}", "encoding": "no-op", "method": "DELETE", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/dispute-list", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/dispute-list", "encoding": "no-op", "method": "POST", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/dispute-create", "input_headers": ["Authorization", "Content-Type", "Accept-Language"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/dispute-create", "encoding": "no-op", "method": "POST", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/dispute-read/{id}", "input_query_strings": ["id"], "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/dispute-read/{id}", "encoding": "no-op", "method": "GET", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/dispute-update/{id}", "input_query_strings": ["id"], "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/dispute-update/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/issue-categories/{user_type}", "input_query_strings": ["user_type"], "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/issue-categories/{user_type}", "encoding": "no-op", "method": "GET", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/artisan_rating", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/artisan_rating", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/get-presigned-url", "input_query_strings": ["file_path"], "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/get-presigned-url", "encoding": "no-op", "method": "GET", "host": ["http://blob-storage:8010"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/ratings", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/ratings", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/dispute-comment-create", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/dispute-comment-create", "encoding": "no-op", "method": "POST", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/dispute-event-types", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/dispute-event-types", "encoding": "no-op", "method": "GET", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/dispute-event-create", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/dispute-event-create", "encoding": "no-op", "method": "POST", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/dispute-upload-document/{id}", "input_headers": ["Authorization", "Content-Type"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/dispute-upload-document/{id}", "encoding": "no-op", "method": "POST", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/issue-category", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/issue-category", "encoding": "no-op", "method": "POST", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/issue-category/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/issue-category/{id}", "encoding": "no-op", "method": "GET", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/issue-category/{id}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/issue-category/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/issue-category/{id}", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/issue-category/{id}", "encoding": "no-op", "method": "DELETE", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin/bulk-upload-service-providers", "input_headers": ["Authorization", "Content-Type"], "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/admin/bulk-upload-service-providers", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin/bulk-upload-service-providers/v2", "input_headers": ["Authorization", "Content-Type"], "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/admin/bulk-upload-service-providers/v2", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin/bulk-upload/status/{task_id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/admin/bulk-upload/status/{task_id}", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin/bulk-update-groups", "input_headers": ["Authorization"], "input_query_strings": ["old_group", "new_group"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/admin/bulk-update-groups", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/artisan-earnings", "input_headers": ["Authorization"], "input_query_strings": ["skip", "limit"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/artisan-earnings", "encoding": "no-op", "method": "GET", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/user-spends", "input_headers": ["Authorization"], "input_query_strings": ["skip", "limit"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/user-spends", "encoding": "no-op", "method": "GET", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin/soft-delete-user/{username}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/admin/soft-delete-user/{username}", "encoding": "no-op", "method": "PUT", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin/delete-user/{username}", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/admin/delete-user/{username}", "encoding": "no-op", "method": "DELETE", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin/reactivate-user/{username}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {}, "backend": [{"url_pattern": "/admin/reactivate-user/{username}", "encoding": "no-op", "method": "PUT", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin/soft-delete-artisan/{username}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/admin/soft-delete-artisan/{username}", "encoding": "no-op", "method": "PUT", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin/reactivate-artisan/{username}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/admin/reactivate-artisan/{username}", "encoding": "no-op", "method": "PUT", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/signup", "input_headers": [], "method": "POST", "output_encoding": "no-op", "extra_config": {}, "backend": [{"url_pattern": "/signup", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/otp-verify", "input_headers": [], "method": "POST", "output_encoding": "no-op", "extra_config": {}, "backend": [{"url_pattern": "/otp-verify", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/resend-otp", "input_headers": [], "method": "POST", "output_encoding": "no-op", "extra_config": {}, "backend": [{"url_pattern": "/resend-otp", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/cognito-get-user", "input_headers": [], "method": "POST", "output_encoding": "no-op", "extra_config": {}, "backend": [{"url_pattern": "/cognito-get-user", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/renew-token", "input_headers": [], "method": "POST", "output_encoding": "no-op", "extra_config": {}, "backend": [{"url_pattern": "/renew-token", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/payment-callback", "input_query_strings": ["api_key"], "input_headers": ["*"], "method": "POST", "output_encoding": "no-op", "extra_config": {"proxy": {"pass_headers": true}}, "backend": [{"url_pattern": "/payment-callback", "encoding": "no-op", "method": "POST", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/soft-delete-user", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/soft-delete-user", "encoding": "no-op", "method": "PUT", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/soft-delete-artisan", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/soft-delete-artisan", "encoding": "no-op", "method": "PUT", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/create-categories-issues", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/create-categories-issues", "encoding": "no-op", "method": "POST", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/detail-categories-issues/{category_id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/detail-categories-issues/{category_id}", "encoding": "no-op", "method": "GET", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/update-categories-issues/{category_id}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/update-categories-issues/{category_id}", "encoding": "no-op", "method": "PUT", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/delete-categories-issues/{category_id}", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/delete-categories-issues/{category_id}", "encoding": "no-op", "method": "DELETE", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/issues-create", "input_headers": ["Authorization", "Content-Type", "Accept-Language"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/issues-create", "encoding": "no-op", "method": "POST", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/issues-list", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/issues-list", "encoding": "no-op", "method": "POST", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/issue-read/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/issue-read/{id}", "encoding": "no-op", "method": "GET", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/issues-update/{issue_id}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/issues-update/{issue_id}", "encoding": "no-op", "method": "PUT", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/issues-comment-create", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/issues-comment-create", "encoding": "no-op", "method": "POST", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/list-categories-subcategories", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/list-categories-subcategories", "encoding": "no-op", "method": "GET", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/agent-approval/{id}", "input_query_strings": ["id"], "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/agent-approval/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/delete-subcategories/{subcategory_id}", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/delete-subcategories/{subcategory_id}", "encoding": "no-op", "method": "DELETE", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/assign-dispute", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/assign-dispute", "encoding": "no-op", "method": "PUT", "host": ["http://support-service:8013"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking-cancellation", "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking-cancellation", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking-cancellations", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking-cancellations", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking-cancellation/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking-cancellation/{id}", "encoding": "no-op", "method": "GET", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking-cancellation/{id}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking-cancellation/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking-cancellation-approve/{id}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking-cancellation-approve/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/bc-history/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/bc-history/{id}", "encoding": "no-op", "method": "GET", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking-cancellation-reason/create", "input_headers": ["Authorization"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking-cancellation-reason/create", "encoding": "no-op", "method": "POST", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking-cancellation-reason/read", "input_headers": ["Authorization"], "input_query_strings": ["user_type"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking-cancellation-reason/read", "encoding": "no-op", "method": "GET", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking-cancellation-reason/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking-cancellation-reason/{id}", "encoding": "no-op", "method": "GET", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking-cancellation-reason/{id}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking-cancellation-reason/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking-cancellation-reason/{id}", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking-cancellation-reason/{id}", "encoding": "no-op", "method": "DELETE", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/retry-refund/{id}", "input_headers": ["Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/retry-refund/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/logs", "input_headers": ["Authorization"], "input_query_strings": ["skip", "limit"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/logs", "encoding": "no-op", "method": "GET", "host": ["http://audit-log:8011"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/logs/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/logs/{id}", "encoding": "no-op", "method": "GET", "host": ["http://audit-log:8011"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin/bulk-delete-service-providers", "input_headers": ["Authorization", "Content-Type"], "method": "POST", "output_encoding": "no-op", "backend": [{"url_pattern": "/admin/bulk-delete-service-providers", "encoding": "no-op", "method": "POST", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/transaction-status/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/transaction-status/{id}", "encoding": "no-op", "method": "GET", "host": ["http://payment-service:8005"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/admin-read", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/admin-read", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/negotiate-price", "input_headers": ["Content-Type", "Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/negotiate-price", "encoding": "no-op", "method": "PUT", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/negotiate-response", "input_headers": ["Content-Type", "Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/negotiate-response", "encoding": "no-op", "method": "PUT", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/reschedule-booking-notify-to-user", "input_headers": ["Content-Type", "Authorization"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/reschedule-booking-notify-to-user", "encoding": "no-op", "method": "PUT", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/update-customer/{booking_id}", "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/booking/update-customer/{booking_id}", "encoding": "no-op", "method": "PUT", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/booking/generate-invoice", "input_headers": ["Authorization"], "input_query_strings": ["booking_id"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/generate-invoice", "encoding": "no-op", "method": "GET", "host": ["http://booking-service:8004"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/sp-export", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/sp-export", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/user-address-status/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/user-address-status/{id}", "encoding": "no-op", "method": "GET", "host": ["http://profile-service:8001"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/service-rate-sheet-create", "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/service-rate-sheet-create", "encoding": "no-op", "method": "POST", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/service-rate-sheet-read/{id}", "input_headers": ["Authorization"], "method": "GET", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/service-rate-sheet-read/{id}", "encoding": "no-op", "method": "GET", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/service-rate-sheet-list", "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "POST", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/service-rate-sheet-list", "encoding": "no-op", "method": "POST", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/service-rate-sheet-update/{id}", "input_headers": ["Content-Type", "Authorization", "Accept-Language"], "method": "PUT", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/service-rate-sheet-update/{id}", "encoding": "no-op", "method": "PUT", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}, {"endpoint": "/service-rate-sheet-delete/{id}", "input_headers": ["Authorization"], "method": "DELETE", "output_encoding": "no-op", "extra_config": {"auth/validator": {"alg": "RS256", "jwk_url": "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_dRfgO0rKa/.well-known/jwks.json", "cache": true, "allow_empty_token": true}}, "backend": [{"url_pattern": "/service-rate-sheet-delete/{id}", "encoding": "no-op", "method": "DELETE", "host": ["http://service-management:8003"], "extra_config": {"backend/http": {"return_error_details": "details"}}}]}]}