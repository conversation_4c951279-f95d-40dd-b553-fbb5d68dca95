import uuid
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, Header
from sqlalchemy.orm import Session
from sqlalchemy import or_
from pathlib import Path
from app import models, schemas
from app.database import get_db
from app.utils import (
    create_record,
    delete_record,
    get_id_header,
    is_valid_uuid,
    update_record,
)
from typing import Optional
from app.hash import cognito_secret_hash
from sqlalchemy.future import select
from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Annotated, List
from app.file_uploader import upload_file
from app.config import get_settings
from sqlalchemy.types import String
from app.helper import StandardResponse, ErrorResponse
from app.models import *


router = APIRouter()


@router.post("/create-device")
async def create_device(
    request: schemas.CreateDevice,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            email = resp_json.get("email")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )
        
        device = db.query(Device).filter(Device.notification_token == request.notification_token).first()
        
        if device:
            return ErrorResponse(
                status_code=400,
                message="Device already exists"
            )
        
        request_dict = request.dict()

        await create_record(db, Device, request_dict)
        return StandardResponse(
            status_code=200,
            message="Device created successfully"
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )


@router.get("/device-read/{id}")
async def read_device(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )
        if not is_valid_uuid(id):
            return ErrorResponse(
                status_code=400,
                message="Invalid UUID"
            )
        query = select(Device).filter(Device.id == uuid.UUID(id))
        result = await db.execute(query)
        device_obj = result.scalars().first()
        return StandardResponse(
            status_code=200,
            message="Device read successfully",
            data=device_obj
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )


@router.get("/device-list")
async def read_devices(
    q: Optional[str] = None,  # Search by id, user_id, or service_provider_id
    skip: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        # Authenticate User
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        # Base query
        query = select(Device)

        # Apply search filter
        if q:
            query = query.where(
                or_(
                    Device.id.cast(String) == q,
                    Device.user_id.cast(String) == q,
                    Device.service_provider_id.cast(String) == q,
                )
            )

        # Apply pagination
        query = query.offset(skip).limit(limit)

        # Execute query
        result = await db.execute(query)
        devices = result.scalars().all()

        # Total count (without pagination)
        count_query = select(func.count()).select_from(Device)
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()
        return StandardResponse(
            status_code=200,
            message="Devices read successfully",
            data={"total": total_count, "data": devices}
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )


@router.put("/device-update")
async def update_device(
    data: schemas.UpdateDevice,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        query = select(Device).filter(Device.id == data.id)
        result = await db.execute(query)
        get_device = result.scalars().first()

        res = await update_record(db, data, get_device)
        return StandardResponse(
            status_code=200,
            message="Device updated successfully",
            data=res
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )


@router.delete("/device-delete/{id}")
async def delete_device(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=401,
                message=resp.json()
            )

        if not is_valid_uuid(id):
            return ErrorResponse(
                status_code=400,
                message="Invalid UUID"
            )
        query = select(Device).filter(Device.id == uuid.UUID(id))
        result = await db.execute(query)
        get_device = result.scalars().first()

        if get_device is None:
            return ErrorResponse(
                status_code=400,
                message="Device not found"
            )

        res = await delete_record(db, get_device)
        return StandardResponse(
            status_code=200,
            message="Device deleted successfully",
            data=res
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )
