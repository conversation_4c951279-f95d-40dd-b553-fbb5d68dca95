import logging
import json
from app.kafka_producer.config import kafka_producer_config

log = logging.getLogger("uvicorn")


async def broadcast_producer(data):
    encoded_data = json.dumps(data).encode("ascii")
    await kafka_producer_config.send_and_wait("broadcast", encoded_data)


async def service_request_producer(data):
    encoded_data = json.dumps(data).encode("ascii")
    await kafka_producer_config.send_and_wait("service_request", encoded_data)

async def invoice_producer(data):
    print(data, "invoice data")
    encoded_data = json.dumps(data).encode("ascii")
    await kafka_producer_config.send_and_wait("invoice", encoded_data)
