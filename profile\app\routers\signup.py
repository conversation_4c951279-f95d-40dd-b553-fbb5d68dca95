from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException
from app.helper import StandardResponse, ErrorResponse
from app.routers.service_provider import create_artisan, update_artisan_status
from app.utils import create_record
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from app.models import Account, ServiceProvider, Users, Business
from app.database import get_db
from app.schemas import (
    CognitoGetUserRequest,
    SigninRequest,
    SignupRequest,
    OtpVerifyRequest,
    ResendOtpRequest,
    RefreshRequest,
)
from app.cognito_utils import (
    admin_get_user,
    get_token_after_signup,
    resend_confirmation_code,
    add_user_to_group,
    cognito_sign_up,
    confirm_signup,
    cognito_renew_token,
)
from app.routers.users import create_users, update_user_status

router = APIRouter(tags=["Signup"])


@router.post("/renew-token")
def get_new_access_token(request: RefreshRequest):
    try:
        return cognito_renew_token(
            refresh_token=request.refresh_token, role=request.role
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Unexpected error occurred", error=str(e)
        )


# For Both signup/Login
@router.post("/signup")
async def signup(request: SignupRequest, db: AsyncSession = Depends(get_db)):
    try:
        phone_number = request.phone_number
        role = request.role
        country_code = request.country_code

        # check if service provider already exists
        if role == "artisan":
            query = select(ServiceProvider).filter(
                ServiceProvider.phone_number == phone_number
            )
            result = await db.execute(query)
            if result.scalars().first():
                return ErrorResponse(
                    status_code=400, message="Service Provider already exists"
                )
        # check if user already exists
        elif role == "user":
            query = select(Users).filter(Users.phone_number == phone_number)
            result = await db.execute(query)
            if result.scalars().first():
                return ErrorResponse(status_code=400, message="User already exists")

        user_status = admin_get_user(phone_number)
        if user_status is None:
            # ✅ Create Account in Cognito
            try:
                auth_id = cognito_sign_up(phone_number)
                add_user_to_group(auth_id.get("UserSub"), role.lower())
            except Exception as e:
                return ErrorResponse(
                    status_code=500, message="Error creating Cognito user", error=str(e)
                )

            payload = {
                "phone_number": phone_number,
                "country_code": country_code,
                "auth_id": auth_id.get("UserSub"),
            }
            try:
                if role == "artisan":
                    try:
                        # Creating Service Provider Record and Account
                        sp_res = await create_artisan(payload, db)
                        print(sp_res, "user_ressssssss")
                        account = {
                            "user_id": sp_res["data"],
                            "account_type": "ARTISAN",
                            "balance": 0,
                            "currency": "GHS",
                            "status": "ACTIVE",
                        }
                        new_account = await create_record(db, Account, account)

                        return StandardResponse(
                            status_code=200,
                            message="Account created successfully",
                            data=sp_res,
                        )
                    except:
                        return ErrorResponse(
                            status_code=500,
                            message="Error creating database record",
                            error=str(e),
                        )
                elif role == "user":
                    try:
                        # Creating User Record and Account
                        user_res = await create_users(payload, db)
                        print(user_res, "user_ressssssss")
                        account = {
                            "user_id": user_res["data"],
                            "account_type": "USER",
                            "balance": 0,
                            "currency": "GHS",
                            "status": "ACTIVE",
                        }
                        new_account = await create_record(db, Account, account)

                        return StandardResponse(
                            status_code=200,
                            message="Account created successfully",
                            data=user_res,
                        )
                    except:
                        return ErrorResponse(
                            status_code=500,
                            message="Error creating database record",
                            error=str(e),
                        )
            except Exception as e:
                return ErrorResponse(
                    status_code=500,
                    message="Error creating database record",
                    error=str(e),
                )
        else:
            return ErrorResponse(
                status_code=400, message="User already exists in Cognito"
            )
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Unexpected error occurred", error=str(e)
        )


@router.post("/otp-verify")
async def otp_verify(request: OtpVerifyRequest, db: AsyncSession = Depends(get_db)):
    phonenumber = request.phone_number
    confirmation_code = request.otp
    role = request.role
    try:
        user_status = admin_get_user(phonenumber)
        if user_status is None:
            return ErrorResponse(status_code=400, message="User not found in Cognito")
        if user_status.get("UserStatus") != "CONFIRMED":
            cognito_resp = confirm_signup(phonenumber, confirmation_code)
            # auth_id = user_status.get('Username')

            for i in user_status["UserAttributes"]:
                if i["Name"] == "custom:account_id":
                    account_id = i["Value"]
                    break

            if role == "artisan":
                r = await update_artisan_status({"account_id": account_id}, db)
                print(r, "rrrrrrrrrrrrr")
            elif role == "user":
                r = await update_user_status({"account_id": account_id}, db)
                print(r, "rrrrrrrrrrrrr")

            # token = get_token_after_signup(phonenumber)
            # return StandardResponse(status_code=200, message="OTP verified successfully", data=token)
            return StandardResponse(status_code=200, message="OTP verified successfully")
        else:
            return ErrorResponse(status_code=400, message="User already confirmed")
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="OTP verification failed", error=str(e)
        )


@router.post("/resend-otp")
async def resend_otp(request: ResendOtpRequest):
    phonenumber = request.phone_number
    try:
        cognito_resp = resend_confirmation_code(phonenumber)
        return StandardResponse(
            status_code=200, message="OTP resent successfully", data=cognito_resp
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Error resending OTP", error=str(e)
        )


@router.post("/cognito-get-user")
async def cognito_get_user(
    request: CognitoGetUserRequest, db: AsyncSession = Depends(get_db)
):
    phonenumber = request.phone_number
    role = request.role
    country_code = request.country_code
    try:
        # For business_owner, allow lookup by email if phone number is not provided
        if role == "business_owner" and not phonenumber and request.email:
            query = select(Business).filter(Business.email == request.email)
            result = await db.execute(query)
            business = result.scalars().first()
            if business:
                phonenumber = business.phone_number
            else:
                return ErrorResponse(
                    status_code=400,
                    message="user not found in database",
                    error="not_found",
                )

        if not phonenumber:
            return ErrorResponse(
                status_code=422, message="Phone number is missing in body"
            )

        user_status = admin_get_user(phonenumber)
        if user_status is None:
            return ErrorResponse(
                status_code=400, message="user not found in cognito", error="false"
            )  # redirect to signup
        print(user_status)

        if user_status.get("UserStatus") != "CONFIRMED":
            return ErrorResponse(
                status_code=400, message="user not confirmed", error="not_confirmed"
            )  # redirect to resend_otp
        
        payload = {
                "phone_number": phonenumber,
                "country_code": country_code,
                "auth_id": None,
            }
        if role == "artisan":
            query = select(ServiceProvider).filter(
                ServiceProvider.phone_number == phonenumber
            )
            result = await db.execute(query)
            res = result.scalars().first()
            if res:
                current_status = res.status
                if user_status.get("Enabled") == False:
                    return ErrorResponse(
                        status_code=400,
                        message="user not enabled in cognito",
                        error=current_status,
                    )

                return StandardResponse(
                    status_code=200, message="user found in cognito", data=current_status
                )
            else:
                # Trying to get Cognito_id from User
                query = select(Users).filter(Users.phone_number == phonenumber)
                result = await db.execute(query)
                res = result.scalars().first()
                if res:
                    payload["auth_id"] = res.auth_id
                    # Creating Service Provider Record and Account
                    sp_res = await create_artisan(payload, db)
                    print(sp_res, "user_ressssssss")
                    current_status = sp_res.status
                    account = {
                        "user_id": sp_res["data"],
                        "account_type": "ARTISAN",
                        "balance": 0,
                        "currency": "GHS",
                        "status": "ACTIVE",
                    }
                    new_account = await create_record(db, Account, account)

                    if user_status.get("Enabled") == False:
                        return ErrorResponse(
                            status_code=400,
                            message="user not enabled in cognito",
                            error=current_status,
                        )
                    return StandardResponse(status_code=200, message="user found in cognito", data=current_status)

                return ErrorResponse(
                    status_code=400,
                    message="user not found in database",
                    error="not_found",
                )
        elif role == "user":
            query = select(Users).filter(Users.phone_number == phonenumber)
            result = await db.execute(query)
            res = result.scalars().first()
            if res:
                current_status = res.status
                if user_status.get("Enabled") == False:
                    return ErrorResponse(
                        status_code=400,
                        message="user not enabled in cognito",
                        error=current_status,
                    )
                return StandardResponse(
                    status_code=200, message="user found in cognito", data=current_status
                )
            else:
                # Trying to get Cognito_id from Service Provider
                query = select(ServiceProvider).filter(ServiceProvider.phone_number == phonenumber)
                result = await db.execute(query)
                res = result.scalars().first()
                if res:
                    payload["auth_id"] = res.auth_id
                    # Creating User Record and Account
                    user_res = await create_users(payload, db)
                    print(user_res, "user_ressssssss")
                    current_status = user_res.status
                    account = {
                        "user_id": user_res["data"],
                        "account_type": "USER",
                        "balance": 0,
                        "currency": "GHS",
                        "status": "ACTIVE",
                    }
                    new_account = await create_record(db, Account, account)

                    if user_status.get("Enabled") == False:
                        return ErrorResponse(
                            status_code=400,
                            message="user not enabled in cognito",
                            error=current_status,
                        )
                    return StandardResponse(status_code=200, message="user found in cognito", data=current_status)
                
                return ErrorResponse(
                    status_code=400,
                    message="user not found in database",
                    error="not_found",
                )

        elif role == "business_owner":
            query = select(Business).filter(Business.phone_number == phonenumber)
            result = await db.execute(query)
            res = result.scalars().first()
            if res:
                if user_status.get("Enabled") == False:
                    return ErrorResponse(
                        status_code=400,
                        message="Business owner not enabled in cognito",
                        error=res.status,
                    )
                return StandardResponse(
                    status_code=200, message="Business owner found in cognito", data=res.status
                )
            else:
                return ErrorResponse(
                    status_code=400,
                    message="Business owner not found in database",
                    error="not_found",
                )

    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Error getting user status", error=str(e)
        )

from app.cognito_utils import cognito_client
from app.config import get_settings

@router.post("/login")
async def login(request: SigninRequest):
    """Login and get Access & Refresh Token"""
    try:
        phone_number = request.phone_number
        auth_response = cognito_client.initiate_auth(
            AuthFlow="USER_AUTH",
            ClientId=get_settings().COGNITO_CLIENT_ID,
            AuthParameters={
                "USERNAME": phone_number,
                "PREFERRED_CHALLENGE": "SMS_OTP",
            }
        )
        print(auth_response, "auth_response")
        return StandardResponse(
            status_code=200, message="Authentication Initialized", data=auth_response
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error logging in", error=str(e))
    

@router.post("/login_verify")
async def login_verify(request: SigninRequest):
    """Login and get Access & Refresh Token"""
    try:
        phone_number = request.phone_number
        otp = request.otp
        session = request.session
        auth_response = cognito_client.respond_to_auth_challenge(
            ChallengeName="SMS_OTP",
            ClientId=get_settings().COGNITO_CLIENT_ID,
            ChallengeResponses={ 
                "USERNAME" : phone_number,
                "SMS_OTP_CODE" : otp 
            },
            Session=session
        )
        print(auth_response, "auth_response")
        return StandardResponse(status_code=200, message="Login successful", data=auth_response)
        # return {
        #     "message": "Login successful!",
        #     "access_token": auth_response["AuthenticationResult"]["AccessToken"],
        #     "refresh_token": auth_response["AuthenticationResult"]["RefreshToken"],
        #     "id_token": auth_response["AuthenticationResult"]["IdToken"]
        # }
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error logging in", error=str(e))


# @app.post("/refresh-token")
# async def refresh_token(refresh_token: str):
#     """Use Refresh Token to get a new Access Token"""
#     try:
#         response = cognito_client.initiate_auth(
#             ClientId=CLIENT_ID,
#             AuthFlow="REFRESH_TOKEN_AUTH",
#             AuthParameters={"REFRESH_TOKEN": refresh_token}
#         )

#         return {
#             "access_token": response["AuthenticationResult"]["AccessToken"]
#         }

#     except Exception as e:
#         raise HTTPException(status_code=400, detail=str(e))
