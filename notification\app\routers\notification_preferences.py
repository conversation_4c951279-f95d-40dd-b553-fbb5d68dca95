from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from uuid import UUID
from app.database import get_db
from app.models import NotificationPreferences
from app.schemas import NotificationPreferencesCreate, NotificationPreferencesOut, NotificationPreferencesUpdate
from app.utils import create_record, update_record, delete_record
from app.helper import StandardResponse, ErrorResponse
from app.auth import permission_checker
from fastapi.responses import JSONResponse

router = APIRouter(prefix="/notification-preferences", tags=["Notification Preferences"])


@router.post("/")
async def create_notification_preferences(
    payload: NotificationPreferencesCreate, 
    db: AsyncSession = Depends(get_db),
    # user = Depends(permission_checker)
):
    try:
        # if isinstance(user, JSONResponse):
        #     return user
        
        # Check if notification preferences already exist for this user and device
        existing = await db.execute(
            select(NotificationPreferences).where(
                NotificationPreferences.user_id == payload.user_id,
                NotificationPreferences.device_id == payload.device_id
            )
        )
        if existing.scalars().first():
            return ErrorResponse(
                status_code=400, 
                message="Notification preferences already exist for this user and device"
            )

        # Create new notification preferences
        new_preferences = await create_record(db, NotificationPreferences, payload.model_dump())
        
        if isinstance(new_preferences, str):  # Error occurred
            return ErrorResponse(status_code=500, message=new_preferences)
        
        data = NotificationPreferencesOut.model_validate(new_preferences, from_attributes=True)
        return StandardResponse(
            status_code=201,
            data=data,
            message="Notification preferences created successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Notification preference creation failed",error=str(e))


@router.get("/user/{user_id}")
async def get_notification_preferences_by_user(
    user_id: UUID, 
    db: AsyncSession = Depends(get_db),
    user = Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        result = await db.execute(
            select(NotificationPreferences).where(NotificationPreferences.user_id == user_id)
        )
        preferences_list = result.scalars().all()

        if not preferences_list:
            return ErrorResponse(
                status_code=404, 
                message="No notification preferences found for this user"
            )

        data = [
            NotificationPreferencesOut.model_validate(prefs, from_attributes=True)
            for prefs in preferences_list
        ]
        
        return StandardResponse(
            status_code=200,
            data=data,
            message="Notification preferences retrieved successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Notification preferences retrieval failed",error=str(e))


@router.get("/")
async def get_all_notification_preferences(
    db: AsyncSession = Depends(get_db),
    user = Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):
            return user
        
        result = await db.execute(select(NotificationPreferences))
        preferences_list = result.scalars().all()

        data = [
            NotificationPreferencesOut.model_validate(prefs, from_attributes=True)
            for prefs in preferences_list
        ]
        
        return StandardResponse(
            status_code=200,
            data=data,
            message="All notification preferences retrieved successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Notification preferences retrieval failed",error=str(e))


@router.put("/{preferences_id}")
async def update_notification_preferences(
    preferences_id: UUID,
    payload: NotificationPreferencesUpdate,
    db: AsyncSession = Depends(get_db),
    user = Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
         
        result = await db.execute(
            select(NotificationPreferences).where(NotificationPreferences.id == preferences_id)
        )
        preferences = result.scalars().first()

        if not preferences:
            return ErrorResponse(
                status_code=404, 
                message="Notification preferences not found"
            )

        # Update the preferences using the utility function
        updated_preferences = await update_record(db, payload, preferences)

        data = NotificationPreferencesOut.model_validate(updated_preferences, from_attributes=True)
        return StandardResponse(
            status_code=200,
            data=data,
            message="Notification preferences updated successfully"
        )
    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message="Notification preferences update failed",error=str(e))




@router.delete("/{preferences_id}")
async def delete_notification_preferences(
    preferences_id: UUID,
    db: AsyncSession = Depends(get_db),
    user = Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        result = await db.execute(
            select(NotificationPreferences).where(NotificationPreferences.id == preferences_id)
        )
        preferences = result.scalars().first()

        if not preferences:
            return ErrorResponse(
                status_code=404, 
                message="Notification preferences not found"
            )

        await delete_record(db, preferences)
        return StandardResponse(
            status_code=200,
            message="Notification preferences deleted successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Notification preferences deletion failed",error=str(e))