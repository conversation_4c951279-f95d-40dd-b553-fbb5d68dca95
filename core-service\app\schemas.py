from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from uuid import UUID
from enum import Enum
from datetime import datetime
from app.models_enum import UserStatusType, ServiceProviderStatusType, Gender

class UserProfileBase(BaseModel):
    first_name: Optional[str] = Field(None, min_length=2)
    last_name: Optional[str] = Field(None, min_length=2)
    phone_number: Optional[str] = Field(None)
    email: Optional[EmailStr] = None
    status: Optional[UserStatusType] = None
    last_active_datetime: Optional[datetime] = None

class UserProfileCreate(UserProfileBase):
    pass

class UserListRequest(BaseModel):
    user_id: Optional[UUID] = None
    q: Optional[str] = None
    role_name: Optional[str] = None
    status: Optional[UserStatusType] = None
    skip: int = 1
    limit: int = 10


class UserProfileOut(BaseModel):
    id: UUID
    first_name: Optional[str]
    last_name: Optional[str]
    phone_number: Optional[str]
    email: Optional[EmailStr]
    profile_image_url: Optional[str]
    status: Optional[UserStatusType]
    last_active_datetime: Optional[datetime]
    notification_token: Optional[str]
    is_profile_complete: Optional[bool]

    class Config:
        from_attributes = True


# Address Schemas
class AddressDetail(BaseModel):
    address: str
    latitude: float
    longitude: float
    address_category: str

class AddressCreate(BaseModel):
    name: Optional[str]
    details: AddressDetail
    is_primary: Optional[bool] = False
    locality: Optional[str]

class AddressUpdate(BaseModel):
    name: Optional[str] = None
    details: Optional[AddressDetail] = None
    is_primary: Optional[bool] = None

class AddressOut(BaseModel):
    id: UUID
    name: Optional[str]
    details: Optional[AddressDetail]
    is_primary: bool
    locality: Optional[str]

    class Config:
        orm_mode = True



# Roles
class PermissionBase(BaseModel):
    user_id: Optional[UUID]
    policy_name: str
    permission: dict


class PermissionCreate(PermissionBase):
    role_id: UUID


class PermissionOut(PermissionBase):
    id: UUID
    role_id: UUID

    class Config:
        orm_mode = True


class RoleBase(BaseModel):
    role_name: str


class RoleCreate(RoleBase):
    pass


class RoleOut(RoleBase):
    id: UUID
    permissions: List[PermissionOut] = []

    class Config:
        orm_mode = True


# User Blacklist
class UsersBlacklistBase(BaseModel):
    user_id: UUID
    reason: Optional[str]
    is_revoked: Optional[bool] = False
    last_revoked_datetime: Optional[datetime]
    blacklisted_by: Optional[UUID]


class UsersBlacklistCreate(UsersBlacklistBase):
    pass


class UsersBlacklistUpdate(BaseModel):
    reason: Optional[str] = None
    is_revoked: Optional[bool] = None
    last_revoked_datetime: Optional[datetime] = None
    blacklisted_by: Optional[str] = None

class UsersBlacklistOut(BaseModel):
    id: UUID
    user_id: UUID
    reason: Optional[str]
    is_revoked: bool
    last_revoked_datetime: Optional[datetime]
    blacklisted_by: Optional[UUID]

    class Config:
        orm_mode = True


# For Signup schemas
class CognitoRole(str, Enum):
    ARTISAN = "artisan"
    USER = "user"
    BUSINESS_PROVIDER = "business_provider"


# class SignupRequest(BaseModel):
#     phone_number: str
#     country_code: str
#     role: CognitoRole
class SignupRequest(BaseModel):
    phone_number: str
    country_code: str
    role: Optional[CognitoRole] = None  # role only mandatory during signup

class OtpVerifyRequest(BaseModel):
    phone_number: str
    country_code: str
    otp: str
    session: Optional[str] = None  # Required only for Login
    role: Optional[CognitoRole] = None  # Required only for Signup

class RefreshRequest(BaseModel):
    refresh_token: str
    role: CognitoRole
    phone_number: str
class ResendOtpRequest(BaseModel):
    phone_number: str

class TestSignupRequest(BaseModel):
    country_code: str
    phone_number: str
    email: str
    role: CognitoRole

class CognitoGetUserRequest(BaseModel):
    phone_number: Optional[str] = None
    email: Optional[str] = None
    role: CognitoRole


# User Preferences Schemas
class UserPreferencesBase(BaseModel):
    currency: str
    language: str
    is_otp_enabled: Optional[bool] = True
    country: str


class UserPreferencesCreate(UserPreferencesBase):
    user_id: UUID


class UserPreferencesUpdate(BaseModel):
    currency: Optional[str] = None
    language: Optional[str] = None
    is_otp_enabled: Optional[bool] = None
    country: Optional[str] = None


class UserPreferencesOut(BaseModel):
    id: UUID
    user_id: UUID
    currency: str
    language: str
    is_otp_enabled: bool
    country: str

    class Config:
        from_attributes = True
    
class AdminSignupRequest(BaseModel):
    first_name: str
    last_name: str
    email: str
    phone_number: str
    country_code: str
    role: str
    status: Optional[UserStatusType] = None

class SigninRequest(BaseModel):
    phone_number: Optional[str] = None
    session: Optional[str] = None
    otp: Optional[str] = None
class CognitoLogoutRequest(BaseModel):
    access_token: str

# Region Schemas
class RegionBase(BaseModel):
    name: str
    short_name: str
    area_codes: List[str]

class RegionCreate(RegionBase):
    pass

class RegionUpdate(BaseModel):
    name: Optional[str] = None
    short_name: Optional[str] = None
    area_codes: Optional[List[str]] = None

class RegionOut(BaseModel):
    id: UUID
    name: str
    short_name: str
    area_codes: List[str]
    is_active: bool
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True

class ServiceProviderApproval(BaseModel):
    status: UserStatusType
    reason: Optional[str] = None

class DeleteUser(BaseModel):
    country_code: str
    phone_number: str

class ForgotPasswordRequest(BaseModel):
    country_code: str
    phone_number: str

class ResetPasswordRequest(BaseModel):
    country_code: str
    phone_number: str
    otp: str
    new_password: str
