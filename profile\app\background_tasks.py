import asyncio
import logging
import os
from typing import Dict, Any, List
import uuid
import pandas as pd
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db, engine
from app.bulk_upload import process_excel_file

logger = logging.getLogger(__name__)

# Store task status in memory (in production, use Redis or a database)
task_status = {}

async def run_bulk_upload_task(file_path: str, token: str) -> str:
    """
    Create a background task for bulk upload processing.
    
    Args:
        file_path: Path to the Excel file
        token: Authentication token
        
    Returns:
        Task ID for tracking the progress
    """
    # Generate a unique task ID
    task_id = str(uuid.uuid4())
    
    # Initialize task status
    task_status[task_id] = {
        "status": "pending",
        "progress": 0,
        "total": 0,
        "completed": 0,
        "errors": [],
        "message": "Task queued"
    }
    
    # Start the background task
    asyncio.create_task(
        _process_bulk_upload(task_id, file_path, token)
    )
    
    return task_id

async def _process_bulk_upload(task_id: str, file_path: str, token: str):
    """
    Process the bulk upload in the background.
    
    Args:
        task_id: Unique task identifier
        file_path: Path to the Excel file
        token: Authentication token
    """
    try:
        # Update task status
        task_status[task_id]["status"] = "processing"
        task_status[task_id]["message"] = "Processing started"
        
        # Get row count for progress tracking
        try:
            df = pd.read_excel(file_path)
            total_rows = len(df)
            task_status[task_id]["total"] = total_rows
        except Exception as e:
            logger.error(f"Error reading Excel file: {e}")
            task_status[task_id]["status"] = "failed"
            task_status[task_id]["message"] = f"Error reading Excel file: {str(e)}"
            return
        
        # Get a database session
        async with AsyncSession(engine) as db:
            try:
                # Process the file
                result = await process_excel_file(db, file_path, token)
                
                # Update task status based on result
                if result["success"]:
                    task_status[task_id]["status"] = "completed"
                    task_status[task_id]["completed"] = result["created"]
                    task_status[task_id]["errors"] = result["errors"]
                    task_status[task_id]["message"] = result["message"]
                    task_status[task_id]["progress"] = 100
                else:
                    task_status[task_id]["status"] = "failed"
                    task_status[task_id]["errors"] = result["errors"]
                    task_status[task_id]["message"] = result["message"]
            except Exception as e:
                logger.error(f"Error processing file: {e}")
                task_status[task_id]["status"] = "failed"
                task_status[task_id]["message"] = f"Error processing file: {str(e)}"
                raise
        
        # Clean up the file after processing
        try:
            os.remove(file_path)
        except Exception as e:
            logger.error(f"Error removing temporary file: {e}")
            
    except Exception as e:
        logger.error(f"Background task error: {e}")
        task_status[task_id]["status"] = "failed"
        task_status[task_id]["message"] = f"Background task error: {str(e)}"
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging

def get_task_status(task_id: str) -> Dict[str, Any]:
    """
    Get the status of a background task.
    
    Args:
        task_id: The task ID
        
    Returns:
        Task status information
    """
    if task_id not in task_status:
        return {
            "status": "not_found",
            "message": "Task not found"
        }
    
    return task_status[task_id]