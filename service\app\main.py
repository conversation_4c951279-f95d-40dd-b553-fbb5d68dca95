import asyncio
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.routes import category, services, service_request, service_broadcast , taxes,artisan_assigned,cart,service_request_artisan, artisan,rating, booking_cancellation, booking_cancellation_reasons, refund
# from app.kafka_producer.config import kafka_producer_config
# from app.kafka_consumer.consumer import service_request_consumer, consume_service_request_consumer, invoice_consumer, consume_invoice_consumer
import logging

log = logging.getLogger("uvicorn")


app = FastAPI(
              debug=True, 
              docs_url="/service/docs",           # Swagger UI
              redoc_url=None,                     # Disable ReDoc (optional)
              openapi_url="/service/openapi.json" # OpenAPI schema path
      )


# CORS Middleware (Allow all origins for frontend integration)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Change to specific frontend domain in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# @app.on_event("startup")
# async def startup_event():
#     """Start up event for FastAPI application."""
#     log.info("Starting up...")
    # kafka_conn = False
    # while not kafka_conn: 
    #     try:
    #         await kafka_producer_config.start()
    #         await service_request_consumer.start()
    #         await invoice_consumer.start()
    #         kafka_conn = True
    #     except:
    #         pass
    # log.info("Kafka Started...")
    # asyncio.create_task(consume_service_request_consumer())
    # asyncio.create_task(consume_invoice_consumer())

# @app.on_event("shutdown")
# async def shutdown_event():
#     """Shutdown event for FastAPI application."""
#     log.info("Shutting down...")
#     await kafka_producer_config.stop()
#     await service_request_consumer.stop()


@app.get("/health")
def read_root():
    return {"message": "Service is Up and running"}

# Registering Routes
app.include_router(category.router)
app.include_router(services.router)
app.include_router(service_request.router)
# app.include_router(service_broadcast.router)
app.include_router(taxes.router)
app.include_router(artisan_assigned.router)
app.include_router(cart.router)
app.include_router(artisan.router)
app.include_router(rating.router)
app.include_router(service_request_artisan.router)
app.include_router(booking_cancellation.router)
app.include_router(booking_cancellation_reasons.router)
app.include_router(refund.router)


