from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.routes import artisan_search
from app.database import enable_postgis
import uvicorn
# from app.audit_log import log_requests

app = FastAPI(
    title="API",
    description="FastAPI-based backend for services",
    version="1.0.0"
)

# app.middleware("http")(log_requests)


# CORS Middleware (Allow all origins for frontend integration)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Change to specific frontend domain in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database and seed data
@app.on_event("startup")
async def startup_event():
    try:
        # enable_postgis()  # Enable PostGIS extension
        # print("PostGIS enabled successfully")
        pass
    except Exception as e:
        print(f"Error enabling PostGIS: {str(e)}")

@app.get("/health")
def read_root():
    return {"message": "Service is Up and running"}

# Registering Routes
app.include_router(artisan_search.router)
