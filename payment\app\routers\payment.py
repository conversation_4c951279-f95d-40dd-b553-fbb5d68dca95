from datetime import datetime, timedelta
import http
import uuid
import os
import random

from fastapi import APIRouter, Depends, HTTPException, Header, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_
from typing import Optional, Annotated

from app.database import get_db
from app import models, schemas
from app.utils import (
    BookingStatus,
    check_datetime,
    create_record,
    get_gtipay_transaction_detail,
    update_payment_rec,
    update_record,
    delete_record,
    get_id_header,
    is_valid_uuid,
)
from app.notification import send_push_notification
import json
import requests
from app.config import settings

# from app.utils import TransactionTypeType, StatusType
# from app.models import Payment, Transaction
from app.utils import TransactionTypeType, StatusType, PaymentMethodType, get_surcharges, get_tax
from app.schemas import RefundPayment, RequestPayment, UpdateTransactionStatus

router = APIRouter()
from app.helper import StandardResponse, ErrorResponse
from app.models import Account, Booking, ServiceProvider, Transaction, Payment, Users
from sqlalchemy.sql import text

from app.models import Services, Category, Payment
import logging
from sqlalchemy import select, and_, select, join, outerjoin,or_
from sqlalchemy.orm import aliased
from sqlalchemy.orm.attributes import flag_modified
from app.utils import StatusType
from uuid import UUID

# Create aliases for Users and ServiceProvider tables
Users_to = aliased(Users)
ServiceProvider_to = aliased(ServiceProvider)

# Configure the logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# ================================
# Payment Routes
# ================================
@router.post("/payment-create")
async def create_payment(
    request: schemas.CreatePayment,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Create a new Payment record.
    """
    try:
        print(request, "request")
        resp = await get_id_header(Authorization)
        print(resp.json())
        if resp.status_code == 200:
            resp_json = resp.json()
            # account_id = resp_json.get("account_id")
            account_id = request.user_id
        else:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        request_dict = request.dict()
        new_dict = request_dict
        print(request_dict)
        surcharges = get_surcharges(request_dict["base_service_fee"])
        tax = get_tax(request_dict["base_service_fee"])
        new_dict['surcharges'] = surcharges
        new_dict['tax'] = tax
        u_acc_id = account_id
        u_query = select(Account).filter(Account.user_id == u_acc_id)
        u_result = await db.execute(u_query)
        acc_id = request_dict["artisan_id"]

        u_account_obj = u_result.scalars().first()
        print(u_account_obj, "u_account_obj")

        a_query = select(Account).filter(Account.user_id == acc_id)
        a_result = await db.execute(a_query)
        a_account_obj = a_result.scalars().first()

        payment_link = ""
        wallet_balance = 0
        if a_account_obj is None:
            return ErrorResponse(status_code=404, message="Artisan account not found")
        print(request_dict["payment_method"], "payment method")
        if not u_account_obj and request_dict["payment_method"].lower() != "cash":
            return ErrorResponse(status_code=404, message="User account not found")
        # print("u_account_obj", u_account_obj.account_details)
        a_account_balance = a_account_obj.balance
        u_account_balance = u_account_obj.balance

        if u_account_balance < 0:
            total_payable = (
                request_dict["base_service_fee"]
                + surcharges + tax
                - u_account_balance
            )
            u_account_balance = 0
        else:
            total_payable = (
                request_dict["base_service_fee"] + surcharges + tax
            )

        if request_dict["payment_method"].lower() == "cash":
            new_dict["status"] = "SUCCESS"
            send_push_notification(
                auth_token=Authorization,
                title="Payment Confirmed",
                message="Payment has been confirmed",
                sender_id=request.user_id,
                type="user",
            )
            send_push_notification(
                auth_token=Authorization,
                title="Payment Confirmed",
                message="Payment has been received",
                sender_id=request.artisan_id,
                type="service_provider",
            )

            new_payment = await create_record(db, Payment, new_dict)
            if isinstance(new_payment, str):
                return ErrorResponse(
                    status_code=400,
                    message=str(new_payment),
                    error={"error": new_payment},
                )
            new_payment_id = str(new_payment.id)

            user_transaction = Transaction(
                payment_id=new_payment_id,
                from_entity=str(account_id),
                payment_method=request_dict["payment_method"],
                to_entity=str(request_dict["artisan_id"]),
                transaction_type=TransactionTypeType.PAYMENT,
                status=StatusType.SUCCESS,
                amount=total_payable,
            )
            # artisan_transaction = Transaction(
            #     payment_id=new_payment_id,
            #     from_entity=str(request_dict["artisan_id"]),
            #     payment_method=request_dict["payment_method"],
            #     to_entity=str(account_id),  # TODO: change to GTI Account ID
            #     transaction_type=TransactionTypeType.PAYMENT,
            #     status=StatusType.INIT,
            #     amount=request_dict["surcharges"],
            # )
            u_record = await create_record(db, Transaction, user_transaction.as_dict())
            # a_record = await create_record(
            #     db, Transaction, artisan_transaction.as_dict()
            # )

            setattr(
                a_account_obj, "balance", (a_account_balance - (surcharges + tax))
            )
            await db.commit()
            await db.refresh(a_account_obj)

            if u_account_balance == 0:
                setattr(u_account_obj, "balance", 0)
                await db.commit()
                await db.refresh(u_account_obj)

            return StandardResponse(
                status_code=200,
                message="Payment completed successfully",
                data={
                    "data": "Payment completed successfully",
                    "payment_id": new_payment_id,
                },
            )
        elif request_dict["payment_method"].lower() == "wallet":
            new_dict["status"] = "INIT"
            login_url = f"{settings.WALLET_API_URL}/Authentication/Login"

            login_payload = json.dumps(
                {
                    "username": settings.WALLET_USERNAME,
                    "password": settings.WALLET_PASSWORD,
                }
            )
            login_headers = {"accept": "*/*", "Content-Type": "application/json"}

            login_response = requests.request(
                "POST", login_url, headers=login_headers, data=login_payload
            )
            response_data = login_response.json()
            token = response_data.get("token")
            print(token)
            if token:
                wallet_txn_id = str(random.randint(10**13, 10**14 - 1))
                user_transaction = Transaction(
                        payment_id=None,
                        from_entity=str(account_id),
                        payment_method=request_dict["payment_method"],
                        to_entity=str(request_dict["artisan_id"]),
                        transaction_type=TransactionTypeType.PAYMENT,
                        status=StatusType.INIT,
                        amount=total_payable,
                        transaction_id=wallet_txn_id
                    )
                u_txn_record = await create_record(db, Transaction, user_transaction.as_dict())

                send_money_url = (f"{settings.WALLET_API_URL}/DebitMoney/DebitMoneyService")
                send_money_payload = json.dumps(
                    {
                        "accountName": u_account_obj.account_details["walletName"],
                        "accountNumber": u_account_obj.account_details["walletNumber"],
                        "amount": total_payable,
                        "channel": "MNO",
                        "institutionCode": u_account_obj.account_details["walletCode"],
                        "transactionId": wallet_txn_id,
                        "debitNaration": "JOBCONNECT Service Payment",
                        "currency": "GHS",
                    }
                )

                print(send_money_payload, "payload")

                send_money_headers = {
                    "accept": "text/plain",
                    "Authorization": f"Bearer {token}",  # Use the token from the previous response
                    "Content-Type": "application/json",
                }

                send_money_response = requests.post(
                    send_money_url, headers=send_money_headers, data=send_money_payload
                )

                print(send_money_response.json(), "send_money_response")

                if send_money_response.json()["statusCode"] in ["200", "202"]:
                    print("success")
                    new_dict["status"] = "SUCCESS"
                    new_dict["payment_details"] = json.loads(send_money_response.text)
                    new_payment = await create_record(db, Payment, new_dict)
                    if isinstance(new_payment, str):
                        return ErrorResponse(
                            status_code=400,
                            message=str(new_payment),
                            error={"error": new_payment},
                        )
                    new_payment_id = str(new_payment.id)

                    # Updating payment_id in transaction entry
                    setattr(u_txn_record, 'payment_id', new_payment_id)
                    await db.commit()
                    await db.refresh(u_txn_record)
                    # user_transaction = Transaction(
                    #     payment_id=new_payment_id,
                    #     from_entity=str(account_id),
                    #     payment_method=request_dict["payment_method"],
                    #     to_entity=str(request_dict["artisan_id"]),
                    #     transaction_type=TransactionTypeType.PAYMENT,
                    #     status=StatusType.SUCCESS,
                    #     amount=total_payable,
                    #     transaction_id=wallet_txn_id
                    # )
                    # u_record = await create_record(db, Transaction, user_transaction.as_dict())
                    # setattr(
                    #     a_account_obj,
                    #     "balance",
                    #     a_account_balance + request_dict["base_service_fee"],
                    # )
                    # await db.commit()
                    # await db.refresh(a_account_obj)

                    # if u_account_balance == 0:
                    #     setattr(u_account_obj, "balance", 0)
                    #     await db.commit()
                    #     await db.refresh(u_account_obj)

                    return StandardResponse(
                        status_code=200,
                        message="Payment completed successfully",
                        data={
                            "data": "Payment completed successfully",
                            "payment_id": new_payment_id,
                        },
                    )
                else:
                    return ErrorResponse(
                        status_code=500,
                        message="Payment Failed",
                        error={"error": send_money_response.text},
                    )

        elif request_dict["payment_method"].lower() == "card":
            new_dict["status"] = "INIT"
            try:
                user_transaction = Transaction(
                        payment_id=None,
                        from_entity=str(account_id),
                        payment_method=request_dict["payment_method"],
                        to_entity=str(request_dict["artisan_id"]),
                        transaction_type=TransactionTypeType.PAYMENT,
                        status=StatusType.INIT,
                        amount=total_payable,
                    )

                u_record = await create_record(db, Transaction, user_transaction.as_dict())
                print(new_dict["status"])
                url = f"{settings.CARD_API_URL}/open/orders/create"
                payload = json.dumps(
                    # {
                    #     "data": {
                    #         "action": "AUTH",
                    #         "class": "ECOM",
                    #         "capture_method": "AUTOMATIC",
                    #         "payment_token": "",
                    #         "customer_details": {
                    #             "m_customer_id": str(account_id),
                    #             "name": u_account_obj.account_details["name"],
                    #             "email": u_account_obj.account_details["email"],
                    #             "mobile": u_account_obj.account_details["mobile"],
                    #             "code": u_account_obj.account_details["code"],
                    #         },
                    #         "billing_details": {
                    #             "address_line1": u_account_obj.account_details[
                    #                 "address_line1"
                    #             ],
                    #             "address_line2": u_account_obj.account_details[
                    #                 "address_line2"
                    #             ],
                    #             "city": u_account_obj.account_details["city"],
                    #             "province": u_account_obj.account_details["province"],
                    #             "country": u_account_obj.account_details["country"],
                    #             "pin": u_account_obj.account_details["pin"],
                    #         },
                    #         "shipping_details": {
                    #             "address_line1": u_account_obj.account_details[
                    #                 "address_line1"
                    #             ],
                    #             "address_line2": u_account_obj.account_details[
                    #                 "address_line2"
                    #             ],
                    #             "city": u_account_obj.account_details["city"],
                    #             "province": u_account_obj.account_details["province"],
                    #             "country": u_account_obj.account_details["country"],
                    #             "pin": u_account_obj.account_details["pin"],
                    #         },
                    #         "order_details": {
                    #             "m_order_id": str(request_dict["service_request_id"]),
                    #             "amount": request_dict["base_service_fee"]
                    #             + request_dict["surcharges"],
                    #             "currency": request_dict["currency"],
                    #             "description": "Jobconnectz Service Payment",
                    #         },
                    #         "urls": {
                    #             # "success": "https://webhook.site/5b21baa6-603f-403a-b4dd-d08614d34b2b",
                    #             # "cancel": "https://webhook.site/5b21baa6-603f-403a-b4dd-d08614d34b2b",
                    #             # "failure": "https://webhook.site/5b21baa6-603f-403a-b4dd-d08614d34b2b"
                    #             "success": "https://uat-checkout.gtipayglobal.com/checkout/status/success",
                    #             "cancel": "https://uat-checkout.gtipayglobal.com/checkout/status/cancel",
                    #             "failure": "https://uat-checkout.gtipayglobal.com/checkout/status/failure",
                    #         },
                    #     }
                    # }
                    {
                        "data": {
                            "action": "SALE",
                            "class": "ECOM",
                            "capture_method": "AUTOMATIC",
                            "payment_token": "",
                            "customer_details": {
                                "m_customer_id": str(account_id),
                                "name": u_account_obj.account_details["name"],
                                "email": u_account_obj.account_details["email"],
                                "mobile": u_account_obj.account_details["mobile"],
                                "code": u_account_obj.account_details["code"],
                            },
                            "billing_details": {
                                "address_line1": u_account_obj.account_details[
                                    "address_line1"
                                ],
                                # "address_line2": u_account_obj.account_details[
                                #     "address_line2"
                                # ],
                                "city": u_account_obj.account_details["city"],
                                # "province": u_account_obj.account_details["province"],
                                "country": u_account_obj.account_details["country"],
                                # "pin": u_account_obj.account_details["pin"],
                            },
                            "shipping_details": {
                                "address_line1": u_account_obj.account_details[
                                    "address_line1"
                                ],
                                # "address_line2": u_account_obj.account_details[
                                #     "address_line2"
                                # ],
                                "city": u_account_obj.account_details["city"],
                                # "province": u_account_obj.account_details["province"],
                                "country": u_account_obj.account_details["country"],
                                # "pin": u_account_obj.account_details["pin"],
                            },
                            "order_details": {
                                "m_order_id": str(request_dict["service_request_id"]),
                                "amount": total_payable,
                                "currency": request_dict["currency"],
                                "description": "Jobconnectz Service Payment",
                            },
                            "urls": {
                                # "success": "https://webhook.site/5b21baa6-603f-403a-b4dd-d08614d34b2b",
                                # "cancel": "https://webhook.site/5b21baa6-603f-403a-b4dd-d08614d34b2b",
                                # "failure": "https://webhook.site/5b21baa6-603f-403a-b4dd-d08614d34b2b"
                                # "success": "https://uat-checkout.gtipayglobal.com/checkout/status/success",
                                # "cancel": "https://uat-checkout.gtipayglobal.com/checkout/status/cancel",
                                # "failure": "https://uat-checkout.gtipayglobal.com/checkout/status/failure",
                            },
                        }
                    }
                )
                print(payload)
                headers = {
                    "merchant-key": settings.CARD_MERCHANT_KEY,
                    "merchant-secret": settings.CARD_MERCHANT_SECRET,
                    "Content-Type": "application/json",
                    "Cookie": "AWSALB=8JFTC8y88vPc/fSN6IXX+pa8VVP3meHjNy1KdAi0FZKpLgeNlmJ+rEvky4Ho+hgTHDytrBWiSPWtRHOQMwqX1ZTfVz+RlkwVPg52gtFLiyRyU1iEDJU2Yf82Lq7K; AWSALBCORS=8JFTC8y88vPc/fSN6IXX+pa8VVP3meHjNy1KdAi0FZKpLgeNlmJ+rEvky4Ho+hgTHDytrBWiSPWtRHOQMwqX1ZTfVz+RlkwVPg52gtFLiyRyU1iEDJU2Yf82Lq7K",
                }
                response = requests.request("POST", url, headers=headers, data=payload)

                print(response.text)

                if json.loads(response.text)["status"] == "SUCCESS":
                    print("success")
                    p_order_id = json.loads(response.text)["p_order_id"]
                    payment_link = json.loads(response.text)["payment_link"]
                    new_dict["status"] = "SUCCESS"
                    new_dict["payment_details"] = json.loads(response.text)
                    new_payment = await create_record(db, Payment, new_dict)

                    if isinstance(new_payment, str):
                        return ErrorResponse(
                            status_code=400,
                            message=str(new_payment),
                            error={"error": new_payment},
                        )
                    new_payment_id = str(new_payment.id)

                    # Updating payment_id & transaction_id in transaction entry
                    setattr(u_record, 'payment_id', new_payment_id)
                    setattr(u_record, 'transaction_id', p_order_id)
                    await db.commit()
                    await db.refresh(u_record)
                    # user_transaction = Transaction(
                    #     payment_id=new_payment_id,
                    #     from_entity=str(account_id),
                    #     payment_method=request_dict["payment_method"],
                    #     to_entity=str(request_dict["artisan_id"]),
                    #     transaction_type=TransactionTypeType.PAYMENT,
                    #     status=StatusType.SUCCESS,
                    #     amount=total_payable,
                    #     transaction_id=p_order_id,
                    # )

                    # u_record = await create_record(
                    #     db, Transaction, user_transaction.as_dict()
                    # )

                    # setattr(
                    #     a_account_obj,
                    #     "balance",
                    #     a_account_balance + request_dict["base_service_fee"],
                    # )
                    # await db.commit()
                    # await db.refresh(a_account_obj)

                    # if u_account_balance == 0:
                    #     setattr(u_account_obj, "balance", 0)
                    #     await db.commit()
                    #     await db.refresh(u_account_obj)

                    print("payment_link", payment_link)
                    return StandardResponse(
                        status_code=200,
                        message="Payment completed successfully",
                        data={
                            "data": "Payment completed successfully",
                            "p_order_id": p_order_id,
                            "payment_id": new_payment_id,
                            "payment_link": payment_link,
                        },
                    )
                else:
                    return ErrorResponse(
                        status_code=500, message="ERROR", error={"error": response.text}
                    )
            except Exception as e:
                return ErrorResponse(status_code=500, message=f"Error: {e}")

        # if request_dict["payment_method"].lower() == "cash":
        #     user_transaction = Transaction(
        #         payment_id=new_payment_id,
        #         from_entity=str(account_id),
        #         payment_method=request_dict["payment_method"],
        #         to_entity=str(request_dict["artisan_id"]),
        #         transaction_type=TransactionTypeType.PAYMENT,
        #         status=StatusType.SUCCESS,
        #         amount=request_dict["base_service_fee"] + request_dict["surcharges"],
        #     )
        #     artisan_transaction = Transaction(
        #         payment_id=new_payment_id,
        #         from_entity=str(request_dict["artisan_id"]),
        #         payment_method=request_dict["payment_method"],
        #         to_entity=str(account_id),  # TODO: change to GTI Account ID
        #         transaction_type=TransactionTypeType.PAYMENT,
        #         status=StatusType.INIT,
        #         amount=request_dict["surcharges"],
        #     )
        #     u_record = await create_record(db, Transaction, user_transaction.as_dict())
        #     a_record = await create_record(
        #         db, Transaction, artisan_transaction.as_dict()
        #     )

        # else:
        # user_transaction = Transaction(
        #     payment_id=new_payment_id,
        #     from_entity=str(account_id),
        #     payment_method=request_dict["payment_method"],
        #     to_entity=str(request_dict["artisan_id"]),
        #     transaction_type=TransactionTypeType.PAYMENT,
        #     status=StatusType.SUCCESS,
        #     amount=request_dict["base_service_fee"] + request_dict["surcharges"],
        # )

        # u_record = await create_record(db, Transaction, user_transaction.as_dict())
        # a_record = await create_record(db, Transaction, artisan_transaction.as_dict())
        # return StandardResponse(
        #     status_code=200,
        #     message="Payment completed successfully",
        #     data={
        #         "data": "Payment completed successfully",
        #         "p_order_id": p_order_id,
        #         "payment_id": new_payment_id,
        #         "payment_link": payment_link,
        #     },
        # )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


async def read_payment(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    query = select(Payment).filter(Payment.artisan_id == id)
    result = await db.execute(query)

    payment_obj = result.scalars().all()
    print(payment_obj)
    if not payment_obj:
        return ErrorResponse(status_code=404, message="Earnings not found")

    return StandardResponse(
        status_code=200,
        message="Earnings fetched successfully",
        data=payment_obj.as_dict(),
    )


@router.get("/payment-read/{id}")
async def read_payment(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Fetch a single Payment by UUID `id`.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        payment_id = uuid.UUID(id)  # raises ValueError if invalid
        query = select(Payment).filter(Payment.id == payment_id)
        result = await db.execute(query)
        payment_obj = result.scalars().first()

        if not payment_obj:
            return ErrorResponse(status_code=404, message="Payment not found")

        return StandardResponse(
            status_code=200,
            message="Payment fetched successfully",
            data=payment_obj.as_dict(),
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/payment-list")
async def list_payments(
    q: Optional[str] = None,
    skip: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Retrieve a paginated list of Payments.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        # Basic pagination
        query = select(Payment).offset(skip).limit(limit)
        result = await db.execute(query)
        payments = result.scalars().all()

        # Count total
        count_query = select(func.count(Payment.id))
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()

        return StandardResponse(
            status_code=200,
            message="Payments fetched successfully",
            data={"total": total_count, "data": [p.as_dict() for p in payments]},
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.put("/payment-update/{id}")
async def update_payment(
    id: str,
    request: schemas.UpdatePayment = Depends(),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Update an existing Payment by UUID `id`.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        payment_id = uuid.UUID(id)
        query = select(Payment).filter(Payment.id == payment_id)
        result = await db.execute(query)
        get_payment = result.scalars().first()

        if not get_payment:
            return ErrorResponse(status_code=404, message="Payment not found")

        res = await update_record(db, request, get_payment)
        return StandardResponse(
            status_code=200, message="Payment updated successfully", data=res
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.delete("/payment-delete/{id}")
async def delete_payment(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Delete a Payment by UUID `id`.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        payment_id = uuid.UUID(id)
        query = select(Payment).filter(Payment.id == payment_id)
        result = await db.execute(query)
        get_payment = result.scalars().first()

        if not get_payment:
            return ErrorResponse(status_code=404, message="Payment not found")

        res = await delete_record(db, get_payment)
        return StandardResponse(
            status_code=200, message="Payment deleted successfully", data=res
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


# ================================
# Transaction Routes
# ================================
@router.post("/transaction-create")
async def create_transaction(
    request: schemas.CreateTransaction = Depends(),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Create a new Transaction record.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        request_dict = request.dict()
        print(request_dict)
        # payment_method = request.dict().get("payment_method")
        # if payment_method == "card":

        # print(response.text)

        new_transaction = await create_record(db, Transaction, request_dict)
        if isinstance(new_transaction, str):
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": new_transaction}
            )

        return StandardResponse(
            status_code=200,
            message="Transaction created successfully",
            data={"transaction_id": str(new_transaction.id)},
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/transaction-read/{id}")
async def read_transaction(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Fetch a single Transaction by UUID `id`.
    """
    try:
        # resp = await get_id_header(Authorization)
        # if resp.status_code != 200:
        #     return ErrorResponse(status_code=400, message={"error": resp.json()})

        trans_id = uuid.UUID(id)
        query = select(Transaction).filter(Transaction.id == trans_id)
        result = await db.execute(query)
        transaction_obj = result.scalars().first()

        if not transaction_obj:
            return ErrorResponse(status_code=404, message="Transaction not found")

        return StandardResponse(
            status_code=200,
            message="Transaction fetched successfully",
            data=transaction_obj.as_dict(),
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/transaction-list")
async def list_transactions(
    skip: int = 0,
    limit: int = 10,
    transaction_type: Optional[str] = None,
    payment_method: Optional[str] = None,
    status: Optional[str] = None,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Retrieve a paginated list of Transactions.
    """
    try:
        filters = []

        # Parse date filters
        if from_date:
            from_date_obj = datetime.strptime(from_date, "%Y-%m-%d")
            filters.append(Transaction.created_at >= from_date_obj)

        if to_date:
            to_date_obj = (
                datetime.strptime(to_date, "%Y-%m-%d")
                + timedelta(days=1)
                - timedelta(seconds=1)
            )
            filters.append(Transaction.created_at <= to_date_obj)

        if transaction_type and transaction_type.lower() not in {"null", ""}:
            try:
                # Convert input to uppercase to match the enum values which are all uppercase
                filters.append(
                    Transaction.transaction_type
                    == TransactionTypeType(transaction_type.upper())
                )
            except ValueError:
                return ErrorResponse(
                    status_code=400,
                    message=f"Invalid transaction_type value: {transaction_type}. Please provide a valid transaction type.",
                )
        if payment_method and payment_method.lower() not in {"null", ""}:
            try:
                # Convert input to uppercase to match the enum values which are all uppercase
                filters.append(
                    Transaction.payment_method
                    == PaymentMethodType(payment_method.upper())
                )
            except ValueError:
                return ErrorResponse(
                    status_code=400,
                    message=f"Invalid payment_method value: {payment_method}. Please provide a valid payment method.",
                )
        if status and status.lower() not in {"null", ""}:
            try:
                # Convert input to uppercase to match the enum values which are all uppercase
                filters.append(Transaction.status == StatusType(status.upper()))
            except ValueError:
                return ErrorResponse(
                    status_code=400,
                    message=f"Invalid status value: {status}. Please provide a valid status.",
                )

        # Build query with joins for entity names
        query = (
            select(
                Transaction,
                Users.first_name.label("from_user_first_name"),
                Users.last_name.label("from_user_last_name"),
                ServiceProvider.first_name.label("from_artisan_first_name"),
                ServiceProvider.last_name.label("from_artisan_last_name"),
                Users_to.first_name.label("to_user_first_name"),
                Users_to.last_name.label("to_user_last_name"),
                ServiceProvider_to.first_name.label("to_artisan_first_name"),
                ServiceProvider_to.last_name.label("to_artisan_last_name"),
                Booking.id.label("booking_id"),
                Booking.booking_order_id.label("booking_order_id"),
            )
            .outerjoin(
                Users,
                and_(
                    Transaction.from_entity.isnot(None),
                    text("transactions.from_entity::uuid = users.id")
                )
            )
            .outerjoin(
                ServiceProvider,
                and_(
                    Transaction.from_entity.isnot(None),
                    text("transactions.from_entity::uuid = service_provider.id")
                )
            )
            .outerjoin(
                Users_to,
                and_(
                    Transaction.to_entity.isnot(None),
                    text("transactions.to_entity::uuid = users_1.id")
                )
            )
            .outerjoin(
                ServiceProvider_to,
                and_(
                    Transaction.to_entity.isnot(None),
                    text("transactions.to_entity::uuid = service_provider_1.id")
                )
            )
            .outerjoin(
                Booking,
                Transaction.payment_id == Booking.payment_id
            )
            .where(and_(*filters))
            .offset(skip)
            .limit(limit)
            .order_by(Transaction.created_at.desc())
        )
        result = await db.execute(query)
        transactions_with_names = result.all()

        count_query = select(func.count(Transaction.id)).where(and_(*filters))
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()

        # Process transactions to include entity names
        processed_transactions = []
        for t in transactions_with_names:
            transaction_dict = t[0].as_dict()
            
            # Add booking information
            transaction_dict["booking_id"] = str(t[9]) if t[9] else None
            transaction_dict["booking_order_id"] = t[10] if t[10] else None
            
            # Get from entity name based on transaction type
            if t[0].transaction_type == TransactionTypeType.PAYMENT:
                # For PAYMENT: from_entity is user_id
                transaction_dict["from_entity_name"] = f"{t[1] or ''} {t[2] or ''}".strip() if t[1] or t[2] else None
                # to_entity is artisan_id
                transaction_dict["to_entity_name"] = f"{t[7] or ''} {t[8] or ''}".strip() if t[7] or t[8] else None
            elif t[0].transaction_type == TransactionTypeType.PAYOUT:
                # For PAYOUT: both from_entity and to_entity are artisan_id
                transaction_dict["from_entity_name"] = f"{t[3] or ''} {t[4] or ''}".strip() if t[3] or t[4] else None
                transaction_dict["to_entity_name"] = f"{t[7] or ''} {t[8] or ''}".strip() if t[7] or t[8] else None
            elif t[0].transaction_type == TransactionTypeType.REFUND:
                # For REFUND: both from_entity and to_entity are user_id
                transaction_dict["from_entity_name"] = f"{t[1] or ''} {t[2] or ''}".strip() if t[1] or t[2] else None
                transaction_dict["to_entity_name"] = f"{t[5] or ''} {t[6] or ''}".strip() if t[5] or t[6] else None
            
            processed_transactions.append(transaction_dict)

        return StandardResponse(
            status_code=200,
            message="Transactions fetched successfully",
            data={"total": total_count, "data": processed_transactions},
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.put("/transaction-update/{id}")
async def update_transaction(
    id: str,
    request: schemas.UpdateTransaction = Depends(),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Update an existing Transaction.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        trans_id = uuid.UUID(id)
        query = select(Transaction).filter(Transaction.id == trans_id)
        result = await db.execute(query)
        get_transaction = result.scalars().first()

        if not get_transaction:
            return ErrorResponse(status_code=404, message="Transaction not found")

        res = await update_record(db, request, get_transaction)
        return StandardResponse(
            status_code=200, message="Transaction updated successfully", data=res
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.delete("/transaction-delete/{id}")
async def delete_transaction(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Delete a Transaction record by UUID `id`.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        trans_id = uuid.UUID(id)
        query = select(Transaction).filter(Transaction.id == trans_id)
        result = await db.execute(query)
        get_transaction = result.scalars().first()

        if not get_transaction:
            return ErrorResponse(status_code=404, message="Transaction not found")

        res = await delete_record(db, get_transaction)
        return StandardResponse(
            status_code=200, message="Transaction deleted successfully", data=res
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.put("/update-transaction-status")
async def update_transaction_status(
    request: UpdateTransactionStatus,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Update transaction status, gateway_status, and gateway_response in the Payment and Transaction tables.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        # Validate UUID
        if not is_valid_uuid(request.payment_id):
            return ErrorResponse(status_code=400, message="Invalid payment ID format")

        payment_id = uuid.UUID(request.payment_id)

        # Find the payment
        query = select(Payment).filter(Payment.id == payment_id)
        result = await db.execute(query)
        payment = result.scalars().first()

        if not payment:
            return ErrorResponse(status_code=404, message="Payment not found")

        # Update payment status if provided
        if request.status is not None:
            payment.status = request.status

        # Save payment changes
        await db.commit()
        await db.refresh(payment)

        # Now update the related transaction(s)
        if request.status is not None:
            # Find transactions related to this payment
            trans_query = select(Transaction).filter(
                Transaction.payment_id == str(payment_id),
                Transaction.from_entity == str(payment.artisan_id),
            )
            trans_result = await db.execute(trans_query)
            transactions = trans_result.scalars().all()

            # Update each matching transaction
            for transaction in transactions:
                transaction.status = request.status
                if request.gateway_status is not None:
                    transaction.gateway_status = request.gateway_status
                if request.gateway_response is not None:
                    transaction.gateway_response = request.gateway_response

            await db.commit()

        return StandardResponse(
            status_code=200,
            message="Payment and transaction status updated successfully",
            data=payment.as_dict(),
        )

    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message=f"Error: {e}")


from sqlalchemy.ext.declarative import DeclarativeMeta
import json


class AlchemyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj.__class__, DeclarativeMeta):
            # Convert SQLAlchemy model to dictionary
            fields = {}
            for field in [
                x for x in dir(obj) if not x.startswith("_") and x != "metadata"
            ]:
                data = obj.__getattribute__(field)
                try:
                    json.dumps(data)
                    fields[field] = data
                except TypeError:
                    fields[field] = None
            return fields
        return json.JSONEncoder.default(self, obj)


@router.post("/request-payment")
async def request_payment(
    request: Optional[RequestPayment] = None,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Update transaction status, gateway_status, and gateway_response in the Payment and Transaction tables.
    """
    try:
        request_amount = request.amount if request and request.amount else None
        payment_mode = request.payment_mode if request and request.payment_mode else "wallet"
        payment_method = payment_mode.upper()
 
        resp = await get_id_header(Authorization)
        print(resp.json())
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        acc_id = uuid.UUID(account_id)
        a_query = select(Account).filter(Account.user_id == acc_id)
        a_result = await db.execute(a_query)
        a_account_obj = a_result.scalars().first()
        if a_account_obj is None:
            return ErrorResponse(status_code=404, message="Account not found")
        a_account_balance = a_account_obj.balance
        request_amount = a_account_balance
        new_dict = a_account_obj

        # # Find the payment
        # query = select(Payment).filter(Payment.artisan_id == account_id)
        # result = await db.execute(query)
        # payments = result.scalars().all()

        if a_account_balance == 0:
            return ErrorResponse(status_code=404, message="Your account balance is 0")

        if request_amount is None or request_amount == 0:
            request_amount = a_account_balance

        print(request_amount, "request_amount")
        print(a_account_balance, "a_account_balance")

        if a_account_balance < request_amount:
            return ErrorResponse(
                status_code=404,
                message="Your account balance is less than the requested amount",
            )

        trans_query = (
            select(Transaction)
            .filter(
                Transaction.from_entity == str(acc_id),
                Transaction.to_entity == str(acc_id),
                Transaction.transaction_type == "PAYOUT",
            )
            .order_by(Transaction.created_at.desc())
            .limit(1)
        )
        trans_result = await db.execute(trans_query)
        trans_obj = trans_result.scalars().first()

        if trans_obj:
            if not check_datetime(trans_obj.created_at):
                return ErrorResponse(
                    status_code=404,
                    message="You can only request payment once in 24 hours",
                )

        # Authenticate with the wallet API
        login_url = f"{settings.WALLET_API_URL}/Authentication/Login"
        login_payload = json.dumps(
            {
                "username": settings.WALLET_USERNAME,
                "password": settings.WALLET_PASSWORD,
            }
        )
        login_headers = {"accept": "*/*", "Content-Type": "application/json"}
        login_response = requests.request(
            "POST", login_url, headers=login_headers, data=login_payload
        )
        auth_token = json.loads(login_response.text)["token"]
        print("auth_token", auth_token)
        if auth_token:
            enquiry_url = f"{settings.WALLET_API_URL}/NameEnquiry/NameEnquiryService"
            temp_account_obj = json.dumps(a_account_obj, cls=AlchemyEncoder)
            temp_account_obj = json.loads(temp_account_obj)
            print(temp_account_obj, type(temp_account_obj), "checkpoint")

            account_details = temp_account_obj.get("account_details") or {}
            wallet_number = account_details.get("walletNumber")
            wallet_code = account_details.get("walletCode")


            if not wallet_number or not wallet_code:
                return ErrorResponse(
                    status_code=422,
                    message="Missing wallet details",
                    error={"error": "walletNumber or walletCode is missing"},
                )
            
            enquiry_payload = {
                "accountNumber": wallet_number,
                "institutionCode": wallet_code,
                "transactionId": str(random.randint(10**13, 10**14 - 1)),
                "channel": "MNO",
            }

            print(enquiry_payload, "checkpoint1-----------")
            enquiry_headers = {
                "accept": "text/plain",
                "Authorization": f"Bearer {auth_token}",  # Use the token from the previous response
                "Content-Type": "application/json",
            }
            print(enquiry_payload, enquiry_headers, "checkpoint2==========")
            # enquiry_resonse = requests.request(
            #     "POST", enquiry_url, headers=enquiry_headers, data=enquiry_payload
            # )
            try:
                enquiry_resonse = requests.post(
                    enquiry_url,
                    headers=enquiry_headers,
                    json=enquiry_payload  # Automatically serializes to JSON
                )
            except requests.RequestException as e:
                # Network-level error handling
                return ErrorResponse(
                    status_code=500,
                    message="ERROR",
                    error={"error": f"Request failed: {str(e)}"},
                )
            print(enquiry_resonse.text, "enquiry_resonse")
            response_data = json.loads(enquiry_resonse.text)

            if json.loads(enquiry_resonse.text)["statusCode"] == "200":
                # print(enquiry_resonse.text)
                # temp_account_obj["account_details"]["accountName"] = json.loads(
                #     enquiry_resonse.text
                # )["accountName"]
                account_name = response_data["data"].get("accountName")
                account_number = response_data["data"].get("accountNumber")
                if not account_name or not account_number:
                    return ErrorResponse(
                        status_code=400,
                        message="Missing account details from wallet name enquiry",
                        error={"error": "accountName or accountNumber not found"},
                    )
                temp_account_obj["account_details"]["accountName"] = account_name
                temp_account_obj["account_details"]["accountNumber"] = account_number
                # request_dict["account_details"]["accountNumber"] = json.loads(enquiry_resonse.text)["accountNumber"]
            else:
                return ErrorResponse(
                    status_code=400,
                    message="ERROR",
                    error={"error": "Wallet enquiry failed"},
                )
        else:
            return ErrorResponse(
                status_code=400,
                message="ERROR",
                error={"error": "Wallet authentication failed"},
            )
        # payment_balance = 0
        # for payment in payments:
        #     print(payment.base_service_fee, "feeeeeeeeeeee")
        #     payment_balance += payment.base_service_fee
        transact_id = str(random.randint(10**13, 10**14 - 1))
        if payment_mode == "bank":
            receive_money_payload = json.dumps(
                {
                    "accountName": temp_account_obj["account_details"]["accountName"],
                    "accountNumber": temp_account_obj["account_details"][
                        "accountNumber"
                    ],
                    "amount": request_amount,
                    "channel": "INTERBANK",
                    "institutionCode": temp_account_obj["account_details"]["bankCode"],
                    "transactionId": transact_id,
                    "creditNaration": "JOBCONNECT Service Payment",
                    "currency": "GHS",
                    "currencyAmount": 0.00,
                    "originCountryCode": "",
                    "senderName": "jobconnectz",
                }
            )
        elif payment_mode == "wallet":
            receive_money_payload = json.dumps(
                {
                    "accountName": temp_account_obj["account_details"]["walletName"],
                    "accountNumber": temp_account_obj["account_details"][
                        "walletNumber"
                    ],
                    "amount": request_amount,
                    "channel": "MNO",
                    "institutionCode": temp_account_obj["account_details"][
                        "walletCode"
                    ],
                    "transactionId": transact_id,
                    "creditNaration": "JOBCONNECT Service Payment",
                    "currency": "GHS",
                    "currencyAmount": 0.00,
                    "originCountryCode": "",
                    "senderName": "jobconnectz",
                }
            )
        else:
            return ErrorResponse(status_code=400, message="Invalid payment mode")

        # Creating Transaction Entry
        a_transaction = Transaction(
                payment_id=None,
                from_entity=str(account_id),
                payment_method=payment_method,
                to_entity=str(account_id),
                transaction_type=TransactionTypeType.PAYOUT,
                status=StatusType.INIT,
                amount=request_amount,
                transaction_id=transact_id,
            )
        u_record = await create_record(db, Transaction, a_transaction.as_dict())
        
        receive_money_url = f"{settings.WALLET_API_URL}/SendMoney/SendMoneyService"
        receive_money_headers = {
            "accept": "text/plain",
            "Authorization": f"Bearer {auth_token}",  # Use the token from the previous response
            "Content-Type": "application/json",
        }

        receive_money_response = requests.post(
            receive_money_url, headers=receive_money_headers, data=receive_money_payload
        )

        print(receive_money_response.json(), "receive_money_response")
        receive_money_resp = receive_money_response.json()
        if receive_money_resp["statusCode"] in ["200", "202"]:
            print("withdraw success")
            # new_dict["status"] = "SUCCESS"
            # new_dict["payment_details"] = json.loads(receive_money_response.text)
            # a_transaction = Transaction(
            #     payment_id=transact_id,
            #     from_entity=str(account_id),
            #     payment_method=payment_method,
            #     to_entity=str(account_id),
            #     transaction_type=TransactionTypeType.PAYOUT,
            #     status=StatusType.SUCCESS,
            #     amount=request_amount,
            #     transaction_id=transact_id,
            # )
        else:
            # a_transaction = Transaction(
            #     payment_id=transact_id,
            #     from_entity=str(account_id),
            #     payment_method=payment_method,
            #     to_entity=str(account_id),
            #     transaction_type=TransactionTypeType.PAYOUT,
            #     status=StatusType.FAILED,
            #     amount=request_amount,
            #     transaction_id=transact_id,
            # )
            return ErrorResponse(
                status_code=500,
                message="ERROR",
                error={"error": receive_money_resp.get("statusDesc", "")},
            )
        # Creating Transaction Entry
        # u_record = await create_record(db, Transaction, a_transaction.as_dict())

        # Updating Account Balance after payment disbursement
        # setattr(a_account_obj, "balance", a_account_balance - request_amount)
        # await db.commit()
        # await db.refresh(a_account_obj)
        return StandardResponse(
            status_code=200,
            message="Payment disbursed successfully",
            data="Payment disbursed successfully",
        )

    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/transaction-details")
async def get_transaction_details(
    p_order_id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Fetch transaction details from GTI API using p_order_id.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        # Call GTI API to get transaction details
        url = f"{settings.CARD_API_URL}/open/orders/transaction-details?p_order_id={p_order_id}"

        headers = {
            "merchant-key": settings.CARD_MERCHANT_KEY,
            "merchant-secret": settings.CARD_MERCHANT_SECRET,
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers)

        if response.status_code != 200:
            return ErrorResponse(
                status_code=response.status_code,
                message=f"Error from GTI API: {response.text}",
            )

        transaction_data = response.json()

        return StandardResponse(
            status_code=200,
            message="Transaction details fetched successfully",
            data=transaction_data.get("data", {}),
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


# ================================
# Account Routes
# ================================
@router.post("/account-create")
async def create_account(
    request: schemas.CreateAccount,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Create a new Account record.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        request_dict = request.dict()
        # print(request_dict["accountNumber"] and request_dict["institutionCode"])
        if request_dict["account_details"] and request_dict["account_details"].get(
            "walletNumber", None
        ):
            login_url = f"{settings.WALLET_API_URL}/Authentication/Login"

            login_payload = json.dumps(
                {
                    "username": settings.WALLET_USERNAME,
                    "password": settings.WALLET_PASSWORD,
                }
            )
            login_headers = {"accept": "*/*", "Content-Type": "application/json"}

            login_response = requests.request(
                "POST", login_url, headers=login_headers, data=login_payload
            )
            wallet_response = requests.request(
                "POST", login_url, headers=login_headers, data=login_payload
            )
            auth_token = login_response.json()["token"]
            print(auth_token)
            if auth_token:
                enquiry_url = (
                    f"{settings.WALLET_API_URL}/NameEnquiry/NameEnquiryService"
                )
                enquiry_payload = json.dumps(
                    {
                        "channel": "MNO",
                        "accountNumber": request_dict["account_details"][
                            "walletNumber"
                        ],
                        "institutionCode": request_dict["account_details"][
                            "walletCode"
                        ],
                        "transactionId": str(random.randint(10**13, 10**14 - 1)),
                    }
                )
                enquiry_payload_acc = json.dumps(
                    {
                        "channel": "INTERBANK",
                        "accountNumber": request_dict["account_details"][
                            "accountNumber"
                        ],
                        "institutionCode": request_dict["account_details"]["bankCode"],
                        "transactionId": str(random.randint(10**13, 10**14 - 1)),
                    }
                )
                print(enquiry_payload)
                enquiry_headers = {
                    "accept": "text/plain",
                    "Authorization": f"Bearer {auth_token}",  # Use the token from the previous response
                    "Content-Type": "application/json",
                }
                enquiry_resonse = requests.request(
                    "POST", enquiry_url, headers=enquiry_headers, data=enquiry_payload
                )

                enquiry_resonse_acc = requests.request(
                    "POST",
                    enquiry_url,
                    headers=enquiry_headers,
                    data=enquiry_payload_acc,
                )
                print(enquiry_resonse.json())
                temp_acc_details = request_dict["account_details"]
                if enquiry_resonse.json()["statusCode"] == "200":
                    print(enquiry_resonse_acc.text)
                    temp_acc_details["walletName"] = enquiry_resonse.json()["data"][
                        "accountName"
                    ]
                    temp_acc_details["accountName"] = enquiry_resonse_acc.json()[
                        "data"
                    ]["accountName"]
                    # request_dict["account_details"]["accountNumber"] = json.loads(enquiry_resonse.text)["accountNumber"]
                else:
                    return ErrorResponse(
                        status_code=400,
                        message="ERROR",
                        error={"error": "Provide correct account details"},
                    )
                print("123444")
                request_dict["account_details"] = temp_acc_details
        new_account = await create_record(db, Account, request_dict)
        if isinstance(new_account, str):
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": new_account}
            )

        return StandardResponse(
            status_code=200,
            message="Account created successfully",
            data={"account_id": str(new_account.id)},
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/account-read")
async def read_account(
    # id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Fetch a single Account record by UUID `id`.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            role = resp_json.get("user_role")
        else:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        acc_id = uuid.UUID(account_id)
        query = select(Account).filter(Account.user_id == acc_id)
        result = await db.execute(query)
        account_obj = result.scalars().first()

        if not account_obj:
            account = {
                    "user_id": account_id,
                    "account_type": role.upper(),
                    "balance": 0,
                    "currency": "GHS",
                    "status": "ACTIVE",
                        }
            new_account = await create_record(db, Account, account)
            return StandardResponse(
                status_code=200,
                message="Account created successfully",
                data=new_account.as_dict(),
            )
            # return ErrorResponse(status_code=404, message="Account not found")

        return StandardResponse(
            status_code=200,
            message="Account fetched successfully",
            data=account_obj.as_dict(),
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/account-list")
async def list_accounts(
    skip: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Retrieve a paginated list of Accounts.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        query = select(Account).offset(skip).limit(limit)
        result = await db.execute(query)
        accounts = result.scalars().all()

        count_query = select(func.count(Account.id))
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()

        return StandardResponse(
            status_code=200,
            message="Accounts fetched successfully",
            data={"total": total_count, "data": [a.as_dict() for a in accounts]},
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.put("/account-update")
async def update_account(
    # id: str,
    request: schemas.UpdateAccount,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Update an existing Account record.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )
        request_dict = request.dict()
        balance = request_dict.pop("balance")
        print(request_dict)
        # acc_id = str(request_dict.get("user_id"))
        acc_id = str(request.user_id)
        print(acc_id, "acc idddd")
        query = select(Account).filter(Account.user_id == acc_id)
        result = await db.execute(query)
        get_account = result.scalars().first()
        print(get_account, "acc obj")
        if not get_account:
            return ErrorResponse(status_code=404, message="Account not found")

        # print(request_dict["accountNumber"])
        if request_dict["account_details"].get("walletNumber", None):
            login_url = f"{settings.WALLET_API_URL}/Authentication/Login"

            login_payload = json.dumps(
                {
                    "username": settings.WALLET_USERNAME,
                    "password": settings.WALLET_PASSWORD,
                }
            )
            login_headers = {"accept": "*/*", "Content-Type": "application/json"}

            login_response = requests.request(
                "POST", login_url, headers=login_headers, data=login_payload
            )
            auth_token = login_response.json()["token"]
            print(auth_token)
            if auth_token:
                enquiry_url = (
                    f"{settings.WALLET_API_URL}/NameEnquiry/NameEnquiryService"
                )
                print(1)
                enquiry_payload = json.dumps(
                    {
                        "channel": "MNO",
                        "accountNumber": request_dict["account_details"][
                            "walletNumber"
                        ],
                        "institutionCode": request_dict["account_details"][
                            "walletCode"
                        ],
                        "transactionId": str(random.randint(10**13, 10**14 - 1)),
                    }
                )
                print(2)
                # enquiry_payload_acc = json.dumps(
                #     {
                #         "channel": "INTERBANK",
                #         "accountNumber": request_dict["account_details"][
                #             "accountNumber"
                #         ],
                #         "institutionCode": request_dict["account_details"]["bankCode"],
                #         "transactionId": str(random.randint(10**13, 10**14 - 1)),
                #     }
                # )
                print(enquiry_payload)
                enquiry_headers = {
                    "accept": "text/plain",
                    "Authorization": f"Bearer {auth_token}",  # Use the token from the previous response
                    "Content-Type": "application/json",
                }
                enquiry_resonse = requests.request(
                    "POST", enquiry_url, headers=enquiry_headers, data=enquiry_payload
                )
                print(enquiry_resonse.json(), "enquiry_resonse")

                # enquiry_resonse_acc = requests.request(
                #     "POST",
                #     enquiry_url,
                #     headers=enquiry_headers,
                #     data=enquiry_payload_acc,
                # )
                # print(enquiry_resonse_acc.json())
                # if enquiry_resonse_acc.json()["statusCode"] == "200":
                #     print(enquiry_resonse_acc.text)
                #     request_dict["account_details"][
                #         "walletName"
                #     ] = enquiry_resonse.json()["data"]["accountName"]
                #     request_dict["account_details"][
                #         "accountName"
                #     ] = enquiry_resonse_acc.json()["data"]["accountName"]
                #     # request_dict["account_details"]["accountNumber"] = json.loads(enquiry_resonse.text)["accountNumber"]
                # else:
                #     return ErrorResponse(
                #         status_code=400,
                #         message="ERROR",
                #         error={"error": "Provide correct account details"},
                #     )
                print(enquiry_resonse.json())
                if enquiry_resonse.json()["statusCode"] == "200":
                    print(enquiry_resonse.text)
                    request_dict["account_details"][
                        "walletName"
                    ] = enquiry_resonse.json()["data"]["accountName"]
                    # request_dict["account_details"][
                    #     "accountName"
                    # ] = enquiry_resonse.json()["data"]["accountName"]
                    # request_dict["account_details"]["accountNumber"] = json.loads(enquiry_resonse.text)["accountNumber"]
                else:
                    return ErrorResponse(
                        status_code=400,
                        message="ERROR",
                        error={"error": "Provide correct account details"},
                    )

        print(request_dict, "final request_dict")
        res = await update_payment_rec(db, request_dict, get_account)
        return StandardResponse(
            status_code=200, message="Account updated successfully", data=res
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")

@router.patch("/account/contact-info", response_model=schemas.UpdateContactInfoResponse)
async def update_account_contact_info(
    payload: schemas.UpdateContactInfoRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Patch endpoint to update `email` and/or `mobile` in the `account_details` field
    of an Account object identified by `account_id` from the Authorization header.

    Only updates if the keys already exist in `account_details`.

    Returns:
    - 200 OK with updated email and mobile
    - 404 if account not found
    - 400 if missing or invalid input
    - 500 on internal error
    """
    try:
        account_id = payload.account_id
        if not account_id:
            return ErrorResponse(status_code=400, message="Missing 'account_id' in token")

        # Fetch account
        query = select(Account).where(Account.user_id == UUID(str(account_id)))
        result = await db.execute(query)
        account = result.scalars().first()

        if not account:
            return ErrorResponse(status_code=404, message="Account not found")

        # Update only existing keys
        updated = False
        account_details = account.account_details or {}

        if payload.email and "email" in account_details:
            account_details["email"] = payload.email
            updated = True
        if payload.mobile and "mobile" in account_details:
            account_details["mobile"] = payload.mobile
            updated = True

        if not updated:
            # return ErrorResponse(status_code=400, message="No valid fields to update (keys must already exist)")
            return {
                "account_id": str(account.id),
                "email": payload.email,
                "mobile": payload.mobile,
            }

        account.account_details = account_details
        flag_modified(account, "account_details")

        await db.commit()
        await db.refresh(account)

        return {
            "account_id": str(account.id),
            "email": account_details.get("email"),
            "mobile": account_details.get("mobile"),
        }

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Internal server error",
            error={"details": str(e)}
        )

@router.delete("/account-delete/{id}")
async def delete_account(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Delete an Account record by UUID `id`.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        acc_id = uuid.UUID(id)
        query = select(Account).filter(Account.id == acc_id)
        result = await db.execute(query)
        get_account = result.scalars().first()

        if not get_account:
            return ErrorResponse(status_code=404, message="Account not found")

        res = await delete_record(db, get_account)
        return StandardResponse(
            status_code=200, message="Account deleted successfully", data=res
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


# @router.get("/artisan-earnings")
# async def get_artisan_earnings(
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     """
#     Get earnings for an artisan based on their account_id from the token.
#     This endpoint returns all completed payments for the artisan.
#     """
#     try:
#         # Verify artisan's identity from token
#         resp = await get_id_header(Authorization)
#         if resp.status_code != 200:
#             return ErrorResponse(status_code=401, message="Unauthorized access")

#         resp_json = resp.json()
#         artisan_id = resp_json.get("account_id")

#         if not artisan_id:
#             return ErrorResponse(
#                 status_code=400, message="Artisan ID not found in token"
#             )

#         # Use a native SQL query to get all the data we need in one go
#         # This avoids multiple API calls and joins the necessary tables
#         sql_query = text(
#             """
#               WITH booking_data AS (
#                     SELECT
#                         b.id as booking_id,
#                         b.service_id,
#                         s.name as service_name,
#                         c.name as category_name
#                     FROM
#                         bookings b
#                     LEFT JOIN
#                         services s ON b.service_id = s.id
#                     LEFT JOIN
#                         category c ON s.parent_id = c.id
#                 )
#                 SELECT
#                     p.id as payment_id,
#                     p.service_request_id,
#                     p.user_id,
#                     p.payment_method,
#                     p.base_service_fee,
#                     p.surcharges,
#                     p.created_at as payment_date,
#                     p.currency,
#                     t.id as transaction_id,
#                     u.first_name,
#                     u.last_name,
#                     u.profile_pic,
#                     COALESCE(s.name, 'Unknown') AS service_name,
#                     COALESCE(c.name, 'Unknown') AS category_name,
#                     COALESCE(a.balance, 0) AS wallet_balance
#                 FROM
#                     payments p
#                 JOIN
#                     transactions t ON t.payment_id = p.id AND t.to_entity = :artisan_id
#                 LEFT JOIN
#                     users u ON p.user_id = u.id
#                 LEFT JOIN
#                     accounts a ON u.id = a.user_id
#                 LEFT JOIN
#                     services s ON p.service_request_id = s.id
#                 LEFT JOIN
#                     category c ON s.parent_id = c.id
#                 WHERE
#                     p.artisan_id = :artisan_id_uuid
#                     AND p.status = 'SUCCESS'
#                     AND t.status = 'SUCCESS'
#                 ORDER BY
#                     p.payment_date DESC;

#             """
#         )
#         # Execute the native SQL query
#         try:
#             result = await db.execute(
#                 sql_query,
#                 {"artisan_id": artisan_id, "artisan_id_uuid": uuid.UUID(artisan_id)},
#             )
#             rows = result.mappings().fetchall()
#             # If the native query fails (e.g., due to schema differences), fall back to the original method
#             if not rows:
#                 return await get_artisan_earnings_fallback(
#                     db, artisan_id, Authorization
#                 )

#             # Process the results
#             earnings_data = []
#             new_wallet_balance = 0
#             for row in rows:
#                 # Convert row to dict for easier access
#                 row_dict = dict(row)

#                 # Calculate net amount
#                 net_amount = row_dict.get("base_service_fee", 0) + row_dict.get(
#                     "surcharges", 0
#                 )

#                 # Format user name
#                 first_name = row_dict.get("first_name", "")
#                 last_name = row_dict.get("last_name", "")
#                 user_name = (
#                     f"{first_name} {last_name}".strip()
#                     if (first_name or last_name)
#                     else "Unknown User"
#                 )
#                 print("row_dict", row_dict)
#                 earnings_data.append(
#                     {
#                         "payment_id": str(row_dict.get("payment_id")),
#                         "service_request_id": str(row_dict.get("service_request_id")),
#                         "user_name": user_name,
#                         "user_profile": row_dict.get("profile_pic"),
#                         "payment_date": row_dict.get("payment_date"),
#                         "payment_method": row_dict.get("payment_method"),
#                         "gross_amount": row_dict.get("base_service_fee"),
#                         "surcharge": row_dict.get("surcharges"),
#                         "net_amount": net_amount,
#                         "currency": row_dict.get("currency"),
#                         "transaction_id": (
#                             str(row_dict.get("transaction_id"))
#                             if row_dict.get("transaction_id")
#                             else None
#                         ),
#                         "service_name": row_dict.get("service_name", "Unknown Service"),
#                         "service_category": row_dict.get(
#                             "category_name", "Unknown Category"
#                         ),
#                     }
#                 )
#                 new_wallet_balance = row_dict.get("wallet_balance")
#             new_response = dict(
#                 {"earnings_data": earnings_data, "wallet_balance": new_wallet_balance}
#             )
#             print(new_response)
#             return StandardResponse(
#                 status_code=200,
#                 message="Artisan earnings fetched successfully",
#                 data=new_response,
#             )

#         except Exception as e:
#             print(f"Native query failed: {e}")
#             # Fall back to the original method if the native query fails
#             return await get_artisan_earnings_fallback(db, artisan_id, Authorization)

#     except Exception as e:
#         return ErrorResponse(
#             status_code=500, message=f"Error fetching earnings: {str(e)}"
#         )


async def get_artisan_earnings_fallback(db, artisan_id, Authorization):
    """
    Fallback method to get artisan earnings using the original approach with API calls.
    Used if the native SQL query fails.
    """
    try:
        # Query payments where artisan_id matches and status is SUCCESS
        query = (
            select(Payment)
            .filter(
                Payment.artisan_id == uuid.UUID(artisan_id),
                Payment.status == StatusType.SUCCESS,
            )
            .order_by(Payment.created_at.desc())
        )

        result = await db.execute(query)
        payments = result.scalars().all()

        # Get account balance for the artisan
        account_query = select(Account).filter(Account.user_id == uuid.UUID(artisan_id))
        account_result = await db.execute(account_query)
        account = account_result.scalars().first()
        account_balance = account.balance if account else 0

        # Prepare response data with additional details
        earnings_data = []
        for payment in payments:
            # Get transaction details for this payment
            tx_query = select(Transaction).filter(
                Transaction.payment_id == str(payment.id),
                Transaction.to_entity == artisan_id,  # Only get payments to artisan
            )
            tx_result = await db.execute(tx_query)
            transaction = tx_result.scalars().first()

            # Get user details (name) from profile service
            user_details = await get_user_details(payment.user_id, Authorization)

            # Get service details from service API
            # service_details = await get_service_details(payment.service_request_id, Authorization)

            # Calculate net amount (what artisan received)
            net_amount = payment.base_service_fee + payment.surcharges

            earnings_data.append(
                {
                    "payment_id": str(payment.id),
                    "service_request_id": str(payment.service_request_id),
                    "user_name": (
                        f"{user_details.get('first_name', '')} {user_details.get('last_name', '')}"
                        if user_details
                        else "Unknown User"
                    ),
                    "payment_date": payment.payment_date,
                    "payment_method": payment.payment_method,
                    "gross_amount": payment.base_service_fee,
                    "surcharge": payment.surcharges,
                    "net_amount": net_amount,
                    "currency": payment.currency,
                    "transaction_id": str(transaction.id) if transaction else None,
                    "wallet_balance": account_balance,
                    # "service_name": service_details.get("service_name", "Unknown Service"),
                    # "service_category": service_details.get("service_category", "Unknown Category")
                }
            )

        return StandardResponse(
            status_code=200,
            message="Artisan earnings fetched successfully",
            data=earnings_data,
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500, message=f"Error fetching earnings: {str(e)}"
        )


async def get_service_details_by_booking_id(db: AsyncSession, booking_id: uuid.UUID):
    """
    Get service name and category name for a booking using a SQL query.

    Args:
        db: Database session
        booking_id: UUID of the booking

    Returns:
        Dictionary containing service_name and service_category
    """
    try:
        # SQL query to join bookings, services, and category tables
        sql_query = text(
            """
            SELECT 
                s.name AS service_name,
                c.name AS category_name
            FROM 
                bookings b
            LEFT JOIN 
                services s ON b.service_id = s.id
            LEFT JOIN 
                category c ON s.parent_id = c.id
            WHERE 
                b.id = :booking_id
        """
        )

        # Execute the query
        result = await db.execute(sql_query, {"booking_id": booking_id})
        row = result.mappings().first()

        if row:
            return {
                "service_name": row.get("service_name", "Unknown Service"),
                "service_category": row.get("category_name", "Unknown Category"),
            }
        return {
            "service_name": "Unknown Service",
            "service_category": "Unknown Category",
        }

    except Exception as e:
        print(f"Error fetching service details: {e}")
        return {
            "service_name": "Unknown Service",
            "service_category": "Unknown Category",
        }


async def get_user_spends_fallback(db, user_id, Authorization):
    """
    Fallback method to get artisan earnings using the original approach with API calls.
    Used if the native SQL query fails.
    """
    try:
        # Query payments where artisan_id matches and status is SUCCESS
        query = (
            select(Payment)
            .filter(
                Payment.user_id == uuid.UUID(user_id),
                Payment.status == StatusType.SUCCESS,
            )
            .order_by(Payment.created_at.desc())
        )

        result = await db.execute(query)
        payments = result.scalars().all()

        # Prepare response data with additional details
        earnings_data = []
        for payment in payments:
            # Get transaction details for this payment
            tx_query = select(Transaction).filter(
                Transaction.payment_id == str(payment.id),
                Transaction.to_entity == user_id,  # Only get payments to artisan
            )
            tx_result = await db.execute(tx_query)
            transaction = tx_result.scalars().first()

            # Get user details (name) from profile service
            # user_details = await get_user_details(payment.user_id, Authorization)

            artisan_details = await get_artisan_details(
                payment.artisan_id, Authorization
            )

            # Get service details from service API
            # service_details = await get_service_details(payment.service_request_id, Authorization)

            # Calculate net amount (what artisan received)
            net_amount = payment.base_service_fee + payment.surcharges
            service_details = await get_service_details_by_booking_id(
                db, payment.service_request_id
            )

            print(service_details, "service_details")

            earnings_data.append(
                {
                    "payment_id": str(payment.id),
                    "service_request_id": str(payment.service_request_id),
                    "user_name": (
                        # f"{user_details.get('first_name', '')} {user_details.get('last_name', '')}"
                        # if user_details
                        # else "Unknown User"
                        f"{artisan_details.get('first_name', '')} {artisan_details.get('last_name', '')}"
                        if artisan_details
                        else "Unknown Artisan"
                    ),
                    "profile_pic": artisan_details.get("profile_pic"),
                    "payment_date": payment.created_at,
                    "payment_method": payment.payment_method,
                    "gross_amount": payment.base_service_fee,
                    "surcharge": payment.surcharges,
                    "net_amount": net_amount,
                    "currency": payment.currency,
                    "transaction_id": str(transaction.id) if transaction else None,
                    "service_name": service_details["service_name"],
                    "service_category": service_details["service_category"],
                    # "service_name": service_details.get("service_name", "Unknown Service"),
                    # "service_category": service_details.get("service_category", "Unknown Category")
                }
            )

        return StandardResponse(
            status_code=200,
            message="User spends fetched successfully",
            data=earnings_data,
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500, message=f"Error fetching spends: {str(e)}"
        )


# async def get_service_details(service_request_id: uuid.UUID, auth_token: str):
#     """
#     Get service details from the service API.
#     """
#     try:
#         # First get booking details from schedule service to get service_id
#         schedule_api_url = os.getenv("BE_SCHEDULE_API_URL", "http://schedule-service:8002")
#         booking_response = requests.get(
#             f"{schedule_api_url}/booking/booking-read/{service_request_id}",
#             headers={"Authorization": auth_token}
#         )

# if booking_response.status_code != 200:
#     print(f"Failed to get booking details: {booking_response.status_code} - {booking_response.text}")
#     return {}

#         booking_data = booking_response.json().get("data", {})
#         service_id = booking_data.get("service_id")

# if not service_id:
#     print(f"No service_id found in booking data: {booking_data}")
#     return {}

#         # Now get service details from service API
#         service_api_url = os.getenv("BE_SERVICE_API_URL", "http://service-service:8003")
#         service_response = requests.get(
#             f"{service_api_url}/services-read/{service_id}",
#             headers={"Authorization": auth_token}
#         )

# if service_response.status_code != 200:
#     print(f"Failed to get service details: {service_response.status_code} - {service_response.text}")
#     return {}

#         service_data = service_response.json().get("data", {})

#         # Get category details
#         parent_id = service_data.get("parent_id")
#         category_name = "Unknown Category"

#         if parent_id:
#             category_response = requests.get(
#                 f"{service_api_url}/category-read/{parent_id}",
#                 headers={"Authorization": auth_token}
#             )

#             if category_response.status_code == 200:
#                 category_data = category_response.json().get("data", {})
#                 category_name = category_data.get("name", "Unknown Category")

#         return {
#             "service_name": service_data.get("name", "Unknown Service"),
#             "service_category": category_name
#         }

#     except Exception as e:
#         print(f"Error fetching service details: {e}")
#         return {}


async def get_user_details(user_id: uuid.UUID, auth_token: str):
    """
    Get user details from the profile service.
    """
    try:
        profile_api_url = os.getenv("BE_PROFILE_API_URL", "http://profile-service:8001")
        response = requests.get(
            f"{profile_api_url}/user-read/{user_id}",
            headers={"Authorization": auth_token},
        )

        if response.status_code == 200:
            return response.json().get("data", {})
        return None
    except Exception as e:
        print(f"Error fetching user details: {e}")
        return None


async def get_artisan_details(artisan_id: uuid.UUID, auth_token: str):
    """
    Get user details from the profile service.
    """
    try:
        profile_api_url = os.getenv("BE_PROFILE_API_URL", "http://profile-service:8001")
        response = requests.get(
            f"{profile_api_url}/sp-read/{artisan_id}",
            headers={"Authorization": auth_token},
        )

        if response.status_code == 200:
            return response.json().get("data", {})
        return None
    except Exception as e:
        print(f"Error fetching artisan details: {e}")
        return None


# @router.get("/user-spends")
# async def get_user_spends(
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     """
#     Get earnings for an artisan based on their account_id from the token.
#     This endpoint returns all completed payments for the artisan.
#     """
#     try:
#         # Verify artisan's identity from token
#         resp = await get_id_header(Authorization)
#         if resp.status_code != 200:
#             return ErrorResponse(status_code=401, message="Unauthorized access")

#         resp_json = resp.json()
#         user_id = resp_json.get("account_id")
#         # user_id = '166c22e6-44c3-47a5-a52e-76281982366a'

#         if not user_id:
#             return ErrorResponse(status_code=400, message="User ID not found in token")

#         # Use a native SQL query to get all the data we need in one go
#         # This avoids multiple API calls and joins the necessary tables
#         sql_query = text(
#             """
#                 WITH booking_data AS (
#                     SELECT
#                         b.id as booking_id,
#                         b.service_id,
#                         s.name as service_name,
#                         c.name as category_name
#                     FROM
#                         bookings b
#                     LEFT JOIN
#                         services s ON b.service_id = s.id
#                     LEFT JOIN
#                         category c ON s.parent_id = c.id
#                 )
#                 SELECT
#                     p.id as payment_id,
#                     p.service_request_id,
#                     p.user_id,
#                     p.artisan_id,
#                     p.payment_method,
#                     p.base_service_fee,
#                     p.surcharges,
#                     p.created_at as payment_date,
#                     p.currency,
#                     t.id as transaction_id,
#                     concat(a.first_name, a.last_name) as user_name,
#                     a.profile_pic,
#                     COALESCE(s.name, 'Unknown') AS service_name,
#                     COALESCE(c.name, 'Unknown') AS category_name
#                 FROM
#                     payments p
#                 JOIN
#                     transactions t ON t.payment_id = p.id AND t.to_entity = :user_id
#                 LEFT JOIN
#                     users u ON p.user_id = u.id
#                 LEFT JOIN
#                     service_provider a ON p.artisan_id = a.id
#                 LEFT JOIN
#                     bookings b ON p.service_request_id = b.id
#                 LEFT JOIN
#                     services s ON b.service_id = s.id
#                 LEFT JOIN
#                     category c ON s.parent_id = c.id
#                 WHERE
#                     p.user_id = :user_id_uuid
#                     AND p.status = 'SUCCESS'
#                     AND t.status = 'SUCCESS'
#                 ORDER BY
#                     p.payment_date DESC
#             """
#         )
#         # Execute the native SQL query
#         try:
#             result = await db.execute(
#                 sql_query,
#                 {"user_id": user_id, "user_id_uuid": uuid.UUID(user_id)},
#             )
#             rows = result.mappings().fetchall()
#             print(rows, "rowssssssssssss")
#             # If the native query fails (e.g., due to schema differences), fall back to the original method
#             if not rows:
#                 return await get_user_spends_fallback(db, user_id, Authorization)

#             # Process the results
#             earnings_data = []
#             for row in rows:
#                 # Convert row to dict for easier access
#                 row_dict = dict(row)

#                 # Calculate net amount
#                 net_amount = row_dict.get("base_service_fee", 0) + row_dict.get(
#                     "surcharges", 0
#                 )

#                 # Format user name
#                 first_name = row_dict.get("first_name", "")
#                 last_name = row_dict.get("last_name", "")
#                 user_name = (
#                     f"{first_name} {last_name}".strip()
#                     if (first_name or last_name)
#                     else "Unknown User"
#                 )
#                 print("row_dict", row_dict)
#                 earnings_data.append(
#                     {
#                         "payment_id": str(row_dict.get("payment_id")),
#                         "service_request_id": str(row_dict.get("service_request_id")),
#                         "user_name": user_name,
#                         "user_profile": row_dict.get("profile_pic"),
#                         "payment_date": row_dict.get("payment_date"),
#                         "payment_method": row_dict.get("payment_method"),
#                         "gross_amount": row_dict.get("base_service_fee"),
#                         "surcharge": row_dict.get("surcharges"),
#                         "net_amount": net_amount,
#                         "currency": row_dict.get("currency"),
#                         "transaction_id": (
#                             str(row_dict.get("transaction_id"))
#                             if row_dict.get("transaction_id")
#                             else None
#                         ),
#                         "service_name": row_dict.get("service_name", "Unknown Service"),
#                         "service_category": row_dict.get(
#                             "category_name", "Unknown Category"
#                         ),
#                     }
#                 )

#             return StandardResponse(
#                 status_code=200,
#                 message="User spends fetched successfully",
#                 data=earnings_data,
#             )

#         except Exception as e:
#             print(f"Native query failed: {e}")
#             # Fall back to the original method if the native query fails
#             return await get_user_spends_fallback(db, user_id, Authorization)

#     except Exception as e:
#         return ErrorResponse(
#             status_code=500, message=f"Error fetching spends: {str(e)}"
#         )


@router.post("/refund-payment")
async def refund_payment(
    request: RefundPayment,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Update transaction status, gateway_status, and gateway_response in the Payment and Transaction tables.
    """
    try:
        request_amount = request.amount
        user_id = request.user_id
        payment_method = request.payment_method
        p_order_id = request.p_order_id
        payment_id = request.payment_id
        is_amount_refunded = False
        cancellation_id = request.cancellation_id

        print(payment_method, 'payment_method')
        print(payment_id, 'payment_id')
        print(request_amount, 'request_amount')

        resp = await get_id_header(Authorization)
        print(resp.json())
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        account_id = uuid.UUID(user_id)
        u_query = select(Account).filter(Account.user_id == account_id)
        u_result = await db.execute(u_query)
        u_account_obj = u_result.scalars().first()
        u_account_details = u_account_obj.account_details
        if u_account_obj is None:
            return ErrorResponse(status_code=404, message="Account not found")
        u_account_balance = u_account_obj.balance
        new_dict = u_account_obj

        if payment_method == "wallet":
            # Authenticate with the wallet API
            login_url = f"{settings.WALLET_API_URL}/Authentication/Login"
            login_payload = json.dumps(
                {
                    "username": settings.WALLET_USERNAME,
                    "password": settings.WALLET_PASSWORD,
                }
            )
            login_headers = {"accept": "*/*", "Content-Type": "application/json"}
            login_response = requests.request(
                "POST", login_url, headers=login_headers, data=login_payload
            )
            auth_token = json.loads(login_response.text)["token"]
            print("auth_token", auth_token)
            if auth_token:
                transact_id = str(random.randint(10**13, 10**14 - 1))
                # Creating Transaction Entry
                a_transaction = Transaction(
                    payment_id=payment_id,
                    from_entity=str(account_id),
                    payment_method=payment_method.upper(),
                    to_entity=str(account_id),
                    transaction_type=TransactionTypeType.REFUND,
                    status=StatusType.INIT,
                    amount=request_amount,
                    transaction_id=transact_id,
                    cancellation_id=cancellation_id
                )
                u_record = await create_record(db, Transaction, a_transaction.as_dict())

                receive_money_payload = json.dumps(
                    {
                        "accountName": u_account_details["walletName"],
                        "accountNumber": u_account_details["walletNumber"],
                        "amount": request_amount,
                        "channel": "MNO",
                        "institutionCode": u_account_details["walletCode"],
                        "transactionId": transact_id,
                        "creditNaration": "JOBCONNECTZ REFUND",
                        "currency": "GHS",
                        "currencyAmount": 0.00,
                        "originCountryCode": "",
                        "senderName": "jobconnectz",
                    }
                )
                receive_money_url = (
                    f"{settings.WALLET_API_URL}/SendMoney/SendMoneyService"
                )
                receive_money_headers = {
                    "accept": "text/plain",
                    "Authorization": f"Bearer {auth_token}",  # Use the token from the previous response
                    "Content-Type": "application/json",
                }
                receive_money_response = requests.post(
                    receive_money_url,
                    headers=receive_money_headers,
                    data=receive_money_payload,
                )
                print(receive_money_response.json(), "receive_money_response")
                receive_money_resp = receive_money_response.json()

                if receive_money_resp["statusCode"] in ["200", "202"]:
                    is_amount_refunded = True
                    # Updating Account Balance after payment refund through wallet
                    setattr(
                        u_account_obj, "balance", u_account_balance + request_amount
                    )
                    await db.commit()
                    await db.refresh(u_account_obj)
                else:
                    is_amount_refunded = False
            else:
                return ErrorResponse(
                    status_code=400,
                    message="ERROR",
                    error={"error": "Wallet authentication failed"},
                )
        elif payment_method == "card":
            # Getting transaction details from GTI API
            trans_resp = get_gtipay_transaction_detail(p_order_id)
            print(trans_resp, "trans respppp")
            if trans_resp.get("status") == "success":
                transact_id = trans_resp["data"]["transaction_history"][-1]["transaction_id"]
            else:
                return ErrorResponse(
                    status_code=400, message="ERROR", error={"error": str(trans_resp)}
                )
            
            # Creating Transaction Entry
            a_transaction = Transaction(
                payment_id=payment_id,
                from_entity=str(account_id),
                payment_method=payment_method.upper(),
                to_entity=str(account_id),
                transaction_type=TransactionTypeType.REFUND,
                status=StatusType.INIT,
                amount=request_amount,
                transaction_id=p_order_id,
            )
            u_record = await create_record(db, Transaction, a_transaction.as_dict())

            
            url = f"{settings.CARD_API_URL}/open/orders/transaction"
            receive_money_payload = json.dumps(
                {
                    "action": "REFUND",
                    "transaction_id": transact_id,
                    "amount": {"currencyCode": "GHS", "value": request_amount},
                    "reason": "jobconnectz refund",
                }
            )
            print(receive_money_payload, "receive_money_payload")
            # print(x)

            headers = {
                "merchant-key": settings.CARD_MERCHANT_KEY,
                "merchant-secret": settings.CARD_MERCHANT_SECRET,
                "Content-Type": "application/json",
                "Cookie": "AWSALB=8JFTC8y88vPc/fSN6IXX+pa8VVP3meHjNy1KdAi0FZKpLgeNlmJ+rEvky4Ho+hgTHDytrBWiSPWtRHOQMwqX1ZTfVz+RlkwVPg52gtFLiyRyU1iEDJU2Yf82Lq7K; AWSALBCORS=8JFTC8y88vPc/fSN6IXX+pa8VVP3meHjNy1KdAi0FZKpLgeNlmJ+rEvky4Ho+hgTHDytrBWiSPWtRHOQMwqX1ZTfVz+RlkwVPg52gtFLiyRyU1iEDJU2Yf82Lq7K",
            }
            response = requests.request(
                "POST", url, headers=headers, data=receive_money_payload
            )
            print(response.text, "response")
            receive_money_resp = response.json()
            # print(resp_json, "resp_json")
            if receive_money_resp["status"].upper() == "SUCCESS":
                is_amount_refunded = True
            else:
                is_amount_refunded = False
        else:
            return ErrorResponse(status_code=400, message="Invalid payment method")

        if is_amount_refunded:
            pass
            # a_transaction = Transaction(
            #     payment_id=payment_id,
            #     from_entity=str(account_id),
            #     payment_method=payment_method.upper(),
            #     to_entity=str(account_id),
            #     transaction_type=TransactionTypeType.REFUND,
            #     status=StatusType.SUCCESS,
            #     amount=request_amount,
            #     gateway_response=receive_money_resp,
            #     transaction_id=transact_id if payment_method == "wallet" else p_order_id,
            # )
            # # Creating Transaction Entry
            # u_record = await create_record(db, Transaction, a_transaction.as_dict())
        else:
            # a_transaction = Transaction(
            #     payment_id=payment_id,
            #     from_entity=str(account_id),
            #     payment_method=payment_method.upper(),
            #     to_entity=str(account_id),
            #     transaction_type=TransactionTypeType.REFUND,
            #     status=StatusType.FAILED,
            #     amount=request_amount,
            #     gateway_response=receive_money_resp,
            #     transaction_id=transact_id if payment_method == "wallet" else p_order_id,
            # )
            # # Creating Transaction Entry
            # u_record = await create_record(db, Transaction, a_transaction.as_dict())
            return ErrorResponse(
                status_code=500,
                message="ERROR",
                error={"error": receive_money_resp.get("statusDesc", "")},
            )
        return StandardResponse(
            status_code=200,
            message="Refund completed successfully",
            data="Refund completed successfully",
        )
    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/user-spends")
async def get_user_spends(
    skip: int = 1,
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        if not Authorization:
            return ErrorResponse(
                status_code=401, message="Authorization header missing"
            )

        # Verify artisan's identity from token
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized access")

        resp_json = resp.json()
        print(resp_json, "resp_json")
        user_id = resp_json.get("account_id")
        # user_id = '22c9b931-7468-4ae1-96b3-eaf114b83c97'

        if not user_id:
            return ErrorResponse(status_code=400, message="User ID not found in token")

        # Base query with join to ServiceProvider (artisans)
        query = (
            select(
                Payment.id.label("payment_id"),
                Payment.user_id,
                Payment.artisan_id,
                Payment.service_request_id.label("service_request_id"),
                Payment.created_at.label("payment_date"),
                Payment.payment_method,
                Payment.status.label("payment_status"),
                Payment.base_service_fee.label("gross_amount"),
                Payment.surcharges,
                Payment.tax,
                Payment.currency,
                Services.name.label("service_name"),
                Category.name.label("category_name"),
                # Transaction.id.label("transaction_id"),
                ServiceProvider.first_name.label("first_name"),
                ServiceProvider.last_name.label("last_name"),
                ServiceProvider.profile_pic.label("profile_pic"),
            )
            .select_from(Payment)
            .outerjoin(Services, Payment.service_request_id == Services.id)
            .outerjoin(Category, Services.parent_id == Category.id)
            # .outerjoin(
            #     Transaction,
            #     and_(
            #         Transaction.payment_id == Payment.id,
            #         Transaction.from_entity == user_id,
            #     ),
            # )
            .outerjoin(ServiceProvider, Payment.artisan_id == ServiceProvider.id)
            .join(Booking, Booking.payment_id == Payment.id)  # <-- new join
            .where(
                and_(
                    Payment.user_id == user_id,
                    Payment.status == StatusType.SUCCESS,
                    # Transaction.status == StatusType.SUCCESS,
                    Booking.status != BookingStatus.CANCELLED,
                    Booking.status != BookingStatus.REJECTED,
                )
            )
            .order_by(Payment.created_at.desc())
        )

        # Get total count BEFORE applying offset/limit
        count_query = query.with_only_columns(func.count(Payment.id)).order_by(None)
        count_result = await db.execute(count_query)
        total = count_result.scalar() or 0

        # Get total spend (gross_amount + surcharges)
        # total_spends_query = query.with_only_columns(
        #     func.coalesce(func.sum(Payment.base_service_fee + Payment.surcharges), 0)
        # ).order_by(None)
        # spend_result = await db.execute(total_spends_query)
        # total_spends = float(spend_result.scalar() or 0)

        # Apply pagination
        adjusted_skip = (skip - 1) * limit if skip > 0 else 0
        query = query.offset(adjusted_skip).limit(limit)

        result = await db.execute(query)
        rows = result.mappings().all()
        print(len(rows), "result")
        logger.debug(f"Number of rows returned: {len(rows)}")

        if not rows:
            return StandardResponse(status_code=200, message="No spends found", data=[])

        response_data = []
        for row in rows:
            gross = float(row.gross_amount or 0)
            surcharges = float(row.surcharges or 0)
            tax = float(row.tax or 0)
            # artisan_id = row.artisan_id
            # artisan_details = await get_artisan_details(artisan_id, Authorization)
            # if not artisan_details:
            #     continue
            response_data.append(
                {
                    "payment_id": str(row.payment_id),
                    "service_request_id": str(row.service_request_id),
                    "user_name": (
                        f"{row.get('first_name', '')} {row.get('last_name', '')}"
                        if row
                        else "Unknown Artisan"
                    ),
                    "profile_pic": (row.get("profile_pic") if row else None),
                    "payment_date": row.payment_date,
                    "payment_method": row.payment_method,
                    "payment_status": row.payment_status,
                    "gross_amount": gross,
                    "surcharge": surcharges,
                    "tax": tax,
                    "net_amount": gross + surcharges,
                    "currency": row.currency,
                    # "transaction_id": row.transaction_id,
                    "service_name": row.service_name,
                    "service_category": row.category_name,
                }
            )

        paginated_response = {
            "pagination": {
                "total": total,
                "page": skip,
                "limit": limit,
                "pages": (total + limit - 1) // limit,
            },
            # "total_spends": total_spends,
            "spends": response_data,
        }

        return StandardResponse(
            status_code=200,
            message="User spends fetched successfully",
            data=paginated_response,
        )

    except Exception as e:
        logger.error(f"Error fetching spends: {str(e)}")
        return ErrorResponse(
            status_code=500, message=f"Error fetching spends: {str(e)}"
        )


@router.get("/artisan-earnings")
async def get_artisan_earnings(
    skip: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:

        if not Authorization:
            return ErrorResponse(
                status_code=401, message="Authorization header missing"
            )

        # Verify artisan's identity from token
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized access")

        resp_json = resp.json()
        artisan_id = resp_json.get("account_id")

        if not artisan_id:
            return ErrorResponse(
                status_code=400, message="Artisan ID not found in token"
            )
        # Get account balance for the artisan
        account_query = select(Account).filter(Account.user_id == uuid.UUID(artisan_id))
        account_result = await db.execute(account_query)
        account = account_result.scalars().first()
        account_balance = account.balance if account else 0

        # # Optional: Log existing statuses for debugging
        # status_check_query = select(Payment.status).where(Payment.artisan_id == artisan_id)
        # status_result = await db.execute(status_check_query)
        # all_statuses = status_result.scalars().all()
        # logger.debug(f"Statuses found for artisan_id {artisan_id}: {all_statuses}")
        query = (
            select(
                Payment.id.label("payment_id"),
                Payment.artisan_id,
                Payment.user_id,
                Payment.service_request_id.label("service_request_id"),
                Payment.created_at.label("payment_date"),
                Payment.payment_method,
                Payment.status.label("payment_status"),
                Payment.base_service_fee.label("gross_amount"),
                Payment.surcharges,
                Payment.tax,
                Payment.currency,
                Services.name.label("service_name"),
                Category.name.label("category_name"),
                # Transaction.id.label("transaction_id"),
                Users.first_name.label("first_name"),
                Users.last_name.label("last_name"),
                Users.profile_pic.label("profile_pic"),
            )
            .select_from(Payment)
            .outerjoin(Services, Payment.service_request_id == Services.id)
            .outerjoin(Category, Services.parent_id == Category.id)
            # .outerjoin(
            #     Transaction,
            #     and_(
            #         Payment.id == Transaction.payment_id,
            #         Transaction.to_entity == artisan_id,
            #     ),
            # )
            .outerjoin(Users, Payment.user_id == Users.id)
            .join(Booking, Booking.payment_id == Payment.id)  # <-- new join
            .where(
                and_(
                    Payment.artisan_id == artisan_id,
                    Payment.status == StatusType.SUCCESS,
                    # Transaction.status == StatusType.SUCCESS,
                    Booking.status == BookingStatus.COMPLETED,  # <-- new condition
                )
            )
            .order_by(Payment.created_at.desc())
        )

        # Get total count BEFORE applying offset/limit
        count_query = query.with_only_columns(func.count(Payment.id)).order_by(None)
        count_result = await db.execute(count_query)
        total = count_result.scalar() or 0

        # Get total earnings (gross_amount + surcharges)
        total_earnings_query = query.with_only_columns(
            func.coalesce(func.sum(Payment.base_service_fee), 0)
        ).order_by(None)
        total_earnings_result = await db.execute(total_earnings_query)
        total_earnings = float(total_earnings_result.scalar() or 0)

        # Apply pagination
        adjusted_skip = (skip - 1) * limit if skip > 0 else 0
        query = query.offset(adjusted_skip).limit(limit)

        result = await db.execute(query)
        rows = result.mappings().all()
        logger.debug(f"Number of rows returned: {len(rows)}")

        if not rows:
            return StandardResponse(status_code=200, message="No earnings found", data=[])

        response_data = []
        for row in rows:
            gross = float(row.gross_amount or 0)
            surcharges = float(row.surcharges or 0)
            tax = float(row.tax or 0)
            # user_id = row.user_id
            # user_details = await get_user_details(user_id, Authorization)
            # if not user_details:
            #     continue

            response_data.append(
                {
                    "payment_id": str(row.payment_id),
                    "service_request_id": str(row.service_request_id),
                    "user_name": (
                        f"{row.get('first_name', '')} {row.get('last_name', '')}"
                        if row
                        else "Unknown user"
                    ),
                    "profile_pic": (row.get("profile_pic") if row else None),
                    "payment_date": row.payment_date,
                    "payment_method": row.payment_method,
                    "payment_status": row.payment_status,
                    "gross_amount": gross,
                    "surcharge": surcharges,
                    "tax": tax,
                    "net_amount": gross + surcharges,
                    "currency": row.currency,
                    # "transaction_id": row.transaction_id,
                    "service_name": row.service_name,
                    "service_category": row.category_name,
                }
            )
            new_response = dict(
                {
                    "earnings_data": response_data,
                    "wallet_balance": account_balance,
                    "total_earnings": total_earnings,
                    "pagination": {
                        "total": total,
                        "page": skip,
                        "limit": limit,
                        "pages": (total + limit - 1) // limit,
                    },
                }
            )

        return StandardResponse(
            status_code=200,
            message="artisan earnings fetched successfully",
            data=new_response,
        )

    except Exception as e:
        logger.error(f"Error fetching earnings: {str(e)}")
        return ErrorResponse(
            status_code=500, message=f"Error fetching earnings: {str(e)}"
        )


@router.get("/transaction-status/{id}")
async def get_transaction_status(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Fetch a transaction status for given Payment `id`.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(
                status_code=400, message="ERROR", error={"error": resp.json()}
            )

        payment_id = uuid.UUID(id)  # raises ValueError if invalid
        query = select(Transaction).filter(Transaction.payment_id == payment_id, Transaction.transaction_type == 'PAYMENT').order_by(Transaction.created_at.desc()).limit(1)
        result = await db.execute(query)
        trans_obj = result.scalars().first()

        if not trans_obj:
            return ErrorResponse(status_code=404, message="Transaction not found")
        
        status = trans_obj.status
        if status != 'INIT':
            payment_query = select(Payment).filter(Payment.id == payment_id)
            payment_result = await db.execute(payment_query)
            payment_obj = payment_result.scalars().first()
            payment_obj.status = status
            await db.commit()
            await db.refresh(payment_obj)


        return StandardResponse(
            status_code=200,
            message="Transaction fetched successfully",
            data=trans_obj.as_dict(),
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")
