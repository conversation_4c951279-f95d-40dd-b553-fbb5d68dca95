from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.database import get_db
from app.schemas.helper import StandardResponse, ErrorResponse, RequestLog
from app.models import AuditLog, Users, ServiceProvider, Admin
import json
from uuid import UUID

router = APIRouter()

@router.post("/log")
async def log_entry(log: RequestLog, db: Session = Depends(get_db)):
    try:
        log = log.__dict__
        log_entry = AuditLog(**log)
        db.add(log_entry)
        db.commit()
        return StandardResponse(status_code=200, message="Logged successfully")
    except Exception as e:
        return ErrorResponse(status_code=500, message="Failed to log", error=str(e))
    

@router.get("/logs")
async def get_logs(skip: int = 0, limit: int = 10, db: Session = Depends(get_db)):
    try:
        logs = db.query(AuditLog, Admin, Users, ServiceProvider)\
        .outerjoin(Users, AuditLog.user_id == Users.id)\
        .outerjoin(ServiceProvider, AuditLog.user_id == ServiceProvider.id)\
        .outerjoin(Admin, AuditLog.user_id == Admin.id).order_by(AuditLog.created_at.desc()).offset(skip).limit(limit).all()
        response_data = []
        for audit_log, admin, user, service_provider in logs:
            audit_log = audit_log.__dict__
            if admin:
                audit_log['user_details'] = admin.as_dict()
            elif user:
                audit_log['user_details'] = user.as_dict()
            elif service_provider:
                audit_log['user_details'] = service_provider.as_dict()
            else:
                audit_log['user_details'] = None
            response_data.append(audit_log)
        
        # logs = [{**log[0].__dict__, 'user_details': log[1].as_dict() if log[1] else None} for log in logs]
        return StandardResponse(status_code=200, message="Logs fetched successfully", data=response_data)
    except Exception as e:
        return ErrorResponse(status_code=500, message="Failed to fetch logs", error=str(e))
    
@router.get("/logs/{id}")
async def get_log(id: UUID, db: Session = Depends(get_db)):
    try:
        log = db.query(AuditLog, Admin, Users, ServiceProvider)\
        .outerjoin(Users, AuditLog.user_id == Users.id)\
        .outerjoin(ServiceProvider, AuditLog.user_id == ServiceProvider.id)\
        .outerjoin(Admin, AuditLog.user_id == Admin.id)\
        .filter(AuditLog.id == id).first()
        if not log:
            return ErrorResponse(status_code=404, message="Log not found")
        audit_log, admin, user, service_provider = log
        audit_log = audit_log.__dict__
        if admin:
            audit_log['user_details'] = admin.as_dict()
        elif user:
            audit_log['user_details'] = user.as_dict()
        elif service_provider:
            audit_log['user_details'] = service_provider.as_dict()
        else:
            audit_log['user_details'] = None
        return StandardResponse(status_code=200, message="Log fetched successfully", data=audit_log)
    except Exception as e:
        return ErrorResponse(status_code=500, message="Failed to fetch log", error=str(e))
