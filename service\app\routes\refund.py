import uuid
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON>pen<PERSON>, <PERSON><PERSON>, Request
from fastapi.responses import JSONResponse
from app.service_calls.payment import initiate_refund
from sqlalchemy.orm import Session
from app.database import get_db
from app.utils.crud import get_record_by_id, create_record, delete_record, update_record
from typing import Optional
from typing import Annotated
from app.schemas.helper import StandardResponse, ErrorResponse
from app.utils.auth import permission_checker
from app.schemas.refund import RefundRequest
from app.models import BookingCancellation, Account, Booking, Payment

router = APIRouter(prefix="/refund",tags=["Initiate Refund"])

@router.post("/initiate-refund/{id}")
async def initiate_refund_payment(
    id: str,
    req: Request,
    request: RefundRequest,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    """
    Update transaction status, gateway_status, and gateway_response in the Payment and Transaction tables.
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        token = req.headers.get("Authorization", None) if req and req.headers else None

        request_amount = request.refund_amount
        reason_for_refund = request.reason_for_refund

        bc_obj = db.query(BookingCancellation).filter(BookingCancellation.id == id).first()
        if not bc_obj:
            return ErrorResponse(status_code=404, message="Booking cancellation not found")
    
        booking_obj = db.query(Booking).filter(Booking.id == bc_obj.booking_id).first()
        if not booking_obj:
            return ErrorResponse(status_code=404, message="Booking not found")
        
        payment_method = booking_obj.payment_type

        payment_obj = db.query(Payment).filter(Payment.invoice_id == booking_obj.invoice_id).first()
        if not payment_obj:
            return ErrorResponse(status_code=404, message="Payment not found")
        
        payment_details = payment_obj.payment_details
        if isinstance(payment_details, dict):
            p_order_id = payment_details.get("p_order_id", None)
        else:
            p_order_id = None
        
        # u_account_obj = db.query(Account).filter(Account.user_id == user_id, Account.account_type == payment_method.upper()).first()
        # if not u_account_obj:
        #     return ErrorResponse(status_code=404, message="Account not found")
        
        # if not u_account_obj.account_details:
        #     return ErrorResponse(status_code=404, message="Account details not found")
        
        # u_account_balance = u_account_obj.balance

        payload = {
                'amount': request_amount,
                'user_id': str(booking_obj.user_id),
                'payment_method': payment_method.lower(),
                'p_order_id': p_order_id,
                'payment_id': str(payment_obj.id),
                'cancellation_id': str(id)
        }

        resp = await initiate_refund(payload, token)
        
        bc_obj.status = "refund_initiated"
        bc_obj.refund_amount = request_amount
        bc_obj.reason_for_refund = reason_for_refund
        db.commit()
        db.refresh(bc_obj)

        return StandardResponse(
            status_code=200,
            message="Refund initiated successfully",
            data="Refund initiated successfully",
        )
    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message=f"Error: {e}")