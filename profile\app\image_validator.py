from fastapi import UploadFile
from typing import Optional, <PERSON><PERSON>

# Constants for image validation
MAX_IMAGE_SIZE = 5 * 1024 * 1024  # 5MB in bytes
ALLOWED_IMAGE_TYPES = {
    "image/jpeg",
    "image/jpg",
    "image/png",
    "application/pdf"
}

async def validate_image(
    file: UploadFile,
    max_size: int = MAX_IMAGE_SIZE,
    allowed_types: set = ALLOWED_IMAGE_TYPES
) -> Tuple[bool, Optional[str]]:
    """
    Validates an image file for size and format.
    
    Args:
        file (UploadFile): The image file to validate
        max_size (int): Maximum allowed file size in bytes (default: 5MB)
        allowed_types (set): Set of allowed MIME types
        
    Returns:
        Tuple[bool, Optional[str]]: (is_valid, error_message)
        - is_valid: True if validation passes, False otherwise
        - error_message: Error message if validation fails, None if validation passes
    """
    try:
        # Validate file size
        file_size = 0
        for chunk in file.file:
            file_size += len(chunk)
            if file_size > max_size:
                return False, "Upload failed: File size must be less than 5MB"
        
        # Reset file pointer after reading
        await file.seek(0)
        
        # Validate file type
        if file.content_type not in allowed_types:
            allowed_extensions = []
            if "image/jpeg" in allowed_types or "image/jpg" in allowed_types:
                allowed_extensions.append("JPG/JPEG")
            if "image/png" in allowed_types:
                allowed_extensions.append("PNG")
            if "application/pdf" in allowed_types:
                allowed_extensions.append("PDF")
            
            return False, f"Upload failed: Only {', '.join(allowed_extensions)} files are allowed"
            
        return True, None
        
    except Exception as e:
        return False, f"Error validating file: {str(e)}" 