import os
import requests
import boto3
from fastapi import Request
from app.schemas.helper import ErrorResponse
from fastapi.responses import JSONResponse
import random
import string
import time
from app.config import settings
from app.utils.cognito_password_encrypt import encrypt_password
import hmac
import hashlib
import base64
from app.config import get_settings

settings = get_settings()

async def get_id_header(Authorization):
    print(Authorization,"in get_id_header")
    if not Authorization:
        return {'error': "Token required"}
    try:
        jwt_token = Authorization.split(" ")[1]
        baseurl = os.getenv('BE_AUTH_API_URL')
        response = requests.post(f"{baseurl}/validate-token/", json={'token': jwt_token})
        return response
    except Exception as e:
        return {'error': f"Error: {e}"}



async def permission_checker(request: Request):
    token = request.headers.get("Authorization")
    if not token:
        # return JSONResponse(
        #     status_code=401,
        #     content=ErrorResponse(401, "Authorization token required").dict()
        # )
        return ErrorResponse(401, "Authorization token required")

    try:
        auth_url = os.getenv("BE_AUTH_API_URL")  # e.g. http://auth-service:8000
        headers = {
            "Authorization": token,
            "X-Original-Path": request.url.path,
            "X-Original-Method": request.method
        }

        response = requests.post(f"{auth_url}/validate-token", headers=headers)
        resp_data = response.json()

        if response.status_code != 200:
            return JSONResponse(status_code=resp_data.get("status_code", 403), content=resp_data)

        return resp_data["data"]  # contains user_id, auth_id, role_id, role_name

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(500, "Permission service failed", str(e)).dict()
        )
    
def generate_cognito_password(length=12):
    """
    Generates a Cognito-compliant random password using epoch time for uniqueness.
    """
    if length < 8:
        raise ValueError("Password length must be at least 8 characters.")

    # ✅ Get last 5 digits of the current epoch time
    epoch_part = str(int(time.time()))[-5:]

    # ✅ Define character groups
    uppercase = random.choice(string.ascii_uppercase)  # At least 1 uppercase
    lowercase = random.choice(string.ascii_lowercase)  # At least 1 lowercase
    digit = random.choice(string.digits)  # At least 1 digit
    special = random.choice("!@#$%^&*")  # At least 1 special character

    # ✅ Generate remaining random characters
    remaining_length = length - 5 - 4  # 5 from epoch + 4 required chars
    random_chars = "".join(
        random.choices(
            string.ascii_letters + string.digits + "!@#$%^&*", k=remaining_length
        )
    )

    # ✅ Combine all parts
    password = uppercase + lowercase + digit + special + random_chars + epoch_part

    # ✅ Shuffle the password to avoid predictable patterns
    password = "".join(random.sample(password, len(password)))
    print("password_line91",password)
    return password
print("COGNITO_REGION:", settings.COGNITO_REGION)
print("✅ ACCESS KEY:", settings.AWS_ACCESS_KEY_ID)
print("✅ SECRET KEY:", settings.AWS_SECRET_ACCESS_KEY)

# ✅ Create Cognito Client with Credentials
cognito_client = boto3.client(
    "cognito-idp",
    region_name="eu-central-1",
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,  # Use Client ID as access key
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,  # Use Client Secret as secret key
    # aws_access_key_id=settings.COGNITO_CLIENT_ID,  # Use Client ID as access key
    # aws_secret_access_key=settings.COGNITO_CLIENT_SECRET  # Use Client Secret as secret key
)
def generate_secret_hash(username, client_id, client_secret):
    """
    Generates a secret hash required for AWS Cognito API calls.
    """
    message = username + client_id
    dig = hmac.new(client_secret.encode(), message.encode(), hashlib.sha256).digest()
    return base64.b64encode(dig).decode()

def cognito_sign_up(phonenumber):
    try:
        print("line116 in auth")
        password = generate_cognito_password()
        encrypted_password = encrypt_password(password)
        print("line119")
        response = cognito_client.sign_up(
            ClientId=settings.CLIENT_ID,
            Username=phonenumber,
            UserAttributes=[
                {"Name": "email", "Value": ""},
                {"Name": "phone_number", "Value": phonenumber},
            ],
            Password=password,
            SecretHash=generate_secret_hash(
                phonenumber, settings.CLIENT_ID, settings.CLIENT_SECRET
            ),
        )
        print(response, "respppppppppppppppp")
        return response, encrypted_password
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")
    


async def update_cognito_attributes(
    username: str, attributes: dict, replace_all: bool = False
):
    """
    Updates Cognito user attributes using admin privileges.
    Can be used for both regular users and admins.

    Args:
        username: The username/email of the user
        attributes: Dictionary of attributes to update
                   (e.g., {"custom:local_user_id": "123"} or {"custom:local_admin_id": "456"})
        replace_all: If True, replaces all attributes. If False, only updates/adds specified attributes
    """
    try:
        if not replace_all:
            user_info = cognito_client.admin_get_user(
                UserPoolId=get_settings().COGNITO_USER_POOL_ID, Username=username
            )

            existing_attributes = {
                attr["Name"]: attr["Value"]
                for attr in user_info.get("UserAttributes", [])
                if attr["Name"]
                != "sub"  # Exclude the 'sub' attribute as it's immutable
            }

            merged_attributes = {**existing_attributes, **attributes}
        else:
            merged_attributes = attributes

        # Ensure 'sub' is not in the attributes to update
        if "sub" in merged_attributes:
            del merged_attributes["sub"]

        user_attributes = [
            {"Name": key, "Value": value} for key, value in merged_attributes.items()
        ]

        response = cognito_client.admin_update_user_attributes(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=username,
            UserAttributes=user_attributes,
        )
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def admin_get_user(phonenumber):
    try:
        response = cognito_client.admin_get_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=phonenumber,
        )
        print(response, "respppppppppppppppp")
        return response
    except Exception as e:
        if (
            hasattr(e, "response")
            and e.response.get("Error", {}).get("Code") == "UserNotFoundException"
        ):
            return None
        raise Exception(f"Cognito Error: {str(e)}")



async def get_user_attributes(username: str):
    """
    Retrieves all user attributes from Cognito using an access token.
    
    Args:
        token: The Cognito access token
        
    Returns:
        Dictionary of user attributes
    """
    try:
        print(username, 'username')
        user_info = cognito_client.admin_get_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=username
        )
        
        # Convert attributes list to dictionary for easier access
        attributes = {}
        print(user_info, 'user_info')
        for attr in user_info.get('UserAttributes', []):
            attributes[attr['Name']] = attr['Value']
        
        # Add username to attributes
        attributes['username'] = user_info.get('Username')
        print(attributes, 'attributes')
        
        return attributes
        
    except Exception as e:
        # print(e)
        raise Exception(f"Cognito Error: {e}")
