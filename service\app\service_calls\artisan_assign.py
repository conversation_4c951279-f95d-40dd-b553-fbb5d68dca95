import requests
import json
from app.config import settings
# import httpx

async def create_assign_artisan(payload):
    """Assign artisan to booking"""
    # try:
    print(1111111111111111111)
    url = "http://service-service:8003/request-mapping/create-artisan-assigned"
    response = requests.post(url, json=payload)
    # async with httpx.AsyncClient() as client:
        # response = await client.post(url, json=payload)
    print(2222222222222222222)
    print(response.json(), "response")
    if response.status_code == 200:
        return response.json().get("data")
    return None
    # except Exception as e:
    #     print(f"Error assigning artisan: {e}")
    #     return None