from pydantic import BaseModel, UUID4, Field
from datetime import date, datetime, time

from typing import Optional

class ServiceProviderStatusUpdate(BaseModel):
    artisan_id: Optional[str]
    live_status: str  # online/offline
    current_latitude: float = Field(..., ge=-90, le=90)  # Latitude must be between -90 and 90
    current_longitude: float = Field(..., ge=-180, le=180)  # Longitude must be between -180 and 180

class ServiceProviderLeaveCreate(BaseModel):
    leave_date: date
    reason: Optional[str] = None

class ServiceProviderLeaveUpdate(BaseModel):
    reason: Optional[str] = None

class ServiceProviderLeaveResponse(BaseModel):
    id: UUID4
    service_provider_id: UUID4
    leave_date: date
    reason: Optional[str]
    created_at: datetime
    

class ArtisanSearchRequest(BaseModel):
    service_id: str
    latitude: float
    longitude: float
    max_distance: float = 5  # Default max distance is 10 km
    start_time: time
    end_time: time
    booking_date: date
    sort_by: str = "rating"  # Default sort by rating
    page: int = 1
    limit: int = 10


class LiveArtisan(BaseModel):
    service_id: str
    latitude: float
    longitude: float
    max_distance: int = 10
    page: int = 1
    limit: int = 10
    