import uuid
import random
from datetime import date, time
from sqlalchemy.orm import Session
from geoalchemy2.functions import ST_GeomFromText
from app.database import get_db  # Ensure this is your actual DB session import
import math
from fastapi import Depends
from enum import Enum

from app.models import ServiceProvider  # Ensure the correct model is imported
# from app.enums import ServiceProviderStatusType, AdminRole, Gender  # Import your Enums


class ServiceProviderStatusType(str, Enum):
    CREATED = "created"
    APPROVED = "approved"
    REJECTED = "rejected"


class AdminRole(str, Enum):
    ADMIN = "admin"
    SUPERADMIN = "superadmin"

class Gender(str, Enum):
    MALE = "male"
    FEMALE = "female"
    
    
# Define central coordinates (e.g., New York)
CENTER_LAT = 40.7128
CENTER_LONG = -74.0060

# Define skill categories
SKILLS = ["Plumber", "Electrician", "Carpenter", "Painter", "Mechanic", "Technician"]
SKILL_LEVELS = ["Beginner", "Intermediate", "Expert"]
LIVE_STATUS = ["online", "offline", "busy"]

# Define radius groups in meters (5km, 10km, 15km, etc.)
RADIUS_GROUPS = [5000, 10000, 15000, 20000, 25000]


def generate_random_location(center_lat, center_long, radius_meters):
    """
    Generate random latitude and longitude within a given radius (meters) from a central point.
    """
    # Convert radius to degrees
    earth_radius = 6371000  # Earth's radius in meters
    delta_lat = (radius_meters / earth_radius) * (180 / math.pi)
    delta_long = delta_lat / abs(math.cos(math.radians(center_lat)))

    # Generate random point within the radius
    random_lat = center_lat + random.uniform(-delta_lat, delta_lat)
    random_long = center_long + random.uniform(-delta_long, delta_long)

    return random_lat, random_long


def seed_data():
    """Seed initial service providers into the database."""
    db = next(get_db())  # Get the DB session
    
    # Check if table already has data
    if db.query(ServiceProvider).first():
        print("✅ Service Provider table already has data. Skipping seed.")
        db.close()
        return

    print("🌱 Seeding initial service provider data...")

    providers = []
    total_providers = 0

    for radius in RADIUS_GROUPS:
        for _ in range(20):  # 20 providers per radius
            lat, long = generate_random_location(CENTER_LAT, CENTER_LONG, radius)

            provider = ServiceProvider(
                id=uuid.uuid4(),
                auth_id=f"auth_{total_providers + 1}",
                first_name=f"User{total_providers + 1}",
                last_name="Doe",
                email=f"user{total_providers + 1}@example.com",
                gender=random.choice(["MALE", "FEMALE"]),  # Use Enum values
                dob=date(random.randint(1980, 2000), random.randint(1, 12), random.randint(1, 28)),
                phone_number=str(random.randint(**********, **********)),
                primary_location=f"Radius {radius // 1000}km Area",
                skill=random.choice(SKILLS),
                skill_level=random.choice(SKILL_LEVELS),
                rating=random.randint(1, 5),
                status=random.choice(["CREATED", "APPROVED", "REJECTED"]),  # Use Enum values
                live_status=random.choice(LIVE_STATUS),
                latitude=lat,
                longitude=long,
                location=ST_GeomFromText(f"POINT({long} {lat})", srid=4326),  # Ensure correct order
                work_from_hrs=time(random.randint(6, 10), 0, 0),
                work_to_hrs=time(random.randint(17, 22), 0, 0),
                break_from_hrs=time(random.randint(12, 14), 0, 0),
                break_to_hrs=time(random.randint(13, 15), 0, 0),
                weekdays=random.sample(range(1, 8), random.randint(5, 7))  # Random working days
            )

            providers.append(provider)
            total_providers += 1

    db.add_all(providers)
    db.commit()
    db.close()
    
    print(f"✅ {total_providers} Service Providers seeded successfully!")


if __name__ == "__main__":
    seed_data()
