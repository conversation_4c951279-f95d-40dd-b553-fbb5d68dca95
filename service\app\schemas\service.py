from pydantic import BaseModel, EmailStr, <PERSON><PERSON>, UUID4
from datetime import datetime, date
from typing import Optional, Union
from fastapi import Form
from pydantic.types import conint
from app.models_enum import PricingType, ServiceType, ServiceUnits

class CreateServices:
    def __init__(
        self,
        name: Optional[str] = Form(None),
        parent_id: Optional[UUID4] = Form(None),
        duration: Optional[int] = Form(0),
        price: Optional[float] = Form(0),
        description: Optional[str] = Form(None),
        service_code: Optional[str] = Form(None),
    ):
        self.name = name
        self.parent_id = parent_id
        self.duration = duration
        self.price = price
        self.description = description
        self.service_code = service_code

    def to_dict(self):
        return {
            "name": self.name,
            "parent_id": str(self.parent_id),
            "duration": self.duration,
            "price": self.price,
            "description": self.description,
            "service_code": self.service_code
            }


class ServicesResponse(BaseModel):
    id: UUID4
    name: str
    parent_id: UUID4
    banner: str
    duration: int

class UpdateServices:
    def __init__(
        self,
        name: Optional[str] = Form(None),
        parent_id: Optional[UUID4] = Form(None),
        duration: Optional[int] = Form(0),
        price: Optional[float] = Form(0),
        description: Optional[str] = Form(None),
        service_code: Optional[str] = Form(None),
    ):
        self.name = name
        self.parent_id = parent_id
        self.duration = duration
        self.price = price
        self.description = description
        self.service_code = service_code

