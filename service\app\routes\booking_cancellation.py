# app/routes/booking_cancellation.py

from fastapi import APIRouter, Depends , Request
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import update, or_, and_
from sqlalchemy import or_
from uuid import UUID

from app.database import get_db
from app.models import Booking, InvoiceItem, BookingCancellationReason, BookingCancellation, UserProfiles, ArtisanAssigned, Invoice, ArtisanDetail, Services, Payment, ActivityLog
from app.schemas.helper import StandardResponse, ErrorResponse
from app.schemas.booking_cancelation import CancelRequestPayload, BookingCancellationListFilters, UpdateCancellationStatusPayload
from app.utils.auth import permission_checker
from app.utils.activity_feed import log_activity
from app.models_enum import InvoiceItemStatus, UserType, BookingCancellationStatus, BookingStatus, ArtisanAssignStatus, ActivityType
from app.utils.notification import send_push_notification
from fastapi.responses import JSONResponse
from app.utils.helper_res import get_avg_rating_by_user
router = APIRouter()

@router.post("/request-cancel", response_model=StandardResponse)
def request_cancel_service(
    payload: CancelRequestPayload,
    request:Request,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        token = request.headers.get("Authorization")

        user_id = user.get("user_id")

        # Validate cancellation reason
        reason = db.query(BookingCancellationReason).filter(
            BookingCancellationReason.id == payload.cancellation_reason_id
        ).first()
        if not reason:
            return ErrorResponse(message="Invalid cancellation reason ID", status_code=404)

        # Fetch the booking
        booking = db.query(Booking).filter(
            Booking.id == payload.booking_id,
            Booking.user_id == user_id
        ).first()
        if not booking:
            return ErrorResponse(message="Booking not found or does not belong to this user", status_code=404)

        invoice_items = []

        if payload.is_complete_booking_cancel:
            # Fetch all invoice items for this invoice
            invoice_items = db.query(InvoiceItem).filter(
                InvoiceItem.invoice_id == booking.invoice_id
            ).all()

            if not invoice_items:
                return ErrorResponse(message="No invoice items found for this booking", status_code=404)

            # Mark all invoice items as CANCEL_REQUESTED
            for item in invoice_items:
                item.status = InvoiceItemStatus.CANCEL_REQUESTED
                db.add(item)

            # Create single cancellation record for full booking
            cancellation = BookingCancellation(
                user_id=user_id,
                booking_id=booking.id,
                agent_id=None,
                invoice_id=booking.invoice_id,
                invoice_item_id=None,  # None indicates full booking cancel
                status=BookingCancellationStatus.PENDING,
                cancellation_reason_id=payload.cancellation_reason_id,
                user_type=UserType.USER,
            )
            db.add(cancellation)

            # Change Booking status to cancel
            booking.status = BookingStatus.CANCEL_REQUESTED
            db.commit()

            if booking.assigned_agent_id:
                # send the fcm to AGENT
                send_push_notification(
                    auth_token=token,
                    title="User Requested Cancellation",
                    message=f"Service has been cancelled",
                    sender_id=str(booking.assigned_agent_id),
                    type="agent",
                    user_id= str(booking.assigned_agent_id),  # assigned agent id
                    fcm_request_type ="booking",
                    data={
                        "booking_id": str(booking.id),
                        "booking_order_id": str(booking.booking_order_id)
                    }
                )

        else:
            # Single item cancel
            if not payload.invoice_item_id:
                return ErrorResponse(message="invoice_item_id is required for single item cancellation", status_code=400)

            invoice_items = db.query(InvoiceItem).filter(
                or_(
                    InvoiceItem.id == payload.invoice_item_id,
                    InvoiceItem.parent_id == payload.invoice_item_id
                )
            ).all()

            if not invoice_items:
                return ErrorResponse(message="Invoice item not found", status_code=404)

            # Validate invoice_id match for main item
            main_item = next((item for item in invoice_items if item.id == payload.invoice_item_id), None)
            if not main_item or main_item.invoice_id != booking.invoice_id:
                return ErrorResponse(message="Invoice item does not belong to booking", status_code=400)

            # Update status of all related items
            for item in invoice_items:
                if item.invoice_id == booking.invoice_id:
                    item.status = InvoiceItemStatus.CANCEL_REQUESTED
                    db.add(item)

            # Create cancellation record only for the specific invoice_item in payload
            cancellation = BookingCancellation(
                user_id=user_id,
                booking_id=booking.id,
                agent_id=None,
                invoice_id=main_item.invoice_id,
                invoice_item_id=main_item.id,
                status=BookingCancellationStatus.PENDING,
                cancellation_reason_id=payload.cancellation_reason_id,
                user_type=UserType.USER,
            )
            db.add(cancellation)

            db.commit()

            # return StandardResponse(message="Cancellation request submitted", status_code=201)
        log_activity(
            db=db,
            title="Booking Cancellation Request",
            description="User requested booking cancellation",
            reference_id=payload.booking_id,
            customer_id=booking.user_id,
            activity_type=ActivityType.BOOKING_CANCELLATION_REQUESTED,
        )

        if booking.assigned_agent_id:
            # send the fcm to AGENT
            send_push_notification(
                auth_token=token,
                title="User Requested Cancellation",
                message=f"Service has been cancelled",
                sender_id=str(booking.assigned_agent_id),
                type="agent",
                user_id= str(booking.assigned_agent_id),  # assigned agent id
                fcm_request_type ="booking",
                data={
                    "booking_id": str(booking.id),
                    "booking_order_id": str(booking.booking_order_id)
                }
            )

        return StandardResponse(message="Cancellation request submitted", status_code=201)

    except Exception as e:
        db.rollback()
        return ErrorResponse(message=f"Internal Error: {str(e)}", status_code=500)

@router.post("/agent-request-cancel", response_model=StandardResponse)
def request_cancel_service(
    payload: CancelRequestPayload,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        # if isinstance(user, JSONResponse):
        #     return user

        user_id = user.get("user_id")

        # Validate cancellation reason
        reason = db.query(BookingCancellationReason).filter(
            BookingCancellationReason.id == payload.cancellation_reason_id
        ).first()
        if not reason:
            return ErrorResponse(message="Invalid cancellation reason ID", status_code=404)

        # Validate booking
        booking = db.query(Booking).filter(Booking.id == payload.booking_id).first()
        if not booking:
            return ErrorResponse(message="Booking not found", status_code=404)
        
        if booking.status == BookingStatus.CANCELLED:
            return ErrorResponse(message="Booking Already cancelled", status_code=400)

        invoice_id = booking.invoice_id
        if not invoice_id:
            return ErrorResponse(message="Invoice not found for this booking", status_code=404)

        if payload.is_complete_booking_cancel:
            # Fetch all invoice items linked to this invoice
            invoice_items = db.query(InvoiceItem).filter(
                InvoiceItem.invoice_id == invoice_id
            ).all()

            if not invoice_items:
                return ErrorResponse(message="No invoice items found for full cancellation", status_code=404)

            # Mark all invoice items as CANCEL_REQUESTED
            for item in invoice_items:
                item.status = InvoiceItemStatus.CANCEL_REQUESTED
                db.add(item)

            # Create ONE cancellation record with is_full_booking_cancel = True
            cancellation = BookingCancellation(
                agent_id=user_id,
                user_id=booking.user_id,
                booking_id=booking.id,
                invoice_id=invoice_id,
                invoice_item_id=None,  # No specific item, this is for full cancel
                cancellation_reason_id=payload.cancellation_reason_id,
                status=BookingCancellationStatus.APPROVED,
                user_type=UserType.AGENT,
                is_full_booking_cancel=True
            )
            db.add(cancellation)

            # Change Booking status to cancel
            booking.status = BookingStatus.CANCELLED

        else:
            # Partial cancellation - single invoice item
            if not payload.invoice_item_id:
                return ErrorResponse(message="invoice_item_id is required for partial cancellation", status_code=400)

            invoice_item = db.query(InvoiceItem).filter(
                InvoiceItem.id == payload.invoice_item_id,
                InvoiceItem.invoice_id == invoice_id
            ).first()

            if not invoice_item:
                return ErrorResponse(message="Invoice item not found", status_code=404)

            invoice_item.status = InvoiceItemStatus.CANCELLED
            db.add(invoice_item)

            # Create one cancellation record for the single item
            cancellation = BookingCancellation(
                agent_id=user_id,
                user_id=booking.user_id,
                booking_id=booking.id,
                invoice_id=invoice_id,
                invoice_item_id=invoice_item.id,
                cancellation_reason_id=payload.cancellation_reason_id,
                status=BookingCancellationStatus.APPROVED,
                user_type=UserType.AGENT,
                is_full_booking_cancel=False
            )
            db.add(cancellation)

        log_activity(
            db=db,
            title="Booking Cancellation Request",
            description="Agent requested booking cancellation",
            reference_id=payload.booking_id,
            customer_id=user_id,
            activity_type=ActivityType.BOOKING_CANCELLATION_REQUESTED,
        )

        db.commit()
        return StandardResponse(message="Cancellation request submitted", status_code=201)

    except Exception as e:
        db.rollback()
        return ErrorResponse(message=f"Internal Error: {str(e)}", status_code=500)


@router.post("/cancellations", response_model=StandardResponse)
def list_booking_cancellations(
    request: BookingCancellationListFilters,
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    try:
        # if isinstance(user, JSONResponse):
        #     return user

        query = db.query(BookingCancellation, UserProfiles).outerjoin(UserProfiles, BookingCancellation.user_id == UserProfiles.id)

        if request.user_id:
            query = query.filter(BookingCancellation.user_id == request.user_id)

        if request.artisan_id:
            query = query.filter(BookingCancellation.artisan_id == request.artisan_id)

        if request.status:
            query = query.filter(BookingCancellation.status == request.status)

        if request.start_date and request.end_date:
            query = query.filter(
                BookingCancellation.created_at >= request.start_date,
                BookingCancellation.created_at <= request.end_date
            )

        filters = []
        # i need to search by booking order id and user name
        if request.q:
            # Join with Booking table to search by booking_order_id
            query = query.join(Booking, BookingCancellation.booking_id == Booking.id)
            filters.append(or_(
                Booking.booking_order_id.ilike(f"%{request.q}%"),
                UserProfiles.first_name.ilike(f"%{request.q}%"),
                UserProfiles.last_name.ilike(f"%{request.q}%"),
                # Full name search (first_name + last_name)
                (UserProfiles.first_name + ' ' + UserProfiles.last_name).ilike(f"%{request.q}%"),
                # Full name search (last_name + first_name)
                (UserProfiles.last_name + ' ' + UserProfiles.first_name).ilike(f"%{request.q}%"),
            ))

        if filters:
            query = query.where(and_(*filters))
    

        total = query.count()
        offset = (request.skip - 1) * request.limit

        cancellations = query.order_by(BookingCancellation.created_at.desc()) \
            .offset(offset).limit(request.limit).all()

        data = []

        for bc_tuple in cancellations:
            # bc_tuple is (BookingCancellation, UserProfiles)
            bc = bc_tuple[0]  # Get the BookingCancellation object
            booking = db.query(Booking).filter_by(id=bc.booking_id).first()
            invoice = db.query(Invoice).filter_by(id=booking.invoice_id).first() if booking and booking.invoice_id else None
            user_profile = db.query(UserProfiles).filter_by(id=booking.user_id).first() if booking else None

            # Get cancellation reason text
            reason_obj = (
                db.query(BookingCancellationReason)
                .filter_by(id=bc.cancellation_reason_id)
                .first()
            )
            reason_text = reason_obj.reason if reason_obj else None

            # Determine who cancelled (Agent or User)
            invoice_items = invoice.items if invoice else []
            agent_created = any(item.is_agent_created for item in invoice_items)
            cancel_by = "Agent" if agent_created else "User" if invoice_items else None

            data.append({
                "id": str(bc.id),
                "booking_id": str(bc.booking_id),
                "booking_order_id": booking.booking_order_id if booking else None,
                "invoice_id": str(bc.invoice_id) if bc.invoice_id else None,
                "user_id": str(bc.user_id),
                "artisan_id": str(bc.artisan_id) if bc.artisan_id else None,
                "status": bc.status.value,
                "reason": reason_text,
                "created_at": bc.created_at.isoformat() if bc.created_at else None,
                "customer_name": f"{user_profile.first_name or ''} {user_profile.last_name or ''}".strip() if user_profile else None,
                "cancel_by": cancel_by,
                "payment_method": invoice.payment_method if invoice else None,
                "invoice_status": invoice.payment_status.value if invoice else None,
            })

        return StandardResponse(
            message="Booking cancellations fetched",
            status_code=200,
            data={
                "results": data,
                "pagination": {
                    "total": total,
                    "limit": request.limit,
                    "page": request.skip,
                    "pages": (total + request.limit - 1) // request.limit
                }
            }
        )
    except Exception as e:
        db.rollback()
        return ErrorResponse(status_code=500, message=f"Error fetching cancellations: {str(e)}")


@router.post("/cancellation/approve", response_model=StandardResponse)
def approve_cancellation(
    request:Request,
    payload: UpdateCancellationStatusPayload,
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    try:
        if isinstance(user, JSONResponse):
            return user
        
        token = request.headers.get("Authorization")
        if not token:
            return ErrorResponse(status_code=401, message="Authorization token is required")
        # Fetch cancellation record
        cancellation = db.query(BookingCancellation).filter(
            BookingCancellation.id == payload.cancellation_id
        ).first()

        if not cancellation:
            return ErrorResponse(status_code=404, message="Cancellation not found")

        # Optional: Validate if already approved
        if cancellation.status == BookingCancellationStatus.APPROVED:
            return ErrorResponse(status_code=400, message="Already approved")

        # Update cancellation status
        cancellation.status = payload.status
        if payload.agent_id:
            cancellation.agent_id = payload.agent_id

        # Update invoice items if cancellation is approved
        if payload.status == BookingCancellationStatus.APPROVED:
            if cancellation.is_full_booking_cancel:
                # Full invoice cancellation
                invoice_items = db.query(InvoiceItem).filter(
                    InvoiceItem.invoice_id == cancellation.invoice_id
                ).all()
            else:
                invoice_items = db.query(InvoiceItem).filter(
                    or_(
                        InvoiceItem.id == cancellation.invoice_item_id,
                        InvoiceItem.parent_id == cancellation.invoice_item_id
                    )
                ).all()

            for item in invoice_items:
                item.status = InvoiceItemStatus.CANCELLED
                db.add(item)

        db.commit()

        # Send push notification if user profile found
        user_profile = db.query(UserProfiles).filter(
            UserProfiles.id == cancellation.user_id
        ).first()

        if user_profile:
            send_push_notification(
                auth_token=token,
                title="Cancellation Approved",
                message="Your booking cancellation has been approved.",
                sender_id=str(user_profile.id),
                type="user",
                data={
                    "cancellation_id": str(cancellation.id),
                    "booking_id": str(cancellation.booking_id),
                    "status": cancellation.status.value,
                    "request_type": "booking"
                }
            )
        
        log_activity(
            db=db,
            title="Booking Cancellation Approved",
            description="Agent approved booking cancellation",
            reference_id=cancellation.booking_id,
            customer_id=cancellation.user_id,
            activity_type=ActivityType.BOOKING_CANCELLATION_APPROVED,
        )

        return StandardResponse(
            status_code=200,
            message=f"Cancellation status updated to {payload.status}",
        )
    except Exception as e:
        db.rollback()
        return ErrorResponse(status_code=500, message=f"Update failed: {str(e)}")


@router.get("/cancellation-summary/{cancellation_id}", response_model=StandardResponse)
def get_cancellation_summary(
    cancellation_id: str,
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    try:
        # Fetch cancellation record
        cancellation = db.query(BookingCancellation).filter_by(id=cancellation_id).first()
        if not cancellation:
            return ErrorResponse(status_code=404, message="Cancellation not found")

        # Fetch booking
        booking = db.query(Booking).filter_by(id=cancellation.booking_id).first()
        if not booking:
            return ErrorResponse(status_code=404, message="Booking not found")

        # Fetch user details
        user_profile = db.query(UserProfiles).filter_by(id=booking.user_id).first()

        # Fetch invoice and items
        invoice = db.query(Invoice).filter_by(id=booking.invoice_id).first() if booking.invoice_id else None
        service_list = []
        
        if invoice and invoice.items:
            # If cancellation is for full booking (invoice_item_id is null), include all invoice items
            # If cancellation is for specific item, only include that item
            items_to_process = []
            
            if cancellation.invoice_item_id is None:
                # Full booking cancellation - include all invoice items
                items_to_process = invoice.items
            else:
                # Partial cancellation - only include the specific cancelled item
                specific_item = next((item for item in invoice.items if item.id == cancellation.invoice_item_id), None)
                if specific_item:
                    items_to_process = [specific_item]
            
            for item in items_to_process:
                # Fetch all artisan assignments for this item
                assigned_artisans = db.query(ArtisanAssigned).filter_by(invoice_item_id=item.id).all()

                artisans = []
                for assigned in assigned_artisans:
                    artisan_user = db.query(UserProfiles).filter_by(id=assigned.artisan_id).first()
                    artisan_detail = db.query(ArtisanDetail).filter_by(user_id=assigned.artisan_id).first()
                    # get the job count for that artisan , completed status for that artisan in artisan assigned table
                    job_count = db.query(ArtisanAssigned).filter(
                        ArtisanAssigned.artisan_id == assigned.artisan_id,
                        ArtisanAssigned.status == ArtisanAssignStatus.COMPLETED
                    ).count()
                    if artisan_user:
                        artisans.append({
                            "first_name": artisan_user.first_name,
                            "last_name": artisan_user.last_name,
                            "email": artisan_user.email,
                            "phone_number": artisan_user.phone_number,
                            "profile_picture": artisan_user.profile_image_url if artisan_user.profile_image_url else None,
                            # "experience": artisan_detail.experience if artisan_detail else None,
                            "skill": artisan_detail.skill if artisan_detail else None,
                            "ratings": get_avg_rating_by_user(db, assigned.artisan_id),
                            "job_count": job_count
                        })

                # Get service name from Service table
                service = db.query(Services).filter_by(id=item.service_id).first()
                # get the paid date from payment table based on invoice id and item id
                # payment = db.query(Payment).filter_by(invoice_id=invoice.id, invoice_item_id=item.id).first()
                service_list.append({
                    "service_id": item.service_id,
                    "service_name": service.name if service else None,
                    "price": item.price,
                    "quantity": item.quantity,
                    "total_amount": item.total_amount,
                    # "status": item.status.value,
                    # "paid_date": payment.payment_date.isoformat() if payment and payment.payment_date else None,
                    "artisans": artisans,
                })

        # Cancellation Reason
        booking_cancelation_reason = db.query(BookingCancellationReason).filter_by(id=cancellation.cancellation_reason_id).first()
        
        # Get payment details for the invoice
        payment = None
        if invoice:
            payment = db.query(Payment).filter_by(invoice_id=invoice.id).first()
        
        activity_logs = db.query(ActivityLog).filter_by(reference_id=booking.id).order_by(ActivityLog.created_at.asc()).all()
        activity_logs_data = [
            {
                "title": log.title,
                "description": log.description,
                "activity_type": log.activity_type,
                "created_at": log.created_at.isoformat()
            }
            for log in activity_logs
        ]

        
        data = {
            "cancellation": {
                "id": str(cancellation.id),
                "reason": booking_cancelation_reason.reason if booking_cancelation_reason else None,
                "status": cancellation.status.value,
                "cancellation_requested_date": cancellation.created_at.isoformat(),
                "cancel_by": cancellation.user_type,
            },
            "booking": {
                "id": str(booking.id),
                "booking_order_id": str(booking.booking_order_id),
                "requested_type": booking.request_type,
                "date": booking.booking_date.isoformat(),
                "requested_time": booking.requested_time.isoformat() if booking.requested_time else None,
                "latitude": booking.user_latitude,
                "longitude": booking.user_longitude,
                "address": booking.user_address,
                "status": booking.status.value,
                "payment_method": invoice.payment_method,
            },
            "user": {
                "first_name": user_profile.first_name if user_profile else None,
                "last_name": user_profile.last_name if user_profile else None,
                "email": user_profile.email if user_profile else None,
                "phone_number": user_profile.phone_number if user_profile else None,
                "profile_picture": user_profile.profile_image_url if user_profile else None,
            },
            "services": service_list,
            "activity_logs": activity_logs_data,
            "invoice_details": {
                "invoice_id": str(invoice.id),
                "total_amount": invoice.total_amount,
                "payment_status": invoice.payment_status.value,
                "payment_method": invoice.payment_method.value if invoice.payment_method else None,
                "payment_date": payment.payment_date.isoformat() if payment and payment.payment_date else None
            } if invoice else None
        }

        return StandardResponse(status_code=200, message="Cancellation summary fetched", data=data)
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        db.rollback()
        return ErrorResponse(status_code=500, message="Internal server error", error=str(e))
