from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File, Form , Header
from sqlalchemy import or_, String, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List, Optional , Annotated
from app.db_async import get_db

from app.models import IssueCategorys, Issues, SubCategory, IssueComments, IssueHistory
from app.models import Admin
from app.models import ServiceProvider , Users
from app.schemas.helper import StandardResponse, ErrorResponse
from app.schemas.issues import (
    IssueCategoryCreate,
    IssueCategoryResponse,
    SubCategoryCreate,
    SubCategoryResponse,
    IssueCategoryUpdate,
    SubCategoryUpdate,
    IssueCreateForm,
    IssueUpdate , IssuesCommentCreate , IssuesListRequest
)
from uuid import UUID
import logging
import json
from app.utils.enums import IssueStatus, IssuePriority, UserType
from fastapi import Header
from datetime import datetime
from sqlalchemy.sql import func
from sqlalchemy.sql import and_, delete
from sqlalchemy import desc
from app.s3_upload import upload_file_direct
from app.config import get_settings
import uuid
import os
from sqlalchemy.orm import Session, aliased
from app.utils.notification import send_push_notification
from app.utils.auth import get_id_header
router = APIRouter()
logger = logging.getLogger(__name__)



async def validate_token(Authorization: str = Header(...)):
    """Dependency function to validate token"""
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            user_role = resp_json.get("user_role")
            auth_token = resp_json.get("auth_token")
            return {"account_id": account_id,"user_role":user_role ,"auth_token": auth_token}
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Unauthorized"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )

@router.post("/create-categories-issues", response_model=StandardResponse)
async def create_issue_category(
    category: IssueCategoryCreate,
    db: AsyncSession = Depends(get_db),
    token_data: dict = Depends(validate_token)
):
    """
    Create a new issue category with optional subcategories.
    """
    async with db.begin() as transaction:
        try:
            # Check if category with same code already exists
            stmt = select(IssueCategorys).where(IssueCategorys.code == category.code)
            result = await db.execute(stmt)
            existing_category = result.scalar_one_or_none()
            
            if existing_category:
                return ErrorResponse(
                    status_code=400,
                    message=f"Category with code '{category.code}' already exists"
                )

            # Create new category
            db_category = IssueCategorys(
                name=category.name,
                code=category.code,
                description=category.description,
                default_priority=category.default_priority
            )
            db.add(db_category)
            await db.flush()

            # Create subcategories if provided
            subcategories_data = []
            if category.subcategories:
                for subcat in category.subcategories:
                    db_subcategory = SubCategory(
                        name=subcat.name,
                        description=subcat.description,
                        category_id=db_category.id
                    )
                    db.add(db_subcategory)
                    subcategories_data.append({
                        "name": subcat.name,
                        "description": subcat.description
                    })

            # Commit will be handled by the transaction context manager
            await db.flush()

            # Prepare response data
            response_data = {
                "id": str(db_category.id),
                "name": db_category.name,
                "code": db_category.code,
                "description": db_category.description,
                "default_priority": db_category.default_priority,
                "created_at": db_category.created_at,
                "updated_at": db_category.updated_at,
                "subcategories": []
            }

            # Query the created subcategories
            if category.subcategories:
                stmt = select(SubCategory).where(SubCategory.category_id == db_category.id)
                result = await db.execute(stmt)
                subcategories = result.scalars().all()
                
                response_data["subcategories"] = [
                    {
                        "id": str(subcat.id),
                        "name": subcat.name,
                        "description": subcat.description,
                        "created_at": subcat.created_at,
                        "updated_at": subcat.updated_at
                    }
                    for subcat in subcategories
                ]

            return StandardResponse(
                status_code=200,
                message="Issue category and subcategories created successfully",
                data=response_data
            )

        except Exception as e:
            logger.error(f"Error creating issue category: {str(e)}")
            # No need to explicitly rollback as it's handled by the transaction context
            raise HTTPException(
                status_code=500,
                detail=f"Error creating issue category: {str(e)}"
            )

@router.put("/update-categories-issues/{category_id}", response_model=StandardResponse)
async def update_issue_category(
    category_id: UUID,
    category_update: IssueCategoryUpdate,
    db: AsyncSession = Depends(get_db),
    token_data: dict = Depends(validate_token)
):
    """
    Update an issue category and its subcategories.
    """
    try:
        # Get existing category
        stmt = select(IssueCategorys).where(IssueCategorys.id == category_id)
        result = await db.execute(stmt)
        db_category = result.scalar_one_or_none()

        if not db_category:
            return ErrorResponse(
                status_code=404,
                message=f"Issue category with id '{category_id}' not found"
            )

        # Check if category is linked to any issues
        category_usage_stmt = select(
            func.count(Issues.id).label('issue_count')
        ).where(
            Issues.category_id == category_id
        )
        category_usage_result = await db.execute(category_usage_stmt)
        issue_count = category_usage_result.scalar()

        if issue_count > 0:
            # Check what fields are being updated
            update_data = category_update.dict(exclude={'subcategories'}, exclude_unset=True)
            fields_to_update = []
            
            if 'code' in update_data and update_data['code'] != db_category.code:
                fields_to_update.append('code')
            if 'name' in update_data and update_data['name'] != db_category.name:
                fields_to_update.append('name')
            
            if fields_to_update:
                return ErrorResponse(
                    status_code=400,
                    message=f"Unable to update the '{db_category.name}' category because it is associated with {issue_count} existing issues"
                )

        # Check if new code conflicts with existing category
        if category_update.code and category_update.code != db_category.code:
            code_stmt = select(IssueCategorys).where(
                and_(
                    IssueCategorys.code == category_update.code,
                    IssueCategorys.id != category_id
                )
            )
            code_result = await db.execute(code_stmt)
            if code_result.scalar_one_or_none():
                return ErrorResponse(
                    status_code=400,
                    message=f"Category with code '{category_update.code}'has already exists"
                )

        # Update category fields
        update_data = category_update.dict(exclude={'subcategories'}, exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_category, field, value)

        # Handle subcategories if provided
        if category_update.subcategories is not None:
            # Get existing subcategories
            existing_subcats_stmt = select(SubCategory).where(SubCategory.category_id == category_id)
            existing_subcats_result = await db.execute(existing_subcats_stmt)
            existing_subcats = existing_subcats_result.scalars().all()
            existing_subcat_ids = {str(subcat.id) for subcat in existing_subcats}

            # Get subcategories that are in use
            used_subcats_stmt = select(
                SubCategory.id,
                SubCategory.name,
                func.count(Issues.id).label('issue_count')
            ).join(
                Issues,
                Issues.subcategory_id == SubCategory.id,
                isouter=True
            ).where(
                SubCategory.id.in_(existing_subcat_ids)
            ).group_by(SubCategory.id, SubCategory.name)

            used_subcats_result = await db.execute(used_subcats_stmt)
            used_subcats = used_subcats_result.all()
            
            # Create a map of used subcategories with their issue counts
            used_subcat_map = {
                str(subcat.id): {
                    'name': subcat.name,
                    'issue_count': subcat.issue_count
                } for subcat in used_subcats if subcat.issue_count > 0
            }

            # Check if any subcategories to be removed are in use
            subcategories_to_remove = set(existing_subcat_ids) - {str(subcat.id) for subcat in existing_subcats if subcat.name in [s.name for s in category_update.subcategories]}
            in_use_subcategories = subcategories_to_remove.intersection(used_subcat_map.keys())

            if in_use_subcategories:
                in_use_details = [
                    f"{used_subcat_map[subcat_id]['name']} ({used_subcat_map[subcat_id]['issue_count']} issues)"
                    for subcat_id in in_use_subcategories
                ]
                return ErrorResponse(
                    status_code=400,
                    message=f"Can't remove sub categories that are linked to existing issues: {', '.join(in_use_details)}"
                )

            # Create a map of existing subcategories by name for easy lookup
            existing_subcat_map = {subcat.name: subcat for subcat in existing_subcats}

            # Process each new subcategory
            for subcat in category_update.subcategories:
                if subcat.name in existing_subcat_map:
                    # Update existing subcategory
                    existing_subcat = existing_subcat_map[subcat.name]
                    existing_subcat.description = subcat.description
                else:
                    # Create new subcategory
                    new_subcat = SubCategory(
                        name=subcat.name,
                        description=subcat.description,
                        category_id=category_id
                    )
                    db.add(new_subcat)

            # Delete unused subcategories
            unused_subcat_ids = existing_subcat_ids - {str(subcat.id) for subcat in existing_subcats if subcat.name in [s.name for s in category_update.subcategories]}
            if unused_subcat_ids:
                delete_stmt = delete(SubCategory).where(
                    and_(
                        SubCategory.id.in_(unused_subcat_ids),
                        SubCategory.category_id == category_id
                    )
                )
                await db.execute(delete_stmt)

        await db.commit()

        # Query the updated category with its subcategories
        stmt = select(IssueCategorys).where(IssueCategorys.id == category_id)
        result = await db.execute(stmt)
        updated_category = result.scalar_one()

        # Get subcategories in a separate query
        subcat_stmt = select(SubCategory).where(SubCategory.category_id == category_id)
        subcat_result = await db.execute(subcat_stmt)
        subcategories = subcat_result.scalars().all()

        response_data = {
            "id": str(updated_category.id),
            "name": updated_category.name,
            "code": updated_category.code,
            "description": updated_category.description,
            "default_priority": updated_category.default_priority,
            "created_at": updated_category.created_at,
            "updated_at": updated_category.updated_at,
            "subcategories": [
                {
                    "id": str(subcat.id),
                    "name": subcat.name,
                    "description": subcat.description,
                    "created_at": subcat.created_at,
                    "updated_at": subcat.updated_at
                }
                for subcat in subcategories
            ]
        }

        return StandardResponse(
            status_code=200,
            message="Issue category and subcategories updated successfully",
            data=response_data
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"Error updating issue category: {str(e)}")
        return ErrorResponse(
            status_code=500,
            message=f"Error updating issue category: {str(e)}"
        )

@router.delete("/delete-categories-issues/{category_id}", response_model=StandardResponse)
async def delete_issue_category(
    category_id: UUID,
    db: AsyncSession = Depends(get_db),
    token_data: dict = Depends(validate_token)
):
    """
    Delete an issue category and all its associated subcategories.
    """
    try:
        # Get existing category with issues count
        query = select(IssueCategorys).where(IssueCategorys.id == category_id)
        result = await db.execute(query)
        db_category = result.scalar_one_or_none()

        if not db_category:
            return ErrorResponse(
                status_code=404,
                message="Issue category not found"
            )

        # Check if category has associated issues
        issues_query = select(func.count()).select_from(Issues).where(Issues.category_id == category_id)
        issues_result = await db.execute(issues_query)
        issues_count = issues_result.scalar()

        if issues_count > 0:
            return ErrorResponse(
                status_code=400,
                message="Cannot delete category with associated issues"
            )

        # Delete category (subcategories will be deleted automatically due to cascade)
        await db.delete(db_category)
        await db.commit()

        return StandardResponse(
            status_code=200,
            message=f"Category '{db_category.name}' and its subcategories deleted successfully"
        )
    except Exception as e:
        await db.rollback()
        logger.error(f"Error deleting issue category: {str(e)}")
        return ErrorResponse(
            status_code=500,
            message=f"Error deleting issue category: {str(e)}"
        )

# @router.post("/issues-create", response_model=StandardResponse)
# async def create_issue(
#     data: IssueCreateForm = Depends(),
#     attachments: List[UploadFile] = File(None),
#     db: AsyncSession = Depends(get_db),
#     token_data: dict = Depends(validate_token)
# ):
#     """
#     Create a new issue with optional image uploads.
#     Images are stored in S3.
#     """
#     try:
#         logger.info("Starting issue creation process")

#         # Validate customer id exists in at least one of users, service providers, or admin tables
#         customer_query = select(Users).where(Users.id == data.customer_id)
#         customer_result = await db.execute(customer_query)
#         customer = customer_result.scalar_one_or_none()

#         if not customer:
#             # Check service providers table
#             service_provider_query = select(ServiceProvider).where(ServiceProvider.id == data.customer_id)
#             service_provider_result = await db.execute(service_provider_query)
#             service_provider = service_provider_result.scalar_one_or_none()

#             if not service_provider:
#                 # Check admin table
#                 admin_query = select(Admin).where(Admin.id == data.customer_id)
#                 admin_result = await db.execute(admin_query)
#                 admin = admin_result.scalar_one_or_none()

#                 if not admin:
#                     return ErrorResponse(
#                         status_code=404,
#                         message="Customer ID not found"
#                     )
#         # Convert string IDs to UUID
#         try:
#             customer_id_uuid = uuid.UUID(data.customer_id)
#             category_id_uuid = uuid.UUID(data.category_id)
#             subcategory_id_uuid = uuid.UUID(data.subcategory_id) if data.subcategory_id else None
#             assigned_to_uuid = uuid.UUID(data.assigned_to) if data.assigned_to else None
#         except ValueError as e:
#             logger.error(f"UUID conversion error: {str(e)}")
#             return ErrorResponse(
#                 status_code=400,
#                 message="Invalid UUID format for IDs"
#             )
        
#         # Convert status and priority to enums
#         try:
#             status_enum = IssueStatus[data.status.upper()]
#             priority_enum = IssuePriority[data.priority.upper()] if data.priority else None
#             user_type_enum = UserType[data.user_type.upper()]
#         except KeyError as e:
#             return ErrorResponse(
#                 status_code=400,
#                 message="Invalid status, priority, or user_type value"
#             )
        
#         # Verify category exists
#         logger.info(f"Verifying category exists: {category_id_uuid}")
#         category_query = select(IssueCategorys).where(IssueCategorys.id == category_id_uuid)
#         category_result = await db.execute(category_query)
#         category = category_result.scalar_one_or_none()

#         if not category:
#             return ErrorResponse(
#                 status_code=404,
#                 message="Issue category not found"
#             )
#         logger.info(f"Category found: {category.name} (code: {category.code})")

#         # Verify subcategory if provided
#         if subcategory_id_uuid:
#             logger.info(f"Verifying subcategory exists: {subcategory_id_uuid}")
#             subcategory_query = select(SubCategory).where(
#                 SubCategory.id == subcategory_id_uuid,
#                 SubCategory.category_id == category_id_uuid
#             )
#             subcategory_result = await db.execute(subcategory_query)
#             subcategory = subcategory_result.scalar_one_or_none()

#             if not subcategory:
#                 return ErrorResponse(
#                     status_code=404,
#                     message="Subcategory not found or does not belong to the specified category"
#                 )
#             logger.info(f"Subcategory found: {subcategory.name}")

#         # Generate case number
#         logger.info(f"Generating case number for category code: {category.code}")
#         try:
#             case_number = await generate_case_number(db, category.code)
#             logger.info(f"Generated case number: {case_number}")
#         except Exception as e:
#             logger.error(f"Error generating case number: {str(e)}")
#             return ErrorResponse(
#                 status_code=500,
#                 message=f"Error generating case number: {str(e)}"
#             )

#         # Handle file uploads
#         supporting_docs = []
#         if attachments:
#             logger.info(f"Processing {len(attachments)} file attachments")
#             settings = get_settings()
#             for file in attachments:
#                 if file is not None and file.filename:
#                     logger.info(f"Uploading file: {file.filename}")
#                     # Upload to S3
#                     file_url = upload_file_direct(file, settings.S3_IMAGES_FOLDER)
#                     if "error" in file_url or "Error" in file_url:
#                         logger.error(f"File upload error: {file_url.get('message', 'Unknown error')}")
#                         return ErrorResponse(
#                             status_code=500,
#                             message=f"Failed to upload file: {file_url.get('message', 'Unknown error')}"
#                         )
#                     supporting_docs.append(file_url["filename"])
#                     logger.info(f"File uploaded successfully: {file_url['filename']}")

#         # Get user name for history entry
#         account_id = token_data.get("account_id")
#         user_name = account_id
#         print("Initial user_name:", user_name)  # Debug log       
#         logger.info(f"Using account_id for history: {account_id}")

#         # Create new issue
#         logger.info("Creating new issue record")
#         db_issue = Issues(
#             customer_id=customer_id_uuid,
#             customer_name=data.customer_name,
#             case_number=case_number,
#             category_id=category_id_uuid,
#             subcategory_id=subcategory_id_uuid,
#             description=data.description,
#             supporting_documents=",".join(supporting_docs) if supporting_docs else None,
#             assigned_to=assigned_to_uuid,
#             status=status_enum,
#             priority=priority_enum or category.default_priority,
#             user_type=user_type_enum
#         )
        
#         # Add and commit to database
#         logger.info("Adding issue to database")
#         db.add(db_issue)
#         logger.info("Flushing database to get issue ID")
#         await db.flush()  # Flush to get the issue ID without committing
#         logger.info(f"Issue ID generated: {db_issue.id}")

#         # Create history entries
#         logger.info("Creating history entries")
#         history_entries = [
#             IssueHistory(
#                 issue_id=db_issue.id,
#                 event_code="ISSUE_CREATED",
#                 event_name="Issue created",
#                 user=data.customer_name if data.customer_name else user_name
#             )
#         ]

#         # Add history entry for assignment if assigned_to is provided
#         if assigned_to_uuid:
#             logger.info(f"Adding assignment history entry for: {assigned_to_uuid}")
#             history_entries.append(
#                 IssueHistory(
#                     issue_id=db_issue.id,
#                     event_code="ASSIGNED_TO_AGENT",
#                     event_name="Assigned to agent",
#                     user=data.customer_name if data.customer_name else user_name
#                 )
#             )

#         # Add history entries to database
#         logger.info("Adding history entries to database")
#         for entry in history_entries:
#             db.add(entry)

#         logger.info("Committing transaction")
#         await db.commit()
#         logger.info("Refreshing issue record")
#         await db.refresh(db_issue)

#         # Create response data
#         logger.info("Preparing response data")
#         response_data = {
#             "id": str(db_issue.id),
#             "customer_id": str(db_issue.customer_id),
#             "customer_name": db_issue.customer_name,
#             "case_number": db_issue.case_number,
#             "category_id": str(db_issue.category_id),
#             "subcategory_id": str(db_issue.subcategory_id) if db_issue.subcategory_id else None,
#             "description": db_issue.description,
#             "supporting_documents": db_issue.supporting_documents.split(",") if db_issue.supporting_documents else None,
#             "assigned_to": str(db_issue.assigned_to) if db_issue.assigned_to else None,
#             "status": db_issue.status.value if db_issue.status else None,
#             "priority": db_issue.priority.value if db_issue.priority else None,
#             "user_type": db_issue.user_type.value if db_issue.user_type else None,
#             "created_at": db_issue.created_at.isoformat() if db_issue.created_at else None,
#             "updated_at": db_issue.updated_at.isoformat() if db_issue.updated_at else None
#         }


#         logger.info("Issue creation completed successfully")
#         return StandardResponse(
#             status_code=200,
#             message="Issue created successfully",
#             data=response_data
#         )
#     except Exception as e:
#         logger.error(f"Error creating issue: {str(e)}", exc_info=True)
#         await db.rollback()
#         return ErrorResponse(
#             status_code=500,
#             message=f"Error creating issue: {str(e)}"
#         )

@router.post("/issues-create", response_model=StandardResponse)
async def create_issue(
    data: IssueCreateForm = Depends(),
    attachments: List[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
    token_data: dict = Depends(validate_token),
):
    try:
        # Validate token data
        account_id = token_data.get("account_id")
        if not account_id:
            return ErrorResponse(status_code=401, message="Account ID is required")

        role = token_data.get("user_role")
        if not role:
            return ErrorResponse(status_code=401, message="User role is required")

        # Validate customer ID
        if not data.customer_id:
            return ErrorResponse(status_code=400, message="Customer ID is required")

        customer_query = None

        # Build query based on role and user type
        if role in ["user", "artisan"]:
            # For regular users/artisans, verify by ID
            if data.user_type == UserType.USER:
                customer_query = select(Users).where(Users.id == data.customer_id)
            elif data.user_type == UserType.ARTISAN:
                customer_query = select(ServiceProvider).where(
                    ServiceProvider.id == data.customer_id)
        elif role in ["admin", "superadmin", "agent"]:
            # For admin roles, verify by phone number
            if data.user_type == UserType.ARTISAN:
                customer_query = select(ServiceProvider).where(
                    ServiceProvider.phone_number == data.customer_id)
            elif data.user_type == UserType.USER:
                customer_query = select(Users).where(Users.phone_number == data.customer_id)
            elif data.user_type in [UserType.ADMIN, UserType.SUPERADMIN, UserType.AGENT]:
                customer_query = select(Admin).where(Admin.phone_number == data.customer_id)
        else:
            return ErrorResponse(status_code=403, message="Invalid user role")

        if customer_query is None:
            return ErrorResponse(
                status_code=400,
                message="Invalid user type or customer ID format",
            )    

        customer_result = await db.execute(customer_query)
        customer = customer_result.scalar_one_or_none()

        if not customer:
            return ErrorResponse(status_code=404, message="Customer ID not found")

        category_query = select(IssueCategorys).where(
            IssueCategorys.id == data.category_id
        )
        category_result = await db.execute(category_query)
        category = category_result.scalar_one_or_none()

        if not category:
            return ErrorResponse(status_code=404, message="Issue category not found")

        subcategory_query = select(SubCategory).where(
            SubCategory.id == data.subcategory_id,
            SubCategory.category_id == data.category_id,
        )
        subcategory_result = await db.execute(subcategory_query)
        subcategory = subcategory_result.scalar_one_or_none()

        if not subcategory:
            return ErrorResponse(
                status_code=404,
                message="Subcategory not found or does not belong to the specified category",
            )

        case_number = await generate_case_number(db, category.code)

        supporting_docs = []
        if attachments:
            logger.info(f"Processing {len(attachments)} file attachments")
            settings = get_settings()
            for file in attachments:
                if file is not None and file.filename:
                    logger.info(f"Uploading file: {file.filename}")
                    # Upload to S3
                    file_url = upload_file_direct(file, settings.S3_IMAGES_FOLDER)
                    if "error" in file_url or "Error" in file_url:
                        logger.error(
                            f"File upload error: {file_url.get('message', 'Unknown error')}"
                        )
                        return ErrorResponse(
                            status_code=500,
                            message=f"Failed to upload file: {file_url.get('message', 'Unknown error')}",
                        )
                    supporting_docs.append(file_url["filename"])
                    logger.info(f"File uploaded successfully: {file_url['filename']}")

        db_issue = Issues(
            customer_id=customer.id,
            case_number=case_number,
            category_id=data.category_id,
            subcategory_id=data.subcategory_id,
            description=data.description,
            supporting_documents=",".join(supporting_docs) if supporting_docs else None,
            assigned_to=data.assigned_to,
            status=data.status,
            priority=data.priority or category.default_priority,
            user_type=data.user_type,
        )

        db.add(db_issue)
        await db.flush()

        history_entries = [
            IssueHistory(
                issue_id=db_issue.id,
                event_code="ISSUE_CREATED",
                event_name="Issue created",
                user=f"{customer.first_name or ''} {customer.last_name or ''}".strip(),
            )
        ]

        if data.assigned_to:
            history_entries.append(
                IssueHistory(
                    issue_id=db_issue.id,
                    event_code="ASSIGNED_TO_AGENT",
                    event_name="Assigned to agent",
                    user=f"{customer.first_name or ''} {customer.last_name or ''}".strip(),
                )
            )

        for entry in history_entries:
            db.add(entry)
        customer_name = f"{customer.first_name or ''} {customer.last_name or ''}".strip()
        await db.commit()
        await db.refresh(db_issue)

        # Get customer name based on user_type
        customer_name = "Unknown"
        if db_issue.user_type == UserType.USER:
            user_query = select(Users).where(Users.id == db_issue.customer_id)
            user_result = await db.execute(user_query)
            user = user_result.scalar_one_or_none()
            if user:
                customer_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
        elif db_issue.user_type == UserType.ARTISAN:
            provider_query = select(ServiceProvider).where(ServiceProvider.id == db_issue.customer_id)
            provider_result = await db.execute(provider_query)
            provider = provider_result.scalar_one_or_none()
            if provider:
                customer_name = f"{provider.first_name or ''} {provider.last_name or ''}".strip()
        else:
            admin_query = select(Admin).where(Admin.id == db_issue.customer_id)
            admin_result = await db.execute(admin_query)
            admin = admin_result.scalar_one_or_none()
            if admin:
                customer_name = f"{admin.first_name or ''} {admin.last_name or ''}".strip()

        # Create response data
        response_data = {
            "id": str(db_issue.id),
            "customer_id": str(db_issue.customer_id),
            "customer_name": customer_name,
            "case_number": db_issue.case_number,
            "category_id": str(db_issue.category_id),
            "subcategory_id": str(db_issue.subcategory_id),
            "description": db_issue.description,
            "supporting_documents": (
                db_issue.supporting_documents.split(",")
                if db_issue.supporting_documents
                else None
            ),
            "assigned_to": str(db_issue.assigned_to) if db_issue.assigned_to else None,
            "status": db_issue.status.value if db_issue.status else None,
            "priority": db_issue.priority.value if db_issue.priority else None,
            "user_type": db_issue.user_type.value if db_issue.user_type else None,
            "created_at": (
                db_issue.created_at.isoformat() if db_issue.created_at else None
            ),
            "updated_at": (
                db_issue.updated_at.isoformat() if db_issue.updated_at else None
            ),
        }

        if db_issue.user_type == UserType.USER:
            await send_push_notification(
                auth_token=token_data.get("auth_token"),
                title=f"Issue {db_issue.status.value if db_issue.status else None}",
                message=db_issue.description,
                type="user", 
                sender_id=db_issue.customer_id,
            )

        if db_issue.user_type == UserType.ARTISAN:
            await send_push_notification(
                auth_token=token_data.get("auth_token"),
                title=f"Issue {db_issue.status.value if db_issue.status else None}",
                message=db_issue.description,
                type="service_provider",
                sender_id=db_issue.customer_id,
            )
            
        return StandardResponse(
            status_code=200, message="Issue created successfully", data=response_data
        )

    except Exception as e:
        logger.error(f"Error creating issue: {str(e)}", exc_info=True)
        await db.rollback()
        return ErrorResponse(status_code=500, message=f"Error creating issue: {str(e)}")
 

# @router.post("/issues-list", response_model=StandardResponse)
# async def list_issues_post(
#     request: IssuesListRequest,
#     db: AsyncSession = Depends(get_db),
#     token_data: dict = Depends(validate_token)
# ):
#     """
#     List all issues with optional filters and pagination using POST method.
#     """
#     try:
#         if not token_data:
#             return ErrorResponse(status_code=401, message="Authorization header is required")
        
#         base_query = select(Issues).join(IssueCategorys, Issues.category_id == IssueCategorys.id).outerjoin(
#             SubCategory, Issues.subcategory_id == SubCategory.id
#         )

#         # Filter conditions
#         filters = []

#         if request.status and request.status.lower() not in {"null", ""}:
#             try:
#                 filters.append(Issues.status == IssueStatus(request.status))
#             except ValueError:
#                 pass  
            
#         if request.customer_id and request.customer_id.lower() not in {"null", ""}:
#             try:
#                 filters.append(Issues.customer_id == UUID(request.customer_id))
#             except ValueError:
#                 pass

#         if request.category_id and request.category_id.lower() not in {"null", ""}:
#             try:
#                 filters.append(Issues.category_id == UUID(request.category_id))
#             except ValueError:
#                 pass  

#         if request.priority and request.priority.lower() not in {"null", ""}:
#             try:
#                 filters.append(Issues.priority == IssuePriority(request.priority))
#             except ValueError:
                
#                 pass  

#         if request.user_type and request.user_type.lower() not in {"null", ""}:
#             try:
#                 filters.append(Issues.user_type == UserType(request.user_type))
#             except ValueError:
#                 pass  

#         # Apply filters to base query
#         if filters:
#             base_query = base_query.where(*filters)

#         # Get total count
#         total_count_query = select(func.count()).select_from(base_query.subquery())
#         total = (await db.execute(total_count_query)).scalar()

#         if request.limit == 0:
#             return StandardResponse(
#                 status_code=200,
#                 message="Issues list read successfully",
#                 data={"total": total, "data": []}
#             )

#         # Calculate pagination
#         total_pages = (total + request.limit - 1) // request.limit
#         current_page = (request.skip // request.limit) + 1

#         # Fetch paginated data
#         query = base_query.add_columns(
#             IssueCategorys.name.label("category_name"),
#             SubCategory.name.label("subcategory_name")
#         ).order_by(Issues.created_at.desc()).offset(request.skip).limit(request.limit)

#         result = await db.execute(query)
#         issues_data = result.all()

#         # Format response
#         formatted_issues = []
#         for issue, category_name, subcategory_name in issues_data:
#             # Parse supporting documents
#             supporting_docs = None
#             if issue.supporting_documents:
#                 try:
#                     supporting_docs = issue.supporting_documents.split(",")
#                 except json.JSONDecodeError:
#                     supporting_docs = None
            
#             # Get assigned agent details if available
#             assigned_to_details = None
#             if issue.assigned_to:
#                 try:
#                     logger.info(f"Fetching admin details for assigned_to: {issue.assigned_to}")
#                     admin_query = select(Admin).where(Admin.id == issue.assigned_to)
#                     admin_result = await db.execute(admin_query)
#                     admin = admin_result.scalar_one_or_none()
#                     if admin:
#                         first_name = admin.first_name if admin.first_name else ""
#                         last_name = admin.last_name if admin.last_name else ""
#                         full_name = f"{first_name} {last_name}".strip() or "Unknown"
#                         logger.info(f"Found admin: {full_name} for assigned_to: {issue.assigned_to}")
                        
#                         assigned_to_details = {
#                             "id": str(issue.assigned_to),
#                             "name": full_name,
#                             "email": admin.email,
#                             "phone_number": admin.phone_number,
#                             "role": admin.role,
#                             "status": admin.status,
#                             "first_name": first_name,
#                             "last_name": last_name,
#                             "gender": admin.gender,
#                         }
#                     else:
#                         logger.warning(f"No admin found for assigned_to: {issue.assigned_to}")
#                 except Exception as e:
#                     logger.error(f"Error fetching admin details for {issue.assigned_to}: {str(e)}", exc_info=True)
            
#             formatted_issues.append({
#                 "id": issue.id,
#                 "customer_id": issue.customer_id,
#                 "customer_name": issue.customer_name,
#                 "case_number": issue.case_number,
#                 "category_id": issue.category_id,
#                 "category_name": category_name,
#                 "subcategory_id": issue.subcategory_id,
#                 "subcategory_name": subcategory_name,
#                 "description": issue.description,
#                 "supporting_documents": supporting_docs,
#                 "assigned_to": str(issue.assigned_to) if issue.assigned_to else None,
#                 "assigned_to_details": assigned_to_details,
#                 "status": issue.status.value if issue.status else None,
#                 "priority": issue.priority.value if issue.priority else None,
#                 "user_type": issue.user_type.value if issue.user_type else None,
#                 "created_at": issue.created_at,
#                 "updated_at": issue.updated_at
#             })

#         return StandardResponse(
#             status_code=200,
#             message="Issues retrieved successfully",
#             data={
#                 "issues": formatted_issues,
#                 "pagination": {
#                     "current_page": current_page,
#                     "page_size": request.limit,
#                     "total_pages": total_pages,
#                     "total_items": total
#                 }
#             }
#         )

#     except Exception as e:
#         logger.error(f"Error listing issues: {str(e)}")
#         return ErrorResponse(status_code=500, message=f"Error listing issues: {str(e)}")

@router.post("/issues-list", response_model=StandardResponse)
async def issues_list(
    request: IssuesListRequest,
    db: AsyncSession = Depends(get_db),
    token_data: dict = Depends(validate_token),
):
    try:
        if not token_data:
            return ErrorResponse(status_code=401, message="Authorization header is required")
        account_id = token_data.get("account_id")
        role = token_data.get("user_role")
        if not account_id or not role:
            return ErrorResponse(status_code=401, message="Account ID and role are required")

        user_alias = aliased(Users)
        provider_alias = aliased(ServiceProvider)
        admin_alias = aliased(Admin)

        # Base query
        base_query = (
            select(
                Issues,
                IssueCategorys.name.label("category_name"),
                SubCategory.name.label("subcategory_name"),
                user_alias.first_name.label("user_first_name"),
                user_alias.last_name.label("user_last_name"),
                provider_alias.first_name.label("provider_first_name"),
                provider_alias.last_name.label("provider_last_name"),
                admin_alias.first_name.label("assigned_first_name"),
                admin_alias.last_name.label("assigned_last_name"),
            )
            .join(IssueCategorys, Issues.category_id == IssueCategorys.id, isouter=True)
            .join(SubCategory, Issues.subcategory_id == SubCategory.id, isouter=True)
            .join(user_alias, and_(Issues.customer_id == user_alias.id, Issues.user_type == UserType.USER), isouter=True)
            .join(provider_alias, and_(Issues.customer_id == provider_alias.id, Issues.user_type == UserType.ARTISAN), isouter=True)
            .join(admin_alias, Issues.assigned_to == admin_alias.id, isouter=True)
        )

        # Apply filters
        if request.q:
            search_term = f"%{request.q}%"
            base_query = base_query.filter(
                or_(
                    Issues.id.cast(String).ilike(search_term),
                    Issues.case_number.ilike(search_term),
                    func.concat(user_alias.first_name, ' ', user_alias.last_name).ilike(search_term),
                    func.concat(provider_alias.first_name, ' ', provider_alias.last_name).ilike(search_term),
                )
            )

        if request.customer_id:
            base_query = base_query.filter(Issues.customer_id == request.customer_id)

        if request.status:
            base_query = base_query.filter(Issues.status == request.status)

        if request.category_id:
            base_query = base_query.filter(Issues.category_id == request.category_id)

        if request.sub_category:
            base_query = base_query.filter(Issues.subcategory_id == request.sub_category)

        if request.priority:
            priority_enum = IssuePriority[request.priority] if isinstance(request.priority, str) else request.priority
            base_query = base_query.filter(Issues.priority == priority_enum)

        if request.user_type:
            base_query = base_query.filter(Issues.user_type == request.user_type)

        if request.assigned_to:
            base_query = base_query.filter(Issues.assigned_to == request.assigned_to)

        if role == 'agent':
            base_query = base_query.filter(Issues.assigned_to == account_id)

        # Total filtered count BEFORE pagination
        total_filtered_query = select(func.count()).select_from(base_query.subquery())
        total_filtered_count = (await db.execute(total_filtered_query)).scalar()

        # Total count (all issues)
        total_count = (await db.execute(select(func.count()).select_from(Issues))).scalar()

        # Pagination logic
        adjusted_skip = (request.skip - 1) * request.limit if request.skip > 0 else 0
        total_pages = (total_filtered_count + request.limit - 1) // request.limit
        current_page = (request.skip // request.limit) + 1

        paginated_query = (
            base_query.order_by(Issues.created_at.desc())
            .offset(adjusted_skip)
            .limit(request.limit)
        )

        results = await db.execute(paginated_query)
        issues_data = results.all()

        # Format issues
        formatted_issues = []
        for (
            issue,
            category_name,
            subcategory_name,
            user_first_name,
            user_last_name,
            provider_first_name,
            provider_last_name,
            assigned_first_name,
            assigned_last_name,
        ) in issues_data:

            customer_name = (
                f"{user_first_name or ''} {user_last_name or ''}".strip()
                if issue.user_type == UserType.USER else
                f"{provider_first_name or ''} {provider_last_name or ''}".strip()
                if issue.user_type == UserType.ARTISAN else "Unknown"
            )

            assigned_to_details = None
            if issue.assigned_to:
                full_name = f"{assigned_first_name or ''} {assigned_last_name or ''}".strip()
                assigned_to_details = {
                    "id": str(issue.assigned_to),
                    "name": full_name,
                    "first_name": assigned_first_name,
                    "last_name": assigned_last_name,
                }

            formatted_issues.append({
                "id": issue.id,
                "customer_id": issue.customer_id,
                "customer_name": customer_name,
                "case_number": issue.case_number,
                "category_id": issue.category_id,
                "category_name": category_name,
                "subcategory_id": issue.subcategory_id,
                "subcategory_name": subcategory_name,
                "description": issue.description,
                "supporting_documents": issue.supporting_documents.split(",") if issue.supporting_documents else None,
                "assigned_to": str(issue.assigned_to) if issue.assigned_to else None,
                "assigned_to_details": assigned_to_details,
                "status": issue.status.value if issue.status else None,
                "priority": issue.priority.value if issue.priority else None,
                "user_type": issue.user_type.value if issue.user_type else None,
                "created_at": issue.created_at,
                "updated_at": issue.updated_at,
            })

        # Unassigned count
        unassigned_query = select(func.count()).select_from(Issues).filter(Issues.assigned_to == None)
        unassigned_count = (await db.execute(unassigned_query)).scalar()

        # Status count
        status_counts_query = select(Issues.status, func.count()).group_by(Issues.status)
        status_counts = dict(
            (status.value if status else "UNKNOWN", count)
            for status, count in (await db.execute(status_counts_query)).all()
        )

        # Priority count
        if request.priority:
            priority_counts = {
                request.priority: (
                    await db.execute(select(func.count()).filter(Issues.priority == request.priority))
                ).scalar()
            }
        else:
            priority_counts_query = select(Issues.priority, func.count()).group_by(Issues.priority)
            priority_counts = dict(
                (priority.value if priority else "UNKNOWN", count)
                for priority, count in (await db.execute(priority_counts_query)).all()
            )
        # Unassigned count (no filters)
        unassigned_query = select(func.count()).select_from(Issues).filter(Issues.assigned_to == None)
        unassigned_count = (await db.execute(unassigned_query)).scalar()

        # Status count (all status)
        status_counts_query = select(Issues.status, func.count()).group_by(Issues.status)
        status_counts = dict((status.value if status else None, count) for status, count in (await db.execute(status_counts_query)).all())

        # Priority count (filtered if provided)
        if request.priority:
            priority_counts = {
                request.priority.value: (
                    (await db.execute(select(func.count()).filter(Issues.priority == request.priority))).scalar()
                )
            }
        else:
            priority_counts_query = select(Issues.priority, func.count()).group_by(Issues.priority)
            priority_counts = dict((priority.value if priority else None, count) for priority, count in (await db.execute(priority_counts_query)).all())


        return StandardResponse(
            status_code=200,
            message="Issues retrieved successfully",
            data={
                "issues": formatted_issues,
                "total_count": total_count,
                "total_filtered_count": total_filtered_count,
                "pagination": {
                    "current_page": current_page,
                    "page_size": request.limit,
                    "total_pages": total_pages,
                    "total_items": total_filtered_count,
                },
                "counts": {
                    "unassigned_count": unassigned_count,
                    "by_status": status_counts,
                    "by_priority": priority_counts,
                }
            },
        )

    except Exception as e:
        logger.error(f"Error listing issues: {str(e)}")
        return ErrorResponse(status_code=500, message=f"Error listing issues: {str(e)}")
 

@router.put("/issues-update/{issue_id}", response_model=StandardResponse)
async def update_issue(
    issue_id: UUID,
    data: IssueUpdate,
    db: AsyncSession = Depends(get_db),
    token_data: dict = Depends(validate_token)
):
    """
    Update an issue.
    """
    try:
        if not token_data:
            return ErrorResponse(status_code=401, message="Authorization header is required")
            
        account_id = token_data.get("account_id")
        if not account_id:
            return ErrorResponse(status_code=401, message="Invalid authentication: No account ID found")

        # Get existing issue
        query = select(Issues).where(Issues.id == issue_id)
        result = await db.execute(query)
        db_issue = result.scalar_one_or_none()

        if not db_issue:
            return ErrorResponse(
                status_code=404,
                message="Issue not found"
            )

        # Track status changes for history
        old_status = db_issue.status
        old_assigned_to = db_issue.assigned_to
            
        # Convert string IDs to UUID if provided
        update_data = {}
        
        if data.description is not None:
            update_data["description"] = data.description
            
        if data.assigned_to is not None:
            try:
                # Convert to string first to handle both string and UUID inputs
                assigned_to_str = str(data.assigned_to)
                logger.info(f"Converting assigned_to string: {assigned_to_str}")
                assigned_to_uuid = uuid.UUID(assigned_to_str)
                update_data["assigned_to"] = assigned_to_uuid
                logger.info(f"Will update assigned_to to: {assigned_to_uuid}")
            except ValueError as e:
                logger.error(f"Invalid UUID format for assigned_to: {e}")
                return ErrorResponse(
                    status_code=400,
                    message=f"Invalid UUID format for assigned_to: {e}"
                )
                
        if data.status is not None:
            update_data["status"] = data.status
            logger.info(f"Will update status to: {data.status}")
            
        if data.priority is not None:
            update_data["priority"] = data.priority
            logger.info(f"Will update priority to: {data.priority}")
            
        if data.resolution_notes is not None:
            update_data["resolution_notes"] = data.resolution_notes
            logger.info(f"Will update resolution_notes to: {data.resolution_notes}")
            
        if data.resolution_date is not None:
            update_data["resolution_date"] = data.resolution_date
            logger.info(f"Will update resolution_date to: {data.resolution_date}")

        # Apply updates
        for key, value in update_data.items():
            setattr(db_issue, key, value)
            
        # Update timestamps
        db_issue.updated_at = datetime.now()
        account_id = token_data.get("account_id")
        user_name = account_id
        print("Initial user_name:", user_name)  # Debug log
        admin_query = select(Admin).where(Admin.id == account_id)
        admin_result = await db.execute(admin_query)
        admin = admin_result.scalar_one_or_none()

        if admin:
            first_name = admin.first_name if admin.first_name else ""
            last_name = admin.last_name if admin.last_name else ""
            user_name = f"{first_name} {last_name}".strip() or user_name

        # Add history entry for status change
        if data.status and data.status != old_status:
            history_entry = IssueHistory(
                issue_id=issue_id,
                event_code=f"STATUS_CHANGED_TO_{data.status.upper()}",
                event_name=f"Status changed to {data.status.lower()}",
                user=user_name
            )
            db.add(history_entry)

        # Add history entry for assignment change
        if data.assigned_to and data.assigned_to != old_assigned_to:
            history_entry = IssueHistory(
                issue_id=issue_id,
                event_code="ASSIGNED_TO_AGENT",
                event_name="Assigned to Agent",
                user=user_name
            )
            db.add(history_entry)

        await db.commit()
        await db.refresh(db_issue)

        # Get customer name based on user_type
        customer_name = "Unknown"
        if db_issue.user_type == UserType.USER:
            user_query = select(Users).where(Users.id == db_issue.customer_id)
            user_result = await db.execute(user_query)
            user = user_result.scalar_one_or_none()
            if user:
                customer_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
        elif db_issue.user_type == UserType.ARTISAN:
            provider_query = select(ServiceProvider).where(ServiceProvider.id == db_issue.customer_id)
            provider_result = await db.execute(provider_query)
            provider = provider_result.scalar_one_or_none()
            if provider:
                customer_name = f"{provider.first_name or ''} {provider.last_name or ''}".strip()

        # Create response data
        response_data = {
            "id": str(db_issue.id),
            "customer_id": str(db_issue.customer_id),
            "customer_name": customer_name,
            "case_number": db_issue.case_number,
            "category_id": str(db_issue.category_id),
            "subcategory_id": str(db_issue.subcategory_id) if db_issue.subcategory_id else None,
            "description": db_issue.description,
            "assigned_to": str(db_issue.assigned_to) if db_issue.assigned_to else None,
            "status": db_issue.status.value if db_issue.status else None,
            "priority": db_issue.priority.value if db_issue.priority else None,
            "user_type": db_issue.user_type.value if db_issue.user_type else None,
            "created_at": db_issue.created_at.isoformat() if db_issue.created_at else None,
            "updated_at": db_issue.updated_at.isoformat() if db_issue.updated_at else None
        }

        return StandardResponse(
            status_code=200,
            message="Issue updated successfully",
            data=response_data
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"Error updating issue: {str(e)}")
        return ErrorResponse(
            status_code=500,
            message="Error updating issue",
            error=str(e)
        )



@router.get("/list-categories-subcategories", response_model=StandardResponse)
async def list_categories_with_subcategories(
    category_id: Optional[UUID] = None,
    category_name: Optional[str] = None,
    category_code: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    token_data: dict = Depends(validate_token)
):
    """
    List all categories with their subcategories and optional filters.
    """
    try:
        # Join categories with subcategories
        query = select(
            IssueCategorys,
            SubCategory.id.label('subcategory_id'),
            SubCategory.name.label('subcategory_name'),
            SubCategory.description.label('subcategory_description'),
            SubCategory.created_at.label('subcategory_created_at'),
            SubCategory.updated_at.label('subcategory_updated_at')
        ).outerjoin(
            SubCategory, IssueCategorys.id == SubCategory.category_id
        )
        
        # Apply filters
        if category_id:
            query = query.where(IssueCategorys.id == category_id)
        if category_name:
            query = query.where(IssueCategorys.name.ilike(f"%{category_name}%"))
        if category_code:
            query = query.where(IssueCategorys.code.ilike(f"%{category_code}%"))
            
        query = query.order_by(IssueCategorys.name, SubCategory.name)
        result = await db.execute(query)
        categories_data = result.all()

        # Format response data
        categories_dict = {}
        for (
            category, subcategory_id, subcategory_name, 
            subcategory_description, subcategory_created_at, 
            subcategory_updated_at
        ) in categories_data:
            if category.id not in categories_dict:
                categories_dict[category.id] = {
                    "id": category.id,
                    "name": category.name,
                    "code": category.code,
                    "description": category.description,
                    "default_priority": category.default_priority,
                    "created_at": category.created_at,
                    "updated_at": category.updated_at,
                    "subcategories": []
                }
            
            if subcategory_id:  # Only add if subcategory exists
                categories_dict[category.id]["subcategories"].append({
                    "id": subcategory_id,
                    "name": subcategory_name,
                    "description": subcategory_description,
                    "created_at": subcategory_created_at,
                    "updated_at": subcategory_updated_at
                })

        # Convert dictionary to list
        formatted_categories = list(categories_dict.values())

        return StandardResponse(
            status_code=200,
            message="Categories with subcategories retrieved successfully",
            data=formatted_categories
        )
    except Exception as e:
        logger.error(f"Error listing categories with subcategories: {str(e)}")
        return ErrorResponse(
            status_code=500,
            message=f"Error listing categories with subcategories: {str(e)}"
        )

async def generate_case_number(db: AsyncSession, category_code: str) -> str:
    """
    Generate a unique case number for a new issue in the format: {CATEGORY_CODE}-YYMM-COUNT
    where:
    - CATEGORY_CODE: The code from the category (e.g., JCACC)
    - YYMM: Last 2 digits of current year + 2-digit month
    - COUNT: Sequential number for the category in the current year and month
    """
    try:
        now = datetime.now()
        year_suffix = str(now.year)[-2:]  # Last 2 digits of year
        month_suffix = f"{now.month:02d}"  # 2-digit month

        # Get the count of issues for this category in the current year and month
        query = (
            select(Issues.case_number)
            .join(IssueCategorys, Issues.category_id == IssueCategorys.id)
            .where(
                IssueCategorys.code == category_code,
                func.extract("year", Issues.created_at) == now.year,
                func.extract("month", Issues.created_at) == now.month,
            )
            .order_by(desc(Issues.created_at))
            .limit(1)
        )

        result = await db.execute(query)
        last_case = result.scalar_one_or_none()

        if last_case:
            try:
                last_count = int(last_case.split("-")[-1])
            except ValueError:
                last_count = 0
        else:
            last_count = 0

        new_count = last_count + 1
        
        return f"{category_code}-{month_suffix}{year_suffix}-{new_count}"
    except Exception as e:
        logger.error(f"Error generating case number: {str(e)}")
        raise


@router.post("/issues-comment-create", response_model=StandardResponse)
async def add_dispute_comment(
    data: IssuesCommentCreate,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        if not Authorization:
            return ErrorResponse(status_code=401, message="Authorization header is required")
            
        print("Authorization header:", Authorization)  # Debug log
        resp = await get_id_header(Authorization)
        print("get_id_header response:", resp.status_code, resp.json() if hasattr(resp, 'json') else resp)  # Debug log
        
        if not hasattr(resp, 'status_code'):
            return ErrorResponse(status_code=401, message="Invalid authentication response")
            
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            print("Account ID:", account_id)  # Debug log
            if not account_id:
                return ErrorResponse(status_code=401, message="Invalid authorization: No account ID found")
        else:
            error_msg = resp.json() if hasattr(resp, 'json') else "Authentication failed"
            return ErrorResponse(status_code=401, message=f"Unauthorized: {error_msg}")

        # Find the issue using async query
        issue_query = select(Issues).where(Issues.id == data.issue_id)
        issue_result = await db.execute(issue_query)
        issue = issue_result.scalar_one_or_none()
        
        if not issue:
            return ErrorResponse(status_code=404, message="Issue not found")

        # Get user name for the comment
        user_name = account_id
        print("Initial user_name:", user_name)  # Debug log

        # Directly query the Admin table
        admin_query = select(Admin).where(Admin.id == account_id)
        admin_result = await db.execute(admin_query)
        admin = admin_result.scalar_one_or_none()

        if admin:
            first_name = admin.first_name if admin.first_name else ""
            last_name = admin.last_name if admin.last_name else ""
            user_name = f"{first_name} {last_name}".strip() or user_name
 
        # Create comment with author_name
        comment = IssueComments(
            id=uuid.uuid4(),
            issue_id=data.issue_id, 
            author_id=account_id,
            author_name=user_name,
            comment=data.comment
        )
        db.add(comment)

        # # Create history entry for comment
        # history_entry = IssueHistory(
        #     issue_id=issue.id,  
        #     event_code="COMMENT_ADDED",
        #     event_name="Comment added",
        #     user=user_name
        # )
        # db.add(history_entry)

        # Handle status change if provided
        old_status = issue.status
        if data.status and data.status != old_status:
            issue.status = data.status

            # Add history entry for status change
            event_code = f"STATUS_CHANGED_TO_{data.status.value.upper()}"
            status_history_entry = IssueHistory(
                issue_id=issue.id,  
                event_code=event_code,
                event_name=f"Status changed to {data.status.value.lower()}",
                user=user_name
            )
            db.add(status_history_entry)

            # If status is RESOLVED, set resolution date
            if data.status == IssueStatus.RESOLVED:
                issue.resolution_date = datetime.now()
                if data.resolution_notes:
                    issue.resolution_notes = data.resolution_notes

        # Update issue last updated timestamp
        issue.updated_at = datetime.now()

        await db.commit()
        await db.refresh(comment)
        await db.refresh(issue)  # Refresh the issue object to ensure all attributes are loaded

        # Prepare response
        comment_dict = {
            "id": str(comment.id),
            "issue_id": str(comment.issue_id),  
            "author_id": str(comment.author_id),
            "author_name": comment.author_name,
            "comment": comment.comment,
            "date": comment.date.isoformat() if comment.date else None
        }

        # Include status change in response if applicable
        if data.status and data.status != old_status:
            comment_dict["status_changed"] = {
                "from": old_status.value if old_status else None,
                "to": data.status.value,
            }
            
        try:
            if issue.user_type == UserType.USER:
                await send_push_notification(
                    auth_token=Authorization,
                    title=f"Issue {issue.status.value}",
                    message=data.comment,
                    type="user",
                    sender_id=issue.customer_id,
                )

            elif issue.user_type == UserType.ARTISAN:
                await send_push_notification(
                    auth_token=Authorization, 
                    title=f"Issue {issue.status.value}",
                    message=data.comment,
                    type="service_provider",
                    sender_id=issue.customer_id,
                )
        except Exception as notification_error:
            logger.error(f"Error sending notification: {str(notification_error)}")
            # Continue with the response even if notification fails
            
        return StandardResponse(
            status_code=200,
            message="Comment added successfully",
            data=comment_dict
        )
    except Exception as e:
        await db.rollback()
        logger.error(f"Error adding comment: {str(e)}", exc_info=True)
        return ErrorResponse(status_code=500, message=f"Error adding comment: {str(e)}")
    

@router.get("/issue-read/{id}", response_model=StandardResponse)
async def read_issue(
    id: str,
    db: AsyncSession = Depends(get_db),
    token_data: dict = Depends(validate_token)
):
    """
    Get detailed information about a specific issue including its comments and history.
    """
    try:
        if not token_data:
            return ErrorResponse(status_code=401, message="Authorization header is required")
            
        account_id = token_data.get("account_id")
        if not account_id:
            return ErrorResponse(status_code=401, message="Invalid authentication: No account ID found")

        # Validate UUID format
        try:
            issue_uuid = UUID(str(id))
        except ValueError:
            return ErrorResponse(
                status_code=400,
                message=f"Invalid issue ID format: '{id}'. Please provide a valid UUID."
            )

        # Get issue details with category and subcategory names
        issue_query = (
            select(Issues, IssueCategorys.name.label('category_name'), SubCategory.name.label('subcategory_name'))
            .outerjoin(IssueCategorys, Issues.category_id == IssueCategorys.id)
            .outerjoin(SubCategory, Issues.subcategory_id == SubCategory.id)
            .where(Issues.id == issue_uuid)
        )
        issue_result = await db.execute(issue_query)
        issue_row = issue_result.first()

        if not issue_row:
            return ErrorResponse(status_code=404, message="Issue not found")

        issue = issue_row[0]
        category_name = issue_row[1]
        subcategory_name = issue_row[2]

        # Get customer name based on user_type
        customer_name = "Unknown"
        if issue.user_type == UserType.USER:
            user_query = select(Users).where(Users.id == issue.customer_id)
            user_result = await db.execute(user_query)
            user = user_result.scalar_one_or_none()
            if user:
                customer_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
        elif issue.user_type == UserType.ARTISAN:
            provider_query = select(ServiceProvider).where(ServiceProvider.id == issue.customer_id)
            provider_result = await db.execute(provider_query)
            provider = provider_result.scalar_one_or_none()
            if provider:
                customer_name = f"{provider.first_name or ''} {provider.last_name or ''}".strip()

        # Get comments
        comments_query = select(IssueComments).where(IssueComments.issue_id == issue_uuid).order_by(IssueComments.date.desc())
        comments_result = await db.execute(comments_query)
        comments = comments_result.scalars().all()

        # Get history
        history_query = select(IssueHistory).where(IssueHistory.issue_id == issue_uuid).order_by(IssueHistory.date.desc())
        history_result = await db.execute(history_query)
        history = history_result.scalars().all()

        # Create issue dictionary
        issue_dict = {
            "id": str(issue.id),
            "case_number": issue.case_number,
            "customer_id": str(issue.customer_id),
            "customer_name": customer_name,
            "category_id": str(issue.category_id),
            "category_name": category_name,
            "subcategory_id": str(issue.subcategory_id) if issue.subcategory_id else None,
            "subcategory_name": subcategory_name,
            "description": issue.description,
            "supporting_documents": issue.supporting_documents.split(",") if issue.supporting_documents else None,
            "status": issue.status.value if issue.status else None,
            "priority": issue.priority.value if issue.priority else None,
            "user_type": issue.user_type.value if issue.user_type else None,
            "assigned_to": str(issue.assigned_to) if issue.assigned_to else None,
            "resolution_date": issue.resolution_date.isoformat() if issue.resolution_date else None,
            "resolution_note": issue.resolution_notes,
            "created_at": issue.created_at.isoformat() if issue.created_at else None,
            "updated_at": issue.updated_at.isoformat() if issue.updated_at else None
        }

        # Format comments
        comments_list = []
        for comment in comments:
            comment_dict = {
                "id": str(comment.id),
                "author_id": comment.author_id,
                "author_name": comment.author_name,
                "comment": comment.comment,
                "date": comment.date.isoformat() if comment.date else None
            }
            comments_list.append(comment_dict)

        # Format history
        history_list = []
        for entry in history:
            history_dict = {
                "id": str(entry.id),
                "event_code": entry.event_code,
                "event_name": entry.event_name,
                "user": entry.user,
                "date": entry.date.isoformat() if entry.date else None
            }
            history_list.append(history_dict)
        
        for comment in comments:
            comment_history = {
                "id": str(comment.id),
                "event_code": "COMMENT_ADDED",
                "event_name": "Comment Added",
                "comment": comment.comment,
                "author_name": comment.author_name,
                "date": comment.date.isoformat() if comment.date else None
            }
            history_list.append(comment_history)

        # Sort history by date in descending order (most recent first)
        history_list.sort(key=lambda x: x["date"] if x["date"] else "", reverse=True)
        
        # Create response data
        response_data = {
            "issue": issue_dict,
            "comments": comments_list,
            "history": history_list
        }

        return StandardResponse(
            status_code=200,
            message="Issue details retrieved successfully",
            data=response_data
        )

    except Exception as e:
        logger.error(f"Error reading issue: {str(e)}")
        return ErrorResponse(
            status_code=500,
            message=f"Error reading issue: {str(e)}"
        )
 






# async def validate_token(Authorization: str = Header(...)):
#     """Dependency function to validate token"""
#     try:
#         resp = await get_id_header(Authorization)
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             account_id = resp_json.get("account_id")
#             user_role = resp_json.get("user_role")
#             auth_token = resp_json.get("auth_token")
#             return {"account_id": account_id,"user_role":user_role ,"auth_token": auth_token}
#         else:
#             raise HTTPException(
#                 status_code=status.HTTP_401_UNAUTHORIZED,
#                 detail="Unauthorized"
#             )
#     except Exception as e:
#         raise HTTPException(
#             status_code=status.HTTP_401_UNAUTHORIZED,
#             detail=str(e)
#         )

# @router.post("/create-categories-issues", response_model=StandardResponse)
# async def create_issue_category(
#     category: IssueCategoryCreate,
#     db: AsyncSession = Depends(get_db),
#     token_data: dict = Depends(validate_token)
# ):
#     """
#     Create a new issue category with optional subcategories.
#     """
#     async with db.begin() as transaction:
#         try:
#             # Check if category with same code already exists
#             stmt = select(IssueCategorys).where(IssueCategorys.code == category.code)
#             result = await db.execute(stmt)
#             existing_category = result.scalar_one_or_none()
            
#             if existing_category:
#                 return ErrorResponse(
#                     status_code=400,
#                     message=f"Category with code '{category.code}' already exists"
#                 )

#             # Create new category
#             db_category = IssueCategorys(
#                 name=category.name,
#                 code=category.code,
#                 description=category.description,
#                 default_priority=category.default_priority
#             )
#             db.add(db_category)
#             await db.flush()

#             # Create subcategories if provided
#             subcategories_data = []
#             if category.subcategories:
#                 for subcat in category.subcategories:
#                     db_subcategory = SubCategory(
#                         name=subcat.name,
#                         description=subcat.description,
#                         category_id=db_category.id
#                     )
#                     db.add(db_subcategory)
#                     subcategories_data.append({
#                         "name": subcat.name,
#                         "description": subcat.description
#                     })

#             # Commit will be handled by the transaction context manager
#             await db.flush()

#             # Prepare response data
#             response_data = {
#                 "id": str(db_category.id),
#                 "name": db_category.name,
#                 "code": db_category.code,
#                 "description": db_category.description,
#                 "default_priority": db_category.default_priority,
#                 "created_at": db_category.created_at,
#                 "updated_at": db_category.updated_at,
#                 "subcategories": []
#             }

#             # Query the created subcategories
#             if category.subcategories:
#                 stmt = select(SubCategory).where(SubCategory.category_id == db_category.id)
#                 result = await db.execute(stmt)
#                 subcategories = result.scalars().all()
                
#                 response_data["subcategories"] = [
#                     {
#                         "id": str(subcat.id),
#                         "name": subcat.name,
#                         "description": subcat.description,
#                         "created_at": subcat.created_at,
#                         "updated_at": subcat.updated_at
#                     }
#                     for subcat in subcategories
#                 ]

#             return StandardResponse(
#                 status_code=200,
#                 message="Issue category and subcategories created successfully",
#                 data=response_data
#             )

#         except Exception as e:
#             logger.error(f"Error creating issue category: {str(e)}")
#             # No need to explicitly rollback as it's handled by the transaction context
#             raise HTTPException(
#                 status_code=500,
#                 detail=f"Error creating issue category: {str(e)}"
#             )

# @router.put("/update-categories-issues/{category_id}", response_model=StandardResponse)
# async def update_issue_category(
#     category_id: UUID,
#     category_update: IssueCategoryUpdate,
#     db: AsyncSession = Depends(get_db),
#     token_data: dict = Depends(validate_token)
# ):
#     """
#     Update an issue category and its subcategories.
#     """
#     try:
#         # Get existing category
#         stmt = select(IssueCategorys).where(IssueCategorys.id == category_id)
#         result = await db.execute(stmt)
#         db_category = result.scalar_one_or_none()

#         if not db_category:
#             return ErrorResponse(
#                 status_code=404,
#                 message=f"Issue category with id '{category_id}' not found"
#             )

#         # Check if new code conflicts with existing category
#         if category_update.code and category_update.code != db_category.code:
#             code_stmt = select(IssueCategorys).where(
#                 and_(
#                     IssueCategorys.code == category_update.code,
#                     IssueCategorys.id != category_id
#                 )
#             )
#             code_result = await db.execute(code_stmt)
#             if code_result.scalar_one_or_none():
#                 return ErrorResponse(
#                     status_code=400,
#                     message=f"Category with code '{category_update.code}' already exists"
#                 )

#         # Update category fields
#         update_data = category_update.dict(exclude={'subcategories'}, exclude_unset=True)
#         for field, value in update_data.items():
#             setattr(db_category, field, value)

#         # Handle subcategories if provided
#         if category_update.subcategories is not None:
#             # Delete existing subcategories
#             delete_stmt = delete(SubCategory).where(SubCategory.category_id == category_id)
#             await db.execute(delete_stmt)

#             # Create new subcategories
#             for subcat in category_update.subcategories:
#                 db_subcategory = SubCategory(
#                     name=subcat.name,
#                     description=subcat.description,
#                     category_id=category_id
#                 )
#                 db.add(db_subcategory)

#         await db.commit()

#         # Query the updated category with its subcategories
#         stmt = select(IssueCategorys).where(IssueCategorys.id == category_id)
#         result = await db.execute(stmt)
#         updated_category = result.scalar_one()

#         # Get subcategories in a separate query
#         subcat_stmt = select(SubCategory).where(SubCategory.category_id == category_id)
#         subcat_result = await db.execute(subcat_stmt)
#         subcategories = subcat_result.scalars().all()

#         response_data = {
#             "id": str(updated_category.id),
#             "name": updated_category.name,
#             "code": updated_category.code,
#             "description": updated_category.description,
#             "default_priority": updated_category.default_priority,
#             "created_at": updated_category.created_at,
#             "updated_at": updated_category.updated_at,
#             "subcategories": [
#                 {
#                     "id": str(subcat.id),
#                     "name": subcat.name,
#                     "description": subcat.description,
#                     "created_at": subcat.created_at,
#                     "updated_at": subcat.updated_at
#                 }
#                 for subcat in subcategories
#             ]
#         }

#         return StandardResponse(
#             status_code=200,
#             message="Issue category and subcategories updated successfully",
#             data=response_data
#         )

#     except Exception as e:
#         await db.rollback()
#         logger.error(f"Error updating issue category: {str(e)}")
#         return ErrorResponse(
#             status_code=500,
#             message=f"Error updating issue category: {str(e)}"
#         )

# @router.delete("/delete-categories-issues/{category_id}", response_model=StandardResponse)
# async def delete_issue_category(
#     category_id: UUID,
#     db: AsyncSession = Depends(get_db),
#     token_data: dict = Depends(validate_token)
# ):
#     """
#     Delete an issue category and all its associated subcategories.
#     """
#     try:
#         # Get existing category with issues count
#         query = select(IssueCategorys).where(IssueCategorys.id == category_id)
#         result = await db.execute(query)
#         db_category = result.scalar_one_or_none()

#         if not db_category:
#             return ErrorResponse(
#                 status_code=404,
#                 message="Issue category not found"
#             )

#         # Check if category has associated issues
#         issues_query = select(func.count()).select_from(Issues).where(Issues.category_id == category_id)
#         issues_result = await db.execute(issues_query)
#         issues_count = issues_result.scalar()

#         if issues_count > 0:
#             return ErrorResponse(
#                 status_code=400,
#                 message="Cannot delete category with associated issues"
#             )

#         # Delete category (subcategories will be deleted automatically due to cascade)
#         await db.delete(db_category)
#         await db.commit()

#         return StandardResponse(
#             status_code=200,
#             message=f"Category '{db_category.name}' and its subcategories deleted successfully"
#         )
#     except Exception as e:
#         await db.rollback()
#         logger.error(f"Error deleting issue category: {str(e)}")
#         return ErrorResponse(
#             status_code=500,
#             message=f"Error deleting issue category: {str(e)}"
#         )

# def generate_case_number(db: Session, category_code: str) -> str:
#     """
#     Generate a unique case number for a new issue in the format: {CATEGORY_CODE}-YYMM-COUNT
#     where:
#     - CATEGORY_CODE: The code from the category (e.g., JCACC)
#     - YYMM: Last 2 digits of current year + 2-digit month
#     - COUNT: Sequential number for the category in the current year and month
#     """
#     try:
#         now = datetime.now()
#         year_suffix = str(now.year)[-2:]  # Last 2 digits of year
#         month_suffix = f"{now.month:02d}"  # 2-digit month

#         # Get the count of issues for this category in the current year and month
#         query = (
#             select(Issues.case_number)
#             .join(IssueCategorys, Issues.category_id == IssueCategorys.id)
#             .where(
#                 IssueCategorys.code == category_code,
#                 func.extract("year", Issues.created_at) == now.year,
#                 func.extract("month", Issues.created_at) == now.month,
#             )
#             .order_by(desc(Issues.created_at))
#             .limit(1)
#         )

#         result = db.execute(query)
#         last_case = result.scalar_one_or_none()

#         if last_case:
#             try:
#                 last_count = int(last_case.split("-")[-1])
#             except ValueError:
#                 last_count = 0
#         else:
#             last_count = 0

#         new_count = last_count + 1
        
#         return f"{category_code}-{month_suffix}{year_suffix}-{new_count}"
#     except Exception as e:
#         logger.error(f"Error generating case number: {str(e)}")
#         raise

# @router.post("/issues-create", response_model=StandardResponse)
# async def create_issue(
#     data: IssueCreateForm = Depends(),
#     attachments: List[UploadFile] = File(None),
#     db: AsyncSession = Depends(get_db),
#     token_data: dict = Depends(validate_token),
# ):
#     try:
#         # Validate token data
#         account_id = token_data.get("account_id")
#         if not account_id:
#             return ErrorResponse(status_code=401, message="Account ID is required")

#         role = token_data.get("user_role")
#         if not role:
#             return ErrorResponse(status_code=401, message="User role is required")

#         # Validate customer ID
#         if not data.customer_id:
#             return ErrorResponse(status_code=400, message="Customer ID is required")

#         customer_query = None

#         # Build query based on role and user type
#         if role in ["user", "artisan"]:
#             # For regular users/artisans, verify by ID
#             if data.user_type == UserType.USER:
#                 customer_query = select(Users).where(Users.id == data.customer_id)
#             elif data.user_type == UserType.ARTISAN:
#                 customer_query = select(ServiceProvider).where(
#                     ServiceProvider.id == data.customer_id)
#         elif role in ["admin", "superadmin", "agent"]:
#             # For admin roles, verify by phone number
#             if data.user_type == UserType.ARTISAN:
#                 customer_query = select(ServiceProvider).where(
#                     ServiceProvider.phone_number == data.customer_id)
#             elif data.user_type == UserType.USER:
#                 customer_query = select(Users).where(Users.phone_number == data.customer_id)
#             elif data.user_type in [UserType.ADMIN, UserType.SUPERADMIN, UserType.AGENT]:
#                 customer_query = select(Admin).where(Admin.phone_number == data.customer_id)
#         else:
#             return ErrorResponse(status_code=403, message="Invalid user role")

#         if customer_query is None:
#             return ErrorResponse(
#                 status_code=400,
#                 message="Invalid user type or customer ID format",
#             )    

#         customer_result = db.execute(customer_query)
#         customer = customer_result.scalar_one_or_none()

#         if not customer:
#             return ErrorResponse(status_code=404, message="Customer ID not found")

#         category_query = select(IssueCategorys).where(
#             IssueCategorys.id == data.category_id
#         )
#         category_result = db.execute(category_query)
#         category = category_result.scalar_one_or_none()

#         if not category:
#             return ErrorResponse(status_code=404, message="Issue category not found")

#         subcategory_query = select(SubCategory).where(
#             SubCategory.id == data.subcategory_id,
#             SubCategory.category_id == data.category_id,
#         )
#         subcategory_result = db.execute(subcategory_query)
#         subcategory = subcategory_result.scalar_one_or_none()

#         if not subcategory:
#             return ErrorResponse(
#                 status_code=404,
#                 message="Subcategory not found or does not belong to the specified category",
#             )

#         # Generate case number synchronously
#         case_number = generate_case_number(db, category.code)

#         supporting_docs = []
#         if attachments:
#             logger.info(f"Processing {len(attachments)} file attachments")
#             settings = get_settings()
#             for file in attachments:
#                 if file is not None and file.filename:
#                     logger.info(f"Uploading file: {file.filename}")
#                     # Upload to S3
#                     file_url = upload_file_direct(file, settings.S3_IMAGES_FOLDER)
#                     if "error" in file_url or "Error" in file_url:
#                         logger.error(
#                             f"File upload error: {file_url.get('message', 'Unknown error')}"
#                         )
#                         return ErrorResponse(
#                             status_code=500,
#                             message=f"Failed to upload file: {file_url.get('message', 'Unknown error')}",
#                         )
#                     supporting_docs.append(file_url["filename"])
#                     logger.info(f"File uploaded successfully: {file_url['filename']}")

#         db_issue = Issues(
#             customer_id=customer.id,
#             case_number=case_number,
#             category_id=data.category_id,
#             subcategory_id=data.subcategory_id,
#             description=data.description,
#             supporting_documents=",".join(supporting_docs) if supporting_docs else None,
#             assigned_to=data.assigned_to,
#             status=data.status,
#             priority=data.priority or category.default_priority,
#             user_type=data.user_type,
#         )

#         db.add(db_issue)
#         db.flush()

#         history_entries = [
#             IssueHistory(
#                 issue_id=db_issue.id,
#                 event_code="ISSUE_CREATED",
#                 event_name="Issue created",
#                 user=f"{customer.first_name or ''} {customer.last_name or ''}".strip(),
#             )
#         ]

#         if data.assigned_to:
#             history_entries.append(
#                 IssueHistory(
#                     issue_id=db_issue.id,
#                     event_code="ASSIGNED_TO_AGENT",
#                     event_name="Assigned to agent",
#                     user=f"{customer.first_name or ''} {customer.last_name or ''}".strip(),
#                 )
#             )

#         for entry in history_entries:
#             db.add(entry)
#         customer_name = f"{customer.first_name or ''} {customer.last_name or ''}".strip()
#         db.commit()
#         db.refresh(db_issue)

#         # Get customer name based on user_type
#         customer_name = "Unknown"
#         if db_issue.user_type == UserType.USER:
#             user_query = select(Users).where(Users.id == db_issue.customer_id)
#             user_result = db.execute(user_query)
#             user = user_result.scalar_one_or_none()
#             if user:
#                 customer_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
#         elif db_issue.user_type == UserType.ARTISAN:
#             provider_query = select(ServiceProvider).where(ServiceProvider.id == db_issue.customer_id)
#             provider_result = db.execute(provider_query)
#             provider = provider_result.scalar_one_or_none()
#             if provider:
#                 customer_name = f"{provider.first_name or ''} {provider.last_name or ''}".strip()
#         else:
#             admin_query = select(Admin).where(Admin.id == db_issue.customer_id)
#             admin_result = db.execute(admin_query)
#             admin = admin_result.scalar_one_or_none()
#             if admin:
#                 customer_name = f"{admin.first_name or ''} {admin.last_name or ''}".strip()

#         # Create response data
#         response_data = {
#             "id": str(db_issue.id),
#             "customer_id": str(db_issue.customer_id),
#             "customer_name": customer_name,
#             "case_number": db_issue.case_number,
#             "category_id": str(db_issue.category_id),
#             "subcategory_id": str(db_issue.subcategory_id),
#             "description": db_issue.description,
#             "supporting_documents": (
#                 db_issue.supporting_documents.split(",")
#                 if db_issue.supporting_documents
#                 else None
#             ),
#             "assigned_to": str(db_issue.assigned_to) if db_issue.assigned_to else None,
#             "status": db_issue.status.value if db_issue.status else None,
#             "priority": db_issue.priority.value if db_issue.priority else None,
#             "user_type": db_issue.user_type.value if db_issue.user_type else None,
#             "created_at": (
#                 db_issue.created_at.isoformat() if db_issue.created_at else None
#             ),
#             "updated_at": (
#                 db_issue.updated_at.isoformat() if db_issue.updated_at else None
#             ),
#         }

#         if db_issue.user_type == UserType.USER:
#             send_push_notification(
#                 auth_token=token_data.get("auth_token"),
#                 title=f"Issue {db_issue.status.value if db_issue.status else None}",
#                 message=db_issue.description,
#                 type="user", 
#                 sender_id=db_issue.customer_id,
#             )

#         if db_issue.user_type == UserType.ARTISAN:
#             send_push_notification(
#                 auth_token=token_data.get("auth_token"),
#                 title=f"Issue {db_issue.status.value if db_issue.status else None}",
#                 message=db_issue.description,
#                 type="service_provider",
#                 sender_id=db_issue.customer_id,
#             )
            
#         return StandardResponse(
#             status_code=200, message="Issue created successfully", data=response_data
#         )

#     except Exception as e:
#         logger.error(f"Error creating issue: {str(e)}", exc_info=True)
#         db.rollback()
#         return ErrorResponse(status_code=500, message=f"Error creating issue: {str(e)}")
 

# @router.post("/issues-list", response_model=StandardResponse)
# async def issues_list(
#     request: IssuesListRequest,
#     db: AsyncSession = Depends(get_db),
#     token_data: dict = Depends(validate_token),
# ):
#     """
#     List all issues with optional filters and pagination using POST method.
#     """
#     try:
#         if not token_data:
#             return ErrorResponse(status_code=401, message="Authorization header is required")
#         account_id = token_data.get("account_id")
#         role = token_data.get("user_role")
#         if not account_id or not role:
#             return ErrorResponse(status_code=401, message="Account ID and role are required")
#         user_alias = aliased(Users)
#         provider_alias = aliased(ServiceProvider)
#         admin_alias = aliased(Admin)
#         query = (
#             select(
#                 Issues,
#                 IssueCategorys.name.label("category_name"),
#                 SubCategory.name.label("subcategory_name"),
#                 user_alias.first_name.label("user_first_name"),
#                 user_alias.last_name.label("user_last_name"),
#                 provider_alias.first_name.label("provider_first_name"),
#                 provider_alias.last_name.label("provider_last_name"),
#                 admin_alias.first_name.label("assigned_first_name"),
#                 admin_alias.last_name.label("assigned_last_name"),
#             )
#             .join(IssueCategorys, Issues.category_id == IssueCategorys.id, isouter=True)
#             .join(SubCategory, Issues.subcategory_id == SubCategory.id, isouter=True)
#             .join(
#                 user_alias,
#                 and_(Issues.customer_id == user_alias.id, Issues.user_type == UserType.USER),
#                 isouter=True,
#             )
#             .join(
#                 provider_alias,
#                 and_(
#                     Issues.customer_id == provider_alias.id,
#                     Issues.user_type == UserType.ARTISAN,
#                 ),
#                 isouter=True,
#             )
#             .join(admin_alias, Issues.assigned_to == admin_alias.id, isouter=True)
#         )

#         if request.q:
#             query = query.filter(Issues.description.ilike(f"%{request.q}%"))

#         if request.customer_id:
#             query = query.filter(Issues.customer_id == request.customer_id)

#         if request.status:
#             query = query.filter(Issues.status == request.status)

#         if request.category_id:
#             query = query.filter(Issues.category_id == request.category_id)

#         if request.priority:
#             query = query.filter(Issues.priority == request.priority)

#         if request.user_type:
#             query = query.filter(Issues.user_type == request.user_type)

#         if request.assigned_to:
#             query = query.filter(Issues.assigned_to == request.assigned_to) 
        
#         if account_id and role == 'agent':
#             query = query.filter(Issues.assigned_to == account_id)

#         # Get total count
#         count_query = select(func.count()).select_from(query.subquery())
#         result = db.execute(count_query)
#         total = result.scalar_one()

#         # Calculate pagination
#         total_pages = (total + request.limit - 1) // request.limit
#         current_page = (request.skip // request.limit) + 1

#         # Fetch paginated data
#         adjusted_skip = (request.skip - 1) * request.limit if request.skip > 0 else 0
#         query = (
#             query
#             .order_by(Issues.created_at.desc())
#             .offset(adjusted_skip)
#             .limit(request.limit)
#         )

#         result = db.execute(query)
#         issues_data = result.all()

#         # Format response
#         formatted_issues = []
#         for (
#             issue,
#             category_name,
#             subcategory_name,
#             user_first_name,
#             user_last_name,
#             provider_first_name,
#             provider_last_name,
#             assigned_first_name,
#             assigned_last_name,
#         ) in issues_data:
#             # Determine customer name based on user type
#             if issue.user_type == UserType.USER:
#                 customer_name = f"{user_first_name or ''} {user_last_name or ''}".strip()
#             elif issue.user_type == UserType.ARTISAN:
#                 customer_name = f"{provider_first_name or ''} {provider_last_name or ''}".strip()
#             else:
#                 customer_name = "Unknown"

#             # Assigned to details (using joined admin)
#             assigned_to_details = None
#             if issue.assigned_to:
#                 full_name = f"{assigned_first_name or ''} {assigned_last_name or ''}".strip()
#                 assigned_to_details = {
#                     "id": str(issue.assigned_to),
#                     "name": full_name or "Unknown",
#                     "first_name": assigned_first_name,
#                     "last_name": assigned_last_name,
#                 }

#             # Parse supporting documents
#             supporting_docs = (
#                 issue.supporting_documents.split(",")
#                 if issue.supporting_documents else None
#             )

#             formatted_issues.append(
#                 {
#                     "id": str(issue.id),
#                     "customer_id": str(issue.customer_id),
#                     "customer_name": customer_name,
#                     "case_number": issue.case_number,
#                     "category_id": str(issue.category_id),
#                     "category_name": category_name,
#                     "subcategory_id": str(issue.subcategory_id) if issue.subcategory_id else None,
#                     "subcategory_name": subcategory_name,
#                     "description": issue.description,
#                     "supporting_documents": supporting_docs,
#                     "assigned_to": str(issue.assigned_to) if issue.assigned_to else None,
#                     "assigned_to_details": assigned_to_details,
#                     "status": issue.status.value if issue.status else None,
#                     "priority": issue.priority.value if issue.priority else None,
#                     "user_type": issue.user_type.value if issue.user_type else None,
#                     "created_at": issue.created_at.isoformat() if issue.created_at else None,
#                     "updated_at": issue.updated_at.isoformat() if issue.updated_at else None,
#                 }
#             )

#         return StandardResponse(
#             status_code=200,
#             message="Issues retrieved successfully",
#             data={
#                 "issues": formatted_issues,
#                 "pagination": {
#                     "current_page": current_page,
#                     "page_size": request.limit,
#                     "total_pages": total_pages,
#                     "total_items": total,
#                 },
#             },
#         )

#     except Exception as e:
#         logger.error(f"Error listing issues: {str(e)}")
#         return ErrorResponse(status_code=500, message=f"Error listing issues: {str(e)}")
 

# @router.put("/issues-update/{issue_id}", response_model=StandardResponse)
# async def update_issue(
#     issue_id: UUID,
#     data: IssueUpdate,
#     db: AsyncSession = Depends(get_db),
#     token_data: dict = Depends(validate_token)
# ):
#     """
#     Update an issue.
#     """
#     try:
#         if not token_data:
#             return ErrorResponse(status_code=401, message="Authorization header is required")
            
#         account_id = token_data.get("account_id")
#         if not account_id:
#             return ErrorResponse(status_code=401, message="Invalid authentication: No account ID found")

#         # Get existing issue
#         query = select(Issues).where(Issues.id == issue_id)
#         result = await db.execute(query)
#         db_issue = result.scalar_one_or_none()

#         if not db_issue:
#             return ErrorResponse(
#                 status_code=404,
#                 message="Issue not found"
#             )

#         # Track status changes for history
#         old_status = db_issue.status
#         old_assigned_to = db_issue.assigned_to
            
#         # Convert string IDs to UUID if provided
#         update_data = {}
        
#         if data.description is not None:
#             update_data["description"] = data.description
            
#         if data.assigned_to is not None:
#             try:
#                 # Convert to string first to handle both string and UUID inputs
#                 assigned_to_str = str(data.assigned_to)
#                 logger.info(f"Converting assigned_to string: {assigned_to_str}")
#                 assigned_to_uuid = uuid.UUID(assigned_to_str)
#                 update_data["assigned_to"] = assigned_to_uuid
#                 logger.info(f"Will update assigned_to to: {assigned_to_uuid}")
#             except ValueError as e:
#                 logger.error(f"Invalid UUID format for assigned_to: {e}")
#                 return ErrorResponse(
#                     status_code=400,
#                     message=f"Invalid UUID format for assigned_to: {e}"
#                 )
                
#         if data.status is not None:
#             update_data["status"] = data.status
#             logger.info(f"Will update status to: {data.status}")
            
#         if data.priority is not None:
#             update_data["priority"] = data.priority
#             logger.info(f"Will update priority to: {data.priority}")
            
#         if data.resolution_notes is not None:
#             update_data["resolution_notes"] = data.resolution_notes
#             logger.info(f"Will update resolution_notes to: {data.resolution_notes}")
            
#         if data.resolution_date is not None:
#             update_data["resolution_date"] = data.resolution_date
#             logger.info(f"Will update resolution_date to: {data.resolution_date}")

#         # Apply updates
#         for key, value in update_data.items():
#             setattr(db_issue, key, value)
            
#         # Update timestamps
#         db_issue.updated_at = datetime.now()
#         account_id = token_data.get("account_id")
#         user_name = account_id
#         print("Initial user_name:", user_name)  # Debug log
#         admin_query = select(Admin).where(Admin.id == account_id)
#         admin_result = await db.execute(admin_query)
#         admin = admin_result.scalar_one_or_none()

#         if admin:
#             first_name = admin.first_name if admin.first_name else ""
#             last_name = admin.last_name if admin.last_name else ""
#             user_name = f"{first_name} {last_name}".strip() or user_name

#         # Add history entry for status change
#         if data.status and data.status != old_status:
#             history_entry = IssueHistory(
#                 issue_id=issue_id,
#                 event_code=f"STATUS_CHANGED_TO_{data.status.upper()}",
#                 event_name=f"Status changed to {data.status.lower()}",
#                 user=user_name
#             )
#             db.add(history_entry)

#         # Add history entry for assignment change
#         if data.assigned_to and data.assigned_to != old_assigned_to:
#             history_entry = IssueHistory(
#                 issue_id=issue_id,
#                 event_code="ASSIGNED_TO_AGENT",
#                 event_name="Assigned to Agent",
#                 user=user_name
#             )
#             db.add(history_entry)

#         await db.commit()
#         await db.refresh(db_issue)

#         # Get customer name based on user_type
#         customer_name = "Unknown"
#         if db_issue.user_type == UserType.USER:
#             user_query = select(Users).where(Users.id == db_issue.customer_id)
#             user_result = await db.execute(user_query)
#             user = user_result.scalar_one_or_none()
#             if user:
#                 customer_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
#         elif db_issue.user_type == UserType.ARTISAN:
#             provider_query = select(ServiceProvider).where(ServiceProvider.id == db_issue.customer_id)
#             provider_result = await db.execute(provider_query)
#             provider = provider_result.scalar_one_or_none()
#             if provider:
#                 customer_name = f"{provider.first_name or ''} {provider.last_name or ''}".strip()

#         # Create response data
#         response_data = {
#             "id": str(db_issue.id),
#             "customer_id": str(db_issue.customer_id),
#             "customer_name": customer_name,
#             "case_number": db_issue.case_number,
#             "category_id": str(db_issue.category_id),
#             "subcategory_id": str(db_issue.subcategory_id) if db_issue.subcategory_id else None,
#             "description": db_issue.description,
#             "assigned_to": str(db_issue.assigned_to) if db_issue.assigned_to else None,
#             "status": db_issue.status.value if db_issue.status else None,
#             "priority": db_issue.priority.value if db_issue.priority else None,
#             "user_type": db_issue.user_type.value if db_issue.user_type else None,
#             "created_at": db_issue.created_at.isoformat() if db_issue.created_at else None,
#             "updated_at": db_issue.updated_at.isoformat() if db_issue.updated_at else None
#         }

#         return StandardResponse(
#             status_code=200,
#             message="Issue updated successfully",
#             data=response_data
#         )

#     except Exception as e:
#         await db.rollback()
#         logger.error(f"Error updating issue: {str(e)}")
#         return ErrorResponse(
#             status_code=500,
#             message="Error updating issue",
#             error=str(e)
#         )



# @router.get("/list-categories-subcategories", response_model=StandardResponse)
# async def list_categories_with_subcategories(
#     category_id: Optional[UUID] = None,
#     category_name: Optional[str] = None,
#     category_code: Optional[str] = None,
#     db: AsyncSession = Depends(get_db),
#     token_data: dict = Depends(validate_token)
# ):
#     """
#     List all categories with their subcategories and optional filters.
#     """
#     try:
#         # Join categories with subcategories
#         query = select(
#             IssueCategorys,
#             SubCategory.id.label('subcategory_id'),
#             SubCategory.name.label('subcategory_name'),
#             SubCategory.description.label('subcategory_description'),
#             SubCategory.created_at.label('subcategory_created_at'),
#             SubCategory.updated_at.label('subcategory_updated_at')
#         ).outerjoin(
#             SubCategory, IssueCategorys.id == SubCategory.category_id
#         )
        
#         # Apply filters
#         if category_id:
#             query = query.where(IssueCategorys.id == category_id)
#         if category_name:
#             query = query.where(IssueCategorys.name.ilike(f"%{category_name}%"))
#         if category_code:
#             query = query.where(IssueCategorys.code.ilike(f"%{category_code}%"))
            
#         query = query.order_by(IssueCategorys.name, SubCategory.name)
#         result = await db.execute(query)
#         categories_data = result.all()

#         # Format response data
#         categories_dict = {}
#         for (
#             category, subcategory_id, subcategory_name, 
#             subcategory_description, subcategory_created_at, 
#             subcategory_updated_at
#         ) in categories_data:
#             if category.id not in categories_dict:
#                 categories_dict[category.id] = {
#                     "id": category.id,
#                     "name": category.name,
#                     "code": category.code,
#                     "description": category.description,
#                     "default_priority": category.default_priority,
#                     "created_at": category.created_at,
#                     "updated_at": category.updated_at,
#                     "subcategories": []
#                 }
            
#             if subcategory_id:  # Only add if subcategory exists
#                 categories_dict[category.id]["subcategories"].append({
#                     "id": subcategory_id,
#                     "name": subcategory_name,
#                     "description": subcategory_description,
#                     "created_at": subcategory_created_at,
#                     "updated_at": subcategory_updated_at
#                 })

#         # Convert dictionary to list
#         formatted_categories = list(categories_dict.values())

#         return StandardResponse(
#             status_code=200,
#             message="Categories with subcategories retrieved successfully",
#             data=formatted_categories
#         )
#     except Exception as e:
#         logger.error(f"Error listing categories with subcategories: {str(e)}")
#         return ErrorResponse(
#             status_code=500,
#             message=f"Error listing categories with subcategories: {str(e)}"
#         )

# @router.post("/issues-comment-create", response_model=StandardResponse)
# async def add_dispute_comment(
#     data: IssuesCommentCreate,
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     try:
#         if not Authorization:
#             return ErrorResponse(status_code=401, message="Authorization header is required")
            
#         print("Authorization header:", Authorization)  # Debug log
#         resp = await get_id_header(Authorization)
#         print("get_id_header response:", resp.status_code, resp.json() if hasattr(resp, 'json') else resp)  # Debug log
        
#         if not hasattr(resp, 'status_code'):
#             return ErrorResponse(status_code=401, message="Invalid authentication response")
            
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             account_id = resp_json.get("account_id")
#             print("Account ID:", account_id)  # Debug log
#             if not account_id:
#                 return ErrorResponse(status_code=401, message="Invalid authorization: No account ID found")
#         else:
#             error_msg = resp.json() if hasattr(resp, 'json') else "Authentication failed"
#             return ErrorResponse(status_code=401, message=f"Unauthorized: {error_msg}")

#         # Find the issue using async query
#         issue_query = select(Issues).where(Issues.id == data.issue_id)
#         issue_result = await db.execute(issue_query)
#         issue = issue_result.scalar_one_or_none()
        
#         if not issue:
#             return ErrorResponse(status_code=404, message="Issue not found")

#         # Get user name for the comment
#         user_name = account_id
#         print("Initial user_name:", user_name)  # Debug log

#         # Directly query the Admin table
#         admin_query = select(Admin).where(Admin.id == account_id)
#         admin_result = await db.execute(admin_query)
#         admin = admin_result.scalar_one_or_none()

#         if admin:
#             first_name = admin.first_name if admin.first_name else ""
#             last_name = admin.last_name if admin.last_name else ""
#             user_name = f"{first_name} {last_name}".strip() or user_name
 
#         # Create comment with author_name
#         comment = IssueComments(
#             id=uuid.uuid4(),
#             issue_id=data.issue_id, 
#             author_id=account_id,
#             author_name=user_name,
#             comment=data.comment
#         )
#         db.add(comment)

#         # # Create history entry for comment
#         # history_entry = IssueHistory(
#         #     issue_id=issue.id,  
#         #     event_code="COMMENT_ADDED",
#         #     event_name="Comment added",
#         #     user=user_name
#         # )
#         # db.add(history_entry)

#         # Handle status change if provided
#         old_status = issue.status
#         if data.status and data.status != old_status:
#             issue.status = data.status

#             # Add history entry for status change
#             event_code = f"STATUS_CHANGED_TO_{data.status.value.upper()}"
#             status_history_entry = IssueHistory(
#                 issue_id=issue.id,  
#                 event_code=event_code,
#                 event_name=f"Status changed to {data.status.value.lower()}",
#                 user=user_name
#             )
#             db.add(status_history_entry)

#             # If status is RESOLVED, set resolution date
#             if data.status == IssueStatus.RESOLVED:
#                 issue.resolution_date = datetime.now()
#                 if data.resolution_notes:
#                     issue.resolution_notes = data.resolution_notes

#         # Update issue last updated timestamp
#         issue.updated_at = datetime.now()

#         await db.commit()
#         await db.refresh(comment)
#         await db.refresh(issue)  # Refresh the issue object to ensure all attributes are loaded

#         # Prepare response
#         comment_dict = {
#             "id": str(comment.id),
#             "issue_id": str(comment.issue_id),  
#             "author_id": str(comment.author_id),
#             "author_name": comment.author_name,
#             "comment": comment.comment,
#             "date": comment.date.isoformat() if comment.date else None
#         }

#         # Include status change in response if applicable
#         if data.status and data.status != old_status:
#             comment_dict["status_changed"] = {
#                 "from": old_status.value if old_status else None,
#                 "to": data.status.value,
#             }
            
#         try:
#             if issue.user_type == UserType.USER:
#                 send_push_notification(
#                     auth_token=Authorization,
#                     title=f"Issue {issue.status.value}",
#                     message=data.comment,
#                     type="user",
#                     sender_id=issue.customer_id,
#                 )

#             elif issue.user_type == UserType.ARTISAN:
#                 send_push_notification(
#                     auth_token=Authorization, 
#                     title=f"Issue {issue.status.value}",
#                     message=data.comment,
#                     type="service_provider",
#                     sender_id=issue.customer_id,
#                 )
#         except Exception as notification_error:
#             logger.error(f"Error sending notification: {str(notification_error)}")
#             # Continue with the response even if notification fails
            
#         return StandardResponse(
#             status_code=200,
#             message="Comment added successfully",
#             data=comment_dict
#         )
#     except Exception as e:
#         await db.rollback()
#         logger.error(f"Error adding comment: {str(e)}", exc_info=True)
#         return ErrorResponse(status_code=500, message=f"Error adding comment: {str(e)}")
    

# @router.get("/issue-read/{id}", response_model=StandardResponse)
# async def read_issue(
#     id: str,
#     db: AsyncSession = Depends(get_db),
#     token_data: dict = Depends(validate_token)
# ):
#     """
#     Get detailed information about a specific issue including its comments and history.
#     """
#     try:
#         if not token_data:
#             return ErrorResponse(status_code=401, message="Authorization header is required")
            
#         account_id = token_data.get("account_id")
#         if not account_id:
#             return ErrorResponse(status_code=401, message="Invalid authentication: No account ID found")

#         # Validate UUID format
#         try:
#             issue_uuid = UUID(str(id))
#         except ValueError:
#             return ErrorResponse(
#                 status_code=400,
#                 message=f"Invalid issue ID format: '{id}'. Please provide a valid UUID."
#             )

#         # Get issue details with category and subcategory names
#         issue_query = (
#             select(Issues, IssueCategorys.name.label('category_name'), SubCategory.name.label('subcategory_name'))
#             .outerjoin(IssueCategorys, Issues.category_id == IssueCategorys.id)
#             .outerjoin(SubCategory, Issues.subcategory_id == SubCategory.id)
#             .where(Issues.id == issue_uuid)
#         )
#         issue_result = await db.execute(issue_query)
#         issue_row = issue_result.first()

#         if not issue_row:
#             return ErrorResponse(status_code=404, message="Issue not found")

#         issue = issue_row[0]
#         category_name = issue_row[1]
#         subcategory_name = issue_row[2]

#         # Get customer name based on user_type
#         customer_name = "Unknown"
#         if issue.user_type == UserType.USER:
#             user_query = select(Users).where(Users.id == issue.customer_id)
#             user_result = await db.execute(user_query)
#             user = user_result.scalar_one_or_none()
#             if user:
#                 customer_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
#         elif issue.user_type == UserType.ARTISAN:
#             provider_query = select(ServiceProvider).where(ServiceProvider.id == issue.customer_id)
#             provider_result = await db.execute(provider_query)
#             provider = provider_result.scalar_one_or_none()
#             if provider:
#                 customer_name = f"{provider.first_name or ''} {provider.last_name or ''}".strip()

#         # Get comments
#         comments_query = select(IssueComments).where(IssueComments.issue_id == issue_uuid).order_by(IssueComments.date.desc())
#         comments_result = await db.execute(comments_query)
#         comments = comments_result.scalars().all()

#         # Get history
#         history_query = select(IssueHistory).where(IssueHistory.issue_id == issue_uuid).order_by(IssueHistory.date.desc())
#         history_result = await db.execute(history_query)
#         history = history_result.scalars().all()

#         # Create issue dictionary
#         issue_dict = {
#             "id": str(issue.id),
#             "case_number": issue.case_number,
#             "customer_id": str(issue.customer_id),
#             "customer_name": customer_name,
#             "category_id": str(issue.category_id),
#             "category_name": category_name,
#             "subcategory_id": str(issue.subcategory_id) if issue.subcategory_id else None,
#             "subcategory_name": subcategory_name,
#             "description": issue.description,
#             "supporting_documents": issue.supporting_documents.split(",") if issue.supporting_documents else None,
#             "status": issue.status.value if issue.status else None,
#             "priority": issue.priority.value if issue.priority else None,
#             "user_type": issue.user_type.value if issue.user_type else None,
#             "assigned_to": str(issue.assigned_to) if issue.assigned_to else None,
#             "resolution_date": issue.resolution_date.isoformat() if issue.resolution_date else None,
#             "resolution_note": issue.resolution_notes,
#             "created_at": issue.created_at.isoformat() if issue.created_at else None,
#             "updated_at": issue.updated_at.isoformat() if issue.updated_at else None
#         }

#         # Format comments
#         comments_list = []
#         for comment in comments:
#             comment_dict = {
#                 "id": str(comment.id),
#                 "author_id": comment.author_id,
#                 "author_name": comment.author_name,
#                 "comment": comment.comment,
#                 "date": comment.date.isoformat() if comment.date else None
#             }
#             comments_list.append(comment_dict)

#         # Format history
#         history_list = []
#         for entry in history:
#             history_dict = {
#                 "id": str(entry.id),
#                 "event_code": entry.event_code,
#                 "event_name": entry.event_name,
#                 "user": entry.user,
#                 "date": entry.date.isoformat() if entry.date else None
#             }
#             history_list.append(history_dict)
        
#         for comment in comments:
#             comment_history = {
#                 "id": str(comment.id),
#                 "event_code": "COMMENT_ADDED",
#                 "event_name": "Comment Added",
#                 "comment": comment.comment,
#                 "author_name": comment.author_name,
#                 "date": comment.date.isoformat() if comment.date else None
#             }
#             history_list.append(comment_history)

#         # Sort history by date in descending order (most recent first)
#         history_list.sort(key=lambda x: x["date"] if x["date"] else "", reverse=True)
        
#         # Create response data
#         response_data = {
#             "issue": issue_dict,
#             "comments": comments_list,
#             "history": history_list
#         }

#         return StandardResponse(
#             status_code=200,
#             message="Issue details retrieved successfully",
#             data=response_data
#         )

#     except Exception as e:
#         logger.error(f"Error reading issue: {str(e)}")
#         return ErrorResponse(
#             status_code=500,
#             message=f"Error reading issue: {str(e)}"
#         )
 





