import logging
import json
from app.kafka_producer.config import kafka_producer_config

logger = logging.getLogger(__name__)


async def cognito_producer(data):
    """
    Send data to the 'cognito' Kafka topic for Cognito user creation.
    
    Args:
        data: Dictionary containing user data to be processed
    """
    try:
        logger.info(f"Sending data to cognito topic for user: {data.get('Trainee Contact Number', 'Unknown')}")
        encoded_data = json.dumps(data).encode("ascii")
        await kafka_producer_config.send_and_wait("cognito", encoded_data)
        logger.info("Successfully sent data to cognito topic")
    except Exception as e:
        logger.error(f"Error sending data to cognito topic: {str(e)}")
        raise


async def db_producer(data):
    """
    Send data to the 'db' Kafka topic for database user creation.
    
    Args:
        data: Dictionary containing user data to be processed in database
    """
    try:
        logger.info(f"Sending data to db topic for user: {data.get('phone_number', 'Unknown')}")
        encoded_data = json.dumps(data).encode("ascii")
        await kafka_producer_config.send_and_wait("db", encoded_data)
        logger.info("Successfully sent data to db topic")
    except Exception as e:
        logger.error(f"Error sending data to db topic: {str(e)}")
        raise
