import uuid
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.database import get_db
from app.schemas.service_provider import (
    ServiceProviderStatusUpdate,
    ServiceProviderLeaveCreate,
    ServiceProviderLeaveUpdate,
    ServiceProviderLeaveResponse,
)
from app.repositories.service_provider import (
    update_service_provider_status,
    create_leave,
    get_service_provider_leaves,
    update_leave,
    delete_leave,
)
from app.models import (
    ServiceProvider,
    ServiceProviderLeave,
    ServiceProviderServiceMapping,
)
from app.redis_config import redis_client
from app.schemas.helper import ErrorResponse, StandardResponse
from fastapi import Query, Header
from typing import List, Optional, Annotated
from datetime import date
import math
from sqlalchemy import and_
from app.utils.auth import get_id_header


router = APIRouter(prefix="/service_provider", tags=["Service Provider"])


@router.put("/status")
async def update_status(
    status_data: ServiceProviderStatusUpdate,
    db: Session = Depends(get_db),
    # Authorization: Annotated[str, Header()] = None,
):
    """
    Update the service provider's status and store location & skills efficiently in Redis.
    Retrieves service IDs from the ServiceProviderServiceMapping table.
    """
    try:
        # resp = await get_id_header(Authorization)
        # if resp.status_code != 200:
        #     # raise HTTPException(status_code=401, detail="Unauthorized")
        #     return ErrorResponse(status_code=401, message="Unauthorized")
        
        # account_id = resp.json().get("account_id")
        # account_id = 'ee351074-926f-4c83-a94c-f44a0ba92ea0'
        artisan_id = status_data.artisan_id
        service_provider = update_service_provider_status(db, artisan_id, status_data)
        if not service_provider:
            # raise HTTPException(status_code=404, detail="Service Provider not found")
            return ErrorResponse(status_code=404, message="Service Provider not found")

        # artisan_id = str(service_provider.id)  # Convert UUID to string
        redis_key = f"artisan:{artisan_id}"

        # Fetch service IDs from ServiceProviderServiceMapping table
        service_ids = (
            db.query(ServiceProviderServiceMapping.services_id)
            .filter(
                ServiceProviderServiceMapping.service_provider_id == uuid.UUID(artisan_id)
            )
            .all()
        )

        print(service_ids, "service_ids")

        # Convert list of tuples to list of strings
        service_ids = [str(service_id[0]) for service_id in service_ids]

        if not service_ids:
            # raise HTTPException(
            #     status_code=400, detail="No services found for this artisan."
            # )
            return ErrorResponse(
                status_code=400, message="No services found for this artisan."
            )

        # Store base data in Redis Hash
        redis_client.hset(
            redis_key,
            mapping={
                "base_latitude": str(service_provider.latitude),
                "base_longitude": str(service_provider.longitude),
                "live_latitude": str(status_data.current_latitude),
                "live_longitude": str(status_data.current_longitude),
                "service_ids": ",".join(service_ids),
                "status": status_data.live_status,
                "weekdays": str(service_provider.weekdays)
            },
        )

        if status_data.live_status == "online":
            for service_id in service_ids:
                service_geo_key = f"geo:service:{service_id}"

                redis_client.geoadd(
                    service_geo_key,
                    (
                        status_data.current_longitude,
                        status_data.current_latitude,
                        artisan_id,
                    ),
                )

                # Add artisan ID under service-based keys
                redis_client.sadd(f"service:{service_id}", artisan_id)

            # Set expiration (optional, refresh every 24 hrs)
            redis_client.expire(redis_key, 86400)
        else:
            # Remove from all stored services if offline
            for service_id in service_ids:
                service_geo_key = f"geo:service:{service_id}"
                redis_client.zrem(service_geo_key, artisan_id)
                redis_client.srem(f"service:{service_id}", artisan_id)

            redis_client.delete(redis_key)

        return StandardResponse(
            status_code=200,
            message=f"Status updated to {status_data.live_status}",
            data={"service_provider_id": artisan_id, "status": status_data.live_status},
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))


# @router.get("/redis_status/{auth_id}", response_model=StandardResponse)
# def check_service_provider_status(auth_id: str):
#     redis_key = f"artisan:{auth_id}"

#     # Check if key exists
#     if not redis_client.exists(redis_key):
#         return StandardResponse(
#             status_code=404,
#             message="Service Provider is offline or not found in Redis",
#             data=None
#         )

#     # Fetch all stored values as a hash
#     data = redis_client.hgetall(redis_key)

#     # Ensure data is not empty
#     if not data:
#         return StandardResponse(
#             status_code=500,
#             message="Data exists but could not be retrieved properly",
#             detail=None
#         )

#     return StandardResponse(
#         status_code=200,
#         message="Service Provider data found in Redis",
#         detail=data
#     )


def haversine_distance(lat1, lon1, lat2, lon2):
    """Calculate distance between two latitude/longitude points in km using Haversine formula."""
    R = 6371  # Radius of Earth in km
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])

    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = (
        math.sin(dlat / 2) ** 2
        + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
    )
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

    return R * c


@router.post("/leave", 
            #  response_model=ServiceProviderLeaveResponse
             )
async def create_service_provider_leave(
    leave_data: ServiceProviderLeaveCreate,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            # raise HTTPException(status_code=401, detail="Unauthorized")
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        account_id = resp.json().get("account_id")
        
        # Get service provider ID from auth_id
        service_provider = db.query(ServiceProvider).filter(ServiceProvider.id == account_id).first()
        if not service_provider:
            # raise HTTPException(status_code=404, detail="Service Provider not found")
            return ErrorResponse(status_code=404, message="Service Provider not found")
        
        # Check if leave already exists for the date
        existing_leave = db.query(ServiceProviderLeave).filter(
            and_(
                ServiceProviderLeave.service_provider_id == service_provider.id,
                ServiceProviderLeave.leave_date == leave_data.leave_date
            ))
        if not service_provider:
            # raise HTTPException(status_code=404, detail="Service Provider not found")
            return ErrorResponse(status_code=404, message="Service Provider not found")

        # Check if leave already exists for the date
        existing_leave = (
            db.query(ServiceProviderLeave)
            .filter(
                and_(
                    ServiceProviderLeave.service_provider_id == service_provider.id,
                    ServiceProviderLeave.leave_date == leave_data.leave_date,
                )
            )
            .first()
        )

        if existing_leave:
            # raise HTTPException(
            #     status_code=400, detail="Leave already exists for this date"
            # )
            return ErrorResponse(
                status_code=400, message="Leave already exists for this date"
            )

        leave = create_leave(db, service_provider.id, leave_data)
        # return leave
        return StandardResponse(
            status_code=200, message="Leave created successfully", data=leave
        )
    except Exception as e:
        # return HTTPException(status_code=500, detail=f"Error: {e}")
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.get("/leaves", 
            # response_model=List[ServiceProviderLeaveResponse]
            )
async def get_leaves(
    start_date: Optional[date] = None,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            # raise HTTPException(status_code=401, detail="Unauthorized")
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        account_id = resp.json().get("account_id")
        service_provider = db.query(ServiceProvider).filter(ServiceProvider.id == account_id).first()
        if not service_provider:
            # raise HTTPException(status_code=404, detail="Service Provider not found")
            return ErrorResponse(status_code=404, message="Service Provider not found")
        
        leaves = get_service_provider_leaves(db, service_provider.id, start_date)
        # return leaves
        return StandardResponse(
            status_code=200, message="Leaves retrieved successfully", data=leaves
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))

@router.put("/leave/{leave_id}", 
            # 8response_model=ServiceProviderLeaveResponse
            )
async def update_service_provider_leave(
    leave_id: str,
    leave_data: ServiceProviderLeaveUpdate,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            # raise HTTPException(status_code=401, detail="Unauthorized")
            return ErrorResponse(status_code=401, message="Unauthorized")

        leave = update_leave(db, leave_id, leave_data)
        if not leave:
            # raise HTTPException(status_code=404, detail="Leave not found")
            return ErrorResponse(status_code=404, message="Leave not found")
        # return leave
        return StandardResponse(
            status_code=200, message="Leave updated successfully", data=leave
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.delete("/leave/{leave_id}")
async def delete_service_provider_leave(
    leave_id: str,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            # raise HTTPException(status_code=401, detail="Unauthorized")
            return ErrorResponse(status_code=401, message="Unauthorized")

        leave = delete_leave(db, leave_id)
        if not leave:
            # raise HTTPException(status_code=404, detail="Leave not found")
            return ErrorResponse(status_code=404, message="Leave not found")

        # return {"message": "Leave deleted successfully"}
        return StandardResponse(status_code=200, message="Leave deleted successfully")
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error", error=str(e))
