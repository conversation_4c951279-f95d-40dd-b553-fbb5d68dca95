[{"uris": ["/core/docs", "/core/openapi.json"], "name": "core-api-docs", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uris": ["/service/docs", "/service/openapi.json"], "name": "service-api-docs", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uris": ["/broadcast/docs", "/broadcast/openapi.json"], "name": "broadcast-api-docs", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE"], "upstream_id": "broadcast", "status": 1}, {"uris": ["/provider-service/docs", "/provider-service/openapi.json"], "name": "provider-service-api-docs", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uris": ["/schedule-service/docs", "/schedule-service/openapi.json"], "name": "schedule-service-api-docs", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE"], "upstream_id": "schedule-service", "status": 1}, {"uris": ["/support-service.dev.svc.cluster.local/docs", "/support-service.dev.svc.cluster.local/openapi.json"], "name": "support-service.dev.svc.cluster.local-api-docs", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uris": ["/payment-service/docs", "/payment-service/openapi.json"], "name": "payment-service.dev.svc.cluster.local-api-docs", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/booking/negotiate-price", "name": "booking-negotiate", "methods": ["OPTIONS", "PUT"], "upstream_id": "booking-service", "status": 1}, {"uri": "/booking/request", "name": "booking-request", "methods": ["OPTIONS", "POST"], "upstream_id": "booking-service", "status": 1}, {"uri": "/booking/*", "name": "booking-by-id", "methods": ["OPTIONS", "GET"], "upstream_id": "booking-service", "status": 1}, {"uri": "/user-list", "name": "user-list", "methods": ["OPTIONS", "GET"], "upstream_id": "profile-service", "status": 1}, {"uri": "/sp-list", "name": "profile-sp-list", "methods": ["OPTIONS", "GET"], "upstream_id": "profile-service", "status": 1}, {"uri": "/auth/test-signup", "name": "test-signup", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service", "status": 1}, {"uri": "/auth/signup-signin", "name": "new-user-signup-or-exitsing-user-signin", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/auth/otp-verify", "name": "new-user-signup-or-login-otp-verify", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/auth/resend-signup-otp", "name": "new-user-signup-resend-otp", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/auth/renew-token", "name": "renew-auth-token", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/auth/cognito-get-user", "name": "fetch-user-info-in-cognito", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/auth/forgot-password", "name": "forgot-password", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/auth/reset-password", "name": "reset-password", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-profiles/", "name": "create-user-in-db", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-profiles/", "name": "fetch-all-users-in-db", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-profiles/*", "name": "fetch-user-info-in-db", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-profiles/update", "name": "update-user-in-db", "methods": ["OPTIONS", "PUT"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-profiles/*", "name": "delete-user", "methods": ["OPTIONS", "DELETE"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/roles/", "name": "create-role", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/roles/", "name": "get-all-roles", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/roles/*", "name": "get-role-by-id", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/roles/*", "name": "update-role-by-id", "methods": ["OPTIONS", "PUT"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/roles/*", "name": "delete-role-by-id", "methods": ["OPTIONS", "DELETE"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/permissions/", "name": "create-permissions", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/permissions/", "name": "fetch-all-permissions", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/permissions/*", "name": "fetch-permissions-by-id", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/permissions/*", "name": "update-permissions-by-id", "methods": ["OPTIONS", "PUT"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/permissions/*", "name": "delete-permissions-by-id", "methods": ["OPTIONS", "DELETE"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/addresses/", "name": "delete-addresses", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/addresses/*", "name": "fetch-addresses-by-id", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/addresses/*", "name": "update-addresses-by-id", "methods": ["OPTIONS", "PUT"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/addresses/*", "name": "delete-addresses-by-id", "methods": ["OPTIONS", "DELETE"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/users-blacklist/", "name": "users-blacklist", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/users-blacklist/", "name": "fetch-all-users-blacklist", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/users-blacklist/*", "name": "fetch-user-blacklist-by-id", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/users-blacklist/*", "name": "update-user-blacklist", "methods": ["OPTIONS", "PUT"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/users-blacklist/*", "name": "users-blacklist", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/taxes-list", "name": "taxes-list", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/booking-cancellation-reason/create", "name": "booking-cancellation-reason-create", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/booking-cancellation-reason/read", "name": "booking-cancellation-reason-read", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/booking-cancellation-reason/*", "name": "booking-cancellation-reason-all-actions-delete-update-read", "methods": ["OPTIONS", "GET", "PUT", "DELETE"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/request-cancel", "name": "booking-cancellation-request", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/taxes-create", "name": "taxes-create", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/taxes-update/*", "name": "taxes-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/taxes-delete/*", "name": "taxes-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/taxes-sp/*", "name": "get_taxes_by_service_provider", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/invoices/create-invoice", "name": "create-invoice", "methods": ["OPTIONS", "POST"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/invoices/update-invoice", "name": "update-invoice", "methods": ["OPTIONS", "PATCH"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/invoices/get-invoice", "name": "get-invoice", "methods": ["OPTIONS", "GET"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/invoices/get-invoice-list", "name": "get-invoice-list", "methods": ["OPTIONS", "GET"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/invoices/delete-invoice", "name": "delete-invoice", "methods": ["OPTIONS", "DELETE"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/invoices/create-invoice-item", "name": "create-invoice-item", "methods": ["OPTIONS", "POST"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/invoices/update-invoice-item", "name": "update-invoice-item", "methods": ["OPTIONS", "PUT"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/invoices/get-invoice-item", "name": "get-invoice-item", "methods": ["OPTIONS", "GET"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/invoices/get-invoice-item-list", "name": "get-invoice-item-list", "methods": ["OPTIONS", "GET"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/invoices/delete-invoice-item", "name": "delete-invoice-item", "methods": ["OPTIONS", "DELETE"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/request-mapping/create-artisan-assigned", "name": "create-artisan-assigned", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/notification-preferences/user", "name": "get_notification_preferences_by_user", "methods": ["OPTIONS", "GET"], "upstream_id": "notification-service.dev.svc.cluster.local", "status": 1}, {"uri": "/notification-preferences", "name": "notification-preferences-create", "methods": ["OPTIONS", "POST"], "upstream_id": "notification-service.dev.svc.cluster.local", "status": 1}, {"uri": "/notification-preferences/*", "name": "notification-preferences-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "notification-service.dev.svc.cluster.local", "status": 1}, {"uri": "/notification-preferences/*", "name": "notification-preferences-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "notification-service.dev.svc.cluster.local", "status": 1}, {"uri": "/notification-preferences", "name": "notification-preferences-list", "methods": ["OPTIONS", "GET"], "upstream_id": "notification-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-preferences", "name": "user-preferences-create", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-preferences/*", "name": "user-preferences-get-by-id", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-preferences", "name": "user-preferences-list", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-preferences/*", "name": "user-preferences-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-preferences/*", "name": "user-preferences-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/auth/logout", "name": "close-the-session", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/cart-create", "name": "cart-create", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/request-mapping/update-artisan-assigned/*", "name": "update-artisan-assigned", "methods": ["OPTIONS", "PUT"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/cart-read/*", "name": "cart-read", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/cart-list", "name": "cart-list", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/cart-user/*", "name": "cart-user", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/cart-update/*", "name": "cart-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/request-mapping/get-artisan-assigned", "name": "get-artisan-assigned", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/request-mapping/get-artisan-assigned-list", "name": "get-artisan-assigned-list", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/request-mapping/delete-artisan-assigned", "name": "delete-artisan-assigned", "methods": ["OPTIONS", "DELETE"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/cart-delete/*", "name": "cart-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/artisan/update-artisan-detail", "name": "update-artisan-details", "methods": ["OPTIONS", "PUT"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/artisan/get-artisan-details", "name": "get-the-artisan-by-id", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/region", "name": "region-create", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/region", "name": "region-list", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/region/*", "name": "region-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/region/*", "name": "region-get-by-id", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/region/*", "name": "region-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/service-requests-list", "name": "service-requests-list-all", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/artisan/artisan-list", "name": "list-of-artisan-details", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/api/v1/kyc-details-create", "name": "submit-kyc-details", "methods": ["OPTIONS", "POST"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/api/v1/kyc-details-update/*", "name": "update-kyc-details", "methods": ["OPTIONS", "PUT"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/status-update", "name": "artisan-arrival-status", "methods": ["OPTIONS", "PUT"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/payment-create-new", "name": "create-payment", "methods": ["OPTIONS", "POST"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/initiate-payment", "name": "initiate-payment-new", "methods": ["POST", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/collect-payment", "name": "collect-payment-new", "methods": ["POST", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/refund-payment", "name": "refund-payment-new", "methods": ["POST", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/request-payment", "name": "request-payment-new", "methods": ["POST", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/payment-status", "name": "payment-status-new", "methods": ["GET", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/booking/details/*", "name": "booking-details-by-order-id", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/kyc-details-create", "name": "kyc-create", "methods": ["OPTIONS", "POST"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/kyc-details-update/*", "name": "kyc-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/kyc-details/*", "name": "kyc-details", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/kyc-info", "name": "kyc-list", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/delete-kyc/*", "name": "kyc-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/service-provider-create", "name": "sp-create", "methods": ["OPTIONS", "POST"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/service-provider-update/*", "name": "sp-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/get-service-provider/*", "name": "sp-details", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/get-service-providers-list", "name": "sp-list", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/delete-servise-provider/*", "name": "sp-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/sp-branch-create", "name": "spbranch-create", "methods": ["OPTIONS", "POST"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/sp-branch-update/*", "name": "spbranch-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/get-sp-branch/*", "name": "spbranch-details", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/get-service-provider-branch-list", "name": "spbranch-list", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/delete-service-provider-branch/*", "name": "spbranch-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/sp-rating-create", "name": "sprating-create", "methods": ["OPTIONS", "POST"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/sp-rating-update/*", "name": "sprating-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/get-sp-rating/*", "name": "sprating-details", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/get-sp-ratings-list", "name": "sprating-list", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/delete-sp_rating/*", "name": "sprating-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/sp-users-create", "name": "spusers-create", "methods": ["OPTIONS", "POST"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/sp-users-update/*", "name": "spusers-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/get-sp-user/*", "name": "kyc-details", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/get-sp-users-list", "name": "spusers-list", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/sp/delete-sp-user/*", "name": "kyc-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "provider-service.dev.svc.cluster.local", "status": 1}, {"uri": "/artisan/onboarding-artisan", "name": "onboarding-artisan", "methods": ["OPTIONS", "PUT"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/category-delete/*", "name": "category-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/category-create", "name": "category-create", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/category-read/*", "name": "category-read", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/category-list", "name": "category-list", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/category-update/*", "name": "category-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/category-list-simple", "name": "category-list-simple", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/services-create", "name": "services-create", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/services-read/*", "name": "services-read", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/services-list", "name": "list-the-services", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/services-update/*", "name": "services-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/services-delete/*", "name": "services-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/services-list-simple", "name": "services-list-simple", "methods": ["OPTIONS", "GET"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/request-service", "name": "request-service", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/assign-artisan", "name": "assign-artisan", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/artisan-soft-lock", "name": "artisan-soft-lock", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/service-request-update/*", "name": "service-request-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/search_artisan/search_live_artisan", "name": "search_live_artisan", "methods": ["OPTIONS", "POST"], "upstream_id": "schedule-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-profiles/users-list", "name": "list-of-users", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-profiles/", "name": "read-users", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/auth/superadmin/create_admin", "name": "create-admin-user", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-profiles/update/*", "name": "update-user", "methods": ["OPTIONS", "PUT"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-profiles/create-user", "name": "create-user", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/create-rating", "name": "create-rating", "methods": ["POST", "OPTIONS"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/artisan/admin/create-artisan", "name": "admin-artisan-create", "methods": ["POST", "OPTIONS"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/artisan/status", "name": "artisan-status", "methods": ["PUT", "OPTIONS"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/dispute-list", "name": "dispute-list", "methods": ["OPTIONS", "POST"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/dispute-create", "name": "dispute-create", "methods": ["OPTIONS", "POST"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/dispute-read/*", "name": "dispute-read", "methods": ["OPTIONS", "GET"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/dispute-update/*", "name": "dispute-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/dispute-comment-create", "name": "dispute-comment-create", "methods": ["OPTIONS", "POST"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/dispute-event-types", "name": "dispute-event-types", "methods": ["OPTIONS", "GET"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/issue-types", "name": "issue-types", "methods": ["OPTIONS", "GET"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/issue-category", "name": "issue-category-create-dispute", "methods": ["OPTIONS", "POST"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/issue-categories/*", "name": "issue-categories-list-dispute", "methods": ["OPTIONS", "GET"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/issue-category/*", "name": "issue-category-update-dispute", "methods": ["OPTIONS", "PUT"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/issue-category/*", "name": "issue-category-delete-dispute", "methods": ["OPTIONS", "DELETE"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/dispute-status/*", "name": "dispute-status", "methods": ["OPTIONS", "PUT"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/dispute-comments/*", "name": "dispute-comments", "methods": ["OPTIONS", "GET"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/assign-dispute", "name": "assign-dispute", "methods": ["OPTIONS", "PUT"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/issues-create", "name": "issues-create", "methods": ["OPTIONS", "POST"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/issues-list", "name": "issues-list", "methods": ["OPTIONS", "POST"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/issues-update/*", "name": "issues-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/issues-delete/*", "name": "issues-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/issues-comment-create", "name": "issues-comment-create", "methods": ["OPTIONS", "POST"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/issue-read/*", "name": "issue-read", "methods": ["OPTIONS", "GET"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/create-categories-issues", "name": "create-categories-issues", "methods": ["OPTIONS", "POST"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/update-categories-issues/*", "name": "update-categories-issues", "methods": ["OPTIONS", "PUT"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/delete-categories-issues/*", "name": "delete-categories-issues", "methods": ["OPTIONS", "DELETE"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/list-categories-subcategories", "name": "list-categories-subcategories", "methods": ["OPTIONS", "GET"], "upstream_id": "support-service.dev.svc.cluster.local", "status": 1}, {"uri": "/locality/get-localities", "name": "get-localities", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/artisan/artisan-earnings-list", "name": "artisan-earnings-list", "methods": ["GET", "OPTIONS"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/artisan/artisan-earnings-details/*", "name": "artisan-earnings-detail", "methods": ["GET", "OPTIONS"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/artisan/user-spends-list/*", "name": "user-spends", "methods": ["GET", "OPTIONS"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/artisan/user-spends-details/*", "name": "user-spends-detail", "methods": ["GET", "OPTIONS"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/ratings/list-feedback", "name": "user-spends-detail", "methods": ["GET", "OPTIONS"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/cancellations", "name": "list-of-cancellations", "methods": ["POST", "OPTIONS"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/cancellation/approve", "name": "take-action-to-cancellation", "methods": ["POST", "OPTIONS"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/cancellation-summary/*", "name": "cancellation-summary", "methods": ["GET", "OPTIONS"], "upstream_id": "service-service", "status": 1}, {"uri": "/send_notification", "name": "send_notification-to-user", "methods": ["POST", "OPTIONS"], "upstream_id": "notification-service.dev.svc.cluster.local", "status": 1}, {"uri": "/invoices/generate-quote", "name": "generate_quote", "methods": ["OPTIONS", "POST"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/initiate-payment", "name": "initiate-payment-new", "methods": ["POST", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/payment-callback", "name": "payment_callback", "methods": ["OPTIONS", "POST"], "upstream_id": "payment-service", "status": 1}, {"uri": "/account-create", "name": "account-create-new", "methods": ["POST", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/account-read/*", "name": "account-read-new", "methods": ["GET", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/account-list", "name": "account-list-new", "methods": ["GET", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/account-update", "name": "account-update-new", "methods": ["PUT", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/account-delete/*", "name": "account-delete-new", "methods": ["DELETE", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/collect-payment", "name": "collect-payment", "methods": ["POST", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/refund-payment", "name": "refund-payment", "methods": ["POST", "OPTIONS"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/add-service-to-booking", "name": "add-service-to-existing-booking", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/user-profiles/sp-export", "name": "exporting-artisan-details", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/chatservice/enqueue_customer", "name": "enque-user", "methods": ["OPTIONS", "POST"], "upstream_id": "chat-service.dev.svc.cluster.local", "status": 1}, {"uri": "/chatservice/agent_available/*", "name": "agent-available", "methods": ["OPTIONS", "POST"], "upstream_id": "chat-service.dev.svc.cluster.local", "status": 1}, {"uri": "/chatservice/queue_status", "name": "queue-status", "methods": ["OPTIONS", "GET"], "upstream_id": "chat-service.dev.svc.cluster.local", "status": 1}, {"uri": "/chatservice/send_message", "name": "send-message", "methods": ["OPTIONS", "POST"], "upstream_id": "chat-service.dev.svc.cluster.local", "status": 1}, {"uri": "/chatservice/create_agent", "name": "agent-create", "methods": ["OPTIONS", "POST"], "upstream_id": "chat-service.dev.svc.cluster.local", "status": 1}, {"uri": "/agent-request-cancel", "name": "booking-cancellation-by-agent", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/admin/bulk-upload-service-providers/v2", "name": "bulk-upload-service-providers", "methods": ["OPTIONS", "POST"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/admin/bulk-upload/status/*", "name": "bulk-upload-status-check", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service", "status": 1}, {"uri": "/admin/bulk-upload/tasks", "name": "bulk-upload-tasks-list", "methods": ["OPTIONS", "GET"], "upstream_id": "core-service", "status": 1}, {"uri": "/auth/admin/delete-user", "name": "admin-delete-the-user", "methods": ["OPTIONS", "DELETE"], "upstream_id": "core-service.dev.svc.cluster.local", "status": 1}, {"uri": "/refund/initiate-refund/*", "name": "initiate-refund", "methods": ["OPTIONS", "POST"], "upstream_id": "service-service.dev.svc.cluster.local", "status": 1}, {"uri": "/chatservice/ws/*", "name": "websocket-chat", "methods": ["GET", "OPTIONS"], "upstream_id": "chat-service.dev.svc.cluster.local", "enable_websocket": true, "status": 1}, {"uri": "/create-rating-agent", "name": "create-rating-agent", "methods": ["POST", "OPTIONS"], "upstream_id": "service-service", "status": 1}, {"uri": "/transaction-create", "name": "create-transaction", "methods": ["OPTIONS", "POST"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/transaction-read/*", "name": "read-transaction", "methods": ["OPTIONS", "GET"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/transaction-list", "name": "list-transaction", "methods": ["OPTIONS", "GET"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/transaction-update/*", "name": "update-transaction", "methods": ["OPTIONS", "PUT"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/transaction-delete/*", "name": "delete-transaction", "methods": ["OPTIONS", "DELETE"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/transaction-status/*", "name": "transaction-status", "methods": ["OPTIONS", "GET"], "upstream_id": "payment-service.dev.svc.cluster.local", "status": 1}, {"uri": "/chatservice/get_conversations", "name": "get-conversations", "methods": ["OPTIONS", "GET"], "upstream_id": "chat-service.dev.svc.cluster.local", "status": 1}, {"uri": "/chatservice/chat_initiate", "name": "chat_initiate", "methods": ["OPTIONS", "POST"], "upstream_id": "chat-service", "status": 1}, {"uri": "/chatservice/get_conversation_list", "name": "get-conversation-list", "methods": ["OPTIONS", "GET"], "upstream_id": "chat-service", "status": 1}, {"uri": "/notifications-list", "name": "notifications-list", "methods": ["OPTIONS", "GET"], "upstream_id": "notification-service", "status": 1}, {"uri": "/update_notification/*", "name": "update-notification", "methods": ["OPTIONS", "PUT"], "upstream_id": "notification-service", "status": 1}, {"uri": "/sp/business-onboarding", "name": "business-onboarding", "methods": ["OPTIONS", "PUT"], "upstream_id": "provider-service", "status": 1}, {"uri": "/sp/business-list", "name": "business-list", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service", "status": 1}, {"uri": "/sp/business-details/*", "name": "business-details", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service", "status": 1}, {"uri": "/sp/business-area", "name": "business-area-create", "methods": ["OPTIONS", "POST"], "upstream_id": "provider-service", "status": 1}, {"uri": "/sp/business-area", "name": "business-area-list", "methods": ["OPTIONS", "GET"], "upstream_id": "provider-service", "status": 1}, {"uri": "/sp/business-area/*", "name": "business-area-update", "methods": ["OPTIONS", "PUT"], "upstream_id": "provider-service", "status": 1}, {"uri": "/sp/business-area/*", "name": "business-area-delete", "methods": ["OPTIONS", "DELETE"], "upstream_id": "provider-service", "status": 1}, {"uri": "/user-profiles/soft-delete", "name": "soft-delete-user", "methods": ["OPTIONS", "DELETE"], "upstream_id": "core-service", "status": 1}]