import uuid
from fastapi import APIRouter, Depends, File, UploadFile, Header
from app.utils.s3_upload import upload_file_direct, s3_delete_file, S3_IMAGES_FOLDER
from app.schemas import category as schemas
from app.database import get_db
from sqlalchemy.orm import Session
from app.utils.auth import get_id_header
from typing import Optional
from app.utils.crud import get_record_by_id, create_record, delete_record, update_record
from sqlalchemy.future import select
from sqlalchemy import func
from typing import Annotated
from app.schemas.helper import StandardResponse, ErrorResponse
from app.models import Category
from app.utils.auth import permission_checker
from fastapi.responses import JSONResponse

router = APIRouter(tags=["Category"])

@router.post("/category-create")
async def create_category(
    request: schemas.CreateCategory = Depends(),
    banner: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db),user=Depends(permission_checker)
    ):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        request_dict = request.to_dict()

        if banner:
            # Calling S3 upload function
            profile_url = upload_file_direct(banner, path=S3_IMAGES_FOLDER)
            print(profile_url, "profile_url")
            request_dict["banner"] = profile_url['filename']
        
        new_category = create_record(db, Category, request_dict)
        if isinstance(new_category, str):
            return ErrorResponse(status_code=400, message=new_category)
        return StandardResponse(
            status_code=200,
            message="Category created successfully",
            data={'data': 'Category created successfully'}
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )

@router.get("/category-read/{id}")
async def read_category(
    id: str,
    db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        category_obj = get_record_by_id(db, Category, uuid.UUID(id))
        if isinstance(category_obj, str):
            return ErrorResponse(status_code=400, message=category_obj)
        return StandardResponse(
            status_code=200,
            message="Category read successfully",
            data=category_obj
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")
    

async def get_categories_with_services(db, category_id):
    categories = db.query(Category).filter(Category.parent_id == category_id).all()
    return categories

@router.get("/category-list")
async def list_category(
    q: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)):
    try:
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user
        # Total category
        categories = db.query(Category).filter(Category.parent_id == None, Category.is_active == True).all()

        lst = []
        for category_obj in categories:
            category_data = category_obj.__dict__
            sub_services = await get_categories_with_services(db, category_data['id'])
            category_data['sub_category'] = sub_services
            lst.append(category_data)

        count_query = select(func.count()).select_from(Category)
        total_result = db.execute(count_query)
        total_count = total_result.scalar()
        print(lst)
        return StandardResponse(
            status_code=200,
            message="Category read successfully",
            data={"total": total_count, "category": lst}
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )
    

@router.put("/category-update/{id}")
async def update_category(
    id: str,
    data: schemas.UpdateCategory = Depends(),
    banner: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        get_category = get_record_by_id(db, Category, uuid.UUID(id))
        if isinstance(get_category, str):
            return ErrorResponse(status_code=400, message=get_category)
        
        if banner:
            if get_category.banner:
                s3_delete_file(get_category.banner)
            # Calling S3 upload function
            profile_url = upload_file_direct(banner, path=S3_IMAGES_FOLDER)
            setattr(get_category, "banner", profile_url['filename'])

        res = update_record(db, data, get_category)
        if isinstance(res, str):
            return ErrorResponse(status_code=400, message=res)
        return StandardResponse(
            status_code=200,
            message="Category updated successfully",
            data=res
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )
    
@router.delete("/category-delete/{id}")
async def delete_category(
    id: str,
    db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        get_category = get_record_by_id(db, Category, uuid.UUID(id))
        if isinstance(get_category, str):
            return ErrorResponse(status_code=400, message=get_category)

        if get_category is None:
            return ErrorResponse(
                status_code=404,
                message="Category not found"
            )
        
        if get_category.banner:
            s3_delete_file(get_category.banner)
        
        res = delete_record(db, get_category)
        return StandardResponse(
            status_code=200,
            message="Category deleted successfully",
            data=res
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message=f"Error: {e}"
        )

@router.get("/category-list-simple")
async def list_category_simple(
    db: Session = Depends(get_db),user=Depends(permission_checker)):
    """
    Get a simplified list of categories with minimal data to avoid recursion issues.
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        # Get categories with minimal data
        categories = db.query(Category).filter(Category.is_active == True).all()

        # Create a simplified list with only the data we need
        simple_categories = []
        for category in categories:
            simple_categories.append({
                "id": str(category.id),
                "name": category.name
            })

        return {
            "status_code": 200,
            "message": "Categories fetched successfully",
            "data": {
                "total": len(simple_categories),
                "data": simple_categories
            }
        }
    except Exception as e:
        return {
            "status_code": 500,
            "message": f"Error: {e}",
            "error": str(e)
        }
