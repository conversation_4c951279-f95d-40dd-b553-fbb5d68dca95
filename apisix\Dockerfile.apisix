# # USER apisix
# FROM apache/apisix:3.4.0-debian

# USER root

# # Copy pre-downloaded jq binary into the container
# COPY ./jq /usr/local/bin/jq

# # Make jq executable
# RUN chmod +x /usr/local/bin/jq
# # ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
# USER apisix
FROM apache/apisix:3.4.0-debian

USER root

# Copy jq binary
COPY ./jq /usr/local/bin/jq
RUN chmod +x /usr/local/bin/jq

# Copy apisix-config (sync.sh etc.)
COPY ./apisix-config/ /usr/local/apisix/apisix-config/

# Clean sync.sh line endings and make it executable
RUN tr -d '\r' < /usr/local/apisix/apisix-config/sync.sh > /usr/local/apisix/apisix-config/sync_fixed.sh && \
    mv /usr/local/apisix/apisix-config/sync_fixed.sh /usr/local/apisix/apisix-config/sync.sh && \
    chmod +x /usr/local/apisix/apisix-config/sync.sh

USER apisix
