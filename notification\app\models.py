from datetime import datetime
from sqlalchemy import (
    JSO<PERSON>,
    <PERSON>ole<PERSON>,
    Column,
    Date,
    Enum as SQL<PERSON><PERSON><PERSON>,
    Float,
    Foreign<PERSON>ey,
    Integer,
    String,
    DateTime,
    Integer,
    Text,
    func,
    Numeric,
    Time,
    ARRAY,
    UniqueConstraint,
)
from sqlalchemy.sql.sqltypes import TIMESTAMP
from sqlalchemy.sql.expression import text
from sqlalchemy.orm import relationship, mapped_column, Mapped
from app.database import Base
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.dialects.postgresql import JSONB, UUID
import uuid
from geoalchemy2 import Geography

# Define the Enum types
from sqlalchemy.dialects.postgresql import ENUM
from app.models_enum import *


alembic_metadata = Base.metadata


class CommonBaseModel(Base):
    __abstract__ = True

    @declared_attr
    def id(cls):
        return Column(
            UUID(as_uuid=True),
            primary_key=True,
            default=uuid.uuid4,
            unique=True,
            nullable=False,
        )

    @declared_attr
    def created_at(cls):
        return Column(DateTime(timezone=True), server_default=func.now())

    @declared_attr
    def updated_at(cls):
        return Column(DateTime(timezone=True), onupdate=func.now())

    @declared_attr
    def is_active(cls):
        return Column(Boolean, server_default="TRUE", nullable=False)

    def as_dict(self):
        return {
            column.name: getattr(self, column.name) for column in self.__table__.columns
        }


# Auditlog Service
class AuditLog(CommonBaseModel):
    __tablename__ = "api_audit_logs"
    method = Column(String, index=True)
    url = Column(String, index=True)
    headers = Column(Text)
    query_params = Column(Text)
    body = Column(Text)
    response = Column(Text)
    user_id = Column(UUID(as_uuid=True))


# Booking Service
class Booking(CommonBaseModel):
    __tablename__ = "bookings"

    sp_id = Column(UUID(as_uuid=True), nullable=True)  # Main service provider id(GTI)
    address_id = Column(UUID(as_uuid=True), nullable=True)  # Main service provider id(GTI)
    cart_ids = Column(JSON, nullable=True)
    booking_order_id = Column(String, nullable=False, unique=True)  # Unique booking order ID
    invoice_id = Column(UUID(as_uuid=True), nullable=True)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    booking_date = Column(Date, nullable=False)
    preferred_arrival_time = Column(Time)
    # description = Column(String, nullable=True)
    user_latitude = Column(Float, nullable=False)
    user_longitude = Column(Float, nullable=False)
    user_address = Column(String, nullable=False)
    locality = Column(String, nullable=True)
    is_rescheduled = Column(Boolean, default=False)
    payment_type = Column(ENUM(PaymentType), nullable=True, default=PaymentType.CASH)
    status = Column(ENUM(BookingStatus), default=BookingStatus.REQUESTED)
    requested_time = Column(DateTime, default=datetime.utcnow)
    updated_time = Column(DateTime, onupdate=datetime.utcnow)
    # description_for_negotiation = Column(Text, nullable=True)
    # pricing_type = Column(ENUM(PricingType), nullable=False, default=PricingType.FIXED, server_default='FIXED')
    request_type = Column(ENUM(ServiceRequestType))
    service_list = Column(JSONB, nullable=True)
    assigned_agent_id = Column(UUID(as_uuid=True), nullable=True)



class BookingCancellation(CommonBaseModel):
    __tablename__ = "booking_cancellation"

    user_id = Column(UUID(as_uuid=True), nullable=False)
    artisan_id = Column(UUID(as_uuid=True), nullable=True)
    agent_id = Column(UUID(as_uuid=True))  # who cancelled from admin panel
    booking_id = Column(UUID(as_uuid=True), ForeignKey("bookings.id"), nullable=False)
    invoice_id = Column(UUID(as_uuid=True), nullable=False)
    invoice_item_id = Column(UUID(as_uuid=True), nullable=True)
    transaction_id = Column(UUID(as_uuid=True))
    payment_id = Column(UUID(as_uuid=True))
    # booking = relationship("Booking", back_populates="cancellation")
    cancellation_reason_id = Column(
        UUID(as_uuid=True), ForeignKey("booking_cancellation_reasons.id"), nullable=True
    )
    user_type = Column(ENUM(UserType), nullable=True)  # Who cancelled the booking
    status = Column(
        ENUM(BookingCancellationStatus), default=BookingCancellationStatus.PENDING
    )
    # assigned_agent_id = Column(UUID(as_uuid=True))
    payment_method = Column(String, nullable=True)
    booking_amount = Column(Float, nullable=True)
    refund_amount = Column(Float, nullable=True)
    # penalty_user_id = Column(UUID(as_uuid=True))  # user or artisan id
    # penalty_user_type = Column(ENUM(UserType), nullable=True)  # On user or artisan
    # penalty = Column(Float, nullable=True)
    # penalty_needed = Column(Boolean, default=False)
    # refund_needed = Column(Boolean, default=False)
    resolved_at = Column(DateTime(timezone=True))
    description = Column(Text, nullable=True)
    # attachments = Column(String, nullable=True)
    reason_for_refund = Column(String, nullable=True)
    # reason_for_penalty = Column(String, nullable=True)
    # is_auto_cancel = Column(Boolean, default=False)
    comments = Column(Text, nullable=True)  # By user or artisan
    is_full_booking_cancel = Column(Boolean, default=False, server_default=text("false"))


class BookingCancellationReason(CommonBaseModel):
    __tablename__ = "booking_cancellation_reasons"

    reason = Column(String, nullable=False)
    user_type = Column(
        ENUM(UserType), nullable=True
    )  # Cancellation user type, user or artisan
    penalty_needed = Column(Boolean, default=False)
    apply_penalty_on = Column(ENUM(UserType), nullable=True)  # On user or artisan
    is_negotiate_cancel = Column(Boolean, default=False)
    is_notify_cancel = Column(Boolean, default=False)


class BookingCancellationHistory(CommonBaseModel):
    __tablename__ = "booking_cancellation_history"

    booking_cancellation_id = Column(
        UUID(as_uuid=True), ForeignKey("booking_cancellation.id"), nullable=False
    )
    description = Column(Text, nullable=True)
    user_name = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)


class BookingHistory(CommonBaseModel):
    __tablename__ = "booking_history"

    booking_id = Column(UUID(as_uuid=True), ForeignKey("bookings.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    artisan_id = Column(UUID(as_uuid=True), nullable=False)
    service_id = Column(UUID(as_uuid=True), nullable=False)
    reason = Column(String, nullable=True)
    status = Column(
        ENUM(BookingStatus), default=BookingStatus.REQUESTED
    )  # pending, confirmed, rejected, cancelled


# Notification Service
class Notification(CommonBaseModel):
    __tablename__ = "notification"

    title = Column(String, nullable=False)
    message = Column(String, nullable=False)
    notification_type = Column(ENUM(NotificationType), nullable=False)
    phone_number = Column(String, nullable=True)
    email = Column(String, nullable=True)
    sender_id = Column(String, nullable=True)
    send_at = Column(TIMESTAMP(timezone=True), nullable=True)
    data = Column(JSONB, nullable=True)
    fcm_request_type = Column(String, nullable=True)
    user_id = Column(UUID(as_uuid=True), nullable=True)
    is_read = Column(Boolean, default=False)


# Payment Service
class Payment(CommonBaseModel):
    __tablename__ = "payments"

    user_id = Column(UUID(as_uuid=True), nullable=False)   
    status = Column(ENUM(StatusType), nullable=False)
    invoice_id = Column(UUID(as_uuid=True), nullable=False)
    invoice_item_id = Column(UUID(as_uuid=True), nullable=True)  # Optional, if payment is for a specific service 
    payment_method = Column(ENUM(PaymentMethodType), nullable=False)
    amount = Column(Float, nullable=False)
    payment_date = Column(DateTime(timezone=True), nullable=True)
    payment_details = Column(JSONB, nullable=True)
    currency = Column(ENUM(CurrencyType), nullable=False)
    is_multiple = Column(Boolean, default=False, nullable=False)  # For multiple payments

class Refund(CommonBaseModel):
    __tablename__ = "refunds"

    user_id = Column(UUID(as_uuid=True), nullable=False)   
    status = Column(ENUM(StatusType), nullable=False)
    invoice_id = Column(UUID(as_uuid=True), nullable=False)
    payment_method = Column(ENUM(PaymentMethodType), nullable=False)
    amount = Column(Float, nullable=False)
    refund_date = Column(DateTime(timezone=True), nullable=True)
    refund_details = Column(JSONB, nullable=True)

class Transaction(CommonBaseModel):
    __tablename__ = "transactions"

    payment_id = Column(UUID(as_uuid=True), ForeignKey("payments.id"), nullable=True)
    from_entity = Column(String, nullable=False)
    to_entity = Column(String, nullable=False)
    transaction_type = Column(ENUM(TransactionTypeType), nullable=False)
    payment_method = Column(ENUM(PaymentMethodType), nullable=False)
    # gateway_response = Column(JSONB, nullable=True)
    # gateway_status = Column(String, nullable=True)
    status = Column(ENUM(StatusType), nullable=False)
    amount = Column(Float, nullable=False)
    transaction_details = Column(JSONB, nullable=True)
    transaction_status = Column(String, nullable=True)  # e.g. "PENDING", "COMPLETED", "FAILED"
    txn_ref_no = Column(String, nullable=True)  # 
    cancellation_ref_id = Column(UUID(as_uuid=True), nullable=True)  # For cancellation payments, store the cancellation ID here

    


class Account(CommonBaseModel):
    __tablename__ = "accounts"
    account_type = Column(ENUM(AccountTypeType), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    balance = Column(Float, nullable=False)
    currency = Column(ENUM(CurrencyType), nullable=False)
    account_details = Column(JSONB, nullable=True)
    status = Column(ENUM(AccountStatusType), nullable=False)


# Profile Service
class Users(CommonBaseModel):
    __tablename__ = "users"

    auth_id = Column(String, nullable=True)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    email = Column(String, unique=True, index=True, nullable=True)
    phone_number = Column(String, unique=True, index=True, nullable=True)
    country_code = Column(String, nullable=True)
    primary_location = Column(String, nullable=True)
    # address = Column(String, nullable=True)
    status = Column(ENUM(UserStatusType), server_default="ACTIVE")
    reason = Column(String, nullable=True)
    rating = Column(Integer)
    profile_pic = Column(String)
    profile_thumbnail = Column(String)
    locations = Column(JSONB, nullable=True, default=list)
    notification_uuid = Column(String, nullable=True)
    devices = relationship("Device", back_populates="user")
    is_profile_complete = Column(Boolean, server_default="FALSE", nullable=False)
    is_confirmed = Column(Boolean, server_default="FALSE", nullable=False)


class ServiceProvider(CommonBaseModel):
    __tablename__ = "service_provider"

    auth_id = Column(String)
    first_name = Column(String)
    last_name = Column(String)
    email = Column(String, unique=True, index=True, nullable=True)
    gender = Column(ENUM(Gender))
    dob = Column(Date)
    phone_number = Column(String, unique=True, index=True)
    country_code = Column(String, nullable=True)
    primary_location = Column(String)
    skill = Column(String)
    skill_level = Column(String)
    experience = Column(Float, default=0.0)
    about_us = Column(Text)
    reg_code = Column(String)
    rating = Column(Integer)
    status = Column(ENUM(ServiceProviderStatusType), server_default="CREATED")
    live_status = Column(String)
    profile_pic = Column(String)
    profile_pic_thumbnail = Column(String)
    license = Column(String)
    certificate = Column(String)
    govt_id = Column(String)
    police_report = Column(String)
    guaranteed_doc = Column(String)
    bank_acc_holder_name = Column(String)
    bank_name = Column(String)
    bank_acc_no = Column(String)
    bank_branch_code = Column(String)
    bank_swift_code = Column(String)
    bank_acc_type = Column(String)

    work_from_hrs = Column(Time)  # Start work time (24-hour format)
    work_to_hrs = Column(Time)  # End work time (24-hour format)
    break_from_hrs = Column(Time)  # Start break time
    break_to_hrs = Column(Time)  # End break time
    weekdays = Column(
        ARRAY(Integer)
    )  # Stores weekdays as numbers (1 = Monday, 7 = Sunday)

    latitude = Column(Float, nullable=False)  # Stores Latitude
    longitude = Column(Float, nullable=False)  # Stores Longitude
    notification_uuid = Column(String, nullable=True)
    reason = Column(String, nullable=True)
    location = Column(
        Geography(geometry_type="POINT", srid=4326)
    )  # Stores lat/lng as a point

    devices = relationship("Device", back_populates="service_provider")
    # service_mappings = relationship(
    #     "ServiceProviderServiceMapping",
    #     back_populates="service_provider",
    #     cascade="all, delete-orphan",
    # )
    # service_mappings = relationship(
    #     "ServiceProviderServiceMapping",
    #     back_populates="service_provider",
    #     cascade="all, delete-orphan",
    # )
    # Add relationship to rate sheets
    rate_sheets = relationship("ServiceRateSheet", back_populates="artisan", cascade="all, delete-orphan")
    # Add relationship to taxes
    taxes = relationship("Tax", back_populates="service_provider", cascade="all, delete-orphan")
    is_profile_complete = Column(Boolean, server_default="FALSE", nullable=False)
    is_confirmed = Column(Boolean, server_default="FALSE", nullable=False)
    # is_fav = Column(Boolean, server_default='FALSE', nullable=False)  need to be add on


class ServiceProviderServiceMapping(CommonBaseModel):
    __tablename__ = "service_provider_service_mapping"

    service_provider_id = Column(UUID(as_uuid=True), ForeignKey("user_profiles.id"), nullable=False )
    # services_id = Column(UUID(as_uuid=True))  # Assuming services table exists
    services_id = Column(UUID(as_uuid=True), nullable=False)
    # user_profile = relationship(
    #     "UserProfiles", back_populates="service_provider_service_mappings"
    # )

    # services_id = relationship("Services", back_populates="service_provider")


class ServiceProviderLeave(CommonBaseModel):
    __tablename__ = "service_provider_leave"

    service_provider_id = Column(
        UUID(as_uuid=True),
        ForeignKey("service_provider.id", ondelete="CASCADE"),
        nullable=False,
    )
    leave_date = Column(Date, nullable=False)
    reason = Column(String, nullable=True)

    # Add relationship to ServiceProvider
    service_provider = relationship("ServiceProvider", backref="leaves")

    __table_args__ = (
        # Ensure a service provider can't have multiple leaves on the same date
        UniqueConstraint("service_provider_id", "leave_date", name="unique_leave_date"),
    )


class Admin(CommonBaseModel):
    __tablename__ = "admin"

    auth_id = Column(String, nullable=True)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    email = Column(String, unique=True, index=True, nullable=True)
    phone_number = Column(String, unique=True, index=True, nullable=True)
    gender = Column(ENUM(Gender))
    role = Column(ENUM(AdminRole))
    status = Column(ENUM(ServiceProviderStatusType), server_default="CREATED")
    reason = Column(String, nullable=True)


class Device(CommonBaseModel):
    __tablename__ = "device"

    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=True
    )
    service_provider_id = Column(
        UUID(as_uuid=True),
        ForeignKey("service_provider.id", ondelete="CASCADE"),
        nullable=True,
    )

    type = Column(ENUM(DeviceType))
    notification_token = Column(String, index=True, nullable=False)
    info = Column(JSONB)

    # Relationships
    user = relationship("Users", back_populates="devices")
    service_provider = relationship("ServiceProvider", back_populates="devices")
    notification_preferences = relationship("NotificationPreferences", back_populates="device")


# Servicemanagement Service
class Category(CommonBaseModel):
    __tablename__ = "category"

    name = Column(String, nullable=False)
    services = relationship(
        "Services",
        back_populates="category",
        lazy="selectin",
        cascade="all, delete-orphan",
    )
    parent_id = Column(UUID(as_uuid=True))
    banner = Column(String, nullable=True)


class Services(CommonBaseModel):
    __tablename__ = "services"

    parent_id = Column(UUID(as_uuid=True), ForeignKey("category.id"), nullable=False)
    name = Column(String, nullable=False)
    banner = Column(String, nullable=True)
    category = relationship("Category", back_populates="services")
    duration = Column(Integer)
    price = Column(Float)
    description = Column(Text)
    is_multiple = Column(Boolean, server_default="FALSE")
    service_code = Column(String, nullable=True, unique=True)
    booking_fee_percentage = Column(Float)
    
    # Add relationship to rate sheets
    rate_sheets = relationship("ServiceRateSheet", back_populates="service", cascade="all, delete-orphan")


class ServiceRateSheet(CommonBaseModel):
    __tablename__ = "service_rate_sheet"

    service_id = Column(UUID(as_uuid=True), ForeignKey("services.id"), nullable=False)
    artisan_id = Column(UUID(as_uuid=True), ForeignKey("service_provider.id"), nullable=False)
    pricing_type = Column(ENUM(PricingType), nullable=False, default=PricingType.FIXED, server_default='FIXED')
    price_per_unit = Column(Float, nullable=True)
    service_type = Column(ENUM(ServiceType), nullable=False, default=ServiceType.REGULAR, server_default='REGULAR')
    units = Column(ENUM(ServiceUnits),nullable=True)
    booking_fee = Column(Float, nullable=False)
    minimum_booking_time = Column(Float, nullable=False, default=60.0)  # Default to 60 minutes

    # Relationships
    service = relationship("Services", back_populates="rate_sheets")
    artisan = relationship("ServiceProvider", back_populates="rate_sheets")
    minimum_booking_time = Column(Float, nullable=False, default=1.0)  # in hours


# # Support Service
class Disputes(CommonBaseModel):
    __tablename__ = "disputes"

    case_number = Column(String, unique=True, nullable=False)
    booking_id = Column(UUID(as_uuid=True), nullable=False)
    customer_id = Column(UUID(as_uuid=True), nullable=False)
    customer_name = Column(String, nullable=True)
    merchant_id = Column(UUID(as_uuid=True), nullable=False)
    merchant_name = Column(String, nullable=True)

    dispute_type = Column(ENUM(DisputeType), nullable=False)
    dispute_sub_type = Column(String, nullable=False)
    status = Column(ENUM(DisputeStatus), default=DisputeStatus.OPEN)
    priority = Column(ENUM(DisputePriority), default=DisputePriority.MEDIUM)

    description = Column(Text, nullable=True)
    supporting_documents = Column(
        String, nullable=True
    )  # Stored as comma-separated URLs

    user_type = Column(ENUM(UserType), nullable=False)
    assigned_to = Column(String, nullable=True)

    # Transaction details (MVP 2)
    transaction_id = Column(String, nullable=True)
    transaction_date = Column(DateTime(timezone=True), nullable=True)
    transaction_amount = Column(Float, nullable=True)
    disputed_amount = Column(Float, nullable=True)
    currency = Column(String, nullable=True)

    # Resolution details
    resolution_date = Column(DateTime(timezone=True), nullable=True)
    resolution_notes = Column(Text, nullable=True)
    refund_amount = Column(Float, nullable=True)
    chargeback_status = Column(ENUM(ChargebackStatus), nullable=True)

    # Audit fields
    created_by = Column(String, nullable=True)
    updated_by = Column(String, nullable=True)
    issue_category_id = Column(
        UUID(as_uuid=True), ForeignKey("issue_categories.id"), nullable=False
    )

    # Relationships
    comments = relationship(
        "DisputeComment", back_populates="dispute", cascade="all, delete-orphan"
    )
    history = relationship(
        "DisputeHistory", back_populates="dispute", cascade="all, delete-orphan"
    )


class DisputeHistory(CommonBaseModel):
    __tablename__ = "dispute_history"

    dispute_id = Column(UUID(as_uuid=True), ForeignKey("disputes.id"), nullable=False)
    event_code = Column(String, ForeignKey("dispute_event_types.code"), nullable=False)
    user = Column(String, nullable=False)
    date = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    dispute = relationship("Disputes", back_populates="history")
    event_type = relationship("DisputeEventTypeModel")


class IssueTypes(CommonBaseModel):
    __tablename__ = "issue_types"

    category_id = Column(
        UUID(as_uuid=True), ForeignKey("issue_categories.id"), nullable=False
    )
    code = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    # Relationship with category
    category_rel = relationship("IssueCategory", back_populates="issue_types")


class IssueCategory(CommonBaseModel):
    __tablename__ = "issue_categories"

    name = Column(
        String, nullable=False
    )  # e.g., "Booking Issues", "Payment and Refund"
    code = Column(
        String, nullable=False
    )  # e.g., "BOOKING_ISSUES", "PAYMENT_AND_REFUND"
    description = Column(Text, nullable=True)
    user_type = Column(ENUM(UserType), nullable=True)
    default_priority = Column(
        ENUM(DisputePriority), nullable=False, default=DisputePriority.MEDIUM
    )
    # Relationship with issue types
    issue_types = relationship("IssueTypes", back_populates="category_rel")


class DisputeComment(CommonBaseModel):
    __tablename__ = "dispute_comments"

    dispute_id = Column(UUID(as_uuid=True), ForeignKey("disputes.id"), nullable=False)
    author_id = Column(UUID(as_uuid=True), nullable=False)
    author_name = Column(String, nullable=False)
    comment = Column(Text, nullable=False)
    date = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship with dispute
    dispute = relationship("Disputes", back_populates="comments")


class DisputeEventTypeModel(CommonBaseModel):
    __tablename__ = "dispute_event_types"

    code = Column(String, nullable=False, unique=True)  # e.g., "DISPUTE_CREATED"
    name = Column(String, nullable=False)  # e.g., "Dispute Created"
    description = Column(Text, nullable=True)


class SubCategory(CommonBaseModel):
    __tablename__ = "subcategories_issues"

    category_id = Column(
        UUID(as_uuid=True), ForeignKey("category_issues.id"), nullable=False
    )
    name = Column(
        String, nullable=False
    )  # e.g., "App crashing", "Payment gateway issue"
    description = Column(Text, nullable=True)
    # Relationships
    category = relationship("IssueCategorys", back_populates="subcategories")
    issues = relationship(
        "Issues", back_populates="subcategory", cascade="all, delete-orphan"
    )


class IssueCategorys(CommonBaseModel):
    __tablename__ = "category_issues"

    name = Column(String, nullable=False)  # e.g., "Accessibility Issues"
    code = Column(String, nullable=False, unique=True)  # e.g., "accessibility"
    description = Column(Text, nullable=True)
    default_priority = Column(
        ENUM(IssuePriority), nullable=False, default=IssuePriority.MEDIUM
    )
    # Relationships
    subcategories = relationship(
        "SubCategory", back_populates="category", cascade="all, delete-orphan"
    )
    issues = relationship("Issues", back_populates="category")


class Issues(CommonBaseModel):
    __tablename__ = "issues"

    customer_id = Column(UUID(as_uuid=True), nullable=False)
    # custom`er_name = Column(String, nullable=True)
    case_number = Column(String, unique=True, nullable=False)

    # Linking to Category and SubCategory
    category_id = Column(
        UUID(as_uuid=True), ForeignKey("category_issues.id"), nullable=False
    )
    subcategory_id = Column(
        UUID(as_uuid=True), ForeignKey("subcategories_issues.id"), nullable=True
    )

    # Relationships
    category = relationship("IssueCategorys", back_populates="issues")
    subcategory = relationship("SubCategory", back_populates="issues")
    comments = relationship(
        "IssueComments", back_populates="issue", cascade="all, delete-orphan"
    )
    history = relationship(
        "IssueHistory", back_populates="issue", cascade="all, delete-orphan"
    )

    description = Column(Text, nullable=True)
    supporting_documents = Column(
        String, nullable=True
    )  # Stored as comma-separated URLs
    assigned_to = Column(UUID(as_uuid=True), nullable=True)  # Assigned support team ID
    status = Column(ENUM(IssueStatus), nullable=False, default=IssueStatus.OPEN)
    priority = Column(ENUM(IssuePriority), default=IssuePriority.MEDIUM)
    user_type = Column(ENUM(UserType), nullable=False)

    resolution_notes = Column(Text, nullable=True)
    resolution_date = Column(DateTime(timezone=True), nullable=True)


class IssueComments(CommonBaseModel):
    __tablename__ = "issue_comments"

    issue_id = Column(UUID(as_uuid=True), ForeignKey("issues.id", ondelete="CASCADE"))
    author_id = Column(UUID(as_uuid=True))
    author_name = Column(String, nullable=True)
    comment = Column(String, nullable=False)
    date = Column(DateTime(timezone=True), server_default=func.now())
    issue = relationship("Issues", back_populates="comments")


class IssueHistory(CommonBaseModel):
    __tablename__ = "issue_history"

    issue_id = Column(UUID(as_uuid=True), ForeignKey("issues.id"), nullable=False)
    event_code = Column(String, nullable=False)
    event_name = Column(String, nullable=True)  # e.g., "ISSUE_CREATED"
    user = Column(String, nullable=False)
    date = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    issue = relationship("Issues", back_populates="history")

# class BusinessServiceMapping(CommonBaseModel):
#     __tablename__ = "business_service_mapping"
#     business_id = Column(
#         UUID(as_uuid=True), ForeignKey("business.id"), nullable=False
#     )
#     service_id = Column(UUID(as_uuid=True), ForeignKey("services.id"), nullable=False)
#     business_provider = relationship("Business", back_populates="service_mappings")


# class BusinessArea(CommonBaseModel):
#     __tablename__ = "business_area"
#     area_name = Column(String, unique=True)


# class Business(CommonBaseModel):
#     __tablename__ = "business"
#     auth_id = Column(String, nullable=True)
#     email = Column(String, unique=True, index=True, nullable=True)
#     country_code = Column(String, nullable=True)
#     phone_number = Column(String, unique=True, index=True)
#     full_name = Column(String)
#     business_name = Column(String, nullable=True)
#     business_type = Column(ENUM(BusinessType))
#     business_registration_number = Column(String, nullable=True)
#     ghana_post_gps_address = Column(String, nullable=True)
#     business_location = Column(String, nullable=True)
#     tax_identification_number = Column(String, nullable=True, unique=True)
#     services_offered = Column(JSONB)
#     service_area_ids = Column(JSONB)
#     business_registration_document = Column(String, nullable=True)
#     business_logo = Column(String, nullable=True)
#     portfolio_image =  Column(ARRAY(String))
#     id_proof = Column(String, nullable=True)
#     signature = Column(String, nullable=True)
#     submit_date = Column(DateTime(timezone=True), nullable=True)
#     page_position = Column(String, nullable=True)
#     status = Column(ENUM(BusinessProviderStatus), default=BusinessProviderStatus.IN_PROGRESS)
#     reason = Column(String, nullable=True)
#     service_mappings = relationship("BusinessServiceMapping", back_populates="business_provider")


# Core Module for JC
class UserProfiles(CommonBaseModel):
    __tablename__ = "user_profiles"
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    # full_name = Column(String, nullable=True) # need to add gender and date of birth
    country_code = Column(String, nullable=True)
    phone_number = Column(String, unique=True, index=True, nullable=True)
    notification_token = Column(String, nullable=True) # FCM token to send notifications
    email = Column(String, unique=True, index=True, nullable=True)
    gender=Column(ENUM(Gender, name="gender", create_type=False))
    dob=Column(String,nullable=True)
    location=Column(String,nullable=True)
    status = Column(
        ENUM(UserStatusType, name="userstatustype", create_type=False),
        server_default=UserStatusType.ACTIVE.value
    )

    last_active_datetime = Column(DateTime(timezone=True), nullable=True)
    profile_image_url = Column(String)
    auth_id = Column(String, unique=True, index=True, nullable=False)
    hash_password = Column(String, nullable=True)
    is_profile_complete = Column(Boolean, server_default="FALSE", nullable=False)
    is_available = Column(Boolean, default=True)

    # Role foreign key
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id"))
    role = relationship("Roles", back_populates="users")
    user_preferences = relationship("UserPreferences", back_populates="user")
    notification_preferences = relationship("NotificationPreferences", back_populates="user")
    artisan_detail = relationship("ArtisanDetail", back_populates="user", uselist=False)
    addresses = relationship("Address", back_populates="user", cascade="all, delete-orphan")
    business_details= relationship("ServiceProviders", back_populates="business_profile", uselist=False)
    # Add relationship to devices
#     service_provider_service_mappings = relationship(
#     "ServiceProviderServiceMapping",
#     back_populates="user_profile",
#     cascade="all, delete-orphan"
# )

class Address(CommonBaseModel):
    __tablename__ = "address"
    # user id for creating address
    name = Column(String, nullable=True)
    locality = Column(String, nullable=True)
    details = Column(JSONB, nullable=True)
    is_primary = Column(Boolean, default=False, nullable=False) 
    user_id = Column(UUID(as_uuid=True), ForeignKey("user_profiles.id"), nullable=False)
    user = relationship("UserProfiles", back_populates="addresses")


class Roles(CommonBaseModel):
    __tablename__ = "roles"
    role_name = Column(String, unique=True, nullable=False) # ,Change to ENUMS (User, Artisan (Comes under GTI Services), Busineess, admin, Super-Admin, Super-User, Service Provider(GTI))

    # Relationship to Permissions
    permissions = relationship("Permissions", back_populates="role", cascade="all, delete")
    users = relationship("UserProfiles", back_populates="role")


class Permissions(CommonBaseModel):
    __tablename__ = "permissions"
    user_id = Column(UUID(as_uuid=True), nullable=True)
    policy_name = Column(String, nullable=False)
    permission = Column(JSON, nullable=False)

    # Foreign key to Role
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id"))
    role = relationship("Roles", back_populates="permissions")

class UsersBlacklist(CommonBaseModel):
    __tablename__ = "users_blacklist"

    user_id = Column(UUID(as_uuid=True), nullable=False)
    reason = Column(String, nullable=True)
    is_revoked = Column(Boolean, default=False)
    last_revoked_datetime = Column(DateTime(timezone=True), nullable=True)
    blacklisted_by = Column(UUID(as_uuid=True), nullable=True)
    # User id relationship

class Tax(CommonBaseModel):
    __tablename__ = "taxes"
    
    sp_id = Column(UUID(as_uuid=True), ForeignKey("service_provider.id"), nullable=False)
    name = Column(String, nullable=False)
    percentage = Column(Float, nullable=False)
    type = Column(ENUM(TaxType), nullable=False, default=TaxType.STANDARD)
    
    # Relationship
    service_provider = relationship("ServiceProvider", back_populates="taxes")

class Invoice(CommonBaseModel):
    __tablename__ = "invoices"
    
    sp_id = Column(String)
    payment_method = Column(ENUM(PaymentType), nullable=True, default=PaymentType.CASH)
    # payment_method = Column(String)
    biller_id = Column(String)
    customer_id = Column(String)
    tax_percentage = Column(Float)
    tax_amount = Column(Float)
    platform_fee_percentage = Column(Float)
    platform_fee_amount = Column(Float)
    base_fee = Column(Float)
    discount_amount = Column(Float)
    booking_fee_percentage = Column(Float)
    booking_fee = Column(Float)
    pending_amount = Column(Float, default=0.0)
    total_amount = Column(Float)
    payment_status = Column(ENUM(InvoiceStatusEnum), nullable=False)
    booking_id = Column(String)
    items = relationship("InvoiceItem", back_populates="invoice")


class InvoiceItem(CommonBaseModel):
    __tablename__ = "invoice_items"

    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id"),nullable=False)
    parent_id = Column(UUID(as_uuid=True), nullable=True)
    service_id = Column(String)
    quantity = Column(Integer)
    price = Column(Float)
    description = Column(String)
    tax_percentage = Column(Float)
    tax_amount = Column(Float)
    platform_fee_percentage = Column(Float)
    platform_fee_amount = Column(Float)
    booking_fee = Column(Float)
    booking_fee_percentage = Column(Float)
    discount_amount = Column(Float)
    total_amount = Column(Float)
    is_agent_created = Column(Boolean)
    status = Column(ENUM(InvoiceItemStatus), default=InvoiceItemStatus.PENDING)
    invoice = relationship("Invoice", back_populates="items")

class ArtisanAssigned(CommonBaseModel):
    __tablename__ = "artisan_assigned"

    invoice_item_id = Column(UUID(as_uuid=True), ForeignKey("invoice_items.id"), nullable=False)
    service_id = Column(UUID(as_uuid=True), ForeignKey("services.id"), nullable=False)
    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id"), nullable=False)
    artisan_id = Column(String, nullable=False)
    start_time = Column(DateTime(timezone=True), nullable=True)
    end_time = Column(DateTime(timezone=True), nullable=True)
    start_otp = Column(String, nullable=True)
    end_otp = Column(String, nullable=True)
    service_start = Column(DateTime(timezone=True), nullable=True)
    service_end = Column(DateTime(timezone=True), nullable=True)
    service_start_artisan_latitude = Column(Float, nullable=True)
    service_start_artisan_longitude = Column(Float, nullable=True)
    service_end_artisan_latitude = Column(Float, nullable=True)
    service_end_artisan_longitude = Column(Float, nullable=True)
    status = Column(ENUM(ArtisanAssignStatus), nullable=False)


class BusinessServiceMapping(CommonBaseModel):
    __tablename__ = "business_service_mapping"
    business_user_id = Column(
        UUID(as_uuid=True), ForeignKey("user_profiles.id"), nullable=False
    )
    service_id = Column(UUID(as_uuid=True), ForeignKey("services.id"), nullable=False)
    service_provider_id = Column(
        UUID(as_uuid=True),
        ForeignKey("Service_Providers.id", ondelete="CASCADE"),
        nullable=False
    )
    business_provider = relationship("ServiceProviders", back_populates="service_mappings")


class BusinessArea(CommonBaseModel):
    __tablename__ = "business_area"
    area_name = Column(String, unique=True)


class ServiceProviders(CommonBaseModel):
    __tablename__ = "Service_Providers"
    
    user_id = Column(UUID(as_uuid=True), ForeignKey("user_profiles.id"), nullable=False, unique=True, index=True)
    business_name = Column(String, nullable=True)
    # country_code = Column(String, nullable=True)
    # business_phone = Column(String, unique=True, index=True, nullable=False)
    # business_email = Column(String, unique=True, index=True, nullable=True)
    # address = Column(String, nullable=True)
    # full_name = Column(String, nullable=True)
    business_type = Column(ENUM(BusinessType), nullable=True)
    business_registration_number = Column(String, nullable=True)
    ghana_post_gps_address = Column(String, nullable=True)
    business_location = Column(String, nullable=True)
    tax_identification_number = Column(String, nullable=True, unique=True)
    services_offered = Column(JSONB, nullable=True)
    service_area_ids = Column(JSONB, nullable=True)
    business_registration_document = Column(String, nullable=True)
    business_logo = Column(String, nullable=True)
    portfolio_image = Column(JSONB, nullable=True)
    id_proof = Column(String, nullable=True)
    signature = Column(String, nullable=True)
    submit_date = Column(DateTime(timezone=True), nullable=True)
    page_position = Column(String, nullable=True)
    status = Column(ENUM(BusinessProviderStatus), default=BusinessProviderStatus.IN_PROGRESS, nullable=True)
    reason = Column(String, nullable=True)
    business_profile = relationship("UserProfiles", back_populates="business_details")
    # service_mappings = relationship("BusinessServiceMapping", back_populates="business_provider")
    service_mappings = relationship("BusinessServiceMapping", back_populates="business_provider", cascade="all, delete-orphan")





class SPBranches(CommonBaseModel):
    __tablename__ = "SP_branches"

    SP_ID = Column(UUID(as_uuid=True), ForeignKey("Service_Providers.id"), index=True, nullable=False)
    Branch_Location = Column(String, nullable=False)
    Address = Column(String, nullable=False)
    Phone = Column(String, unique=True, index=True, nullable=False)
    is_main = Column(Boolean, nullable=False)
    Region = Column(String, nullable=False) #newly added not exits in c2h present in lld

class SPUsers(CommonBaseModel):
    __tablename__ = "SP_users"

    SP_ID = Column(UUID(as_uuid=True), ForeignKey("Service_Providers.id"), index=True, nullable=False)
    User_ID = Column(UUID(as_uuid=True), ForeignKey("user_profiles.id"), index=True, nullable=False)
    SPB_ID = Column(UUID(as_uuid=True), ForeignKey("SP_branches.id"), index=True, nullable=False)
    Region = Column(String, nullable=False) #newly added not exits in c2h present in lld
    
class KycDetails(CommonBaseModel):
    __tablename__ = "KYC_details"
 
    user_id = Column(UUID(as_uuid=True), nullable=False)
    KYC_type = Column(ENUM(KYCTypes), nullable=False)
    document = Column(String, nullable=False)
    validation_status = Column(ENUM(ValidationStatus))
    validation_comments = Column(String, nullable=True)
    expired = Column(String, nullable=True)

class ArtisanDetail(CommonBaseModel):
    __tablename__ = "artisan_details"

    skill = Column(String, nullable=True)
    skill_level = Column(String, nullable=True)
    experience = Column(Float, default=0.0)
    about_us = Column(Text, nullable=True)
    reg_code = Column(String, nullable=True)
    live_status = Column(String, nullable=True)
    license = Column(String, nullable=True)
    work_from_hrs = Column(Time, nullable=True)
    work_to_hrs = Column(Time, nullable=True)
    break_from_hrs = Column(Time, nullable=True)
    break_to_hrs = Column(Time, nullable=True)
    weekdays = Column(ARRAY(Integer), nullable=True)

    is_confirmed = Column(Boolean, server_default="FALSE", nullable=False)

    user_id = Column(UUID(as_uuid=True), ForeignKey("user_profiles.id"), nullable=False, unique=True)
    user = relationship("UserProfiles", back_populates="artisan_detail")


class UserPreferences(CommonBaseModel):
    __tablename__ = "user_preferences"

    user_id = Column(UUID(as_uuid=True), ForeignKey("user_profiles.id"), nullable=False)
    currency = Column(String, nullable=False)
    language = Column(String, nullable=False)
    is_otp_enabled = Column(Boolean, nullable=False, default=True)
    country = Column(String, nullable=False)

    # Relationships
    user = relationship("UserProfiles", back_populates="user_preferences")



#Notification_Preferences
class NotificationPreferences(CommonBaseModel):
    __tablename__ = "notification_preferences"

    user_id = Column(UUID(as_uuid=True), ForeignKey("user_profiles.id"), nullable=False)
    device_id = Column(UUID(as_uuid=True), ForeignKey("device.id"), nullable=False)
    enable_push_notifications = Column(Boolean, nullable=False, default=True)
    enable_email_notifications = Column(Boolean, nullable=False, default=True)
    enable_sms_notifications = Column(Boolean, nullable=False, default=True)

    # Relationships
    user = relationship("UserProfiles", back_populates="notification_preferences")
    device = relationship("Device", back_populates="notification_preferences")

class Cart(CommonBaseModel):
    __tablename__ = "cart"

    user_id = Column(UUID(as_uuid=True), nullable=False)
    service_id = Column(UUID(as_uuid=True), nullable=False)
    qty = Column(Integer, nullable=False)
    description = Column(String, nullable=True)
    attachments = Column(JSONB, nullable=True)
    status = Column(ENUM(CartStatus), default=CartStatus.PENDING)


class Region(CommonBaseModel):
    __tablename__ = "regions"
     
    name = Column(String, nullable=False, unique=True)
    short_name = Column(String, nullable=False)
    area_codes = Column(JSONB, nullable=False)


class SPRatings(CommonBaseModel):
    __tablename__ = "SP_Ratings"

    user_id = Column(UUID(as_uuid=True))
    booking_id = Column(UUID(as_uuid=True), nullable=True)
    # service_id = Column(UUID(as_uuid=True), nullable=True)
    # invoice_item_id = Column(UUID(as_uuid=True) , nullable=True)
    dispute_id = Column(UUID(as_uuid=True), nullable=True)  # Optional, if rating is related to a dispute
    issue_id = Column(UUID(as_uuid=True), nullable=True)  # Optional, if rating is related to an issue
    ref_id = Column(UUID(as_uuid=True))    # rating id
    # rating_type = Column(String, nullable=False)
    # rating_info = Column(JSON, nullable=True)



class Ratings(CommonBaseModel):
    __tablename__ = "ratings"

    rating = Column(Float, nullable=False)
    rating_tags = Column(String, nullable=True)
    feedback = Column(String, nullable=True)
    user_type = Column(ENUM(UserType), nullable=False)

# Agent Service

# class Agent(CommonBaseModel):
#     __tablename__ = "agents"
#     id = Column(Integer, primary_key=True)
#     name = Column(String, nullable=False)
#     is_available = Column(Boolean, default=True)

class Conversation(CommonBaseModel):
    __tablename__ = "conversations"
    
    type = Column(ENUM(ConversationType), nullable=False)
    status = Column(ENUM(ConversationStatus), nullable=False)
    support_ticket_id = Column(Integer, nullable=True)
    assigned_agent_id = Column(UUID(as_uuid=True), ForeignKey("user_profiles.id"), nullable=True)

class Participant(Base):
    __tablename__ = "participants"

    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id"), primary_key=True)
    user_id = Column(UUID(as_uuid=True), primary_key=True)
    role = Column(ENUM(RoleType), nullable=False)
    joined_at = Column(DateTime, server_default=func.now())
    left_at = Column(DateTime, nullable=True)

class Message(CommonBaseModel):
    __tablename__ = "messages"
    
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id"))
    sender_id = Column(UUID(as_uuid=True))
    content = Column(String)
    delivered = Column(Boolean, default=False)
    seen = Column(Boolean, default=False)
    chat_type = Column(ENUM(ChatType))

class ActivityLog(CommonBaseModel):
    __tablename__ = "activity_logs"

    reference_id = Column(UUID(as_uuid=True), nullable=True)
    title = Column(String, nullable=False)  
    description = Column(String, nullable=True)
    customer_id = Column(UUID(as_uuid=True), nullable=False)
    activity_type = Column(ENUM(ActivityType), nullable=False)

class Chatdetails(CommonBaseModel):
    __tablename__ = "chat_details"

    booking_id=Column(UUID(as_uuid=True), nullable=True)
    service_id = Column(UUID(as_uuid=True), nullable=True)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id"), primary_key=True)
    conversation_type = Column(ENUM(ConversationType), nullable=False)
    feedback = Column(String, nullable=True)
    