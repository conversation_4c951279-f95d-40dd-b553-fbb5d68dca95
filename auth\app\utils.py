import uuid
import jwt
import requests
from fastapi import Depends, HTTPException, Head<PERSON>, status
from typing import Annotated, List
import requests
from app.cognito_utils import get_user_attributes
# from database import ClanSessionLocal, RecruiterSessionLocal  


#! for testing purposes only
async def get_id_header(token):
    if not token:
        return {'error': "Token required"}
    try:
        # print("Decoding token...")  # Debug log
        payload = jwt.decode(token, options={"verify_signature": False}, algorithms=["HS256"])
        # print("Token payload:", payload)  # Debug log
        
        # TODO write a wrapper for attribute matching
        # print("Getting user attributes...")  # Debug log
        attributes = await get_user_attributes(payload.get("sub"))
        print("User attributes:", attributes)  # Debug log
        
        # Try to get account_id from custom attribute first, fall back to sub if not found
        account_id = attributes.get("custom:account_id") or attributes.get("sub") or ''
        print("Account ID:", account_id)  # Debug log
        
        if not account_id:
            return {'error': "No account ID found in token"}
            
        return {
            "id": payload.get("sub"),
            "email": payload.get("email"),
            "role": payload.get("role", "admin"),
            "username": payload.get("username"),
            "account_id": account_id,
            "user_role": payload.get("cognito:groups")[0] if payload.get("cognito:groups") else None,
        }
    except jwt.DecodeError as e:
        print("JWT decode error:", str(e))  # Debug log
        return {'error': "Invalid/Expired token format"}
    except Exception as e:
        print("General error:", str(e))  # Debug log
        return {'error': f"Error: {e}"}