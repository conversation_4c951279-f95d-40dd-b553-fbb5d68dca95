aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 813714097634.dkr.ecr.ap-south-1.amazonaws.com


curl -X POST https://eu-central-1drfgo0rka.auth.eu-central-1.amazoncognito.com/oauth2/token -H "Content-Type: application/x-www-form-urlencoded" -d "grant_type=client_credentials&client_id=5g7jhua3cm4hjktg5ghl8v6ouv&client_secret=169a4nkcueg2vncj733905e0mkdsupducf0bjqkcnc2s45gotaan&scope=default-m2m-resource-server-qocz2a/read"

********************
OhwD7r7Fn22fxHdX5BKi4YNa26zy+qa3uO5SOYn7
ap-south-1


aws configure for admincreateuser
********************
Q7uGJY2VpL2nmCn17F7zlqqWbOrU7YwMTkJmugUh
eu-central-1


alembic revision --autogenerate -m "Migration message"
alembic upgrade head

