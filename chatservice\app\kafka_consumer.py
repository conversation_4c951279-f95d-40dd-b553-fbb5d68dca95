from aiokafka import AIOKafkaConsumer
import json
import asyncio
import os

class KafkaAssignmentConsumer:
    def __init__(self, topic: str, ws_manager, bootstrap_servers: str = f"{os.getenv('KAFKA_HOST')}:{os.getenv('KAFKA_PORT')}"):
        self.topic = topic
        self.bootstrap_servers = bootstrap_servers
        self.consumer = None
        self.ws_manager = ws_manager

    async def start(self):
        self.consumer = AIOKafkaConsumer(
            self.topic,
            bootstrap_servers=self.bootstrap_servers,
            group_id="assignment-group",
            value_deserializer=lambda m: json.loads(m.decode("utf-8"))
        )
        await self.consumer.start()
        asyncio.create_task(self.consume())

    async def stop(self):
        if self.consumer:
            await self.consumer.stop()

    async def consume(self):
        async for msg in self.consumer:
            data = msg.value
            agent_id = data.get("agent_id")
            await self.ws_manager.send_to_user(agent_id, {
                "event": data.get("event"),
                "customer_id": data.get("customer_id")
            })
