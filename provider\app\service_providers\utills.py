import uuid
from fastapi import Depends, HTTPException, Header, status
from typing import Annotated, List, Optional
from pydantic import BaseModel

from sqlalchemy.orm import Session
from typing import Any, Dict, Optional
from uuid import UUID


def create_record(db: Session, model, request_dict: Dict[str, Any]):
    """Create a new record in the database"""
    print('calling create record functionnnnnnnn')
    try:
        obj = model(**request_dict)
        db.add(obj)
        db.commit()
        db.refresh(obj)
        return obj
    except Exception as e:
        print(e, 'eeeeeeeeeeeeee')
        db.rollback()
        error_message = f"Database error: {str(e)}"
        print(error_message, 'errrrrrrrrrrrrrrrrrrrrrrrrrrr')
        # raise HTTPException(status_code=500, detail=error_message)
        return error_message


def update_record(db: Session, data, obj):
    """Update an existing record"""
    try:
        for key, value in data.__dict__.items():
            # Skip None or empty string values
            if value is None or (isinstance(value, str) and not value.strip()):
                continue
            # Update the object attribute
            setattr(obj, key, value)

        db.commit()
        db.refresh(obj)
        return obj
    except Exception as e:
        db.rollback()
        error_message = f"Database error: {str(e)}"
        print(error_message, 'errrrrrrrrrrrrrrrrrrrrrrrrrrr')
        return error_message


def delete_record(db: Session, obj):
    """Delete a record from the database"""
    try:
        db.delete(obj)
        db.commit()
        return {"data": "Record deleted successfully"}
    except Exception as e:
        db.rollback()
        error_message = f"Database error: {str(e)}"
        print(error_message, 'errrrrrrrrrrrrrrrrrrrrrrrrrrr')
        return error_message


def check_file_exists(file):
    if file:
        return True
    return False