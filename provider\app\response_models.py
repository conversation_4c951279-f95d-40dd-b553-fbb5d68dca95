from starlette.responses import JSONResponse
from pydantic import BaseModel
from typing import Optional, Any

class StandardResponseModel(BaseModel):
    status: bool
    status_code: int  # HTTP Status Code
    message: str  # Description of the response
    data: Optional[Any] = None  # Any payload data

    def serialize(self):
        return self.dict()
    
    def __iter__(self):
        return iter(self.dict().items())

class ErrorResponseModel(BaseModel):
    status: bool
    status_code: int  # HTTP Status Code
    message: str  # Description of the response
    error: Optional[Any] = None  # Any payload data

    def serialize(self):
        return self.dict()
    
    def __iter__(self):
        return iter(self.dict().items())


def StandardResponse(status:bool, status_code: int, message: str, data: Optional[Any] = None):
    return StandardResponseModel(status=status, status_code=status_code, message=message, data=data).serialize()


def StandardResponseWithoutSerialize(status:bool, status_code: int, message: str, data: Optional[Any] = None):
    return StandardResponseModel(status=status, status_code=status_code, message=message, data=data)

def ErrorResponse(status:bool, status_code: int, message: str, error: Optional[Any] = None):
    content = ErrorResponseModel(status=status, status_code=status_code, message=message, error=error).serialize()
    return JSONResponse(status_code=status_code, content=content)
