from typing import Dict
from uuid import <PERSON><PERSON><PERSON>
from fastapi import WebSocket, WebSocketDisconnect
from starlette.websockets import WebSocketState
from app.redis_cache import RedisCache
from app.notification import send_push_notification
# from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
# from fastapi import Request
# from app.app_loggers import logger
class WebSocketManager:
    def __init__(self):
        self.connections: Dict[int, WebSocket] = {}
        self.redis_cache = RedisCache()

    async def connect(self, user_id: UUID, websocket: WebSocket):
        await websocket.accept()
        self.connections[user_id] = websocket
        print(f"WebSocket connected: user_id={user_id}")
        # logger.info(f"WebSocket connected: user_id={user_id}")
        offline_messages = self.redis_cache.fetch_offline_messages(user_id)
        # logger.info(f"Fetched {len(offline_messages)} offline messages for user_id={user_id}")
        for msg in offline_messages:
            await websocket.send_json(msg)
            # logger.info(f"Sent offline message to user_id={user_id}: {msg}")

    def disconnect(self, user_id: UUID):
        websocket = self.connections.pop(user_id, None)
        if websocket:
            print(f"WebSocket disconnected: user_id={user_id}")
            # logger.info(f"WebSocket disconnected: user_id={user_id}")

    async def send_to_user(self, user_id: UUID, message: dict, token: str = None):
        # logger.info(self.connections)
        # logger.info(f"Attempting to send message to user_id={user_id}")
        # logger.info(self.connections.keys())
        user_id = UUID(user_id)
        websocket = self.connections.get(user_id)
        # logger.info(websocket)
        if websocket:
            try:
                await websocket.send_json(message)
                # logger.info(f"Message sent to user_id={user_id}: {message}")
            except Exception as e:
                print(f"Error sending to user {user_id}: {e}")
                # logger.error(f"Error sending to user_id={user_id}: {e}")
        else:
            # Store message for offline user
            self.redis_cache.store_offline_message(user_id, message)
            # logger.info(f"Stored offline message for user_id={user_id}: {message}")

            # Send push notification to offline user
            if user_id and token:
                try:
                    print(f"Sending push notification to offline user: {user_id}")
                    response_status, response_data = send_push_notification(
                        auth_token=token,
                        title="New Message Received",
                        message="You have a new message",
                        sender_id=str(user_id),
                        user_id=str(user_id),
                        data={
                            "user_id": str(user_id),
                            "fcm_request_type": "chat",
                            # "conversation_id": str(message.get("conversation_id")),
                        }
                    )
                    
                    if response_status == 200:
                        print(f"Push notification sent successfully to user: {user_id}")
                    elif response_status == 404:
                        print(f"No FCM token found for user: {user_id}")
                    else:
                        print(f"Failed to send push notification to user {user_id}: {response_data}")
                        
                except Exception as e:
                    print(f"Error sending push notification to user {user_id}: {e}")
                    # logger.error(f"Error sending push notification to user_id={user_id}: {e}")
            elif not token:
                print(f"No token provided for push notification to user: {user_id}")
           