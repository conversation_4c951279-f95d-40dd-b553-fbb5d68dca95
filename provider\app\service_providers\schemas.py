from pydantic import BaseModel, Field
from typing import Optional

from pydantic import BaseModel, EmailStr, <PERSON>son, UUID4
from datetime import datetime
from fastapi import Form, File, UploadFile
from pydantic.types import conint
import json
from app.models_enum import BusinessType, BusinessProviderStatus


class ServiceProviders(BaseModel):
    business_name : str
    business_phone: str
    business_email: str
    adress: str

class UpdateServiceProviders(BaseModel):
    business_name : Optional[str]
    business_phone: Optional[str]
    business_email: Optional[str]
    adress: Optional[str]


class BusinessResponse(BaseModel):
    id: Optional[UUID4] = None
    user_id: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    full_name: Optional[str] = None
    email: Optional[str] = None
    country_code: Optional[str] = None
    phone_number: Optional[str] = None
    business_name: Optional[str] = None
    business_type: Optional[str] = None
    business_registration_number: Optional[str] = None
    ghana_post_gps_address: Optional[str] = None
    business_location: Optional[str] = None
    tax_identification_number: Optional[str] = None
    services_offered: Optional[list] = None
    service_area_ids: Optional[list] = None
    business_registration_document: Optional[str] = None
    business_logo: Optional[str] = None
    portfolio_image: Optional[list] = None
    id_proof: Optional[str] = None
    signature: Optional[str] = None
    submit_date: Optional[datetime] = None
    page_position: Optional[str] = None
    status: Optional[str] = None
    # reason: Optional[str] = None
    created_at: Optional[datetime] = None


class UpdateBusiness:
    def __init__(
        self,
        first_name: Optional[str] = Form(None),
        last_name: Optional[str] = Form(None),
        email: Optional[str] = Form(None),
        business_name: Optional[str] = Form(None),
        business_type: Optional[str] = Form(None),
        business_registration_number: Optional[str] = Form(None),
        ghana_post_gps_address: Optional[str] = Form(None),
        business_location: Optional[str] = Form(None),
        tax_identification_number: Optional[str] = Form(None),
        services_offered: Optional[str] = Form(None),
        service_area_ids: Optional[str] = Form(None),
        page_position: Optional[str] = Form(None),
        status: BusinessProviderStatus = Form(BusinessProviderStatus.IN_PROGRESS),
        reason: Optional[str] = Form(None),
        submit_date: Optional[datetime] = Form(None)
        
    ):  
        self.first_name = first_name
        self.last_name = last_name
        self.email = email
        self.business_name = business_name
        self.business_type = business_type
        self.business_registration_number = business_registration_number
        self.ghana_post_gps_address = ghana_post_gps_address
        self.business_location = business_location
        self.tax_identification_number = tax_identification_number
        self.services_offered = json.loads(services_offered) if services_offered else []
        self.service_area_ids = json.loads(service_area_ids) if service_area_ids else []
        self.page_position = page_position
        self.status = status 
        self.reason = reason
        self.submit_date = submit_date

class BusinessAreaCreate(BaseModel):
    area_name: str

class BusinessAreaUpdate(BaseModel):
    area_name:str

