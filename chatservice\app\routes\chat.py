from typing import Optional
from uuid import UUID
from fastapi import FastAPI, Depends, WebSocket, WebSocketDisconnect,APIRouter,Query
from sqlalchemy.orm import Session
from starlette.websockets import WebSocketState
import asyncio
from app.kafka_consumer import KafkaAssignmentConsumer
from app.database import SessionLocal, get_db, engine
import app.models as md
from app.schemas import CustomerRequest, MessageCreate, AgentCreate, ChatType,chatinitiaterequest,convesationrequest
from app.redis_cache import RedisCache
from app.kafka_producer import KafkaProducerClient
from app.websocket_manager import WebSocketManager
from app.response_models import (
    StandardResponse,
    StandardResponseWithoutSerialize,
    ErrorResponse
)
from app.kafka_producer import producer
from sqlalchemy import desc
# from app.app_loggers import logger
from app.models_enum import RoleType
# from app.notification import send_push_notification
from app.models import Booking

md.Base.metadata.create_all(bind=engine)
router = APIRouter()
redis_cache = RedisCache()
ws_manager = WebSocketManager()
# producer = KafkaProducerClient()

@router.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: UUID,db: Session = Depends(get_db)):
    # logger.info(f"WebSocket connection request from user_id={user_id}")
    # db = SessionLocal()
    print("inside fn")
    auth_header = websocket.headers.get("Authorization")
    print(auth_header,"authheader")
    if not auth_header:
        # For WebSocket, we need to close the connection instead of returning HTTP response
        await websocket.close(code=4001, reason="Unauthorized - No Authorization header")
        return
    # Extract token from "Bearer <token>" format
    if auth_header.startswith("Bearer "):
        token = auth_header[7:]  # Remove "Bearer " prefix
    else:
        token = auth_header  # Assume it's just the token
    
    if not token:
        await websocket.close(code=4001, reason="Unauthorized - Invalid token format")
        return
    
    await ws_manager.connect(user_id, websocket)
    try:
        while True:
            data = await websocket.receive_json()
            # logger.info(f"Received message data: {data}")
            conversation_id = data["conversation_id"]
            sender_id = data["sender_id"]
            receiver_id = data["receiver_id"]
            content = data["content"]
            chat_type = data.get("chat_type", "support")

            message = md.Message(
                conversation_id=conversation_id,
                sender_id=sender_id,
                content=content,
                delivered=True,
                seen=True,
                chat_type=ChatType(chat_type)
            )
            db.add(message)
            db.commit()
            db.refresh(message)

            # logger.info(f"Message stored in DB from sender {sender_id} to receiver {receiver_id}.")

            # Format created_at as stored in database
            msg_created_at= message.created_at.isoformat() if message.created_at else None

            # Pass token as separate parameter, not in message payload
            await ws_manager.send_to_user(receiver_id, {
                "conversation_id": conversation_id,
                "receiver_id": receiver_id,
                "sender_id": sender_id,
                "content": content,
                "created_at": msg_created_at,  # properly formatted datetime
                "chat_type": chat_type
            }, token)  # Pass token as third parameter
            # logger.info(f"Sent message to user {receiver_id} via WebSocket.")

    except WebSocketDisconnect:
        ws_manager.disconnect(user_id)
        # logger.info(f"WebSocket disconnected for user_id={user_id}")


@router.post("/enqueue_customer")
async def enqueue_customer(req: CustomerRequest, db: Session = Depends(get_db)):
    # logger.info(f"Enqueuing customer with ID {req.customer_id}")
    agent = db.query(md.UserProfiles).filter_by(is_available=True).first()

    if agent:
        # logger.info(f"Assigning available agent {agent.id} to customer {req.customer_id}")
        agent.is_available = False
        redis_cache.mark_agent_unavailable(agent.id)
        db.commit()
        conversation = md.Conversation(
            type=md.ConversationType.support,
            status=md.ConversationStatus.open,
            assigned_agent_id=agent.id
        )
        db.add(conversation)
        db.commit()
        # print(f"✅ Conversation created with ID {conversation.id}")
        db.add_all([
            md.Participant(conversation_id=conversation.id, user_id=req.customer_id, role=RoleType.user),
            md.Participant(conversation_id=conversation.id, user_id=agent.id, role=RoleType.agent)
        ])
        # print(f"✅ Participants added for conversation_id {conversation.id}")
        await producer.send_assignment("assignment_notifications", {
            "agent_id": agent.id,
            "customer_id": req.customer_id,
            "conversation_id": conversation.id,
            "event": "assigned"
        })
        # logger.info(f"Agent {agent.id} assigned to conversation {conversation.id}")

        return StandardResponse(True, 200, "Agent assigned", {
            "agent_id": agent.id,
            "conversation_id": conversation.id
        })
    else:
        # logger.info(f"No agent available. Adding customer {req.customer_id} to queue.")
        redis_cache.enqueue_customer(req.customer_id)
        return StandardResponse(True, 200, "Customer added to queue")

@router.post("/agent_available/{agent_id}")
async def agent_becomes_available(agent_id: UUID, db: Session = Depends(get_db)):
    # logger.info(f"Marking agent {agent_id} as available")
    agent = db.query(md.UserProfiles).filter(md.UserProfiles.id==agent_id).first()
    if not agent:
        # logger.warning(f"Agent {agent_id} not found")
        return ErrorResponse(False, 404, "Agent not found")

    if agent.is_available:
        # logger.info(f"Agent {agent_id} is already marked available")
        return ErrorResponse(False, 400, "Agent already available")

    customer_id = redis_cache.dequeue_customer()
    print(customer_id, "customer_id from redis")

    if customer_id:
        # logger.info(f"Assigning agent {agent.id} to dequeued customer {customer_id}")
        try:
            agent.is_available = False
            redis_cache.mark_agent_unavailable(agent.id)
            db.commit()
            redis_cache.remove_customer_from_redis(customer_id)
            # print(f"✅ Customer {customer_id} removed from Redis queue")
            conversation = md.Conversation(
                type=md.ConversationType.support,
                status=md.ConversationStatus.open,
                assigned_agent_id=agent.id
            )
            db.add(conversation)
            db.commit()  # Get conversation.id

            db.add_all([
                md.Participant(conversation_id=conversation.id, user_id=UUID(customer_id), role=RoleType.user),
                md.Participant(conversation_id=conversation.id, user_id=agent.id, role=RoleType.agent)
            ])
            db.commit()
            # print(f"✅ Conversation + Participants stored for conversation_id {conversation.id}")

            await producer.send_assignment("assignment_notifications", {
                "agent_id": agent.id,
                "customer_id": customer_id,
                "conversation_id": conversation.id,
                "event": "assigned_from_queue"
            })

            return StandardResponse(True, 200, "Customer assigned", {
                "agent_id": agent.id,
                "customer_id": customer_id,
                "conversation_id": conversation.id
            })
        except Exception as e:
            db.rollback()
            print("❌ Error storing conversation:", e)
            return ErrorResponse(False, 500, "Failed to assign customer to agent")
    else:
        # logger.info(f"No customers in queue. Agent {agent.id} marked as available.")
        agent.is_available = True
        redis_cache.mark_agent_available(agent.id)
        db.commit()
        return StandardResponse(True, 200, "Agent marked available")


@router.get("/queue_status")
def get_queue_length():
    length = redis_cache.queue_length()
    # logger.info(f"Queue length: {length}")
    return StandardResponse(True, 200, "Fetched waiting customers successfully", {"waiting_customers": length})


@router.post("/send_message")
def send_message(msg: MessageCreate, db: Session = Depends(get_db)):
    message = md.Message(**msg.dict())
    db.add(message)
    db.commit()
    return StandardResponse(True, 200, "Message saved successfully", {"message_id": message.id})


@router.post("/create_agent")
def create_agent(agent: AgentCreate, db: Session = Depends(get_db)):
    new_agent = md.UserProfiles(first_name=agent.name)
    db.add(new_agent)
    db.commit()
    return StandardResponse(True, 201, "Agent created", {"agent_id": new_agent.id})

# @router.get("/get_conversations")
# def get_conversation(con:convesationrequest, db: Session = Depends(get_db)):
#     conversation = db.query(md.Conversation).filter(md.Conversation.id == con.conversation_id).first()
#     if not conversation:
#         return ErrorResponse(False, 404, "Conversation not found")

#     participants = db.query(md.Participant).filter(md.Participant.conversation_id == con.conversation_id).all()
#     messages = db.query(md.Message).filter(md.Message.conversation_id == con.conversation_id).all()

#     response_data = {
#     "conversation": conversation,
#     "participants": [p.user_id for p in participants],
#     "messages": [
#         {
#             "id": str(m.id),
#             "conversation_id": str(m.conversation_id),
#             "sender_id": str(m.sender_id),
#             "content": m.content,
#             "delivered": m.delivered,
#             "seen": m.seen,
#             "chat_type": m.chat_type.value if m.chat_type else None,
#             "created_at": m.created_at.isoformat() if m.created_at else None,
#             "updated_at": m.updated_at.isoformat() if m.updated_at else None,
#         }
#         for m in messages
#     ]
# }

#     return StandardResponse(True, 200, "Conversation fetched successfully", response_data)

@router.get("/get_conversations")
def get_conversation(conversation_id: Optional[UUID] = (None),
    agent_id: Optional[UUID] =(None),
    customer_id: Optional[UUID] = (None),
    booking_id: Optional[UUID] = (None), db: Session = Depends(get_db)):
    conversation_id = conversation_id

    # Case 1: Direct by conversation_id
    if conversation_id:
        conversation = db.query(md.Conversation).filter(md.Conversation.id == conversation_id).first()
    # Case 2: Lookup by booking_id
    elif booking_id:
        chat_detail = db.query(md.Chatdetails).filter(md.Chatdetails.booking_id == booking_id).first()
        if chat_detail:
            conversation = db.query(md.Conversation).filter(md.Conversation.id == chat_detail.conversation_id).first()
        else:
            return ErrorResponse(False, 404, "No conversation found for the given booking_id")
        
    # ✅ Case 4: customer_id + agent_id + booking_id
    elif agent_id and customer_id and booking_id:
        customer_conversations = db.query(md.Participant.conversation_id).filter(
            md.Participant.user_id == con.customer_id
        ).subquery()

        agent_conversations = db.query(md.Participant.conversation_id).filter(
            md.Participant.user_id == con.agent_id
        ).subquery()

        common_conversations = db.query(md.Chatdetails).filter(
            md.Chatdetails.booking_id == con.booking_id,
            md.Chatdetails.conversation_id.in_(
                db.query(customer_conversations).intersect(db.query(agent_conversations))
            )
        ).first()

        if common_conversations:
            conversation = db.query(md.Conversation).filter(
                md.Conversation.id == common_conversations.conversation_id
            ).first()
        else:
            return ErrorResponse(False, 404, "No conversation found matching agent_id, customer_id, and booking_id")

    # Case 3: Lookup by agent_id + customer_id
    elif agent_id and customer_id:
        # Get all conversation_ids where agent and customer are participants
        customer_conversations = db.query(md.Participant.conversation_id).filter(md.Participant.user_id == customer_id).subquery()
        agent_conversations = db.query(md.Participant.conversation_id).filter(md.Participant.user_id == agent_id).subquery()

        common_conversations = db.query(md.Chatdetails).filter(
            md.Chatdetails.conversation_id.in_(db.query(customer_conversations).intersect(db.query(agent_conversations)))
        ).first()

        if common_conversations:
            conversation = db.query(md.Conversation).filter(md.Conversation.id == common_conversations.conversation_id).first()
        else:
            return ErrorResponse(False, 404, "No common conversation found for the given agent_id and customer_id")
    else:
        return ErrorResponse(False, 400, "Provide either conversation_id, booking_id, or both customer_id and agent_id")

    if not conversation:
        return ErrorResponse(False, 404, "Conversation not found")

    participants = db.query(md.Participant).filter(md.Participant.conversation_id == conversation.id).all()
    messages = db.query(md.Message).filter(md.Message.conversation_id == conversation.id).all()

    response_data = {
        "conversation": conversation,
        "participants": [p.user_id for p in participants],
        "messages": [
            {
                "id": str(m.id),
                "conversation_id": str(m.conversation_id),
                "sender_id": str(m.sender_id),
                "content": m.content,
                "delivered": m.delivered,
                "seen": m.seen,
                "chat_type": m.chat_type.value if m.chat_type else None,
                "created_at": m.created_at.isoformat() if m.created_at else None,
                "updated_at": m.updated_at.isoformat() if m.updated_at else None,
            }
            for m in messages
        ]
    }

    return StandardResponse(True, 200, "Conversation fetched successfully", response_data)


# @router.post("/chat_initiate")
# async def chat_initiate(chatrequest:chatinitiaterequest, db: Session = Depends(get_db)):
#     """chat initiate between customer and agent or artisan and agent"""
#     try:
#         if chatrequest.agent_id and chatrequest.customer_id:
#             conversation = md.Conversation(
#                 type=md.ConversationType.support,
#                 status=md.ConversationStatus.open,
#                 assigned_agent_id=chatrequest.agent_id
#             )
#             db.add(conversation)
#             db.commit()

#             db.add_all([
#                 md.Participant(conversation_id=conversation.id, user_id=chatrequest.customer_id, role=RoleType.user),
#                 md.Participant(conversation_id=conversation.id, user_id=chatrequest.agent_id, role=RoleType.agent)
#             ])
#             db.commit()

#             details=md.Chatdetails(
#                 conversation_id=conversation.id,
#                 booking_id=chatrequest.booking_id,
#                 service_id=chatrequest.service_id,
#                 conversation_type=md.ConversationType.agent
#             )
#             db.add(details)
#             db.commit()

#             return StandardResponse(True, 200, "Chat assignment successful", {
#             "agent_id": chatrequest.agent_id,
#             "customer_id": chatrequest.customer_id,
#             "conversation_id": conversation.id
#             })
#         elif chatrequest.artisan_id and chatrequest.agent_id:
#             conversation = md.Conversation(
#                 type=md.ConversationType.artisan,
#                 status=md.ConversationStatus.open,
#                 assigned_agent_id=chatrequest.artisan_id
#             )
#             db.add(conversation)
#             db.commit()

#             db.add_all([
#                 md.Participant(conversation_id=conversation.id, user_id=chatrequest.artisan_id, role=RoleType.artisan),
#                 md.Participant(conversation_id=conversation.id, user_id=chatrequest.agent_id, role=RoleType.agent)
#             ])
#             db.commit()

#             details = md.Chatdetails(
#                 conversation_id=conversation.id,
#                 booking_id=chatrequest.booking_id,
#                 service_id=chatrequest.service_id,
#                 conversation_type=md.ConversationType.artisan
#             )
#             db.add(details)
#             db.commit()

#             return StandardResponse(True, 200, "Chat assignment successful", {
#                 "artisan_id": chatrequest.artisan_id,
#                 "agent_id": chatrequest.agent_id,
#                 "conversation_id": conversation.id
#             })
#     except Exception as e:
#         db.rollback()
#         print(f"Error during chat assignment: {e}")
#         return ErrorResponse(False, 500, "Failed to assign chat")

@router.post("/chat_initiate")
async def chat_initiate(chatrequest: chatinitiaterequest, db: Session = Depends(get_db)):
    """Initiate chat or return existing one based on agent + customer/artisan + booking_id"""
    try:
        # Shared filter for existing conversation
        def get_existing_conversation_id(booking_id, user_ids: list):
            existing = (
                db.query(md.Chatdetails)
                .filter(md.Chatdetails.booking_id == booking_id)
                .join(md.Conversation, md.Chatdetails.conversation_id == md.Conversation.id)
                .join(md.Participant, md.Participant.conversation_id == md.Conversation.id)
                .filter(md.Participant.user_id.in_(user_ids))
                .all()
            )

            # Group by conversation_id and check that both participants exist
            conversation_map = {}
            for chat in existing:
                conv_id = chat.conversation_id
                if conv_id not in conversation_map:
                    conversation_map[conv_id] = set()
                participants = db.query(md.Participant).filter(md.Participant.conversation_id == conv_id).all()
                for p in participants:
                    conversation_map[conv_id].add(str(p.user_id))

            for conv_id, participant_ids in conversation_map.items():
                if all(str(uid) in participant_ids for uid in user_ids):
                    return conv_id
            return None

        # ========== Support Chat (Customer + Agent) ==========
        if chatrequest.agent_id and chatrequest.customer_id:
            user_ids = [chatrequest.agent_id, chatrequest.customer_id]
            existing_conv_id = get_existing_conversation_id(chatrequest.booking_id, user_ids)
            if existing_conv_id:
                return StandardResponse(True, 200, "Chat already exists", {
                    "agent_id": chatrequest.agent_id,
                    "customer_id": chatrequest.customer_id,
                    "conversation_id": existing_conv_id
                })

            # Create new conversation
            conversation = md.Conversation(
                type=md.ConversationType.support,
                status=md.ConversationStatus.open,
                assigned_agent_id=chatrequest.agent_id
            )
            db.add(conversation)
            db.commit()

            db.add_all([
                md.Participant(conversation_id=conversation.id, user_id=chatrequest.customer_id, role=RoleType.user),
                md.Participant(conversation_id=conversation.id, user_id=chatrequest.agent_id, role=RoleType.agent)
            ])
            db.commit()

            details = md.Chatdetails(
                conversation_id=conversation.id,
                booking_id=chatrequest.booking_id,
                service_id=chatrequest.service_id,
                conversation_type=md.ConversationType.agent
            )
            db.add(details)
            db.commit()

            return StandardResponse(True, 200, "Chat assignment successful", {
                "agent_id": chatrequest.agent_id,
                "customer_id": chatrequest.customer_id,
                "conversation_id": conversation.id
            })
            
        # ========== Artisan Chat (Artisan + Agent) ==========
        elif chatrequest.artisan_id and chatrequest.agent_id:
            user_ids = [chatrequest.artisan_id, chatrequest.agent_id]
            existing_conv_id = get_existing_conversation_id(chatrequest.booking_id, user_ids)
            if existing_conv_id:
                return StandardResponse(True, 200, "Chat already exists", {
                    "artisan_id": chatrequest.artisan_id,
                    "agent_id": chatrequest.agent_id,
                    "conversation_id": existing_conv_id
                })

            # Create new conversation
            conversation = md.Conversation(
                type=md.ConversationType.artisan,
                status=md.ConversationStatus.open,
                assigned_agent_id=chatrequest.artisan_id
            )
            db.add(conversation)
            db.commit()

            db.add_all([
                md.Participant(conversation_id=conversation.id, user_id=chatrequest.artisan_id, role=RoleType.artisan),
                md.Participant(conversation_id=conversation.id, user_id=chatrequest.agent_id, role=RoleType.agent)
            ])
            db.commit()

            details = md.Chatdetails(
                conversation_id=conversation.id,
                booking_id=chatrequest.booking_id,
                service_id=chatrequest.service_id,
                conversation_type=md.ConversationType.artisan
            )
            db.add(details)
            db.commit()

            return StandardResponse(True, 200, "Chat assignment successful", {
                "artisan_id": chatrequest.artisan_id,
                "agent_id": chatrequest.agent_id,
                "conversation_id": conversation.id
            })
        else:
            return ErrorResponse(False, 400, "Missing required fields for chat initiation")

    except Exception as e:
        db.rollback()
        print(f"Error during chat assignment: {e}")
        return ErrorResponse(False, 500, "Failed to assign chat")
    
@router.get("/get_conversation_list")
def get_conversation_list(
    agent_id: UUID = Query(...),
    role: RoleType = Query(...),  # NEW PARAMETER (user or artisan)
    db: Session = Depends(get_db)
):
    try:
        # Step 1: Get all conversation_ids where this agent is a participant
        conversation_ids_query = db.query(md.Participant.conversation_id).filter(
            md.Participant.user_id == agent_id,
            md.Participant.role == RoleType.agent  # Only fetch conversations where agent is actually agent
        )

        conversation_ids = [c.conversation_id for c in conversation_ids_query]

        if not conversation_ids:
            return StandardResponse(True, 404, "No conversations found", [])

        # Step 2: Filter conversations where the other participant is of selected role
        valid_conversations = []
        for conv_id in conversation_ids:
            participants = db.query(md.Participant).filter(
                md.Participant.conversation_id == conv_id
            ).all()

            has_required_role = any(p.role == role for p in participants)
            if has_required_role:
                valid_conversations.append(conv_id)

        if not valid_conversations:
            return StandardResponse(True, 404, f"No {role.value} conversations found", [])

        # Step 3: Fetch chat details
        chat_details = db.query(md.Chatdetails).filter(
            md.Chatdetails.conversation_id.in_(valid_conversations)
        ).all()

        if not chat_details:
            return StandardResponse(True, 404, f"No {role.value} conversations found", [])

        results = []
        for chat in chat_details:
            # Latest message
            last_msg = db.query(md.Message).filter(
                md.Message.conversation_id == chat.conversation_id
            ).order_by(desc(md.Message.created_at)).first()

            # Participants
            participants = db.query(md.Participant).filter(
                md.Participant.conversation_id == chat.conversation_id
            ).all()

            agent_user_id = None
            user_id = None
            for p in participants:
                if p.role == RoleType.agent:
                    agent_user_id = p.user_id
                elif p.role == role:
                    user_id = p.user_id

            # User name (customer or artisan)
            user_name = None
            if user_id:
                user_profile = db.query(md.UserProfiles).filter(
                    md.UserProfiles.id == user_id
                ).first()
                if user_profile:
                    user_name = f"{user_profile.first_name or ''} {user_profile.last_name or ''}".strip()

            # Service names
            service_names = []
            if chat.booking_id and user_id:
                invoice = db.query(md.Invoice).filter(
                    md.Invoice.booking_id == str(chat.booking_id)
                ).first()

                if invoice:
                    invoice_items = db.query(md.InvoiceItem).filter(
                        md.InvoiceItem.invoice_id == invoice.id
                    ).all()

                    service_ids = {item.service_id for item in invoice_items if item.service_id}

                    if service_ids:
                        services = db.query(md.Services).filter(
                            md.Services.id.in_(list(service_ids))
                        ).all()
                        service_names = [s.name for s in services if s.name]
            booking_order_id = None
            if chat.booking_id:
                booking = db.query(Booking).filter(Booking.id == chat.booking_id).first()
                booking_order_id= str(booking.booking_order_id) if booking else None

            results.append({
                "booking_id": str(chat.booking_id) if chat.booking_id else None,
                "booking_order_id":booking_order_id,
                "conversation_id": str(chat.conversation_id),
                "conversation_type": chat.conversation_type.value,
                "feedback": chat.feedback,
                "last_message": last_msg.content if last_msg else None,
                "time_stamp": last_msg.created_at if last_msg else None,
                "agent_id": str(agent_user_id) if agent_user_id else None,
                f"{role.value}_id": str(user_id) if user_id else None,
                f"{role.value}_name": user_name,
                "service_name": service_names,
            })

        return StandardResponse(True, 200, "Conversation list fetched successfully", results)

    except Exception as e:
        print(f"Error fetching conversation list: {e}")
        return ErrorResponse(False, 500, "Internal server error")