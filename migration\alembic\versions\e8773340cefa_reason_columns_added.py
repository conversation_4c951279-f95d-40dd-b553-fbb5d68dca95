"""reason columns added

Revision ID: e8773340cefa
Revises: f40a1cbe36b1
Create Date: 2025-06-23 16:50:57.780381

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e8773340cefa'
down_revision: Union[str, None] = 'f40a1cbe36b1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('business', sa.Column('reason', sa.String(), nullable=True))
    op.create_unique_constraint(None, 'business', ['id'])
    op.create_unique_constraint(None, 'business_area', ['id'])
    op.create_unique_constraint(None, 'business_service_mapping', ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'business_service_mapping', type_='unique')
    op.drop_constraint(None, 'business_area', type_='unique')
    op.drop_constraint(None, 'business', type_='unique')
    op.drop_column('business', 'reason')
    # ### end Alembic commands ###
