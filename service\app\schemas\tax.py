from pydantic import BaseModel, UUID4
from datetime import datetime
from typing import Optional
from enum import Enum
from app.models_enum import TaxType

class CreateTax(BaseModel):
    sp_id: UUID4
    name: str
    percentage: float
    type: TaxType = TaxType.STANDARD

class UpdateTax(BaseModel):
    name: Optional[str] = None
    percentage: Optional[float] = None
    type: Optional[TaxType] = None