from pydantic import BaseModel, UUID4
from typing import Optional, Any, List
from fastapi import Form
from app.models_enum import CartStatus


class CreateCart:
    def __init__(
        self,
        user_id: Optional[str] = Form(...),
        service_id: Optional[str] = Form(...),
        qty: Optional[int] = Form(...),
        description: Optional[str] = Form(None),
    ):
        self.user_id = user_id
        self.service_id = service_id
        self.qty = qty
        self.description = description

    def to_dict(self):
        return {
            "user_id": self.user_id,
            "service_id": self.service_id,
            "qty": self.qty,
            "description": self.description,
        }


class UpdateCart:
    def __init__(
        self,
        qty: Optional[int] = Form(None),
        description: Optional[str] = Form(None),
        status: Optional[str] = Form(None),
    ):
        self.qty = qty
        self.description = description
        self.status = status

    def to_dict(self):
        return {
            "qty": self.qty,
            "description": self.description,
            "status": self.status,
        }


class CartResponse(BaseModel):
    id: UUID4
    user_id: UUID4
    service_id: UUID4
    qty: int
    description: Optional[str] = None
    attachments: Optional[Any] = None
    status: CartStatus