import requests
from typing import Optional
from app.config import settings

async def get_available_artisans(payload, request_type):
    """Get service provider details from profile service"""
    try:
        if request_type == "INSTANT":
            url = f"http://schedule-service:8012/search_artisan/search_live_artisan"
        else:
            url = f"{settings.BE_SCHEDULE_API_URL}/search_artisan/search_artisan"

        # response = requests.post(url, headers={"Authorization": f'Bearer {auth_token}'}, json=payload)
        response = requests.post(url, json=payload)
        if response.status_code == 200:
            return response.json().get("data")
        return None
    except Exception as e:
        print(f"Error fetching artisan details: {e}")
        return None