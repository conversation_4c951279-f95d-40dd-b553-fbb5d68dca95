from app.models import BookingCancellationHistory

# History Status
status_dict = {
    "bc_created": "Booking Cancellation Created",
    "auto_refund_initiated": "Auto Refund Initiated",
    "agent_assigned": "Agent Assigned",
    "refund_approved": "Refund Approved",
    "refund_initiated": "Refund Initiated",
    "refund_failed": "Refund Failed",
    "refund_done": "Refund Completed",
    "resolved": "Status Changed to Resolved",
    "penalty_applied": "Penalty Applied to"
}

def create_booking_cancellation_history(db, data: dict):
    val = BookingCancellationHistory(**data)
    db.add(val)
    db.commit()


def get_booking_cancellation_history(db, id):
    query = db.query(BookingCancellationHistory).filter(BookingCancellationHistory.booking_cancellation_id == id).order_by(BookingCancellationHistory.created_at.asc())
    results = query.all()
    return results