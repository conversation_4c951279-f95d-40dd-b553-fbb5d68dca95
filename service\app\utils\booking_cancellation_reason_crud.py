from sqlalchemy.orm import Session
from app.models import BookingCancellationReason
from app.schemas.booking_cancellation_reasons import BookingCancellationReasonCreate, BookingCancellationReasonUpdate
from typing import List, Optional
from uuid import UUID
from fastapi import HTTPException

def create_booking_cancellation_reason(db: Session, data: BookingCancellationReasonCreate) -> BookingCancellationReason:
    db_booking_cancellation_reason = BookingCancellationReason(**data.model_dump())
    try:
        db.add(db_booking_cancellation_reason)
        db.commit()
        db.refresh(db_booking_cancellation_reason)
        return db_booking_cancellation_reason
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

def get_booking_cancellation_reason(db: Session, id: UUID) -> BookingCancellationReason:
    booking_cancellation_reason = db.query(BookingCancellationReason).filter(BookingCancellationReason.id == id).first()
    if not booking_cancellation_reason:
        raise HTTPException(status_code=404, detail="Booking Cancellation Reason not found")
    return booking_cancellation_reason

def get_booking_cancellation_reasons(**kwargs) -> List[BookingCancellationReason]:
    db = kwargs.get("db")
    skip = kwargs.get("skip", 0)
    limit = kwargs.get("limit", 100)
    is_active = kwargs.get("is_active", None)
    user_type = kwargs.get("user_type", None)
    query = db.query(BookingCancellationReason)
    if is_active is not None:
        query = query.filter(BookingCancellationReason.is_active == is_active)
    if user_type is not None:
        query = query.filter(BookingCancellationReason.user_type == user_type)     # Filter by user_type if provided
    return query.offset(skip).limit(limit).all()

def update_booking_cancellation_reason(db: Session, id: UUID, data: BookingCancellationReasonUpdate) -> BookingCancellationReason:
    db_booking_cancellation_reason = get_booking_cancellation_reason(db, id)
    update_data = data.model_dump(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_booking_cancellation_reason, field, value)
    
    try:
        db.commit()
        db.refresh(db_booking_cancellation_reason)
        return db_booking_cancellation_reason
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

def delete_booking_cancellation_reason(db: Session, booking_cancellation_reason_id: UUID) -> bool:
    db_booking_cancellation_reason = get_booking_cancellation_reason(db, booking_cancellation_reason_id)
    try:
        db.delete(db_booking_cancellation_reason)
        db.commit()
        return True
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))
