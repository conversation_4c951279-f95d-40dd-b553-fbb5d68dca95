# import uuid
# from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, Header
# from sqlalchemy.orm import Session
# from sqlalchemy import or_
# from pathlib import Path
# from app import models, schemas
# from app.database import get_db
# from app.utils import create_record, delete_record, get_id_header, is_valid_uuid, update_record
# from typing import Optional
# from app.hash import cognito_secret_hash
# from sqlalchemy.future import select
# from sqlalchemy import func
# from sqlalchemy.ext.asyncio import AsyncSession
# from typing import Annotated, List
# from app.file_uploader import delete_file, upload_file
# from app.config import get_settings
# from sqlalchemy.orm import selectinload

# # from hash import generate_salt, hash_password

# router = APIRouter()


# @router.post("/generate-invoice")
# async def generate_invoice(
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Head<PERSON>()] = None,
# ):
#     try:
#         resp = await get_id_header(Authorization)
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             auth_id = resp_json.get("id")
#             email = resp_json.get("email")
#         else:
#             return {'error': resp.json()}

#         request_dict = request.to_dict()

        
#         # new_category = await create_record(db, models.Category, request_dict)
#         # if isinstance(new_category, str):
#         #     return {"error": new_category}
#         return {'data': 'invoice generated successfully'}

#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error: {e}")


# @router.get("/category-read/{id}", response_model=schemas.CategoryResponse)
# async def read_category(
#     id: str,
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     try:
#         resp = await get_id_header(Authorization)
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             auth_id = resp_json.get("id")
#         else:
#             return {'error': resp.json()}

#         query = select(models.Category).filter(models.Category.id == uuid.UUID(id))
#         result = await db.execute(query)
#         category_obj = result.scalars().first()
#         return category_obj
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error: {e}")

# async def get_categories_with_services(db, category_id):
#     query = select(models.Category).filter(models.Category.parent_id == category_id)
#     # query = select(models.Category).filter(models.Category.parent_id == category_id).options(selectinload(models.Category.services))
#     result = await db.execute(query)
#     categories = result.scalars().all()
#     return categories

# @router.get("/category-list")
# async def list_category(
#     q: Optional[str] = None,
#     skip: int = 0,
#     limit: int = 10,
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     try:
#         resp = await get_id_header(Authorization)
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             auth_id = resp_json.get("id")
#         else:
#             return {'error': resp.json()}
        
#         # Total category
#         query = select(models.Category).filter(models.Category.parent_id == None)
#         result = await db.execute(query)
#         categories = result.scalars().all()

#         lst = []
#         for category_obj in categories:
#             category_data = category_obj.__dict__
#             sub_services = await get_categories_with_services(db, category_data['id'])
#             category_data['services'] = sub_services
#             lst.append(category_data)


#         # Total count
#         count_query = select(func.count()).select_from(models.Category)
#         total_result = await db.execute(count_query)
#         total_count = total_result.scalar()

#         return {"total": total_count, "data": lst}

#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error: {e}")
    


# @router.put("/category-update/{id}")
# async def update_category(
#     id: str,
#     data: schemas.UpdateCategory = Depends(),
#     banner: Optional[UploadFile] = File(None),
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     try:
#         resp = await get_id_header(Authorization)
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             auth_id = resp_json.get("id")
#         else:
#             return {'error': resp.json()}
        
#         query = select(models.Category).filter(models.Category.id == uuid.UUID(id))
#         result = await db.execute(query)
#         get_category = result.scalars().first()

#         if banner:
#             if get_category.banner:
#                 delete_file(bucket_name=get_settings().SERVICES_BUCKET_NAME, file_name=get_category.banner.split("/")[-1])
#             image_url = upload_file(banner, get_settings().SERVICES_BUCKET_NAME, "image/")
#             setattr(get_category, "banner", image_url)

#         res = await update_record(db, data, get_category)
#         return res
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error: {e}")
    

# @router.delete("/category-delete/{id}")
# async def delete_category(
#     id: str,
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     try:
#         resp = await get_id_header(Authorization)
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             auth_id = resp_json.get("id")
#         else:
#             return {'error': resp.json()}
        
#         query = select(models.Category).filter(models.Category.id == uuid.UUID(id))
#         result = await db.execute(query)
#         get_category = result.scalars().first()

#         if get_category is None:
#             return {"error": "Category not found"}
        
#         res = await delete_record(db, get_category)
#         return res
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error: {e}")
