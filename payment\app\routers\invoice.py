from datetime import datetime
from uuid import UUID
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, Header,status, Request
from sqlalchemy.orm import Session
from sqlalchemy import delete  
from pathlib import Path
# from app import models, schemas
from app.models import Invoice,InvoiceItem,ArtisanAssigned,Services, Booking
from app.models_enum import InvoiceStatusEnum, InvoiceItemStatus
# from app import schemas
from app.schemas import InvoiceCreate,InvoiceItemCreate,InvoiceOut,InvoiceUpdate,InvoiceItemUpdate,GenerateQuoteRequest
from app.database import get_db
from typing import Optional
from app.hash import cognito_secret_hash
from sqlalchemy.future import select
from sqlalchemy import func,update
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Annotated, List
# from app.file_uploader import delete_file, upload_file
from app.config import get_settings
from sqlalchemy.orm import selectinload
from app.helper import StandardResponse,ErrorResponse
from app.utils import get_invoices,delete_invoice
from app.notification import send_push_notification

# # from hash import generate_salt, hash_password

router = APIRouter(prefix="/invoices", tags=["Invoices"])
# router = APIRouter(prefix="/invoice-items", tags=["Invoice Items"])

@router.post("/create-invoice")
async def create_invoice_route(
    invoice: InvoiceCreate,
    db: AsyncSession = Depends(get_db),
    # Authorization: Annotated[str | None, Header()] = None
):
    try:
        total_invoice_amount = 0
        total_tax_amount = 0
        total_discount = 0
        total_platform_fee_amount = 0
        total_booking_fee_amount = 0
        booking_fee = invoice.booking_fee

        # Step 1: Create invoice with dummy totals
        db_invoice = Invoice(
            sp_id=invoice.sp_id,
            payment_method=invoice.payment_method,
            biller_id=invoice.biller_id,
            customer_id=invoice.customer_id,
            tax_percentage=invoice.tax_percentage,
            platform_fee_percentage=invoice.platform_fee_percentage,
            booking_fee_percentage=invoice.booking_fee_percentage,
            booking_fee=booking_fee,
            tax_amount=0,
            platform_fee_amount=0,
            discount_amount=0,
            total_amount=0,
            payment_status=invoice.payment_status,
            booking_id=invoice.booking_id,
        )

        db.add(db_invoice)
        await db.commit()
        await db.refresh(db_invoice)

        # Step 2: Add invoice items and calculate totals
        for item in invoice.items:
            base_amount = item.price * item.quantity

            tax_amount = item.tax_amount
            platform_fee_amount = item.platform_fee_amount
            booking_fee = item.booking_fee
            item_total = base_amount + tax_amount + platform_fee_amount + booking_fee - item.discount_amount

            total_invoice_amount += item_total
            total_tax_amount += tax_amount
            total_platform_fee_amount += platform_fee_amount
            total_booking_fee_amount += booking_fee
            total_discount += item.discount_amount

            db_item = InvoiceItem(
                invoice_id=db_invoice.id,
                service_id=item.service_id,
                quantity=item.quantity,
                price=item.price,
                description=item.description,
                tax_percentage=item.tax_percentage,
                tax_amount=tax_amount,
                platform_fee_percentage=item.platform_fee_percentage,
                platform_fee_amount=platform_fee_amount,
                booking_fee=booking_fee,
                booking_fee_percentage=item.booking_fee_percentage,
                discount_amount=item.discount_amount,
                total_amount=item_total,
            )
            db.add(db_item)

        # Step 3: Update invoice with final totals
        db_invoice.tax_amount = total_tax_amount
        db_invoice.platform_fee_amount = total_platform_fee_amount
        db_invoice.booking_fee = total_booking_fee_amount
        db_invoice.discount_amount = total_discount
        db_invoice.total_amount = total_invoice_amount
        db_invoice.base_fee = sum([item.price * item.quantity for item in invoice.items])

        await db.commit()

        return StandardResponse(
            status_code=200,
            message="Invoice created successfully",
            data={"invoice_id": str(db_invoice.id)}
        )

    except Exception as e:
        await db.rollback()
        return ErrorResponse(
            status_code=500,
            message="Error creating invoice",
            error=str(e)
        )


@router.patch("/update-invoice")
async def update_invoice(
    invoice_id: UUID,
    data: InvoiceUpdate,
    db: AsyncSession = Depends(get_db),
    Authorization: str = Header(None)
):
    try:
        invoice = await db.get(Invoice, invoice_id)
        if not invoice:
            return ErrorResponse(
                status_code=404,
                message="Invoice not found",
                error="Invalid invoice_id"
            )

        for field, value in vars(data).items():
            if value is None or (isinstance(value, str) and not value.strip()):
                continue
            setattr(invoice, field, value)

        invoice.updated_at = datetime.utcnow()
        await db.commit()
        await db.refresh(invoice)

        return StandardResponse(
            status_code=200,
            message="Invoice updated successfully",
            data=invoice
        )
    except Exception as e:
        await db.rollback()
        return ErrorResponse(
            status_code=500,
            message="Failed to update invoice",
            error=str(e)
        )


@router.get("/get-invoice")
async def get_invoice_route(
    invoice_id: UUID,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str | None, Header()] = None
):
    try:
        result = await db.execute(
            select(Invoice)
            .options(selectinload(Invoice.items))  # This loads related items
            .where(Invoice.id == invoice_id)
        )
        invoice = result.scalar_one_or_none()

        if not invoice:
            raise HTTPException(status_code=404, detail="Invoice not found")

        return StandardResponse(
            status_code=200,
            message="Invoice fetched successfully",
            data=InvoiceOut.from_orm(invoice)
        )

    except Exception as e:
        await db.rollback()
        return ErrorResponse(
            status_code=500,
            message="Failed to fetch invoice",
            error=str(e)
        )


@router.get("/get-invoice-list")
async def list_invoices_route(
    skip: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[Optional[str], Header()] = None
):
    try:
        return await get_invoices(db, skip, limit)
    except Exception as e:
        return ErrorResponse(status_code=500, message="Failed to fetch invoices", error=str(e))


@router.delete("/delete-invoice")
async def delete_invoice(
    invoice_id: UUID,
    db: AsyncSession = Depends(get_db),
    Authorization: str = Header(None)
):
    try:
        # Step 1: Check if the invoice exists
        invoice = await db.get(Invoice, invoice_id)
        if not invoice:
            return ErrorResponse(
                status_code=404,
                message="Invoice not found",
                error="Invalid invoice_id"
            )

        # Step 2: Delete artisan_assigned records linked to this invoice
        await db.execute(
            delete(ArtisanAssigned).where(ArtisanAssigned.invoice_id == invoice_id)
        )

        # Step 3: Delete invoice_items linked to this invoice
        await db.execute(
            delete(InvoiceItem).where(InvoiceItem.invoice_id == invoice_id)
        )

        # Step 4: Delete the invoice
        await db.execute(
            delete(Invoice).where(Invoice.id == invoice_id)
        )

        # Step 5: Commit the transaction
        await db.commit()

        return StandardResponse(
            status_code=200,
            message="Invoice and all associated records deleted successfully",
            data={"invoice_id": str(invoice_id)}
        )

    except Exception as e:
        await db.rollback()
        return ErrorResponse(
            status_code=500,
            message="Failed to delete invoice",
            error=str(e)
        )

#Invoice item

@router.post("/create-invoice-item")
async def create_invoice_item(
    item: InvoiceItemCreate,
    invoice_id: UUID,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[Optional[str], Header()] = None
):
    try:
        db_item = InvoiceItem(invoice_id=invoice_id, **item.dict())
        db.add(db_item)
        await db.commit()
        await db.refresh(db_item)

        return StandardResponse(
            status_code=200,
            message="Invoice item created successfully",
            data={"item_id": str(db_item.id)}
        )
    except Exception as e:
        await db.rollback()
        return ErrorResponse(
            status_code=500,
            message="Failed to create invoice item",
            error=str(e)
        )

@router.put("/update-invoice-item")
async def update_invoice_item(
    item_id: UUID,
    data: InvoiceItemUpdate,
    db: AsyncSession = Depends(get_db),
    Authorization: str = Header(None)
):
    try:
        item = await db.get(InvoiceItem, item_id)
        if not item:
            return ErrorResponse(
                status_code=404,
                message="Invoice item not found",
                error="Invalid item_id"
            )

        for field, value in data.model_dump(exclude_unset=True).items():
            if value is not None:
                setattr(item, field, value)

        item.updated_at = datetime.utcnow()
        await db.commit()
        await db.refresh(item)

        return StandardResponse(
            status_code=200,
            message="Invoice item updated successfully",
            data=item
        )
    except Exception as e:
        await db.rollback()
        return ErrorResponse(
            status_code=500,
            message="Failed to update invoice item",
            error=str(e)
        )

@router.get("/get-invoice-item")
async def get_invoice_item(
    item_id: UUID,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[Optional[str], Header()] = None
):
    try:
        result = await db.execute(select(InvoiceItem).where(InvoiceItem.id == item_id))
        item = result.scalar_one_or_none()

        if not item:
            return ErrorResponse(status_code=404, message="Invoice item not found")

        return StandardResponse(
            status_code=200,
            message="Invoice item fetched successfully",
            data=item
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Failed to fetch invoice item",
            error=str(e)
        )
    
@router.get("/get-invoice-item-list")
async def list_invoice_items(
    invoice_id: UUID,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[Optional[str], Header()] = None
):
    try:
        result = await db.execute(select(InvoiceItem).where(InvoiceItem.invoice_id == invoice_id))
        items = result.scalars().all()

        return StandardResponse(
            status_code=200,
            message="Invoice items fetched successfully",
            data=items
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Failed to fetch invoice items",
            error=str(e)
        )

@router.delete("/delete-invoice-item")
async def delete_invoice_item(
    item_id: UUID,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[Optional[str], Header()] = None
):
    try:
        result = await db.execute(select(InvoiceItem).where(InvoiceItem.id == item_id))
        item = result.scalar_one_or_none()

        if not item:
            return ErrorResponse(status_code=404, message="Invoice item not found")

        await db.delete(item)
        await db.commit()

        return StandardResponse(
            status_code=200,
            message="Invoice item deleted successfully",
            data={"item_id": str(item_id)}
        )
    except Exception as e:
        await db.rollback()
        return ErrorResponse(
            status_code=500,
            message="Failed to delete invoice item",
            error=str(e)
        )
    
@router.post("/generate-quote",tags=["Quotaion-request"])
async def generate_quote(request: GenerateQuoteRequest, req: Request, db: AsyncSession = Depends(get_db)):
    try:

        authorization = req.headers.get("Authorization")
        if not authorization:
            return ErrorResponse(status_code=401, message="Authorization header is required")
        
        invoice_item_id = request.invoice_item_id
        # ✅ Step 1: Validate the invoice with status 'QUOTATION'
        # invoice_item_stmt = await db.execute(select(InvoiceItem).where(InvoiceItem.id == invoice_item_id, InvoiceItem.status == InvoiceItemStatus.PENDING))
        invoice_item_stmt = await db.execute(select(InvoiceItem).where(InvoiceItem.id == invoice_item_id))
        invoice_item_result = invoice_item_stmt.scalar_one_or_none()
        if not invoice_item_result:
            return ErrorResponse(
                status_code=404,
                message="Invoice item not found or not in PENDING status"
            )
        
        service_obj = await db.get(Services, invoice_item_result.service_id)


        # ✅ Step 2: Fetch any one invoice item for duplication
        # item_stmt = select(InvoiceItem).where(
        #     InvoiceItem.invoice_id == request.invoice_id,
        #     InvoiceItem.service_id == request.service_id
        # ).limit(1)

        # base_item = await db.scalar(item_stmt)
        # if not base_item:
        #     return ErrorResponse(
        #         status_code=404,
        #         message="No invoice item found for this service"
        #     )

        # ✅ Step 3: Add n duplicates (as per number_of_artisans)
        new_items_total = []
        new_items_base = []
        new_items_tax = []
        new_items_booking = []
        new_items_platform = []
        for _ in range(request.number_of_artisans):
            new_item = InvoiceItem(
                invoice_id=invoice_item_result.invoice_id,
                service_id=invoice_item_result.service_id,
                quantity=invoice_item_result.quantity,
                price=invoice_item_result.price,
                tax_percentage=invoice_item_result.tax_percentage,
                tax_amount=invoice_item_result.tax_amount,
                discount_amount=invoice_item_result.discount_amount,
                total_amount=invoice_item_result.total_amount,
                booking_fee=invoice_item_result.booking_fee,
                booking_fee_percentage=invoice_item_result.booking_fee_percentage,
                platform_fee_percentage=invoice_item_result.platform_fee_percentage,
                platform_fee_amount=invoice_item_result.platform_fee_amount,
                parent_id=invoice_item_id
            )
            db.add(new_item)
            new_items_total.append(new_item.total_amount)
            new_items_base.append(new_item.price)
            new_items_tax.append(new_item.tax_amount)
            new_items_booking.append(new_item.booking_fee)
            new_items_platform.append(new_item.platform_fee_amount)

        await db.flush()

        # ✅ Step 4: Recalculate totals for all items under this invoice
        all_items_stmt = select(InvoiceItem).where(InvoiceItem.invoice_id == invoice_item_result.invoice_id)
        all_items_result = await db.scalars(all_items_stmt)
        all_items = all_items_result.all()

        total_base_fee = sum(item.price * item.quantity for item in all_items)
        total_tax = sum(item.tax_amount if item.tax_amount else 0 for item in all_items)
        total_discount = sum(item.discount_amount if item.discount_amount else 0 for item in all_items)
        total_booking_fee = sum(item.booking_fee if item.booking_fee else 0 for item in all_items)
        total_platform_fee = sum(item.platform_fee_amount if item.platform_fee_amount else 0 for item in all_items)
        total_amount = sum(item.total_amount if item.total_amount else 0 for item in all_items)

        invoice_obj = await db.get(Invoice, invoice_item_result.invoice_id)

        booking_query = await db.execute(select(Booking).where(Booking.invoice_id == invoice_obj.id))
        booking_obj = booking_query.scalar_one_or_none()

        # ✅ Step 5: Update invoice totals
        update_stmt = update(Invoice).where(Invoice.id == invoice_item_result.invoice_id).values(
            base_fee=total_base_fee,
            tax_amount=total_tax,
            discount_amount=total_discount,
            total_amount=total_amount,
            booking_fee=total_booking_fee,
            platform_fee_amount=total_platform_fee,
            pending_amount=invoice_obj.pending_amount + sum(new_items_total),
            payment_status=InvoiceStatusEnum.PARTIAL_PAID if booking_obj.payment_type != "CASH" else "QUOTATION"
        )
        await db.execute(update_stmt)
        await db.commit()

        send_push_notification(
                auth_token=authorization,
                title="Quote for extra artisans",
                message="Quote for extra artisans has been generated successfully",
                sender_id=str(invoice_obj.customer_id),
                type="user",
                data={
                    "booking_id": str(invoice_obj.booking_id),
                    "service_id": str(request.service_id),
                    "service_name": str(service_obj.name),
                    "service_price": str(service_obj.price),
                    "platform_fee_percentage":str(invoice_obj.platform_fee_percentage),
                    "platform_fee_amount" :str(total_platform_fee),
                    "tax_amount" : str(total_tax),
                    "tax_percentage": str(invoice_obj.tax_percentage),
                    "currency": "GHS",
                    "pending_amount": str(invoice_obj.pending_amount + sum(new_items_total)),
                    "base_fee": str(sum(new_items_base)),
                    "booking_fee": str(sum(new_items_booking)),
                    "platform_fee": str(sum(new_items_platform)),
                    "tax": str(sum(new_items_tax)),
                    "invoice_id": str(invoice_obj.id),
                    "invoice_status": str(invoice_obj.payment_status),
                    "request_type": "quotation"
                    

                }
            )

        return StandardResponse(
            status_code=200,
            message=f"Quote updated successfully. {request.number_of_artisans} new artisan item(s) added."
        )

    except Exception as e:
        await db.rollback()
        return ErrorResponse(
            status_code=500,
            message="Failed to generate quote",
            error=str(e)
        )


# async def generate_quote(
#     invoice: InvoiceCreate,
#     db: AsyncSession = Depends(get_db)
# ):
#     try:
#         # Here you would implement the logic to generate a quote based on the invoice data
#         # For now, we will just return a success message
#         return StandardResponse(
#             status_code=200,
#             message="Quote generated successfully",
#             data={"quote_id": "dummy-quote-id"}  # Replace with actual quote ID if needed
#         )
#     except Exception as e:
#         await db.rollback()
#         return ErrorResponse(
#             status_code=500,
#             message="Failed to generate quote",
#             error=str(e)
#         )

# @router.post("/generate-invoice")
# async def generate_invoice(
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     try:
#         resp = await get_id_header(Authorization)
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             auth_id = resp_json.get("id")
#             email = resp_json.get("email")
#         else:
#             return {'error': resp.json()}

#         request_dict = request.to_dict()

        
#         # new_category = await create_record(db, models.Category, request_dict)
#         # if isinstance(new_category, str):
#         #     return {"error": new_category}
#         return {'data': 'invoice generated successfully'}

#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error: {e}")


# @router.get("/category-read/{id}", response_model=schemas.CategoryResponse)
# async def read_category(
#     id: str,
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     try:
#         resp = await get_id_header(Authorization)
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             auth_id = resp_json.get("id")
#         else:
#             return {'error': resp.json()}

#         query = select(models.Category).filter(models.Category.id == uuid.UUID(id))
#         result = await db.execute(query)
#         category_obj = result.scalars().first()
#         return category_obj
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error: {e}")

# async def get_categories_with_services(db, category_id):
#     query = select(models.Category).filter(models.Category.parent_id == category_id)
#     # query = select(models.Category).filter(models.Category.parent_id == category_id).options(selectinload(models.Category.services))
#     result = await db.execute(query)
#     categories = result.scalars().all()
#     return categories

# @router.get("/category-list")
# async def list_category(
#     q: Optional[str] = None,
#     skip: int = 0,
#     limit: int = 10,
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     try:
#         resp = await get_id_header(Authorization)
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             auth_id = resp_json.get("id")
#         else:
#             return {'error': resp.json()}
        
#         # Total category
#         query = select(models.Category).filter(models.Category.parent_id == None)
#         result = await db.execute(query)
#         categories = result.scalars().all()

#         lst = []
#         for category_obj in categories:
#             category_data = category_obj.__dict__
#             sub_services = await get_categories_with_services(db, category_data['id'])
#             category_data['services'] = sub_services
#             lst.append(category_data)


#         # Total count
#         count_query = select(func.count()).select_from(models.Category)
#         total_result = await db.execute(count_query)
#         total_count = total_result.scalar()

#         return {"total": total_count, "data": lst}

#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error: {e}")
    


# @router.put("/category-update/{id}")
# async def update_category(
#     id: str,
#     data: schemas.UpdateCategory = Depends(),
#     banner: Optional[UploadFile] = File(None),
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     try:
#         resp = await get_id_header(Authorization)
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             auth_id = resp_json.get("id")
#         else:
#             return {'error': resp.json()}
        
#         query = select(models.Category).filter(models.Category.id == uuid.UUID(id))
#         result = await db.execute(query)
#         get_category = result.scalars().first()

#         if banner:
#             if get_category.banner:
#                 delete_file(bucket_name=get_settings().SERVICES_BUCKET_NAME, file_name=get_category.banner.split("/")[-1])
#             image_url = upload_file(banner, get_settings().SERVICES_BUCKET_NAME, "image/")
#             setattr(get_category, "banner", image_url)

#         res = await update_record(db, data, get_category)
#         return res
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error: {e}")
    

# @router.delete("/category-delete/{id}")
# async def delete_category(
#     id: str,
#     db: AsyncSession = Depends(get_db),
#     Authorization: Annotated[str, Header()] = None,
# ):
#     try:
#         resp = await get_id_header(Authorization)
#         if resp.status_code == 200:
#             resp_json = resp.json()
#             auth_id = resp_json.get("id")
#         else:
#             return {'error': resp.json()}
        
#         query = select(models.Category).filter(models.Category.id == uuid.UUID(id))
#         result = await db.execute(query)
#         get_category = result.scalars().first()

#         if get_category is None:
#             return {"error": "Category not found"}
        
#         res = await delete_record(db, get_category)
#         return res
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error: {e}")
