"""Booking enums created

Revision ID: e3398a4f2af5
Revises: f29722a40eeb
Create Date: 2025-06-17 15:25:45.279077

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e3398a4f2af5'
down_revision: Union[str, None] = 'f29722a40eeb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bookings', sa.Column('pricing_type', postgresql.ENUM('FIXED', 'MEASURED', 'CUSTOM', name='pricingtype'), server_default='FIXED', nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bookings', 'pricing_type')
    # ### end Alembic commands ###
