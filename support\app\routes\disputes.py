from app.utils.notification import send_push_notification
from sqlalchemy import or_
from tokenize import String
from fastapi import APIRouter, Depends, Header, HTTPException, File, UploadFile, Query , Request
from app.utils.file_uploader import upload_file
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from typing import Annotated
from app.utils.auth import get_id_header 
from app.database import get_db
from app.schemas.disputes import (
    DisputesCreate,
    DisputesUpdate,
    IssuesCreate,
    IssuesUpdate,
    IssueCategoryCreate,
    DisputeCommentCreate,
    DisputeListFilters,
    DisputeAssignmentUpdate,
)
from app.utils.crud import create_record
from app.models import (
    Disputes,
    IssueTypes,
    IssueCategory,
    DisputeComment,
    DisputeHistory,
    DisputeEventTypeModel,
    SPRatings,
    Ratings,
)
from app.services.disputes import (
    generate_case_number,
)
from sqlalchemy import func, select
from typing import List, Optional
import uuid
from datetime import datetime

from app.models import Booking , ArtisanAssigned
from app.models import UserProfiles   # this table is using for admin , users , service provider
from app.schemas.helper import ErrorResponse, StandardResponse
from app.s3_upload import upload_file_direct
from app.config import get_settings
from app.utils.enums import UserType, DisputeStatus, DisputePriority, DisputeType
from app.utils.auth import permission_checker
from fastapi import Depends , Header
from fastapi.responses import JSONResponse

router = APIRouter()


def get_user(db: Session, user_id: str) -> Optional[dict]:
    """Helper function to get user data from UserProfiles table"""
    user = db.query(UserProfiles).filter(UserProfiles.id == user_id).first()
    if user:
        return {
            "id": str(user.id),
            "first_name": user.first_name or "",
            "last_name": user.last_name or "",
            "email": user.email or "",
            "phone_number": user.phone_number or "",
            "country_code": user.country_code or "",
            "status": user.status.value if user.status else None,
            # "profile_image_url": user.profile_image_url or "",
            "gender": user.gender.value if user.gender else None
        }
    return None


def build_full_name(first_name: str, last_name: str, fallback_id: str = None) -> str:
    """Helper function to safely build full name from first_name and last_name"""
    # Handle None values and empty strings
    first = (first_name or "").strip()
    last = (last_name or "").strip()
    full_name = f"{first} {last}".strip()
    # Return full name if not empty, otherwise return fallback
    if full_name:
        return full_name
    # elif fallback_id:
    #     return f"User-{str(fallback_id)[:8]}"
    else:
        return None


@router.post("/dispute-list")
async def dispute_list(
    request: DisputeListFilters,
    req:Request,
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        Authorization = req.headers.get("Authorization")
        if not Authorization:
            return ErrorResponse(status_code=401, message="Authorization header is required")
    
        # Get authenticated user info
        account_id = user.get("user_id")
        print("user_id", account_id)
        role = user.get("role_name")
        
        if not account_id or not role:
            return ErrorResponse(status_code=401, message="Account ID and role are required")

        # Start building the query
        query = db.query(Disputes)

        # Apply user/artisan filters
        if request.user_id:
            query = query.filter(Disputes.customer_id == request.user_id)

        if request.artisan_id:
            query = query.filter(Disputes.customer_id == request.artisan_id)
        
        if request.assigned_to:
            query = query.filter(Disputes.assigned_to == request.assigned_to)

        if account_id and role == 'agent' :
            query = query.filter(Disputes.assigned_to == account_id)

        if request.q:
            query = query.filter(
                or_(
                    Disputes.case_number.ilike(f"%{request.q}%"),
                    Disputes.description.ilike(f"%{request.q}%"),
                )
            )

        # Apply dropdown filters - list values
        if request.priority:
            try:
                priority_list = (
                    request.priority
                    if isinstance(request.priority, list)
                    else [request.priority]
                )
                priority_enums = [DisputePriority(p) for p in priority_list]
                query = query.filter(Disputes.priority.in_(priority_enums))
            except ValueError as e:
                print(f"Invalid priority value: {e}")
                pass

        if request.status:
            try:
                status_list = (
                    request.status
                    if isinstance(request.status, list)
                    else [request.status]
                )
                status_enums = [DisputeStatus(s) for s in status_list]
                query = query.filter(Disputes.status.in_(status_enums))
            except ValueError as e:
                print(f"Invalid status value: {e}")
                pass

        if request.dispute_type:
            try:
                type_list = (
                    request.dispute_type
                    if isinstance(request.dispute_type, list)
                    else [request.dispute_type]
                )
                type_enums = [DisputeType(dt) for dt in type_list]
                query = query.filter(Disputes.dispute_type.in_(type_enums))
            except ValueError as e:
                print(f"Invalid dispute_type value: {e}")
                pass

        # Get total count before pagination
        # CHANGE: Get total count before applying filters
        total_count = db.query(func.count(Disputes.id)).scalar()

        # CHANGE: Get total count after filters but before pagination
        total_filter_count = query.count()

        dispute_subquery = query.with_entities(Disputes.id).subquery()

        # Count unassigned disputes (not paginated)
        unassigned_count = db.query(func.count()).select_from(Disputes).filter(
            Disputes.assigned_to == None
        ).scalar()

        # Count disputes grouped by status
        status_counts_query = (
            db.query(Disputes.status, func.count())
            .filter(Disputes.id.in_(db.query(dispute_subquery.c.id)))
            .group_by(Disputes.status)
            .all()
        )
        by_status = {
            status.value if status else "UNKNOWN": count
            for status, count in status_counts_query
        }

        # Count disputes grouped by priority
        priority_counts_query = (
            db.query(Disputes.priority, func.count())
            .filter(Disputes.id.in_(db.query(dispute_subquery.c.id)))
            .group_by(Disputes.priority)
            .all()
        )
        by_priority = {
            priority.value if priority else "UNKNOWN": count
            for priority, count in priority_counts_query
        }

        # Build counts dictionary
        counts = {
            "unassigned_count": unassigned_count,
            "by_status": by_status,
            "by_priority": by_priority        
        }

        offset = (request.skip - 1) * request.limit if request.skip > 0 else 0
        # Apply pagination and ordering
        disputes = (
            query.order_by(Disputes.created_at.desc())
            .limit(request.limit)
            .offset(offset)
            .all()
        )

        # Convert disputes to dictionaries for the response
        dispute_list = []
        for dispute in disputes:
            # Initialize assigned_to_details
            assigned_to_details = None

            # If there's an assigned agent, fetch their details from UserProfiles
            if dispute.assigned_to:
                try:
                    admin_details = get_user(db, dispute.assigned_to)
                    if admin_details:
                        first_name = admin_details.get("first_name", "")
                        last_name = admin_details.get("last_name", "")
                        full_name = build_full_name(first_name, last_name, dispute.assigned_to)

                        assigned_to_details = {
                            "id": dispute.assigned_to,
                            "name": full_name,
                            "email": admin_details.get("email"),
                            "phone_number": admin_details.get("phone_number"),
                            "role": admin_details.get("role_id"),
                            "status": admin_details.get("status"),
                            "first_name": first_name,
                            "last_name": last_name,
                            "gender": admin_details.get("gender"),
                        }
                except Exception as e:
                    print(f"Error fetching admin details: {str(e)}")
                    import traceback
                    traceback.print_exc()

            dispute_dict = {
                "id": str(dispute.id),
                "case_number": dispute.case_number,
                "booking_id": str(dispute.booking_id),
                "customer_id": str(dispute.customer_id),
                "customer_name": dispute.customer_name,
                "merchant_id": str(dispute.merchant_id),
                "merchant_name": dispute.merchant_name,
                "dispute_type": (
                    dispute.dispute_type.value if dispute.dispute_type else None
                ),
                "dispute_sub_type": dispute.dispute_sub_type,
                "status": dispute.status.value if dispute.status else None,
                "priority": dispute.priority.value if dispute.priority else None,
                "description": dispute.description,
                "assigned_to": assigned_to_details,  # Now an object with agent details
                "user_type": dispute.user_type.value if dispute.user_type else None,
                "created_at": (
                    dispute.created_at.isoformat() if dispute.created_at else None
                ),
                "updated_at": (
                    dispute.updated_at.isoformat() if dispute.updated_at else None
                ),
            }
            dispute_list.append(dispute_dict)

        # Prepare response data
        response_data = {
            "disputes": dispute_list,
            "pagination": {
                "total": total_filter_count,
                "page": request.skip,
                "limit": request.limit,
                "pages": (total_filter_count + request.limit - 1) // request.limit,
            },
            "counts": counts,
            "total_count": total_count,  # All rows in DB
            "total_filter_count": total_filter_count
        }

        return StandardResponse(
            status_code=200, message="Dispute List", data=response_data
        )
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.post("/dispute-create")
async def dispute_create(
    req: Request,
    data: DisputesCreate = Depends(),
    attachments: List[UploadFile] = File(None),
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    try:
        if isinstance(user, JSONResponse):
            return user
        Authorization = req.headers.get("Authorization")
        if not Authorization:
            return ErrorResponse(status_code=401, message="Authorization header is required")
            
        #Get authenticated user info
        account_id = user.get("user_id")
        role= user.get("role_name")

        
        if not account_id:
            return ErrorResponse(status_code=401, message="User ID is required")

        request_dict = data.__dict__
        booking = db.query(Booking).filter(Booking.id == data.booking_id).first()

        if not booking:
            return ErrorResponse(status_code=404, message="Booking not found")

        # check the user_id is in user_profiles table
        user_data = get_user(db, booking.user_id)
        if not user_data:
            return ErrorResponse(status_code=404, message="User not found")

        # Get artisan assigned record
        artisan_assigned = db.query(ArtisanAssigned).filter(ArtisanAssigned.invoice_id == booking.invoice_id).first()
        
        if not artisan_assigned:
            return ErrorResponse(status_code=404, message="No artisan assigned found for this booking")

        issue_category = (
            db.query(IssueCategory)
            .filter(IssueCategory.id == data.issue_category_id)
            .first()
        )
        
        if not issue_category:
            return ErrorResponse(status_code=404, message="Issue category not found")
        # Generate case number
        case_number = generate_case_number(db, issue_category.code)
        request_dict["case_number"] = case_number
        # request_dict.pop("issue_category_id")

        # Set customer and merchant details
        if data.user_type == UserType.USER:
            request_dict["customer_id"] = booking.user_id
            request_dict["merchant_id"] = artisan_assigned.artisan_id

            # Get customer name from UserProfiles
            user_data = get_user(db, booking.user_id)
            print(f"User response: {user_data}")
            if user_data:
                first_name = user_data.get("first_name", "")
                last_name = user_data.get("last_name", "")
                request_dict["customer_name"] = build_full_name(first_name, last_name, booking.user_id)

            # Get merchant name from UserProfiles  
            artisan_data = get_user(db, artisan_assigned.artisan_id)
            print(f"Artisan response: {artisan_data}")
            if artisan_data:
                first_name = artisan_data.get("first_name", "")
                last_name = artisan_data.get("last_name", "")
                request_dict["merchant_name"] = build_full_name(first_name, last_name, artisan_assigned.artisan_id)
        else:
            request_dict["customer_id"] = artisan_assigned.artisan_id
            request_dict["merchant_id"] = booking.user_id

            # Get customer name (artisan in this case) from UserProfiles
            artisan_data = get_user(db, artisan_assigned.artisan_id)
            print(f"Artisan response: {artisan_data}")
            if artisan_data:
                first_name = artisan_data.get("first_name", "")
                last_name = artisan_data.get("last_name", "")
                request_dict["customer_name"] = build_full_name(first_name, last_name, artisan_assigned.artisan_id)

            # Get merchant name (user in this case) from UserProfiles
            user_data = get_user(db, booking.user_id)
            print(f"User response: {user_data}")
            if user_data:
                first_name = user_data.get("first_name", "")
                last_name = user_data.get("last_name", "")
                request_dict["merchant_name"] = build_full_name(first_name, last_name, booking.user_id)

        # Set default names if we couldn't get them from the database
        if not request_dict.get("customer_name"):
            request_dict["customer_name"] = (
                f"Customer-{str(request_dict['customer_id'])[:8]}"
            )

        if not request_dict.get("merchant_name"):
            request_dict["merchant_name"] = (
                f"Merchant-{str(request_dict['merchant_id'])[:8]}"
            )

        # If priority is not provided, get it from the category
        if not data.priority:
            # Find the category that matches the dispute type
            category = (
                db.query(IssueCategory)
                .filter(
                    IssueCategory.code
                    == data.dispute_type.value.upper().replace(" ", "_")
                )
                .first()
            )

            if category:
                request_dict["priority"] = category.default_priority

        # Handle file uploads
        supporting_docs = []
        if attachments:
            settings = get_settings()
            for attachment in attachments:
                if attachment is not None and attachment.filename:
                    # Generate a unique filename
                    file_ext = attachment.filename.split(".")[-1]
                    unique_filename = f"{uuid.uuid4()}.{file_ext}"

                    # Upload to S3 or your storage service
                    file_url = upload_file_direct(attachment, settings.S3_IMAGES_FOLDER)
                    print(file_url["filename"])
                    supporting_docs.append(file_url["filename"])

        if supporting_docs:
            request_dict["supporting_documents"] = ",".join(supporting_docs)

        # Set created_by
        request_dict["created_by"] = account_id
        request_dict["status"] = DisputeStatus.OPEN

        # Create the dispute
        dispute = create_record(db, Disputes, request_dict)
        print(dispute, "dispute")

        # Check if dispute creation was successful
        if isinstance(dispute, str):  # Error message returned as string
            return ErrorResponse(
                status_code=500, message="Failed to create dispute", error=dispute
            )

        # Create history entries
        history_entries = [
            DisputeHistory(
                dispute_id=dispute.id,
                event_code="DISPUTE_CREATED",
                user=request_dict.get("customer_name", account_id),
            )
        ]

        for entry in history_entries:
            db.add(entry)

        db.commit()
        db.refresh(dispute)

        # Manually create a dictionary from the dispute object
        dispute_dict = {
            "id": str(dispute.id),
            "case_number": dispute.case_number,
            "booking_id": str(dispute.booking_id),
            "customer_id": str(dispute.customer_id),
            "customer_name": dispute.customer_name,
            "merchant_id": str(dispute.merchant_id),
            "merchant_name": dispute.merchant_name,
            "dispute_type": (
                dispute.dispute_type.value if dispute.dispute_type else None
            ),
            "dispute_sub_type": dispute.dispute_sub_type,
            "status": dispute.status.value if dispute.status else None,
            "priority": dispute.priority.value if dispute.priority else None,
            "description": dispute.description,
            "supporting_documents": dispute.supporting_documents,
            "created_at": (
                dispute.created_at.isoformat() if dispute.created_at else None
            ),
            "updated_at": (
                dispute.updated_at.isoformat() if dispute.updated_at else None
            ),
        }

        if dispute.user_type == UserType.USER:
            send_push_notification(
                auth_token=Authorization,
                title=f"Dispute {dispute.status.value if dispute.status else None}",
                message=dispute.description,
                type="user",
                sender_id=dispute.customer_id,
            )

        if dispute.user_type == UserType.ARTISAN:
            send_push_notification(
                auth_token=Authorization,
                title=f"Dispute {dispute.status.value if dispute.status else None}",
                message=dispute.description,
                type="service_provider",
                sender_id=dispute.customer_id,
            )

        return StandardResponse(
            status_code=200, message="Dispute Created", data=dispute_dict
        )
    except Exception as e:
        db.rollback()
        import traceback

        print(traceback.format_exc())
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.get("/dispute-read/{id}")
async def dispute_read(
    id: str,
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    try:
        if isinstance(user, JSONResponse):
            return user

        dispute = db.query(Disputes).filter(Disputes.id == id).first()
        if not dispute:
            return ErrorResponse(status_code=404, message="Dispute not available")

        # Get comments and history
        comments = (
            db.query(DisputeComment)
            .filter(DisputeComment.dispute_id == id)
            .order_by(DisputeComment.date)
            .all()
        )

        # Get history with event names
        history_query = (
            db.query(DisputeHistory, DisputeEventTypeModel.name.label("event_name"))
            .join(
                DisputeEventTypeModel,
                DisputeHistory.event_code == DisputeEventTypeModel.code,
            )
            .filter(DisputeHistory.dispute_id == id)
            .order_by(DisputeHistory.date)
        )

        history = []
        for h, event_name in history_query:
            history_item = {
                "id": h.id,
                "event_code": h.event_code,
                "event_name": event_name,
                "user": h.user,
                "date": h.date,
            }
            history.append(history_item)

        # Add comments to history
        for comment in comments:
            comment_history = {
                "id": str(comment.id),
                "event_code": "COMMENT",
                "event_name": "Comment Added",
                "user": comment.author_name,
                "comment": comment.comment,
                "date": comment.date,
            }
            history.append(comment_history)

        # Sort history by date
        history.sort(key=lambda x: x["date"])

        # Fetch phone numbers from UserProfiles based on user_type
        customer_phone = None
        merchant_phone = None

        if dispute.user_type == UserType.USER:
            # Customer is the user, merchant is the artisan
            print(dispute.customer_id, "dispute.customer_id")
            user_data = get_user(db, dispute.customer_id)
            print(user_data, "user_data")
            if user_data:
                customer_phone = user_data.get("phone_number")

            artisan_data = get_user(db, dispute.merchant_id)
            if artisan_data:
                merchant_phone = artisan_data.get("phone_number")
        else:
            # Customer is the artisan, merchant is the user
            artisan_data = get_user(db, dispute.customer_id)    
            if artisan_data:
                customer_phone = artisan_data.get("phone_number")

            user_data = get_user(db, dispute.merchant_id)
            if user_data:
                merchant_phone = user_data.get("phone_number")

        # Create a dictionary from the dispute object with added phone numbers
        dispute_dict = {
            "id": str(dispute.id),
            "case_number": dispute.case_number,
            "booking_id": str(dispute.booking_id),
            "customer_id": str(dispute.customer_id),
            "customer_name": dispute.customer_name,
            "customer_phone": customer_phone,
            "merchant_id": str(dispute.merchant_id),
            "merchant_name": dispute.merchant_name,
            "merchant_phone": merchant_phone,
            "dispute_type": (
                dispute.dispute_type.value if dispute.dispute_type else None
            ),
            "dispute_sub_type": dispute.dispute_sub_type,
            "status": dispute.status.value if dispute.status else None,
            "priority": dispute.priority.value if dispute.priority else None,
            "description": dispute.description,
            "supporting_documents": dispute.supporting_documents,
            "user_type": dispute.user_type.value if dispute.user_type else None,
            "assigned_to": dispute.assigned_to,
            "transaction_id": dispute.transaction_id,
            "transaction_date": (
                dispute.transaction_date.isoformat()
                if dispute.transaction_date
                else None
            ),
            "transaction_amount": dispute.transaction_amount,
            "disputed_amount": dispute.disputed_amount,
            "currency": dispute.currency,
            "resolution_date": (
                dispute.resolution_date.isoformat() if dispute.resolution_date else None
            ),
            "resolution_notes": dispute.resolution_notes,
            "created_at": (
                dispute.created_at.isoformat() if dispute.created_at else None
            ),
            "updated_at": (
                dispute.updated_at.isoformat() if dispute.updated_at else None
            ),
        }

        # Get rating information for this dispute
        rating_info = None
        try:
            # First, get the ref_id from SPRatings table using the dispute_id
            sp_rating = db.query(SPRatings).filter(SPRatings.dispute_id == id).first()
            
            if sp_rating and sp_rating.ref_id:
                # Then, get the rating details from Ratings table using the ref_id
                rating = db.query(Ratings).filter(Ratings.id == sp_rating.ref_id).first()
                
                if rating:
                    rating_info = rating.rating
        except Exception as e:
            print(f"Error fetching rating info: {str(e)}")
            rating_info = None

        # Create response data
        response_data = {
            "dispute": dispute_dict,
            "comments": comments,
            "history": history,
            "rating": rating_info,
        }

        return StandardResponse(
            status_code=200, message="Dispute Details", data=response_data
        )
    except Exception as e:
        import traceback

        print(traceback.format_exc())
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.put("/dispute-update/{id}")
async def dispute_update(
    id: str,
    request: DisputesUpdate,
    req: Request,
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    try:
        if isinstance(user, JSONResponse):
            return user
        Authorization = req.headers.get("Authorization")
        if not Authorization:
            return ErrorResponse(status_code=401, message="Authorization header is required")
            
        #Get authenticated user info
        account_id = user.get("user_id")
        role= user.get("role_name")


        dispute = db.query(Disputes).filter(Disputes.id == id).first()
        if not dispute:
            return ErrorResponse(status_code=404, message="Dispute not available")

        # Get user name from UserProfiles
        print(account_id, "account_id")
        user_data = get_user(db, account_id)
        print(user_data, "user_data")
        if user_data:
            first_name = user_data.get("first_name", "")
            last_name = user_data.get("last_name", "")
            user_name = build_full_name(first_name, last_name, account_id)
        else:
            user_name = f"User-{str(account_id)[:8]}"

        # Track status changes for history
        old_status = dispute.status

        # Update dispute fields
        for key, value in request.dict(exclude_unset=True).items():
            setattr(dispute, key, value)

        dispute.updated_by = account_id
        dispute.updated_at = datetime.now()

        # Add history entry for status change
        if request.status and request.status != old_status:
            event_code = f"STATUS_CHANGED_TO_{request.status.upper()}"
            history_entry = DisputeHistory(
                dispute_id=id, event_code=event_code, user=user_name
            )
            db.add(history_entry)
            if request.status == DisputeStatus.IN_PROGRESS:
                history_entry = DisputeHistory(
                    dispute_id=id, event_code="ASSIGNED_TO_AGENT", user=user_name
                )
                db.add(history_entry)

        db.commit()
        db.refresh(dispute)

        return StandardResponse(
            status_code=200, message="Dispute Updated", data=dispute
        )
    except Exception as e:
        db.rollback()
        import traceback

        print(traceback.format_exc())
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.post("/dispute-comment-create")
async def add_dispute_comment(
    data: DisputeCommentCreate,
    req: Request,
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    try:
        if isinstance(user, JSONResponse):
            return user
        Authorization = req.headers.get("Authorization")
        if not Authorization:
            return ErrorResponse(status_code=401, message="Authorization header is required")
            
        #Get authenticated user info
        account_id = user.get("user_id")
        role= user.get("role_name")
        print(account_id, "account_id33333333333333333333333333")
        print(role, "role11111111111111111111111111111")

        # Find the dispute
        dispute = db.query(Disputes).filter(Disputes.id == data.dispute_id).first()
        if not dispute:
            return ErrorResponse(status_code=404, message="Dispute not found")

        # Get user name for the comment from UserProfiles
        print("Getting user name for account_id:", account_id)  # Debug log
        
        user_data = get_user(db, account_id)
        if user_data:
            first_name = user_data.get("first_name", "")
            last_name = user_data.get("last_name", "")
            user_name = build_full_name(first_name, last_name, account_id)
        else:
            user_name = f"User-{str(account_id)[:8]}"

        # Create comment
        comment = DisputeComment(
            id=uuid.uuid4(),
            dispute_id=data.dispute_id,
            author_id=account_id,
            author_name=user_name,
            comment=data.comment,
        )
        db.add(comment)

        # # Create history entry for comment
        # history_entry = DisputeHistory(
        #     dispute_id=dispute.id, event_code="COMMENT_ADDED", user=user_name
        # )
        # db.add(history_entry)

        # Handle status change if provided
        old_status = dispute.status
        if data.status and data.status != old_status:
            dispute.status = data.status

            # Add history entry for status change
            event_code = f"STATUS_CHANGED_TO_{data.status.value.upper()}"
            status_history_entry = DisputeHistory(
                dispute_id=dispute.id, event_code=event_code, user=user_name
            )
            db.add(status_history_entry)

            # # Add special history entries for specific statuses
            # if data.status == DisputeStatus.IN_PROGRESS:
            #     agent_history_entry = DisputeHistory(
            #         dispute_id=dispute.id,
            #         event_code="ASSIGNED_TO_AGENT",
            #         user=user_name,
            #     )
            #     db.add(agent_history_entry)

            # If status is RESOLVED, set resolution date
            if data.status == DisputeStatus.RESOLVED:
                dispute.resolution_date = datetime.now()
                if data.resolution_notes:
                    dispute.resolution_notes = data.resolution_notes

        # Update dispute last updated timestamp
        dispute.updated_at = datetime.now()
        dispute.updated_by = account_id

        db.commit()
        db.refresh(comment)

        # Prepare response
        comment_dict = {
            "id": str(comment.id),
            "dispute_id": str(comment.dispute_id),
            "author_id": str(comment.author_id),
            "author_name": comment.author_name,
            "comment": comment.comment,
            # "created_at": comment.date.isoformat() if comment.date else None
        }

        # Include status change in response if applicable
        if data.status and data.status != old_status:
            comment_dict["status_changed"] = {
                "from": old_status.value if old_status else None,
                "to": data.status.value,
            }

        if dispute.user_type == UserType.USER:
            send_push_notification(
                auth_token=Authorization,
                title=f"Dispute {dispute.status.value}",
                sender_id=dispute.customer_id,
                message=data.comment,
                type="user",
            )

        if dispute.user_type == UserType.ARTISAN:
            send_push_notification(
                auth_token=Authorization,
                title=f"Dispute {dispute.status.value}",
                sender_id=dispute.customer_id,
                message=data.comment,
                type="service_provider",
            )

        return StandardResponse(
            status_code=200, message="Comment added successfully", data=comment_dict
        )
    except Exception as e:
        db.rollback()
        import traceback

        print(traceback.format_exc())
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.get("/dispute-event-types")
async def get_dispute_event_types(
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        # if isinstance(user, JSONResponse):
        #     return user

        # Get all event types
        event_types = db.query(DisputeEventTypeModel).all()

        return StandardResponse(
            status_code=200,
            message="Dispute event types retrieved successfully",
            data=event_types,
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.get("/issue-types")
async def get_issue_types(
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:

        if isinstance(user, JSONResponse):
            return user

        # Get all categories with their issue types
        categories = db.query(IssueCategory).all()

        # Format the response
        result = {}
        for category in categories:
            issue_types = (
                db.query(IssueTypes).filter(IssueTypes.category_id == category.id).all()
            )

            result[category.code] = [
                {"code": issue.code, "description": issue.description}
                for issue in issue_types
            ]

        return StandardResponse(
            status_code=200, message="Issue types retrieved successfully", data=result
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.post("/issue-category")
async def create_issue_category(
    data: IssueCategoryCreate,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):
            return user

        # Check if category with same code already exists
        # existing = db.query(IssueCategory).filter(IssueCategory.code == data.code).first()
        # if existing:
        #     return ErrorResponse(status_code=400, message="Category with this code already exists")

        # Create new category
        category = IssueCategory(
            id=uuid.uuid4(),
            name=data.name,
            code=data.code,
            user_type=data.user_type,
            description=data.description,
            default_priority=data.default_priority,
        )
        db.add(category)
        db.flush()  # Flush to get the ID

        # Create issue types for this category
        issue_types = []
        for issue_type in data.issue_types:
            print(issue_type)
            new_issue_type = IssueTypes(
                id=uuid.uuid4(),
                category_id=category.id,
                code=issue_type.code,
                description=issue_type.description,
            )
            db.add(new_issue_type)
            issue_types.append(new_issue_type)

        db.commit()
        db.refresh(category)

        # Prepare response with category and issue types
        response_data = {
            "category": {
                "id": str(category.id),
                "name": category.name,
                "code": category.code,
                "description": category.description,
                "default_priority": category.default_priority.value if category.default_priority else None,
                "user_type": category.user_type.value if category.user_type else None,
                "is_active": category.is_active,
                "created_at": category.created_at,
                "updated_at": category.updated_at
            },
            "issue_types": [
                {
                    "id": str(issue_type.id),
                    "code": issue_type.code,
                    "description": issue_type.description,
                    "category_id": str(issue_type.category_id)
                }
                for issue_type in issue_types
            ]
        }

        return StandardResponse(
            status_code=200,
            message="Dispute category created successfully",
            data=response_data,
        )
    except Exception as e:
        db.rollback()
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.get("/issue-categories/{user_type}")
async def get_issue_categories(\
    user_type: str,
    req: Request,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):
            return user

        # Get all categories
        categories = (
            db.query(IssueCategory).filter(IssueCategory.user_type == user_type).all()
        )

        # Prepare response with categories and their issue types
        result = []
        for category in categories:
            issue_types = (
                db.query(IssueTypes).filter(IssueTypes.category_id == category.id).all()
            )

            category_data = {
                "id": category.id,
                "name": category.name,
                "code": category.code,
                "description": category.description,
                "default_priority": category.default_priority,
                "issue_types": [
                    {
                        "id": issue.id,
                        "code": issue.code,
                        "description": issue.description,
                    }
                    for issue in issue_types
                ],
            }
            result.append(category_data)

        return StandardResponse(
            status_code=200,
            message="Dispute categories retrieved successfully",
            data=result,
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.put("/issue-category/{category_id}")
async def update_issue_category(
    category_id: str,
    data: IssueCategoryCreate,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):
            return user

        # Check if category exists
        category = (
            db.query(IssueCategory).filter(IssueCategory.id == category_id).first()
        )
        if not category:
            return ErrorResponse(status_code=404, message="Category not found")

        # Check if another category with the same code exists
        if data.code != category.code:
            existing = (
                db.query(IssueCategory).filter(IssueCategory.code == data.code).first()
            )
            if existing:
                return ErrorResponse(
                    status_code=400,
                    message="Another category with this code already exists",
                )

        # Update category
        category.name = data.name
        category.code = data.code
        category.description = data.description
        category.user_type = data.user_type
        category.default_priority = data.default_priority
        category.updated_at = datetime.now()

        # Delete existing issue types for this category
        db.query(IssueTypes).filter(IssueTypes.category_id == category_id).delete()

        # Create new issue types
        issue_types = []
        for issue_type in data.issue_types:
            new_issue_type = IssueTypes(
                id=uuid.uuid4(),
                category_id=category.id,
                code=issue_type.code,
                description=issue_type.description,
            )
            db.add(new_issue_type)
            issue_types.append(new_issue_type)

        db.commit()
        db.refresh(category)

        # Prepare response with category and issue types
        response_data = {"category": category, "issue_types": issue_types}

        return StandardResponse(
            status_code=200,
            message="Dispute category updated successfully",
            data=response_data,
        )
    except Exception as e:
        db.rollback()
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.delete("/issue-category/{category_id}")
async def delete_issue_category(
    category_id: str,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):
            return user
            
        # Check if category exists
        category = (
            db.query(IssueCategory).filter(IssueCategory.id == category_id).first()
        )
        if not category:
            return ErrorResponse(status_code=404, message="Category not found")

        # Delete issue types for this category
        db.query(IssueTypes).filter(IssueTypes.category_id == category_id).delete()

        # Delete category
        db.delete(category)
        db.commit()

        return StandardResponse(
            status_code=200, message="Dispute category deleted successfully"
        )
    except Exception as e:
        db.rollback()
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.get("/issue-category/{category_id}")
async def get_issue_category(
    category_id: str,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):
            return user

        # Check if category exists
        category = (
            db.query(IssueCategory).filter(IssueCategory.id == category_id).first()
        )
        if not category:
            return ErrorResponse(status_code=404, message="Category not found")

        # Get issue types for this category
        issue_types = (
            db.query(IssueTypes).filter(IssueTypes.category_id == category_id).all()
        )

        # Prepare response
        category_data = {
            "id": category.id,
            "name": category.name,
            "code": category.code,
            "description": category.description,
            "default_priority": category.default_priority,
            "issue_types": [
                {"id": issue.id, "code": issue.code, "description": issue.description}
                for issue in issue_types
            ],
        }

        return StandardResponse(
            status_code=200,
            message="Dispute category retrieved successfully",
            data=category_data,
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.put("/dispute-status/{dispute_id}")
async def update_dispute_status(
    dispute_id: str,
    status: DisputeStatus,
    resolution_notes: Optional[str] = None,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):
            return user
            
        #Get authenticated user info
        account_id = user.get("user_id")
        role= user.get("role_name")
        
        if not account_id or not role:
            return ErrorResponse(status_code=401, message="User authentication failed")


        # Find the dispute
        dispute = db.query(Disputes).filter(Disputes.id == dispute_id).first()
        if not dispute:
            return ErrorResponse(status_code=404, message="Dispute not found")

        # Get user name for history entry from UserProfiles
        user_data = get_user(db, account_id)
        if user_data:
            first_name = user_data.get("first_name", "")
            last_name = user_data.get("last_name", "")
            user_name = build_full_name(first_name, last_name, account_id)
        else:
            user_name = f"User-{str(account_id)[:8]}"

        # Update dispute status
        old_status = dispute.status
        dispute.status = status
        dispute.updated_at = datetime.now()
        dispute.updated_by = account_id

        if resolution_notes:
            dispute.resolution_notes = resolution_notes

        # If status is RESOLVED, set resolution date
        if status == DisputeStatus.RESOLVED:
            dispute.resolution_date = datetime.now()
        # Create history entry for status change
        history_entry = DisputeHistory(
            dispute_id=dispute.id,
            event_code=f"STATUS_CHANGED_TO_{status.value.upper()}",
            user=user_name,
        )
        db.add(history_entry)

        db.commit()
        db.refresh(dispute)

        # Prepare response
        dispute_dict = {
            "id": str(dispute.id),
            "case_number": dispute.case_number,
            "status": dispute.status.value,
            "updated_at": (
                dispute.updated_at.isoformat() if dispute.updated_at else None
            ),
            "resolution_notes": dispute.resolution_notes,
            "resolution_date": (
                dispute.resolution_date.isoformat() if dispute.resolution_date else None
            ),
        }

        return StandardResponse(
            status_code=200,
            message=f"Dispute status updated from {old_status.value} to {status.value}",
            data=dispute_dict,
        )
    except Exception as e:
        db.rollback()
        import traceback

        print(traceback.format_exc())
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.get("/dispute-comments/{dispute_id}")
async def get_dispute_comments(
    dispute_id: str,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):
            return user

        # Find the dispute
        dispute = db.query(Disputes).filter(Disputes.id == dispute_id).first()
        if not dispute:
            return ErrorResponse(status_code=404, message="Dispute not found")

        # Get all comments for this dispute
        comments = (
            db.query(DisputeComment)
            .filter(DisputeComment.dispute_id == dispute_id)
            .order_by(DisputeComment.created_at.asc())
            .all()
        )

        # Prepare response
        comment_list = []
        for comment in comments:
            comment_dict = {
                "id": str(comment.id),
                "author_id": str(comment.author_id),
                "author_name": comment.author_name,
                "comment": comment.comment,
                "created_at": (
                    comment.created_at.isoformat() if comment.created_at else None
                ),
            }
            comment_list.append(comment_dict)

        return StandardResponse(
            status_code=200,
            message="Dispute comments retrieved successfully",
            data=comment_list,
        )
    except Exception as e:
        import traceback

        print(traceback.format_exc())
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.put("/assign-dispute")
async def assign_dispute(
    data: DisputeAssignmentUpdate,
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    try:
        if isinstance(user, JSONResponse):
            return user   
        #Get authenticated user info
        account_id = user.get("user_id")
        role= user.get("role_name")  

        # Find the dispute
        dispute = db.query(Disputes).filter(Disputes.id == data.dispute_id).first()
        if not dispute:
            return ErrorResponse(status_code=404, message="Dispute not found")

        # Store old assigned_to value
        old_assigned_to = dispute.assigned_to

        # Update ONLY the assigned_to field
        dispute.assigned_to = data.assigned_to
        # chech the agent in userprofiles table
        user_data = get_user(db, data.assigned_to)
        if not user_data:
            return ErrorResponse(status_code=404, message="Agent not found")

        try:
            db.commit()
            db.refresh(dispute)
        except Exception as e:
            db.rollback()
            return ErrorResponse(
                status_code=500, message="Error updating dispute", error=str(e)
            )

        # Initialize agent details
        agent_details = {
            "id": data.assigned_to,
            "email": None,
            "phone_number": None,
            "role": None,
            "status": None,
            "first_name": None,
            "last_name": None,
            "gender": None,
        }

        full_name = ""
        try:
            # Get agent details from UserProfiles table
            agent_data = get_user(db, data.assigned_to)
            if agent_data:
                first_name = agent_data.get("first_name", "")
                last_name = agent_data.get("last_name", "")
                full_name = build_full_name(first_name, last_name, data.assigned_to)

                agent_details.update({
                    "email": agent_data.get("email"),
                    "phone_number": agent_data.get("phone_number"),
                    # "role": agent_data.get("role_id"),
                    "status": agent_data.get("status"),
                    "first_name": first_name,
                    "last_name": last_name,
                    "gender": agent_data.get("gender"),
                    # "created_at": agent_data.get("created_at"),
                    # "updated_at": agent_data.get("updated_at"),
                })

        except Exception as e:
            print(f"Error fetching agent details: {str(e)}")
            import traceback
            traceback.print_exc()

        return StandardResponse(
            status_code=200,
            message="Dispute assigned successfully",
            data={
                "dispute_id": str(dispute.id),
                "case_number": dispute.case_number,
                "previous_assigned_to": old_assigned_to,
                "new_assigned_to": data.assigned_to,
                "name": full_name,  # Will be proper name or fallback from build_full_name
                "agent_details": agent_details,
            },
        )
    except Exception as e:
        db.rollback()
        import traceback

        traceback.print_exc()
        return ErrorResponse(status_code=500, message="Error", error=str(e))
