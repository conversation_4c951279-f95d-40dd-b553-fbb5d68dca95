import os
import uuid
import jwt
import requests
from fastapi import Depends, HTTPException, <PERSON><PERSON>, status
from typing import Annotated, List
import requests
from enum import Enum
# from database import ClanSessionLocal, RecruiterSessionLocal

# class ServiceType(str, Enum):
#     DISCOVER = "discover"
#     DIRECT = "direct"

async def get_id_header(Authorization):
    if not Authorization:
        return {'error': "Token required"}
    try:
        jwt_token = Authorization.split(" ")[1]
        baseurl = os.getenv('BE_AUTH_API_URL')
        response = requests.post(f"{baseurl}/validate-token/", json={'token': jwt_token})
        return response
    except Exception as e:
        return {'error': f"Error: {e}"}
    
def is_valid_uuid(val: str) -> bool:
    try:
        uuid.UUID(val)
        return True
    except ValueError:
        return False
    
#Common create function using sqlalchemy orm
async def create_record(db, model, request_dict):
    print('calling create record functionnnnnnnn')
    try:
        obj = model(**request_dict)
        db.add(obj)
        await db.commit()
        await db.refresh(obj)
        return obj
    except Exception as e:
        await db.rollback()
        error_message = f"Database error: {str(e)}"
        print(error_message, 'errrrrrrrrrrrrrrrrrrrrrrrrrrr')
        # raise HTTPException(status_code=500, detail=error_message)
        return error_message    

async def update_record(db, data, obj):
    if data.parent_id is not None:
        try:
            obj.parent_id = uuid.UUID(str(data.parent_id))  # Ensure valid UUID
        except ValueError:
            raise ValueError("Invalid parent_id format")

    for key, value in data.__dict__.items():
        if key == "parent_id":
            continue  # Skip parent_id since it's handled separately
        if value is None or (isinstance(value, str) and not value.strip()):
            continue  # Skip None and empty strings

        setattr(obj, key, value)

    await db.commit()
    await db.refresh(obj)
    return obj

async def delete_record(db, obj):
    await db.delete(obj)
    await db.commit()
    return {"data": "Record deleted successfully"}


def check_file_exists(file):
    if file:
        return True
    return False