import logging
import os
from functools import lru_cache
from pydantic_settings import BaseSettings

log = logging.getLogger("uvicorn")


class Settings(BaseSettings):
    """Class for storing settings."""

    POSTGRES_DB: str = os.getenv("POSTGRES_DB")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD")
    POSTGRES_HOST: str = os.getenv("POSTGRES_HOST")
    POSTGRES_PORT: int = os.getenv("POSTGRES_PORT")
    PGADMIN_DEFAULT_EMAIL: str = os.getenv("PGADMIN_DEFAULT_EMAIL")
    PGADMIN_DEFAULT_PASSWORD: str = os.getenv("PGADMIN_DEFAULT_PASSWORD")
    KEYCLOAK_URL: str = os.getenv("KEYCLOAK_API_URL", "")
    KEYCLOAK_REALM: str = os.getenv("KEYCLOAK_REALM", "")
    COGNITO_REGION:str = os.getenv("COGNITO_REGION", "") 
    CLIENT_ID : str = os.getenv("CLIENT_ID", "")
    USER_POOL_ID : str = os.getenv("USER_POOL_ID", "")
    COGNITO_ISSUER : str = os.getenv("COGNITO_ISSUER", "")
    CLIENT_SECRET : str = os.getenv("CLIENT_SECRET", "")
    MINIO_URL : str = os.getenv("MINIO_URL", "")
    MINIO_ROOT_USER : str = os.getenv("MINIO_ROOT_USER", "")
    MINIO_ROOT_PASSWORD : str = os.getenv("MINIO_ROOT_PASSWORD", "")
    MINIO_ACCESS_KEY : str = os.getenv("MINIO_ACCESS_KEY", "")
    MINIO_SECRET_KEY : str = os.getenv("MINIO_SECRET_KEY", "")
    USER_BUCKET_NAME : str = os.getenv("USER_BUCKET_NAME", "")
    SP_BUCKET_NAME : str = os.getenv("SP_BUCKET_NAME", "")
    SUBCATEGORY_BUCKET_NAME : str = os.getenv("SUBCATEGORY_BUCKET_NAME", "")
    SERVICES_BUCKET_NAME : str = os.getenv("SERVICES_BUCKET_NAME", "")
    BE_PROFILE_API_URL : str = os.getenv("BE_PROFILE_API_URL", "")
    BE_NOTIFICATION_API_URL: str = os.getenv("BE_NOTIFICATION_API_URL")
    WALLET_API_URL: str = os.getenv("WALLET_API_URL", "")
    CARD_API_URL: str = os.getenv("CARD_API_URL", "")
    WALLET_USERNAME: str = os.getenv("WALLET_USERNAME", "")
    WALLET_PASSWORD: str = os.getenv("WALLET_PASSWORD", "")
    CARD_MERCHANT_KEY: str = os.getenv("CARD_MERCHANT_KEY", "")
    CARD_MERCHANT_SECRET: str = os.getenv("CARD_MERCHANT_KEY", "")

@lru_cache()
def get_settings() -> BaseSettings:
    """Get application settings usually stored as environment variables.

    Returns:
        Settings: Application settings.
    """
    log.info("Loading config settings from the environment...")
    return Settings()

settings = get_settings()
