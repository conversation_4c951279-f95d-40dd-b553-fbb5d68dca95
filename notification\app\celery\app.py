from celery import Celery
from app.config import get_settings

celery_app = Celery(
    "notification_service",
    broker=get_settings().NOTIFICATION_REDIS_CELERY_URL,
    backend=get_settings().NOTIFICATION_REDIS_CELERY_URL,
    include=["app.celery.task"],
)

celery_app.conf.task_default_queue = "notification_queue"

# celery_app.conf.update(
#     task_serializer="json",
#     accept_content=["json"],
#     result_serializer="json",
# )

# celery_app.conf.update(
#     task_routes={
#         "app.celery.task.send_delayed_notification": {"queue": "notifications_queue"},
#     }
# )
