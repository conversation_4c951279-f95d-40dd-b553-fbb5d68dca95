from uuid import UUID
from pydantic import BaseModel, UUID4
from typing import Optional
from app.models_enum import ArtisanAssignStatus


class UpdateArtisanStatusRequest(BaseModel):
    booking_id: str
    service_id: str
    id: UUID
    status: ArtisanAssignStatus
    service_start_artisan_latitude: Optional[float] = None
    service_start_artisan_longitude: Optional[float] = None
    service_end_artisan_latitude: Optional[float] = None
    service_end_artisan_longitude: Optional[float] = None

class ServiceStart(BaseModel):
    id: UUID4
    service_start_artisan_latitude: float
    service_start_artisan_longitude: float