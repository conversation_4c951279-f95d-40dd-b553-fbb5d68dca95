from enum import Enum


class BookingStatus(str, Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed" #when artisan accepts booking
    STARTED = "started"
    ARRIVED = "arrived" #when artisan arrives at user location
    ACCEPTED = "accepted" #when user accepts booking
    ONGOING = "ongoing" #when user starts service
    ENDED = "ended" #when artisan ends service
    COMPLETED = "completed" #when user pays for service, should be update in webhook
    CANCELLED = "cancelled"
    REJECTED = "rejected"


class DisputeStatus(str, Enum):
    OPEN = "OPEN"
    IN_PROGRESS = "IN_PROGRESS"
    RESOLVED = "RESOLVED"


class PaymentType(str, Enum):
    CASH = "CASH"
    WALLET = "WALLET"
    CARD = "CARD"


class DisputeType(str, Enum):
    BOOKING_ISSUES = "Booking Issues"
    PAYMENT_AND_REFUND = "Payment and Refund"
    SERVICE_ISSUES = "Service Issues"
    OTHERS = "Others"


class DisputePriority(str, Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


class UserType(str, Enum):
    ARTISAN = "ARTISAN"
    USER = "USER"
    AGENT = "AGENT"


class ChargebackStatus(str, Enum):
    PENDING = "PENDING"
    REFUND_INITIATED = "REFUND_INITIATED"
    PROCESSED = "PROCESSED"


class DisputeEventType(str, Enum):
    DISPUTE_CREATED = "DISPUTE_CREATED"
    ASSIGNED_TO_SYSTEM = "ASSIGNED_TO_SYSTEM"
    ASSIGNED_TO_AGENT = "ASSIGNED_TO_AGENT"
    STATUS_CHANGED_TO_OPEN = "STATUS_CHANGED_TO_OPEN"
    STATUS_CHANGED_TO_IN_PROGRESS = "STATUS_CHANGED_TO_IN_PROGRESS"
    STATUS_CHANGED_TO_RESOLVED = "STATUS_CHANGED_TO_RESOLVED"
    COMMENT_ADDED = "COMMENT_ADDED"
    DOCUMENT_ADDED = "DOCUMENT_ADDED"
    MERCHANT_CONTACTED = "MERCHANT_CONTACTED"
    MERCHANT_RESPONSE_RECEIVED = "MERCHANT_RESPONSE_RECEIVED"
    PAYMENT_GATEWAY_INVESTIGATION = "PAYMENT_GATEWAY_INVESTIGATION"
    REFUND_INITIATED = "REFUND_INITIATED"
    REFUND_COMPLETED = "REFUND_COMPLETED"
    DISPUTE_CLOSED = "DISPUTE_CLOSED"


class Gender(str, Enum):
    MALE = "male"
    FEMALE = "female"


class ServiceProviderStatusType(str, Enum):
    CREATED = "created"
    APPROVED = "approved"
    REJECTED = "rejected"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"


class UserStatusType(str, Enum):
    APPROVED = "approved"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"


class DeviceType(str, Enum):
    ANDROID = "android"
    IOS = "ios"

class PaymentMethodType(str, Enum):
    CARD = "CARD"
    WALLET = "WALLET"
    CASH = "CASH"

class StatusType(str, Enum):
    INIT = "INIT"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

class CurrencyType(str, Enum):
    GHS = "GHS"
    EUR = "EUR"
    USD = "USD"
    AED = "AED"
class BookingCancellationStatus(str, Enum):
    INITIATED = "initiated"
    PENDING = "pending"     # Cancellation accepted by agent
    # APPROVED = "approved"
    COMPLETED = "completed" # Once penalty is applied or agent approves the cancellation
    


class IssueStatus(str, Enum) :
    OPEN = "OPEN"
    IN_PROGRESS = "IN_PROGRESS"
    RESOLVED = "RESOLVED"


class IssuePriority(str, Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


class UserType(str, Enum):
    ARTISAN = "ARTISAN"
    USER = "USER"
    AGENT = "AGENT"
    ADMIN= "ADMIN"
    SUPERADMIN= "SUPERADMIN"
