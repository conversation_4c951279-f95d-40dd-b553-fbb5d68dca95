import json
from uuid import UUID
from pydantic import BaseModel, Field, EmailStr
from typing import Optional
from enum import Enum
from app.models_enum import UserStatusType
from fastapi import Form, UploadFile, File

class PermissionActions(BaseModel):
    can_view: bool = Field(False, description="Whether the user can view records")
    can_add: bool = Field(False, description="Whether the user can add records")
    can_edit: bool = Field(False, description="Whether the user can edit records")
    can_delete: bool = Field(False, description="Whether the user can delete records")

class RolesEnum(str, Enum):
    BUSINESS_PROVIDER = "BUSINESS_PROVIDER"
    BUSINESS_MANAGER = "BUSINESS_MANAGER"
    PERSONAL = "PERSONAL"

class PermissionsSchema(BaseModel):
    Service_Management: Optional[PermissionActions] = Field(
        None, description="Permissions for Service Management"
    )
    Booking_Management: Optional[PermissionActions] = Field(
        None, description="Permissions for Booking Management"
    )
    Employee_Management: Optional[PermissionActions] = Field(
        None, description="Permissions for Employee Management"
    )

class SPManagerCreateForm:
    def __init__(
        self,
        first_name: str = Form(...),
        last_name: str = Form(...),
        email: EmailStr = Form(...),
        country_code: str = Form(...),
        phone_number: str = Form(...),
        role_name: RolesEnum = Form(...),
        region_id: Optional[UUID] = Form(None),
        sp_branch_id: Optional[UUID] = Form(None),
        service_id: Optional[UUID] = Form(None),
        permissions: Optional[str] = Form(None),
        profile_pic: Optional[UploadFile] = File(None),
        certification: Optional[UploadFile] = File(None),
        govtid: Optional[UploadFile] = File(None)
    ):
        self.first_name = first_name
        self.last_name = last_name
        self.email = email
        self.country_code = country_code
        self.phone_number = phone_number
        self.role_name = role_name
        self.region_id = region_id
        self.sp_branch_id = sp_branch_id
        self.profile_pic = profile_pic
        self.certification = certification
        self.govtid = govtid
        self.service_id = service_id

        # Parse permissions if provided, else default to all False
        if permissions:
            try:
                self.permissions = PermissionsSchema(**json.loads(permissions))
            except Exception as e:
                raise ValueError(f"Invalid permissions format: {str(e)}")
        else:
            # Default all permissions to False
            self.permissions = PermissionsSchema(
                Service_Management=PermissionActions(),
                Booking_Management=PermissionActions(),
                Employee_Management=PermissionActions(),
            )

class SPManagerUpdateForm:
    def __init__(
        self,
        user_id: UUID = Form(...),
        first_name: Optional[str] = Form(None),
        last_name: Optional[str] = Form(None),
        region_id: Optional[UUID] = Form(None),
        sp_branch_id: Optional[UUID] = Form(None),
        permissions: Optional[str] = Form(None),  # JSON string
        profile_pic: Optional[UploadFile] = File(None),
        status: Optional[UserStatusType] = Form(None)
    ):
        self.user_id = user_id
        self.first_name = first_name
        self.last_name = last_name
        self.region_id = region_id
        self.sp_branch_id = sp_branch_id
        self.profile_pic = profile_pic
        self.status = status

        self.permissions = None
        if permissions:
            try:
                self.permissions = PermissionsSchema(**json.loads(permissions))
            except Exception as e:
                raise ValueError(f"Invalid permissions format: {str(e)}")
class SPUsersUpdate(BaseModel):
    # sp_id : Optional[int]
    sp_id: Optional[UUID]
    user_id: Optional[UUID]
    spb_id: Optional[UUID]
