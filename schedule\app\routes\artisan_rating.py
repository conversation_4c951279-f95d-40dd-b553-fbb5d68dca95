from sqlalchemy.orm import Session
from app.models import SPRatings, Ratings
from sqlalchemy import func

def get_avg_rating_by_user(session: Session, user_id):
    print("user_idddddddddddddddddd", user_id)
    try:
        avg_rating = (
            session.query(func.avg(Ratings.rating))
            .join(SPRatings, SPRatings.ref_id == Ratings.id)
            .filter(SPRatings.user_id == user_id)
            .scalar()
        )
        return avg_rating
    except:
        return 0