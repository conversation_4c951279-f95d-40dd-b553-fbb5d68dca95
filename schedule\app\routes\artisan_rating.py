import uuid
from sqlalchemy.orm import Session
from typing import Optional #List, Annotated
from app.models import Booking
from app.schemas.helper import StandardResponse, ErrorResponse
from sqlalchemy import func, Numeric

def get_avg_rating(
    db: Session, artisan_id: Optional[str] = None, user_id: Optional[str] = None
):
    try:
        if artisan_id:
            query_result = (
                db.query(
                    Booking.artisan_id,
                    func.count(Booking.artisan_rating).label("total_ratings"),
                    func.round(func.avg(Booking.artisan_rating).cast(Numeric), 2).label(
                        "average_rating"
                    ),
                )
                .filter(
                    Booking.artisan_id == uuid.UUID(artisan_id),
                    Booking.artisan_rating.isnot(None),
                )
                .group_by(Booking.artisan_id)
                .first()
            )

            if not query_result:
                # return StandardResponse(
                #     status_code=200,
                #     message="No ratings found for this artisan",
                #     data={
                #         "artisan_id": artisan_id,
                #         "total_ratings": 0,
                #         "average_rating": 0.0
                #     }
                # )
                data = {
                    "artisan_id": artisan_id,
                    "total_ratings": 0,
                    "average_rating": 0.0,
                }
                return data["average_rating"]

            # return StandardResponse(
            #     status_code=200,
            #     message="Average rating retrieved successfully",
            #     data={
            #         "artisan_id": str(query_result.artisan_id),
            #         "total_ratings": query_result.total_ratings,
            #         "average_rating": float(query_result.average_rating) if query_result.average_rating else 0.0
            #     }
            # )
            data = {
                "artisan_id": str(query_result.artisan_id),
                "total_ratings": query_result.total_ratings,
                "average_rating": (
                    float(query_result.average_rating)
                    if query_result.average_rating
                    else 0.0
                ),
            }
            return data["average_rating"]
        elif user_id:
            query_result = (
                db.query(
                    Booking.user_id,
                    func.count(Booking.customer_rating).label("total_ratings"),
                    func.round(
                        func.avg(Booking.customer_rating).cast(Numeric), 2
                    ).label("average_rating"),
                )
                .filter(
                    Booking.user_id == uuid.UUID(user_id),
                    Booking.customer_rating.isnot(None),
                )
                .group_by(Booking.user_id)
                .first()
            )

            if not query_result:
                # return StandardResponse(
                #     status_code=200,
                #     message="No ratings found for this user",
                #     data={
                #         "user_id": user_id,
                #         "total_ratings": 0,
                #         "average_rating": 0.0
                #     }
                # )
                data = {"user_id": user_id, "total_ratings": 0, "average_rating": 0.0}
                return data["average_rating"]

            # return StandardResponse(
            #     status_code=200,
            #     message="Average rating retrieved successfully",
            #     data={
            #         "user_id": str(query_result.user_id),
            #         "total_ratings": query_result.total_ratings,
            #         "average_rating": float(query_result.average_rating) if query_result.average_rating else 0.0
            #     }
            # )
            data = {
                "user_id": str(query_result.user_id),
                "total_ratings": query_result.total_ratings,
                "average_rating": (
                    float(query_result.average_rating)
                    if query_result.average_rating
                    else 0.0
                ),
            }
            return data["average_rating"]
        return ErrorResponse(
            status_code=400, message="Artisan ID or User ID is required"
        )
    except ValueError:
        return ErrorResponse(status_code=400, message="Invalid UUID format")
    except Exception as e:
        return ErrorResponse(
            status_code=500, message=f"Failed to retrieve average rating: {str(e)}"
        )
