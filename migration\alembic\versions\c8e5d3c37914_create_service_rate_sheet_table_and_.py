"""create service_rate_sheet table and booking fields added

Revision ID: c8e5d3c37914
Revises: e3398a4f2af5
Create Date: 2025-06-18 15:20:10.161679

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'c8e5d3c37914'
down_revision: Union[str, None] = 'e3398a4f2af5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

pricing_type_enum = postgresql.ENUM('FIXED', 'MEASURED', 'CUSTOM', name='pricingtype', create_type=False)
service_type_enum = postgresql.ENUM('REGULAR', 'PREMIUM', name='servicetype', create_type=False)
service_units_enum = postgresql.ENUM('HOUR', 'MINUTE', 'SQ_FT', 'METER', name='serviceunits', create_type=True)


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('service_rate_sheet',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('service_id', sa.UUID(), nullable=False),
    sa.Column('artisan_id', sa.UUID(), nullable=False),
    sa.Column('pricing_type', pricing_type_enum, server_default='FIXED', nullable=False),
    sa.Column('price_per_unit', sa.Float(), nullable=False),
    sa.Column('service_type', service_type_enum, nullable=False),
    sa.Column('units', service_units_enum, nullable=False),
    sa.Column('booking_fee', sa.Float(), nullable=False),
    sa.Column('minimum_booking_time', sa.Float(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), server_default='TRUE', nullable=False),
    sa.ForeignKeyConstraint(['artisan_id'], ['service_provider.id'], ),
    sa.ForeignKeyConstraint(['service_id'], ['services.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.add_column('bookings', sa.Column('booking_fee_status', sa.Boolean(), nullable=True))
    op.add_column('bookings', sa.Column('final_payment_status', sa.Boolean(), nullable=True))
    op.add_column('bookings', sa.Column('final_amount', sa.Float(), nullable=True))
    op.add_column('bookings', sa.Column('booking_fee_amount', sa.Float(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bookings', 'booking_fee_amount')
    op.drop_column('bookings', 'final_amount')
    op.drop_column('bookings', 'final_payment_status')
    op.drop_column('bookings', 'booking_fee_status')
    op.drop_table('service_rate_sheet')
    # ### end Alembic commands ###
