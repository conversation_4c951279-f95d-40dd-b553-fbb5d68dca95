from cryptography.fernet import Fernet

# Generate a key (store this securely, do it only once)
def generate_key() -> bytes:
    x = Fernet.generate_key()
    print(x, 'xxxxxxxxxxxxxxxx')
    return x

# Create Fernet instance
fernet = Fernet(b'Ys-6MQcPzFEEBB-ES6G-WVo3zEbqGozrB5m1Rd2Qxqk=')  # In production, load key from a secure file or vault

# Encrypt password
def encrypt_password(password: str) -> str:
    return fernet.encrypt(password.encode()).decode()

# Decrypt password
def decrypt_password(encrypted_password: str) -> str:
    return fernet.decrypt(encrypted_password.encode()).decode()
