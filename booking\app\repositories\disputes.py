from sqlalchemy.orm import Session
from app.schemas.disputes import IssuesCreate, IssuesUpdate
from app.models.disputes import Issues
import uuid
from sqlalchemy.future import select
from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession


def create_issue(db: Session, data: IssuesCreate):
    issues = Issues(**data.model_dump())
    db.add(issues)
    db.commit()
    db.refresh(issues)
    return issues


def update_issue(db: Session, data: IssuesUpdate):
    issue = db.query(Issues).filter(Issues.id == data.id).first()
    if issue:
        for key, value in data:
            if value is None or (type(value) == str and len(value) == 0):
                continue
            # ✅ Convert `IssueDetails` objects to dict before storing in JSONB column
            if key == "issues" and isinstance(value, list):
                value = [
                    item.dict() for item in value
                ]  # Convert Pydantic models to dict

            setattr(issue, key, value)
        db.commit()
        db.refresh(issue)
        return issue


def read_issue(db: Session, issue_id: str):
    if uuid.UUID(issue_id):
        issue = db.query(Issues).filter(Issues.id == uuid.UUID(issue_id)).first()
        if issue:
            return issue


def issue_list(db: Session):
    # Get all issues
    # issues = await db.execute(select(Issues)).scalars().all()

    # # Get total count
    # total_count = await db.execute(select(func.count()).select_from(Issues)).scalar()
    issues =  db.query(Issues).all()

        # Get total count
    total_count = db.query(Issues).count()
    return {"issues": issues, "total_count": total_count}


def delete_issue(db: Session, issue_id: str):
    if uuid.UUID(issue_id):
        issue = db.query(Issues).filter(Issues.id == uuid.UUID(issue_id)).first()
        if issue:
            db.delete(issue)
            db.commit()
            return {"data": "Issue deleted Successfully"}
