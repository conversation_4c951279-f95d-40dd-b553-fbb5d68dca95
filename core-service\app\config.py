import logging
import os
from functools import lru_cache
from typing import Optional
from pydantic_settings import BaseSettings
from dotenv import load_dotenv
from pathlib import Path

log = logging.getLogger("uvicorn")

# Move up two directories from the current file location to find .env
ENV_PATH = Path(__file__).resolve().parents[2] / ".env"

# Load environment variables from the .env file
load_dotenv(dotenv_path=ENV_PATH)

print("This is the host name loaded")
print(str(os.getenv("POSTGRES_HOST")))


class Settings(BaseSettings):
    """Class for storing settings."""

    POSTGRES_DB: str = os.getenv("POSTGRES_DB")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD")
    POSTGRES_HOST: str = os.getenv("POSTGRES_HOST")
    POSTGRES_PORT: int = int(os.getenv("POSTGRES_PORT", 5432))  # Ensure integer
    PGADMIN_DEFAULT_EMAIL: str = os.getenv("PGADMIN_DEFAULT_EMAIL")
    S3_IMAGES_FOLDER: str = os.getenv("S3_IMAGES_FOLDER", "")
    S3_DOCS_FOLDER: str = os.getenv("S3_DOCS_FOLDER", "")

    # POSTGRES_HOST: str = "localhost"

    # Database URL Construction
    DATABASE_URL: str = (
        f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"
    )

    AWS_REGION: str = os.getenv("AWS_REGION", "")
    AWS_SECRET_ACCESS_KEY: str = os.getenv("AWS_SECRET_ACCESS_KEY", "")
    FROM_EMAIL: str = os.getenv("FROM_EMAIL", "")
    SES_ACCESS_KEY: str = os.getenv("SES_ACCESS_KEY", "")
    AWS_ACCESS_KEY_ID: str = os.getenv("AWS_ACCESS_KEY_ID", "")
    SES_SECRET_KEY: str = os.getenv("SES_SECRET_KEY", "")
    S3_BUCKET: str = os.getenv("S3_BUCKET", "")

    COGNITO_REGION: str = os.getenv("COGNITO_REGION", "")
    COGNITO_CLIENT_ID: str = os.getenv("COGNITO_CLIENT_ID", "")
    COGNITO_CLIENT_SECRET: str = os.getenv("COGNITO_CLIENT_SECRET", "")
    COGNITO_USER_POOL_ID: str = os.getenv("COGNITO_USER_POOL_ID", "")
    TEST_USER_PASSWORD: Optional[str] = os.getenv("TEST_USER_PASSWORD", None)
    CLIENT_ID: str = os.getenv("CLIENT_ID", "")
    CLIENT_SECRET: str = os.getenv("CLIENT_SECRET", "")
    USER_POOL_ID: str = os.getenv("USER_POOL_ID", "")

    # Redis Configuration
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", 6379))
    REDIS_DB: int = int(os.getenv("REDIS_DB", 0))
    BE_NOTIFICATION_API_URL: str = os.getenv("BE_NOTIFICATION_API_URL")

    BE_BLOB_API_URL: str = os.getenv("BE_BLOB_API_URL", "")
    S3_IMAGES_FOLDER: str = os.getenv("S3_IMAGES_FOLDER", "")
    S3_DOCS_FOLDER: str = os.getenv("S3_DOCS_FOLDER", "")
    KAFKA_HOST: str = os.getenv("KAFKA_HOST", "")
    KAFKA_PORT: str = os.getenv("KAFKA_PORT", "")


def get_settings() -> BaseSettings:
    """Get application settings usually stored as environment variables.

    Returns:
        Settings: Application settings.
    """
    log.info("🔧 Loading config settings from the environment...")
    return Settings()


settings = get_settings()

# Debugging: Print loaded configuration (optional, remove in production)
if __name__ == "__main__":
    print("Loaded Configuration:")
    print(f"POSTGRES_DB: {settings.POSTGRES_DB}")
    print(f"POSTGRES_USER: {settings.POSTGRES_USER}")
    print(
        f"POSTGRES_PASSWORD: {settings.POSTGRES_PASSWORD}"
    )  # Remove this in production
    print(f"POSTGRES_HOST: {settings.POSTGRES_HOST}")
    print(f"POSTGRES_PORT: {settings.POSTGRES_PORT}")
    print(f"DATABASE_URL: {settings.DATABASE_URL}")
