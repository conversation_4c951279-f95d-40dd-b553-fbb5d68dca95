import json
import requests
import os
from fastapi import Request, Response
from functools import wraps
import inspect
import logging
import re
from starlette.middleware.base import BaseHTTPMiddleware
from app.utils import get_id_header

logger = logging.getLogger("audit_log")
logging.basicConfig(level=logging.INFO)

UUID_PATTERN = r"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"

# 🚀 List of endpoints to log
LOGGED_ENDPOINTS = [
    # r"^/category-list",
    r"^/category-create",
    # r"^/services-list",
    r"^/services-create",
    rf"^/category-delete/{UUID_PATTERN}",
    rf"^/services-delete/{UUID_PATTERN}",
]

# Middleware for logging
async def log_requests(request: Request, call_next):
    """Middleware to log API requests and responses"""
    if not any(re.match(pattern, request.url.path) for pattern in LOGGED_ENDPOINTS):
        return await call_next(request)  # ❌ Skip logging for this request
    
    authorization = request.headers.get("authorization")
    if authorization:
        try:
            resp = await get_id_header(authorization)
            if resp.status_code == 200:
                resp_json = resp.json()
                account_id = resp_json.get("account_id")
            else:
                account_id = None
        except Exception as e:
            account_id = None
    else:
        account_id = None

    body = await request.body()  # Read request body
    query_params = dict(request.query_params) if request.method == "GET" else {}

    # ✅ Handle Request Body (Avoid logging binary data)
    content_type = request.headers.get("content-type", "")
    if "multipart/form-data" in content_type:
        request_body = "File upload detected - Not logged"
    else:
        try:
            body = await request.body()
            request_body = body.decode("utf-8") if body else ""
        except UnicodeDecodeError:
            request_body = "Unable to decode request body"

    # Process the request and capture the response
    response = await call_next(request)

    # ✅ Handle streaming responses properly
    response_body = b""
    async for chunk in response.body_iterator:
        response_body += chunk  # Collect response chunks
    
    # ✅ Log request and response
    try:
        log_data = {
            "method": request.method,
            "url": str(request.url),
            "query_params": json.dumps(query_params),
            "headers": json.dumps(dict(request.headers)),
            "body": request_body,
            "response": response_body.decode("utf-8") if response_body else "",
            "user_id": account_id
        }
        requests.post(os.getenv("BE_AUDIT_LOG_API_URL")+"/log", json=log_data)
        logger.info("Logged request successfully")
    except Exception as e:
        logger.error(f"Failed to log request: {e}")
    # ✅ Return a fresh response with the same content
    return Response(
        content=response_body, 
        status_code=response.status_code, 
        headers=dict(response.headers), 
        media_type=response.media_type
    )



# Decorator for logging
# def log_middleware(func):
#     @wraps(func)
#     async def wrapper(request: Request, *args, **kwargs):
#         body = await request.body()
#         query_params = dict(request.query_params) if request.method == "GET" else {}

#         # Call the actual endpoint function
#         if inspect.iscoroutinefunction(func):
#             response = await func(request, *args, **kwargs)  # ✅ Async function
#         else:
#             response = func(request, *args, **kwargs)  # ✅ Sync function

#         response_body = response.body if hasattr(response, "body") else str(response)

#         try:
#             log_data = {
#                 "method": request.method,
#                 "url": str(request.url),
#                 "query_params": str(query_params),
#                 "headers": str(request.headers),
#                 "body": body.decode("utf-8") if body else "",
#                 "response": response_body.decode("utf-8") if isinstance(response_body, bytes) else response_body
#             }
#             # requests.post(os.getenv("BE_AUDIT_LOG_API_URL")+"/log", json=log_data)
#             requests.post("http://audit-log:8011/log", json=log_data)
#             logger.info("Logged request successfully")
#         except:
#             logger.error("Failed to log request")
#             pass
#         return response
#     return wrapper