from aiokafka import AIOKafkaProducer
import json
import os

class KafkaProducerClient:
    def __init__(self):
        self.producer = None

    async def start(self):
        self.producer = AIOKafkaProducer(
            bootstrap_servers=f"{os.getenv('KAFKA_HOST')}:{os.getenv('KAFKA_PORT')}",
            value_serializer=lambda v: json.dumps(v,default=str).encode("utf-8")
        )
        await self.producer.start()

    async def stop(self):
        if self.producer:
            await self.producer.stop()

    async def send_assignment(self, topic: str, data: dict):
        if not self.producer:
            raise RuntimeError("Kafka producer not started.")
        print(f"✅ Sending to Kafka topic: {topic}, data: {data}")
        await self.producer.send_and_wait(topic, data)

# ✅ Global instance
producer = KafkaProducerClient()