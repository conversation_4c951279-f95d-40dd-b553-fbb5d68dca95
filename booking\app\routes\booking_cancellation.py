import os
from app.utils.helper import CANCEL_THRESHOLD, PEN<PERSON>TY_AMOUNT, apply_penalty
from fastapi import APIRouter, Depends, HTTPException, Header, File, UploadFile, BackgroundTasks
from app.utils.notification import send_push_notification
from app.utils.booking_cancellation_history import create_booking_cancellation_history, get_booking_cancellation_history, status_dict
from sqlalchemy.orm import Session, joinedload
from app.database import get_db
from app.schemas.helper import ErrorResponse, StandardResponse
from typing import List, Optional, Annotated
from datetime import date, datetime, timedelta
import uuid
from sqlalchemy import and_ , or_ ,cast, func
from app.utils.auth import get_id_header
from app.schemas.booking_cancellation import BookingCancellationCreate, BookingCancellationUpdate, BookingCancellationResponse, ApproveBookingCancellation, BookingCancellationFilter
from app.models import BookingCancellation, BookingCancellationHistory, BookingCancellationReason
from app.models import Booking
from app.models import Payment
from app.models import ServiceProvider, Users
from app.models import Admin
from app.config import get_settings
from app.s3_upload import upload_file_direct
import requests
import logging
from app.utils.service_calls import process_refund
from sqlalchemy.orm import aliased
from sqlalchemy.types import String

logger = logging.getLogger("booking_cancellation")
logging.basicConfig(level=logging.INFO)


router = APIRouter(tags=["Booking Cancellation"])


@router.post("/booking-cancellation")
async def create_booking_cancellation(
    request: BookingCancellationCreate = Depends(),
    attachments: List[UploadFile] = File(None),
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Create a booking cancellation record.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            print(f"Response JSON: {resp_json}")
            account_id = resp_json.get("account_id")
            # account_id = '01f7989b-32f5-4e62-ba43-8c63f8f0668b'
        elif resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        user_type = request.user_type
        booking_id = request.booking_id
        is_booking_timeout = request.__dict__.pop("is_booking_timeout", False)
        agent_mobile = request.__dict__.pop("agent_mobile", None)

        # Getting Booking
        booking = db.query(Booking).filter(Booking.id == booking_id).first()
        if not booking:
            return ErrorResponse(status_code=404, message="Booking not found")

        # Check if booking cancellation already exists for this booking
        bc = db.query(BookingCancellation).filter(BookingCancellation.booking_id == booking_id).first()
        if bc:
            return ErrorResponse(status_code=400, message=f"Booking cancellation already raised by {bc.user_type}")

        today = date.today()

        if user_type == "USER":
            results = db.query(BookingCancellation).filter(func.date(BookingCancellation.created_at) == today, BookingCancellation.user_id == account_id).count()
        elif user_type == "ARTISAN":
            results = db.query(BookingCancellation).filter(func.date(BookingCancellation.created_at) == today, BookingCancellation.artisan_id == account_id).count()
        else:
            return ErrorResponse(status_code=400, message="Invalid user type")
        
        agent_id = None
        user_id = booking.user_id
        artisan_id = booking.artisan_id
        booking_amount = booking.base_service_fee + booking.surcharges
        payment_method = booking.payment_type
        booking_date = str(booking.booking_date)
        booking_start_time = str(booking.start_time)
        penalty_user_type = None
        penalty_user_id = None

        now_datetime = datetime.now()
        formatted_datetime_str = now_datetime.strftime("%Y-%m-%d %H:%M:%S")

        datetime_str = f"{booking_date} {booking_start_time}"
        formatted_datetime_now = datetime.strptime(formatted_datetime_str, "%Y-%m-%d %H:%M:%S")
        booking_datetime = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S")
        hrs_difference = (booking_datetime - formatted_datetime_now) > timedelta(hours=3)
        if hrs_difference:  # If the difference is more than 3 hours
            logger.info("No penalty for before 3 hours, apply auto cancel")
            penalty_needed = False
            is_auto_cancel = True
        else:   # If the difference is less than 3 hours
            logger.info("Apply penalty for last 3 hours")
            is_auto_cancel = False
            # Checking Cancellation Reason
            logger.info("Checking cancellation reason configuration")
            reason = db.query(BookingCancellationReason).filter(BookingCancellationReason.id == request.cancellation_reason_id).first()
            print(reason, "reason")
            if not reason:
                return ErrorResponse(status_code=404, message="Cancellation reason not found")
            if reason.penalty_needed:
                penalty_needed = True
                penalty_user_type = reason.apply_penalty_on
                penalty_user_id = user_id if penalty_user_type == "USER" else artisan_id
                logger.info("Penalty applied based on cancellation reason configuration")
            else:
                penalty_needed = False
                logger.info("No penalty applied based on cancellation reason configuration")

        if is_booking_timeout:
            is_auto_cancel = True
            penalty_needed = False
            logger.info("Booking timeout detected, applying auto cancel")

        # Handle file uploads
        supporting_docs = []
        supporting_documents = None
        if attachments:
            settings = get_settings()
            for attachment in attachments:
                if attachment is not None and attachment.filename:
                    # Generate a unique filename
                    file_ext = attachment.filename.split(".")[-1]
                    unique_filename = f"{uuid.uuid4()}.{file_ext}"
                    # Upload to S3 or your storage service
                    file_url = upload_file_direct(attachment, settings.S3_IMAGES_FOLDER)
                    supporting_docs.append(file_url["filename"])

        if supporting_docs:
            supporting_documents = ",".join(supporting_docs)

        user = db.query(Users).filter(Users.id == user_id).first()
        artisan = db.query(ServiceProvider).filter(ServiceProvider.id == artisan_id).first()
        admin = db.query(Admin).filter(Admin.id == account_id).first()
        if user_type == "USER":
            cancellation_by = user_id
            user_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
        elif user_type == "ARTISAN":
            cancellation_by = artisan_id
            user_name = f"{artisan.first_name or ''} {artisan.last_name or ''}".strip()
        elif user_type == "AGENT":
            cancellation_by = admin.id
            user_name = f"{admin.first_name or ''} {admin.last_name or ''}".strip()
            agent_id = admin.id

        val = BookingCancellation(
            user_id=user_id,
            artisan_id=artisan_id,
            agent_id=agent_id,
            booking_id=request.booking_id,
            transaction_id=None,
            payment_id=None,
            cancellation_reason_id=request.cancellation_reason_id,
            user_type=user_type,
            payment_method=payment_method,
            booking_amount=booking_amount,
            penalty_needed=penalty_needed,
            penalty_user_type=penalty_user_type,
            penalty_user_id=penalty_user_id,
            is_auto_cancel=is_auto_cancel,
            attachments=supporting_documents,
        )
        db.add(val)
        db.commit()
        db.refresh(val)
        bc_id = val.id

        logger.info("Booking cancellation created successfully")
        # Create history entry
        create_booking_cancellation_history(db, {"booking_cancellation_id": bc_id, "description": status_dict["bc_created"], "user_name": user_name, "user_id": cancellation_by})

        print(results, "results")
        if results >= CANCEL_THRESHOLD:
            logger.info(f"Applying penalty for {results+1} booking cancellation")
            apply_penalty(db, user_type, account_id, PENALTY_AMOUNT)
        
        if is_auto_cancel:
            val.status = "completed"
            db.commit()
            db.refresh(val)
            logger.info("Booking cancellation status updated to completed")
            booking.status = "cancelled"
            db.commit()
            db.refresh(booking)
            logger.info("Booking status updated to cancelled")

        # Notify both user and artisan
        if user_type == "USER":
            send_push_notification(
                auth_token=Authorization,
                title="Booking Cancelled",
                message=f"{booking_id} - Booking has been cancelled by user",
                sender_id=artisan_id,
                type="service_provider",
            )
        elif user_type == "ARTISAN":
            send_push_notification(
                auth_token=Authorization,
                title="Booking Cancelled",
                message=f"{booking_id} - Booking has been cancelled by artisan",
                sender_id=user_id,
                type="user",
            )
        return StandardResponse(status_code=200, message="Booking cancellation created successfully")
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))

@router.get("/booking-cancellation/{id}")
async def get_booking_cancellation_by_id(
    id: str,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Get a booking cancellation by ID.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")
                
        result = db.query(BookingCancellation, Users, ServiceProvider, Admin, BookingCancellationReason)\
            .outerjoin(Users, BookingCancellation.user_id == Users.id).\
            outerjoin(ServiceProvider, BookingCancellation.artisan_id == ServiceProvider.id).\
            outerjoin(Admin, BookingCancellation.assigned_agent_id == Admin.id).\
            outerjoin(BookingCancellationReason, BookingCancellationReason.id == BookingCancellation.cancellation_reason_id).\
            filter(BookingCancellation.id == id)
            
        if not result:
            return ErrorResponse(status_code=404, message="Booking cancellation not found")
        
        final_result = result.first()
        
        cancellation, user, artisan, assigned_agent, reason = final_result
        agent_detail = None

        if cancellation.agent_id:
            agent_detail = db.query(Admin).filter(Admin.id == cancellation.agent_id).first()
        
        # Create response data
        cancellation_data = BookingCancellationResponse.from_orm(cancellation)
        response_data = cancellation_data.dict()
        response_data["reason"] = reason.reason if reason else None
        user_idd = None
        artisan_idd = None
        agent_idd = None
        user_name = None
        user_mobile = None
        artisan_name = None
        artisan_mobile = None
        agent_idd = None
        agent_name = None
        agent_mobile = None
        assigned_agent_name = None
        if user:
            user_idd = str(user.id)
            user_name = user.first_name+" "+user.last_name if user.last_name else user.first_name
            user_mobile = user.phone_number
        if artisan:
            artisan_idd = str(artisan.id)
            artisan_name = artisan.first_name+" "+artisan.last_name if artisan.last_name else artisan.first_name
            artisan_mobile = artisan.phone_number
        if agent_detail:
            agent_idd = str(agent_detail.id)
            agent_name = agent_detail.first_name+" "+agent_detail.last_name if agent_detail.last_name else agent_detail.first_name
            agent_mobile = agent_detail.phone_number
        if assigned_agent:
            assigned_agent_name = assigned_agent.first_name+" "+assigned_agent.last_name if assigned_agent.last_name else assigned_agent.first_name
        response_data['user'] = {
                "id": user_idd,
                "name": user_name,
                "mobile": user_mobile,
                "type": "user"
            }
        response_data['artisan'] = {
            "id": artisan_idd,
            "name": artisan_name,
            "mobile": artisan_mobile,
            "type": "artisan"
        }
        response_data['agent'] = {
            "id": agent_idd,
            "name": agent_name,
            "mobile": agent_mobile,
            "type": "agent"
        }
        response_data["assigned_agent_name"] = assigned_agent_name
        response_data["assigned_agent_email"] = assigned_agent.email if assigned_agent else None
        response_data["history"] = get_booking_cancellation_history(db, id)
        
        return StandardResponse(
            status_code=200, 
            message="Booking cancellation retrieved successfully",
            data=response_data
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))

@router.post("/booking-cancellations")
async def get_all_booking_cancellations(
    request: BookingCancellationFilter,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Get all booking cancellations with optional filters.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            print(f"Response JSON: {resp_json}")
            account_id = resp_json.get("account_id")
            role = resp_json.get("user_role")
            if not account_id or not role:
                return ErrorResponse(status_code=401, message="Account ID and role are required")
        else:
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        AssignedAgent = aliased(Admin)
        Agent = aliased(Admin)
        
        # Build the query with join
        query = db.query(BookingCancellation, Users, ServiceProvider, AssignedAgent, Agent, BookingCancellationReason)\
            .outerjoin(Users, BookingCancellation.user_id == Users.id).\
            outerjoin(ServiceProvider, BookingCancellation.artisan_id == ServiceProvider.id).\
            outerjoin(AssignedAgent, BookingCancellation.assigned_agent_id == AssignedAgent.id).\
            outerjoin(Agent, BookingCancellation.agent_id == Agent.id).\
            outerjoin(BookingCancellationReason, BookingCancellationReason.id == BookingCancellation.cancellation_reason_id).order_by(BookingCancellation.created_at.desc())
                        
        
        # Apply filters
        if request.booking_id:
            query = query.filter(BookingCancellation.booking_id == request.booking_id)
        if request.user_id:
            query = query.filter(BookingCancellation.user_id == request.user_id)
        if request.artisan_id:
            query = query.filter(BookingCancellation.artisan_id == request.artisan_id)
        if request.status:
            query = query.filter(BookingCancellation.status == request.status)

        if request.assigned_agent_id:
            query = query.filter(BookingCancellation.assigned_agent_id == request.assigned_agent_id)
        
        if account_id and role == 'agent' :
            query = query.filter(BookingCancellation.assigned_agent_id == account_id)
        
        if request.search:
            query = query.filter(
                or_(
                    BookingCancellation.id.cast(String).ilike(f"%{request.search}%"),
                    cast(BookingCancellation.user_type, String).ilike(f"%{request.search}%")
                )
            )


        # Get total count before pagination
        total_count = query.count()


        offset = (request.skip - 1) * request.limit if request.skip > 0 else 0
        # Apply pagination and ordering
        results = (
            query.order_by(BookingCancellation.created_at.desc())
            .limit(request.limit)
            .offset(offset)
            .all()
        )
                                   
        # Create response data with user first names
        response_data = []
        for cancellation, user, artisan, assigned_agent, agent, reason in results:
            cancellation_data = BookingCancellationResponse.from_orm(cancellation)
            item_data = cancellation_data.dict()
            user_idd = None
            artisan_idd = None
            agent_idd = None
            user_name = None
            user_mobile = None
            artisan_name = None
            artisan_mobile = None
            agent_idd = None
            agent_name = None
            agent_mobile = None
            assigned_agent_name = None
            if user:
                user_idd = str(user.id)
                user_name = user.first_name+" "+user.last_name if user.last_name else user.first_name
                user_mobile = user.phone_number
            if artisan:
                artisan_idd = str(artisan.id)
                artisan_name = artisan.first_name+" "+artisan.last_name if artisan.last_name else artisan.first_name
                artisan_mobile = artisan.phone_number
            if agent:
                agent_idd = str(agent.id)
                agent_name = agent.first_name+" "+agent.last_name if agent.last_name else agent.first_name
                agent_mobile = agent.phone_number
            if assigned_agent:
                assigned_agent_name = assigned_agent.first_name+" "+assigned_agent.last_name if assigned_agent.last_name else assigned_agent.first_name

            item_data["reason"] = reason.reason if reason else None
            item_data['user'] = {
                "id": user_idd,
                "name": user_name,
                "mobile": user_mobile,
                "type": "user"
            }
            item_data['artisan'] = {
                "id": artisan_idd,
                "name": artisan_name,
                "mobile": artisan_mobile,
                "type": "artisan"
            }
            item_data['agent'] = {
                "id": agent_idd,
                "name": agent_name,
                "mobile": agent_mobile,
                "type": "agent"
            }
            item_data["assigned_agent_name"] = assigned_agent_name
            response_data.append(item_data)
        
        # Prepare response data
        response_details = {
            "cancellation": response_data,
            "pagination": {
                "total": total_count,
                "page": request.skip,
                "limit": request.limit,
                "pages": (total_count + request.limit - 1) // request.limit,
            },
        }
        
        return StandardResponse(
            status_code=200, 
            message="Booking cancellations retrieved successfully",
            data=response_details
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))

@router.put("/booking-cancellation/{id}")
async def update_booking_cancellation(
    id: str,
    request: BookingCancellationUpdate,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Assign an agent to a booking cancellation by admin.
    """
    try:
        status = request.status
        cancellation = db.query(BookingCancellation).filter(BookingCancellation.id == id).first()
        if not cancellation:
            return ErrorResponse(status_code=404, message="Booking cancellation not found")
        
        if Authorization is None and status == 'completed':
            acc_id = cancellation.user_id
            user = db.query(Users).filter(Users.id == acc_id).first()
            user_name = f"{user.first_name or ''} {user.last_name or ''}".strip()

            # Update fields if provided
            cancellation.status = status
            cancellation.resolved_at = datetime.now()
            db.commit()
            db.refresh(cancellation)
            logger.info("Refund processed successfully, Booking cancellation status updated to completed")
            # Create history entry
            create_booking_cancellation_history(db, {"booking_cancellation_id": id, "description": status_dict["refund_done"], "user_name": user_name, "user_id": acc_id})
            create_booking_cancellation_history(db, {"booking_cancellation_id": id, "description": status_dict["resolved"], "user_name": user_name, "user_id": acc_id})
            return StandardResponse(
            status_code=200, 
            message="Refund processed successfully")
        
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        cancellation = db.query(BookingCancellation).filter(BookingCancellation.id == id).first()
        if not cancellation:
            return ErrorResponse(status_code=404, message="Booking cancellation not found")
        
        penalty_needed = cancellation.penalty_needed
        cancellation_status = 'pending' if penalty_needed else 'completed'

        assigned_agent_id = request.assigned_agent_id
        cancel_booking = request.__dict__.pop("cancel_booking", False)
        if cancel_booking:
            # if cancellation.assigned_agent_id is None:
            #     return ErrorResponse(status_code=400, message="Agent not assigned to booking cancellation")
            
            # Update booking status to cancelled
            booking = db.query(Booking).filter(Booking.id == cancellation.booking_id).first()
            if not booking:
                return ErrorResponse(status_code=404, message="Booking not found")
            
            setattr(cancellation, 'status', cancellation_status)
            booking.status = "cancelled"
            db.commit()
            db.refresh(booking)
            logger.info("Booking status updated to cancelled")
            return StandardResponse(
            status_code=200, 
            message="Booking cancelled successfully")


        if assigned_agent_id:
            # Update fields if provided
            update_data = request.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(cancellation, key, value)
            
            db.commit()
            db.refresh(cancellation)
            logger.info(f"Agent {request.assigned_agent_id} assigned successfully")
            # Create history entry
            admin = db.query(Admin).filter(Admin.id == account_id).first()
            user_name = admin.first_name+" "+admin.last_name
            create_booking_cancellation_history(db, {"booking_cancellation_id": id, "description": status_dict["agent_assigned"], "user_name": user_name, "user_id": account_id})
            return StandardResponse(
                status_code=200, 
                message="Agent assigned successfully",
                data=BookingCancellationResponse.from_orm(cancellation)
            )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))

@router.delete("/booking-cancellation/{id}")
async def delete_booking_cancellation(
    id: str,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Delete a booking cancellation.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        cancellation = db.query(BookingCancellation).filter(BookingCancellation.id == id).first()
        if not cancellation:
            return ErrorResponse(status_code=404, message="Booking cancellation not found")
        
        db.delete(cancellation)
        db.commit()
        
        return StandardResponse(
            status_code=200, 
            message="Booking cancellation deleted successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.put("/booking-cancellation-approve/{id}")
async def approve_booking_cancellation(
    background_task: BackgroundTasks,
    id: str,
    request: ApproveBookingCancellation,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Approve a booking cancellation.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            # account_id= '934b14ca-ae72-4613-8ced-744c50b51476'
        else:
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        cancellation = db.query(BookingCancellation).filter(BookingCancellation.id == id).first()
        if not cancellation:
            return ErrorResponse(status_code=404, message="Booking cancellation not found")
        
        print(request.__dict__, "request")
        
        booking_id = str(cancellation.booking_id)
        booking_amount = cancellation.booking_amount
        penalty_needed = cancellation.penalty_needed
        
        # penalty_user_type = request.__dict__.pop("user_type")   # To whom penalty is applied, decided by agent
        penalty_user_type = cancellation.penalty_user_type   # To whom penalty is applied, decided by agent
        penalty_user_id = cancellation.penalty_user_id
        penalty = request.penalty
        
        if penalty > booking_amount:
            return ErrorResponse(status_code=400, message="Penalty cannot be greater than Booking amount")

        admin = db.query(Admin).filter(Admin.id == account_id).first()
        user_name = admin.first_name+" "+admin.last_name
        
        # Deduct penalty for user/artisan
        if penalty_needed:
            if penalty_user_id and penalty_user_type.lower() == "user":
                apply_penalty(db, penalty_user_type, penalty_user_id, penalty)
                logger.info(f"Penalty of {penalty} applied to {penalty_user_type} {penalty_user_id} in wallet")
                create_booking_cancellation_history(db, {"booking_cancellation_id": id, "description": f'{status_dict["penalty_applied"]} {penalty_user_type.capitalize()}', "user_name": user_name, "user_id": account_id})
                try:
                    send_push_notification(
                        auth_token=Authorization,
                        title="Penalty Processed",
                        message=f"Penalty of {str(penalty)} has been applied for your booking cancellation {booking_id}",
                        sender_id=penalty_user_id,
                        type=penalty_user_type.lower()
                    )
                except:
                    pass
            elif penalty_user_id and penalty_user_type.lower() == "artisan":
                apply_penalty(db, penalty_user_type, penalty_user_id, penalty)
                logger.info(f"Penalty of {penalty} applied to {penalty_user_type} {penalty_user_id} in wallet")
                create_booking_cancellation_history(db, {"booking_cancellation_id": id, "description": f'{status_dict["penalty_applied"]} {penalty_user_type.capitalize()}', "user_name": user_name, "user_id": account_id})
                try:
                    send_push_notification(
                        auth_token=Authorization,
                        title="Penalty Processed",
                        message=f"Penalty of {str(penalty)} has been applied for your booking cancellation {booking_id}",
                        sender_id=penalty_user_id,
                        type=penalty_user_type.lower()
                    )
                except:
                    pass
        else:
            logger.info("No penalty needed for this cancellation reason")

        update_data = request.model_dump(exclude_unset=True)
        setattr(cancellation, "status", "completed")
        setattr(cancellation, "resolved_at", datetime.now())
        for key, value in update_data.items():
            setattr(cancellation, key, value)
        db.commit()
        db.refresh(cancellation)
        logger.info(f"Booking cancellation approved successfully")
        
        return StandardResponse(
            status_code=200, 
            message="Booking cancellation approved successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.get("/bc-history/{id}")
async def get_bc_history(
    id: str,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Get booking cancellation history by cancellation id.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        query = db.query(BookingCancellationHistory).filter(BookingCancellationHistory.booking_cancellation_id == id).order_by(BookingCancellationHistory.created_at.asc())
    
        results = query.all()
                
        return StandardResponse(
            status_code=200, 
            message="Booking cancellation history retrieved successfully",
            data=results
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.put("/retry-refund/{id}")
async def retry_refund(
    background_task: BackgroundTasks,
    id: str,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Retry refund for booking cancellation.
    """
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        bc_id = id
        bc_obj = db.query(BookingCancellation).filter(BookingCancellation.id == bc_id).first()
        if not bc_obj:
            return ErrorResponse(status_code=404, message="Booking cancellation not found")
        
        bc_status = bc_obj.status
        print(bc_status, 'bc_status')
        if bc_status != "approved":
            return ErrorResponse(status_code=400, message="Booking cancellation status must be in APPROVED status")
        
        admin = db.query(Admin).filter(Admin.id == account_id).first()
        if not admin:
            return ErrorResponse(status_code=404, message="Admin not found")
        user_name = admin.first_name+" "+admin.last_name
        penalty_user_type = None
        if bc_obj.penalty_user_id == bc_obj.user_id:
            penalty_user_type = "user"
        elif bc_obj.penalty_user_id == bc_obj.artisan_id:
            penalty_user_type = "artisan"
        payment_method = bc_obj.payment_method
        user_id = str(bc_obj.user_id)
        payment_id = bc_obj.payment_id
        refund_amount = bc_obj.refund_amount - bc_obj.penalty if penalty_user_type == "user"  else bc_obj.refund_amount

        payment = db.query(Payment).filter(Payment.id == payment_id).first()
        if payment:
            payment_details = payment.payment_details
            
        if payment_details:
            p_order_id = payment_details.get("p_order_id", None)
        else:
            p_order_id = None
                
        #Background task to process refund
        background_task.add_task(process_refund, {"bc_id": bc_id, "user_id": user_id, "booking_amount": refund_amount, "payment_method": payment_method, "p_order_id": p_order_id, "payment_id": payment_id, "user_name": user_name, "bch_user_id": account_id, "Authorization": Authorization})
        return StandardResponse(status_code=200, message="Refund retry initiated successfully")
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))
