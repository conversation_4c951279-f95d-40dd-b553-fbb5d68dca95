from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from uuid import UUID

from app.database import get_db
from app.models import Address, UserProfiles
from app.schemas import AddressCreate, AddressOut, AddressUpdate
from app.helper import StandardResponse, ErrorResponse
from app.utils import create_record, update_record, delete_record
from app.auth import permission_checker
from app.router.locality import add_locality

router = APIRouter(prefix="/addresses", tags=["Address"])


@router.post("/", response_model=StandardResponse)
async def create_address(
    payload: AddressCreate,
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        user_id = user.get("user_id") if isinstance(user, dict) else None
        # Validate user existence
        result = await db.execute(select(UserProfiles).where(UserProfiles.id == user_id))
        user = result.scalars().first()
        if not user:
            return ErrorResponse(status_code=404, message="User not found")

        # If incoming address is primary, update existing primary address to False
        if payload.is_primary:
            await db.execute(
                update(Address)
                .where(Address.user_id == user_id, Address.is_primary == True)
                .values(is_primary=False)
            )

        payload_data = payload.dict()
        payload_data["user_id"] = user.id

        # Create new address
        new_address = await create_record(db, Address, payload_data)
        if isinstance(new_address, str):
            return ErrorResponse(status_code=400, message="Failed to create address", error=new_address)

        data = AddressOut.model_validate(new_address, from_attributes=True)

        # Adding Locality to redis table
        add_locality(table_name='locality', locality=payload.locality)

        return StandardResponse(status_code=201, data=data, message="Address created successfully")

    except Exception as e:
        return ErrorResponse(status_code=500, message="Internal Error", error=str(e))



@router.put("/{address_id}", response_model=StandardResponse)
async def update_address(
    address_id: UUID,
    payload: AddressUpdate,
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user

        # Fetch current address record
        result = await db.execute(select(Address).where(Address.id == address_id))
        address = result.scalars().first()

        if not address:
            return ErrorResponse(status_code=404, message="Address not found")

        update_data = payload.dict(exclude_unset=True)

        # If is_primary=True, set other addresses to False
        if update_data.get("is_primary"):
            await db.execute(
                update(Address)
                .where(Address.user_id == address.user_id, Address.id != address.id)
                .values(is_primary=False)
            )

        # Update current address fields
        for key, value in update_data.items():
            setattr(address, key, value)

        await db.commit()
        await db.refresh(address)

        data = AddressOut.model_validate(address, from_attributes=True)
        return StandardResponse(status_code=200, data=data, message="Address updated successfully")

    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message="Internal Error", error=str(e))

@router.get("/{address_id}", response_model=StandardResponse) # use decorater and depends for authorization 
async def get_address(address_id: UUID, db: AsyncSession = Depends(get_db), user = Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
    
        result = await db.execute(select(Address).where(Address.id == address_id))
        address = result.scalars().first()

        if not address:
            return ErrorResponse(status_code=404, message="Address not found")

        data = AddressOut.model_validate(address, from_attributes=True)
        return StandardResponse(status_code=200, data=data, message="Address updated successfully")
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))


@router.delete("/{address_id}", response_model=StandardResponse)
async def delete_address(address_id: UUID, db: AsyncSession = Depends(get_db), user = Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        result = await db.execute(select(Address).where(Address.id == address_id))
        address = result.scalars().first()

        if not address:
            return ErrorResponse(status_code=404, message="Address not found")

        await delete_record(db, Address, address_id)
        return StandardResponse(status_code=200, message="Address deleted successfully")
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))

@router.get("/", response_model=StandardResponse)
async def get_user_addresses(
    db: AsyncSession = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission check failed
            return user

        user_id = user.get("user_id") if isinstance(user, dict) else None
        if not user_id:
            return ErrorResponse(status_code=400, message="Invalid user")

        # Fetch all addresses for the user
        result = await db.execute(select(Address).where(Address.user_id == user_id))
        addresses = result.scalars().all()

        # Convert to Pydantic model
        data = [AddressOut.model_validate(addr, from_attributes=True) for addr in addresses]

        return StandardResponse(status_code=200, data=data, message="Addresses fetched successfully")

    except Exception as e:
        return ErrorResponse(status_code=500, message="Internal Error", error=str(e))
