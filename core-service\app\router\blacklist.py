# app/routes/blacklist.py
from uuid import UUID
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.models import UsersBlacklist
from app.schemas import (
    UsersBlacklistCreate,
    UsersBlacklistUpdate,
    UsersBlacklistOut
)
from app.helper import StandardResponse, ErrorResponse
from app.database import get_db
from app.utils import create_record, update_record, delete_record
from app.auth import permission_checker
from fastapi.responses import JSONResponse

router = APIRouter(prefix="/users-blacklist", tags=["Users Blacklist"])

@router.post("/", response_model=StandardResponse)
async def create_blacklist(payload: UsersBlacklistCreate, db: AsyncSession = Depends(get_db), user = Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        record = await create_record(db, UsersBlacklist, payload.dict())
        return StandardResponse(
            status_code=201,
            data=UsersBlacklistOut.model_validate(record, from_attributes=True),
            message="User added to blacklist"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))


@router.get("/", response_model=StandardResponse)
async def get_blacklist(db: AsyncSession = Depends(get_db), user = Depends(permission_checker)):
    if isinstance(user, JSONResponse):  # Permission failed
        return user
    try:
        result = await db.execute(select(UsersBlacklist))
        records = result.scalars().all()
        data = [UsersBlacklistOut.model_validate(r, from_attributes=True) for r in records]
        return StandardResponse(status_code=200, data=data, message="User blcaklist fetch successfully")
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))



@router.get("/{blacklist_id}", response_model=StandardResponse)
async def get_blacklist_by_id(blacklist_id: UUID, db: AsyncSession = Depends(get_db), user = Depends(permission_checker)):
    if isinstance(user, JSONResponse):  # Permission failed
        return user
    try:
        result = await db.execute(select(UsersBlacklist).where(UsersBlacklist.id == blacklist_id))
        record = result.scalars().first()
        if not record:
            return ErrorResponse(status_code=404, message="Blacklist record not found")
        return StandardResponse(status_code=200, data=UsersBlacklistOut.model_validate(record, from_attributes=True), message="User blcaklist fetch successfully")
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))


@router.put("/{blacklist_id}", response_model=StandardResponse)
async def update_blacklist(
    blacklist_id: UUID,
    payload: UsersBlacklistUpdate,
    db: AsyncSession = Depends(get_db),
    user = Depends(permission_checker)
):
    if isinstance(user, JSONResponse):  # Permission failed
        return user
    try:
        updated = await update_record(db, UsersBlacklist, blacklist_id, payload.dict(exclude_unset=True))
        return StandardResponse(
            status_code=200,
            data=UsersBlacklistOut.model_validate(updated, from_attributes=True),
            message="Blacklist entry updated successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))



@router.delete("/{blacklist_id}", response_model=StandardResponse)
async def delete_blacklist(blacklist_id: UUID, db: AsyncSession = Depends(get_db), user = Depends(permission_checker)):
    if isinstance(user, JSONResponse):  # Permission failed
        return user
    try:
        await delete_record(db, UsersBlacklist, blacklist_id)
        return StandardResponse(status_code=200, message="Blacklist record deleted successfully")
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))
