import uuid
from fastapi import API<PERSON><PERSON><PERSON>, Depen<PERSON>, Header,Request
from app.schemas.service_request import CreateServiceRequest
from app.models_enum import BookingStatus, PricingType
from app.utils.helper import get_surcharges, get_tax
# from app.services.booking import create_booking_history
from app.database import get_db
from sqlalchemy.orm import Session
from app.utils.auth import get_id_header
from app.utils.crud import create_record
from typing import Annotated
from app.schemas.helper import StandardResponse, ErrorResponse
from app.models import Booking
from app.service_calls.available_artisans import get_available_artisans
from app.utils.notification import send_push_notification
from app.kafka_producer.producer import broadcast_producer
from app.utils.auth import permission_checker
from fastapi.responses import JSONResponse


router = APIRouter(tags=["Service Broadcast"])

@router.post("/service-broadcast/{id}")
async def service_broadcast(
    id: uuid.UUID,
    request:Request,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        token = request.headers.get("Authorization")

        if isinstance(user, JSONResponse):  # Permission failed
            return user
        booking = db.query(Booking).filter(Booking.id == id).first()
        if not booking:
            return ErrorResponse(status_code=404, message="Booking not found")
        
        if booking.status != BookingStatus.PENDING:
            return ErrorResponse(status_code=400, message="Booking already in progress")
        
        request_type = booking.request_type
        payload = {
                "service_id": str(booking.service_id),
                "latitude": booking.user_latitude,
                "longitude": booking.user_longitude,
                "max_distance": 100
                }
        # Send request to artisan search service
        r = await get_available_artisans(payload, request_type)
        print(r)
        if r:
            val = {
                'datas': r,
                'Authorization': token,
                'booking_id': str(booking.id)
            }
            await broadcast_producer(val)
            # for data in r:
            #     send_push_notification(
            #         auth_token=Authorization,
            #         title="New Booking Request",
            #         message=f"You have a new Booking Request - {booking.booking_order_id}",
            #         sender_id=data['artisan_id'],
            #         type="service_provider",
            #         data={},
            #     )
        else:
            artisans = []

        return StandardResponse(status_code=200, message="Booking request sent")
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))
