import googlemaps
from app.config import settings


GOOGLE_API_KEY = settings.GOOGLE_API_KEY
print("Google api key: ", GOOGLE_API_KEY)
gmaps = googlemaps.Client(key=GOOGLE_API_KEY)

def get_distance(origin, destination):
    """Calculate distance using Google Maps API, with fallback on error."""
    try:
        result = gmaps.distance_matrix(origin, destination, mode="driving")
        print("Google Maps API result: ", result)     
        return result['rows'][0]['elements'][0]['distance']['value'] / 1000  # Convert to km
    except Exception as e:
        print(f"Error in get_distance: {e}")
        return None
