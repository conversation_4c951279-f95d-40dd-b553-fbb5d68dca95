from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, declarative_base
from app.config import settings
Base = declarative_base()

# Create engine without database name (connect to PostgreSQL server)
DB_URL_WITHOUT_DB = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"

# Create engine for the actual database
DATABASE_URL = settings.DATABASE_URL
engine = create_engine(
        DATABASE_URL,
        pool_size=80,
        max_overflow=0
    )

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def enable_postgis():
    """Enable PostGIS extension in PostgreSQL."""
    with engine.connect() as conn:
        conn.execute(text("CREATE EXTENSION IF NOT EXISTS postgis;"))
        conn.commit()
        print("✅ PostGIS extension enabled successfully.")

# Dependency function to get a database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_db_session():
    """Get an async database session for use in non-endpoint functions"""
    db = SessionLocal()
    try:
        return db
    except Exception as e:
        db.close()
        raise e
