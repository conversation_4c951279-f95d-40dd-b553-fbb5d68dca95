from typing import Optional
from pydantic import UUID4, BaseModel, Field
from datetime import datetime, date, time
from app.utils.enums import BookingStatus, PaymentType, SortOrder, PricingType


class BookingCreate(BaseModel):
    customer_id: str
    service_provider_id: str
    service_category: str


class BookingResponse(BaseModel):
    id: str
    customer_id: str
    service_provider_id: str
    service_category: str
    booking_status: BookingStatus
    booking_time: datetime


class ServiceStart(BaseModel):
    id: UUID4
    start_otp: str
    service_start_artisan_latitude: float
    service_start_artisan_longitude: float


class ServiceEnd(BaseModel):
    id: UUID4
    stop_otp: str
    service_end_artisan_latitude: float
    service_end_artisan_longitude: float


class ServiceRescheduleRequest(BaseModel):
    id: UUID4
    booking_date: str
    start_time: str
    end_time: str


class ServiceReschedule(BaseModel):
    id: UUID4


class BookingRequest(BaseModel):
    artisan_id: str
    service_id: str
    booking_date: str
    description: str
    start_time: str
    end_time: str
    user_latitude: float
    user_longitude: float
    user_address: str
    base_service_fee: float
    pricing_type: PricingType
    # surcharges: Optional[float] = None
    # payment_type: Optional[PaymentType] = None
    # payment_id: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "artisan_id": "550e8400-e29b-41d4-a716-************",
                "service_id": "550e8400-e29b-41d4-a716-************",
                "booking_date": "2024-03-20",
                "start_time": "09:00",
                "end_time": "10:00",
                "user_latitude": 40.7128,
                "user_longitude": -74.0060,
                "user_address": "123 Main St, New York, NY 10001",
                "base_service_fee": 100.0,
                # "payment_type": PaymentType.CASH,
            }
        }


class BookingResponse(BaseModel):
    booking_id: str
    response: str
    reason: Optional[str] = None


class BookingHistoryRequest(BaseModel):
    artisan_id: Optional[str] = None
    user_id: Optional[str] = None
    booking_status: Optional[str] = "all"
    booking_id: Optional[str] = None
    q: Optional[str] = None  # search by booking_id or booking_order_id
    limit: int = 10
    skip: int = 1

class LatestBookingRequest(BaseModel):
    artisan_id: Optional[str] = None
    user_id: Optional[str] = None
    booking_status: Optional[str] = "all"
    booking_id: Optional[str] = None

class UpdateBookingStatusRequest(BaseModel):
    id: Optional[str] = None
    status: Optional[BookingStatus] = None

class ArtisanRating(BaseModel):
    booking_id: str
    # user_id: Optional[str] = None
    # artisan_id: Optional[str] = None
    user_type: Optional[str] = None  # customer or artisan
    artisan_rating: Optional[int] = None
    artisan_rating_tags: Optional[str] = None
    artisan_feedback: Optional[str] = None
    customer_rating: Optional[int] = None
    customer_rating_tags: Optional[str] = None
    customer_feedback: Optional[str] = None


class RatingRequest(BaseModel):
    artisan_id: Optional[str] = None
    user_id: Optional[str] = None
    page: int = 1
    limit: int = 10
    sort: Optional[SortOrder] = None

class NegotiationRequest(BaseModel):
    id: str
    end_time: Optional[str] = None
    negotiation_price: Optional[float] = None
    description_for_negotiation: Optional[str] = None
    status: Optional[BookingStatus] = None
    cancellation_reason: Optional[str] = None

class NegotiationResponseRequest(BaseModel):
    booking_id: str
    status: BookingStatus
    final_price: Optional[float] = None
    cancellation_reason: Optional[str] = None

class BookingIdRequest(BaseModel):
    booking_id: str

class BookingUpdateRequest(BaseModel):
    name: Optional[str] = None
    phone_number: Optional[str] = None

class RescheduleResponseRequest(BaseModel):
    booking_id: str
    status: BookingStatus
    cancellation_reason: Optional[str] = None
