from enum import Enum
import re
from typing import Optional
from pydantic import BaseModel, field_validator
import requests
from app.config import get_settings, settings
from app.database import get_db_session
from app.models import UserProfiles

db = get_db_session()


class NotificationType(str, Enum):
    SMS = "sms"
    OTP = "otp"
    EMAIL = "email"
    PUSH = "push"


class CustomBaseModel(BaseModel):
    class Config:
        from_attributes = True
        use_enum_values = True

    @field_validator("phone_number", mode="before", check_fields=False)
    @classmethod
    def phone_validation(cls, v):
        if not v:
            return v
        regex = r"^\+?[1-9]{1,4}-?[0-9]{9,15}$"
        if not re.fullmatch(regex, v):
            raise ValueError(
                "Phone number is invalid. It should start with '+' followed by the country code, "
                "optionally include '-', and contain 9-15 digits."
            )
        return v


class NotificationRequest(CustomBaseModel):
    phone_number: Optional[str] = None
    email: Optional[str] = None
    sender_id: Optional[str] = None
    message: str
    notification_type: NotificationType
    title: Optional[str] = None
    data: Optional[dict] = None
    fcm_request_type: Optional[str] = "payment"
    

def get_userprofile_by_id(user_id: str):
    try:
        user = db.query(UserProfiles).filter(UserProfiles.id == user_id).first()
        if user and user.notification_token:
            return user.notification_token
        else:
            return None
    except Exception as e:
        return {"error": str(e)}

# Function to send push notification
def send_push_notification(
    auth_token: str,
    title: str,
    message: str,
    sender_id: str,
    type: str,
    fcm_request_type: Optional[str] = "payment",
    data: dict = {},
):
    """
    Sends a push notification using the API.

    :param auth_token: The authorization token (JWT or other)
    :param message: The message for the notification
    :param sender_id: The ID of the sender
    :return: Response from the API
    """
    print("Sender ID:", sender_id, type, title, message)
    sender_id = get_userprofile_by_id(user_id=sender_id)
    print(sender_id, "sender_id")
    # Get API URL from settings
    BASE_URL = settings.BE_NOTIFICATION_API_URL
    ENDPOINT = "/send_notification"
    API_URL = f"{BASE_URL}{ENDPOINT}"

    # Prepare notification request
    notification_data = NotificationRequest(
        message=message,
        title=title,
        notification_type=NotificationType.PUSH,
        sender_id=str(sender_id),
        data=data,
        fcm_request_type=fcm_request_type
    )

    # Headers
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {auth_token}",
    }
    print(title, message, sender_id)
    print("Sending push notification:", message, "Sender ID:", sender_id)

    # Make API request
    try:
        response = requests.post(
            API_URL, headers=headers, json=notification_data.dict()
        )  # ✅ Fix JSON payload
        response_data = response.json()  # ✅ Ensure response.json() is properly called

        print("Response Code:", response.status_code)
        print("Response Data:", response_data)

        return response.status_code, response_data

    except requests.RequestException as e:
        print("Error sending notification:", str(e))
        return 500, {"error": "Failed to send notification"}
    
# # Function to send push notification
# def send_push_notification(
#     auth_token: str,
#     title: str,
#     message: str,
#     sender_id: str,
#     type: str,
#     fcm_request_type: Optional[str] = "payment",
#     data: dict = {},
# ):
#     """
#     Sends a push notification using the API.

#     :param auth_token: The authorization token (JWT or other)
#     :param message: The message for the notification
#     :param sender_id: The ID of the sender
#     :return: Response from the API
#     """
#     print("Sender ID:", sender_id, type, title, message)
#     sender_id = get_userprofile_by_id(user_id=sender_id)
#     print(sender_id, "sender_id")
#     # Get API URL from settings
#     BASE_URL = settings.BE_NOTIFICATION_API_URL
#     ENDPOINT = "/send_notification"
#     API_URL = f"{BASE_URL}{ENDPOINT}"

#     # Prepare notification request
#     notification_data = NotificationRequest(
#         message=message,
#         title=title,
#         notification_type=NotificationType.PUSH,
#         sender_id=str(sender_id),
#         data=data,
#         fcm_request_type=fcm_request_type
#     )

#     # Headers
#     headers = {
#         "Content-Type": "application/json",
#         "Authorization": f"Bearer {auth_token}",
#     }
#     print(title, message, sender_id)
#     print("Sending push notification:", message, "Sender ID:", sender_id)

#     # Make API request
#     try:
#         response = requests.post(
#             API_URL, headers=headers, json=notification_data.dict()
#         )  # ✅ Fix JSON payload
#         response_data = response.json()  # ✅ Ensure response.json() is properly called

#         print("Response Code:", response.status_code)
#         print("Response Data:", response_data)

#         return response.status_code, response_data

#     except requests.RequestException as e:
#         print("Error sending notification:", str(e))
#         return 500, {"error": "Failed to send notification"}
