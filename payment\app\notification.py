from enum import Enum
import re
from typing import Optional

from pydantic import BaseModel, field_validator
import requests
from app.config import settings


class NotificationType(str, Enum):
    SMS = "sms"
    OTP = "otp"
    EMAIL = "email"
    PUSH = "push"


class CustomBaseModel(BaseModel):
    class Config:
        from_attributes = True
        use_enum_values = True

    @field_validator("phone_number", mode="before", check_fields=False)
    @classmethod
    def phone_validation(cls, v):
        if not v:
            return v
        regex = r"^\+?[1-9]{1,4}-?[0-9]{9,15}$"
        if not re.fullmatch(regex, v):
            raise ValueError(
                "Phone number is invalid. It should start with '+' followed by the country code, "
                "optionally include '-', and contain 9-15 digits."
            )
        return v


class NotificationRequest(CustomBaseModel):
    phone_number: Optional[str] = None
    email: Optional[str] = None
    sender_id: Optional[str] = None
    message: str
    notification_type: NotificationType
    title: Optional[str] = None
    data: Optional[dict] = None


def get_user_by_id(user_id: str, auth_token: str):
    """
    Fetches user details by ID.

    :param api_base_url: Base URL of the FastAPI server (e.g., "http://localhost:8000")
    :param user_id: ID of the user to fetch
    :param auth_token: Authorization token for the request
    :return: Response JSON or error message
    """
    url = f"{settings.BE_PROFILE_API_URL}/user-read/{user_id}"
    headers = {"Authorization": f"Bearer {auth_token}"}

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise an error for bad responses (4xx, 5xx)
        sender_id = response.json()
        print("Response", sender_id)
        sender_id = sender_id["data"]["notification_uuid"]
        # sender_id = sender_id["notification_uuid"]
        print("Response Data:", sender_id)
        return sender_id
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}


def get_service_provider_by_id(sp_id: str, auth_token: str):
    """
    Fetches service provider details by ID.

    :param api_base_url: Base URL of the FastAPI server (e.g., "http://localhost:8000")
    :param sp_id: ID of the service provider to fetch (UUID format)
    :param auth_token: Authorization token for the request
    :return: Response JSON or error message
    """
    url = f"{settings.BE_PROFILE_API_URL}/sp-read/{sp_id}"
    headers = {"Authorization": f"Bearer {auth_token}"}

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise an error for bad responses (4xx, 5xx)
        sender_id = response.json()
        print("Response", sender_id)
        sender_id = sender_id["data"]["notification_uuid"]
        print("Response Data:", sender_id)
        return sender_id
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}


# Function to send push notification
def send_push_notification(
    auth_token: str,
    title: str,
    message: str,
    sender_id: str,
    type: str,
    data: dict = {},
):
    """
    Sends a push notification using the API.

    :param auth_token: The authorization token (JWT or other)
    :param message: The message for the notification
    :param sender_id: The ID of the sender
    :return: Response from the API
    """
    print("Sender ID:", sender_id, type, title, message)
    if type == "user":
        sender_id = get_user_by_id(user_id=sender_id, auth_token=auth_token)
        print(sender_id, "sender_id")
    if type == "service_provider":
        sender_id = get_service_provider_by_id(sp_id=sender_id, auth_token=auth_token)
        print(sender_id, "sender_id")
    print("Sender ID:", sender_id)
    # Get API URL from settings
    BASE_URL = settings.BE_NOTIFICATION_API_URL
    ENDPOINT = "/send_notification"
    API_URL = f"{BASE_URL}{ENDPOINT}"

    # Prepare notification request
    notification_data = NotificationRequest(
        message=message,
        title=title,
        notification_type=NotificationType.PUSH,
        sender_id=str(sender_id),
        data=data,
    )

    # Headers
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {auth_token}",
    }
    print(title, message, sender_id)
    print("Sending push notification:", message, "Sender ID:", sender_id)

    # Make API request
    try:
        response = requests.post(
            API_URL, headers=headers, json=notification_data.dict()
        )  # ✅ Fix JSON payload
        response_data = response.json()  # ✅ Ensure response.json() is properly called

        print("Response Code:", response.status_code)
        print("Response Data:", response_data)

        return response.status_code, response_data

    except requests.RequestException as e:
        print("Error sending notification:", str(e))
        return 500, {"error": "Failed to send notification"}
