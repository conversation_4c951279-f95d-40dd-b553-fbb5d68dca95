from datetime import datetime
import random, json
from uuid import uuid4
from fastapi import APIRouter, Depends, <PERSON><PERSON>, Query, Request
from app.schemas.service_request import CreateServiceRequest, ArtisanSoftLockRequest, AssignArtisanRequest, UpdateServiceRequest, AddServiceCreate
from app.models_enum import BookingStatus, PricingType, ArtisanAssignStatus, CartStatus, InvoiceItemStatus, ActivityType, InvoiceStatusEnum
from app.utils.helper import get_cart_items, get_sp_id, get_surcharges, get_tax, generate_booking_order_id, create_service_request
from app.utils.helper_res import (
    is_valid_uuid, 
    get_empty_user_details, 
    get_empty_invoice_details, 
    get_empty_assigned_artisan_details, get_empty_artisan_details
)
# from app.services.booking import create_booking_history
from app.database import get_db
from app.kafka_producer.producer import service_request_producer
from sqlalchemy import func, or_, and_
from sqlalchemy.orm import Session
from app.utils.auth import get_id_header
from app.utils.crud import create_record
from app.utils.activity_feed import log_activity
from typing import Annotated, List, Optional
from app.schemas.helper import StandardResponse, ErrorResponse
from app.models import Booking, Services, UserProfiles , ArtisanAssigned, Invoice, InvoiceItem , Cart , Address

from app.utils.redis_config import redis_client
from app.utils.auth import permission_checker
from fastapi.responses import JSONResponse
from app.service_calls.invoice import create_invoice
from app.utils.helper_res import get_avg_rating_by_user
from app.utils.notification import send_push_notification
import logging
from app.utils.helper import get_tax, get_booking_fee
from sqlalchemy.orm import aliased
from sqlalchemy import cast, String, select


router = APIRouter(tags=["Service Request"])


@router.post("/request-service")
async def request_service(
    request: CreateServiceRequest,
    db: Session = Depends(get_db),
    # user=Depends(permission_checker)
):
    try:
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user

        cart_items = get_cart_items(request.user_id)

        if not cart_items:
            return ErrorResponse(status_code=400, message="Cart is empty")
        
        cart_rows = db.query(Cart.id).filter(
            Cart.user_id == request.user_id,
            Cart.status == "PENDING"
        ).all()

        cart_ids = [{"cart_id": str(row.id)} for row in cart_rows]

        booking_date = datetime.strptime(request.booking_date, "%Y-%m-%d").date()

        preferred_arrival_time = datetime.strptime(request.preferred_arrival_time, "%H:%M").time()
        
        # Generate booking order ID
        booking_order_id = await generate_booking_order_id(db)

        new_booking = {
                'sp_id' : get_sp_id(db),  # Main service provider id(GTI)
                'booking_order_id' : booking_order_id,
                'user_id' : request.user_id,
                'booking_date' : str(booking_date),
                'preferred_arrival_time' : str(preferred_arrival_time),
                'user_latitude' : request.user_latitude,
                'user_longitude' : request.user_longitude,
                'user_address' : request.user_address,
                'request_type' : request.request_type,
                'service_list' : cart_items,
                'locality' : request.locality,
                'address_id' : request.address_id if request.address_id else None,
                'cart_ids' : cart_ids,
                'payment_type' : request.payment_type if request.payment_type else "CASH"
        }
        # Creating Booking Request
        resp = await create_service_request(new_booking)
        if resp['status'] == 200:
            db.query(Cart).filter(Cart.status == CartStatus.PENDING, Cart.user_id == new_booking['user_id']).update({Cart.status: CartStatus.CHECKED_OUT},synchronize_session=False)
            db.commit()
        else:
            return ErrorResponse(status_code=500, message="Error creating booking")
        
        # Creating Invoice
        new_booking['booking_id'] = str(resp['booking_obj'].id)
        status = await create_invoice(new_booking)
        if status != 200:
            return ErrorResponse(status_code=500, message="Error creating invoice")
        else:
            pass
        log_activity(
            db=db,
            title="Booking Created",
            description="Customer submitted service request",
            reference_id=new_booking['booking_id'],
            customer_id=new_booking['user_id'],
            activity_type=ActivityType.BOOKING_CREATED,
        )
        query = db.query(Booking).filter(Booking.id == new_booking['booking_id']).first()
        return StandardResponse(status_code=200, message="Booking request sent", data={query})
    except Exception as e:
        print(e)
        db.rollback()
        return ErrorResponse(status_code=500, message="Error creating booking", error=str(e))



@router.get("/service-requests-list")
async def get_all_service_requests(
    artisan_id: Optional[str] = None,
    user_id: Optional[str] = None,
    booking_status: Optional[List[str]] = Query(None),
    sort: Optional[str] = None,
    limit: Optional[int] = 10,
    skip: Optional[int] = 1,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    search: Optional[str] = None,
    locality: Optional[List[str]] = Query(None),
    assigned_agent_id: Optional[str] = None,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        account_id = user.get("user_id")
        role = user.get("role_name")

        if not account_id or not role:
            return ErrorResponse(status_code=401, message="Account ID and role are required")

        query = db.query(Booking, UserProfiles).outerjoin(UserProfiles, Booking.user_id == UserProfiles.id)

        # if locality and len(locality) > 0 and any(loc.strip() for loc in locality):
        #     # Filter out empty strings and apply locality filter
        #     valid_localities = [loc.strip() for loc in locality if loc.strip()]
        #     if valid_localities:
        #         query = query.filter(Booking.locality.in_(valid_localities))
        
        # Apply locality filter if provided
        if locality and len(locality) > 0:
            locality_list = []
            
            # Handle both list format and comma-separated string format
            for loc_item in locality:
                if ',' in loc_item:
                    # Handle comma-separated values within each parameter
                    locality_list.extend([loc.strip() for loc in loc_item.split(',')])
                else:
                    locality_list.append(loc_item.strip())
            
            # Remove duplicates and empty values
            valid_localities = list(set([loc for loc in locality_list if loc]))
            
            if valid_localities:
                # Use case insensitive partial matching with ilike
                query = query.filter(
                    or_(*[func.lower(Booking.locality).like(func.lower(f"%{loc}%")) for loc in valid_localities])
                )
        
        # Apply status filter if provided
        if booking_status and len(booking_status) > 0:
            status_list = []
            
            # Handle multiple ways of passing statuses
            for status_param in booking_status:
                # Handle comma-separated values within each parameter
                if ',' in status_param:
                    status_list.extend([status.strip() for status in status_param.split(',')])
                else:
                    status_list.append(status_param.strip())
            
            # Remove duplicates and empty values
            status_list = list(set([status for status in status_list if status]))
            
            # Only apply filter if we have valid status values
            if status_list:
                # Convert string values to enum values
                try:
                    enum_statuses = [BookingStatus(status) for status in status_list]
                    query = query.filter(Booking.status.in_(enum_statuses))
                except ValueError as e:
                    return ErrorResponse(status_code=400, message=f"Invalid booking status: {e}")

        # Apply agent filter if provided
        if assigned_agent_id:
            query = query.filter(
                and_(
                    Booking.assigned_agent_id == assigned_agent_id,
                    Booking.status == BookingStatus.AGENT_ASSIGNED
                )
            )
            
        #Apply artisan filter if provided
        if artisan_id:
            # Filter by artisan through ArtisanAssigned table
            artisan_invoice_ids = db.query(ArtisanAssigned.invoice_id).filter(
                ArtisanAssigned.artisan_id == artisan_id
            ).subquery()
            query = query.filter(Booking.invoice_id.in_(artisan_invoice_ids))
                
        # Apply user filter if provided
        if user_id:
            query = query.filter(Booking.user_id == user_id)
        
        # Apply date filters if provided
        if start_date or end_date:
            try:
                # Parse dates once
                start_date_parsed = None
                end_date_parsed = None
                
                if start_date:
                    start_date_parsed = datetime.strptime(start_date, "%Y-%m-%d").date()
                if end_date:
                    end_date_parsed = datetime.strptime(end_date, "%Y-%m-%d").date()
                
                # Apply filters based on role
                if role in ["artisan", "user"]:
                    # For artisan and user roles, filter by booking_date (Date field)
                    if start_date_parsed and end_date_parsed:
                        # Both dates provided - filter for date range
                        query = query.filter(
                            and_(
                                Booking.booking_date >= start_date_parsed,
                                Booking.booking_date <= end_date_parsed
                            )
                        )
                    elif start_date_parsed:

                        # Only start_date provided - filter for exact date match
                        query = query.filter(Booking.booking_date == start_date_parsed)
                    elif end_date_parsed:
                        # Only end_date provided - filter up to end_date
                        query = query.filter(Booking.booking_date == end_date_parsed)
                else:
                    # For other roles (admin, agent, etc.), filter by requested_time (DateTime field)
                    if start_date_parsed and end_date_parsed:
                        # Both dates provided - filter for date range
                        query = query.filter(
                            and_(
                                func.date(Booking.requested_time) >= start_date_parsed,
                                func.date(Booking.requested_time) <= end_date_parsed
                            )
                        )
                    elif start_date_parsed:
                        # Only start_date provided - filter from start_date onwards
                        query = query.filter(func.date(Booking.requested_time) >= start_date_parsed)
                    elif end_date_parsed:
                        # Only end_date provided - filter up to end_date
                        query = query.filter(func.date(Booking.requested_time) <= end_date_parsed)

            except ValueError:
                return ErrorResponse(status_code=400, message="Invalid date format. Use YYYY-MM-DD")
        
        # Apply search filter if provided
        if search:
            search_term_clean = search.strip()
            if search_term_clean:
                # Create search conditions for different fields
                search_conditions = []
                
                # Search in booking_order_id
                search_conditions.append(
                    Booking.booking_order_id.ilike(f"%{search_term_clean}%")
                )
                
                # Search in user full name (concatenated first_name and last_name)
                search_conditions.append(
                    func.concat(
                        UserProfiles.first_name, 
                        ' ', 
                        UserProfiles.last_name
                    ).ilike(f"%{search_term_clean}%")
                )
                
                # Also keep individual first_name and last_name searches
                search_conditions.append(
                    UserProfiles.first_name.ilike(f"%{search_term_clean}%")
                )
                search_conditions.append(
                    UserProfiles.last_name.ilike(f"%{search_term_clean}%")
                )
                
                # Search in services table
                # Get service IDs that match the search term
                matching_service_ids = db.query(Services.id).filter(
                    Services.name.ilike(f"%{search_term_clean}%")
                ).all()
                
                # Convert to list of strings for easier handling
                matching_service_id_list = [str(service_id[0]) for service_id in matching_service_ids]
                
                # Check if booking's service_list contains any of these service IDs
                if matching_service_id_list:
                    # Create a condition for each matching service ID
                    service_search_conditions = []
                    for service_id in matching_service_id_list:
                        # Check if service_id exists in the service_list JSON array
                        service_search_conditions.append(
                            func.jsonb_path_exists(Booking.service_list, f'$[*] ? (@.service_id == "{service_id}")')
                        )
                        # Also check for direct string format: ["uuid1", "uuid2"]
                        service_search_conditions.append(
                            func.jsonb_path_exists(Booking.service_list, f'$[*] ? (@ == "{service_id}")')
                        )
                    
                    # Add service search conditions to main search conditions
                    search_conditions.append(or_(*service_search_conditions))
                
                # Combine all search conditions with OR
                query = query.filter(or_(*search_conditions))

        # Check if any filters are applied (not just search)
        has_filters = any([
            search and search.strip(),
            locality and len(locality) > 0 and any(loc.strip() for loc in locality),
            # booking_status and len(booking_status) > 0,
            # assigned_agent_id,
            artisan_id,
            user_id,
            start_date or end_date
        ])
        # Count from filtered data BEFORE applying limit
        if has_filters:
            # Get all filtered bookings without limit for accurate counting
            all_filtered_bookings = query.all() 

            # Count from the filtered response data
            booking_count = {
                "REQUESTED": len([b for b in all_filtered_bookings if b[0].status == "REQUESTED"]),
                "ASSIGNED": len([b for b in all_filtered_bookings if b[0].status == "ARTISAN_ASSIGNED"]),
                "AGENT_ASSIGNED": len([b for b in all_filtered_bookings if b[0].status == "AGENT_ASSIGNED" and str(b[0].assigned_agent_id) == str(account_id)]),
                "ONGOING": len([b for b in all_filtered_bookings if b[0].status == "ONGOING"]),
                "COMPLETED": len([b for b in all_filtered_bookings if b[0].status == "COMPLETED"])
            }
            
            # Debug logging for search case
            # print(f"Debug - Search: {search}, Role: {role}, Account ID: {account_id}")
            # print(f"Debug - Total filtered bookings: {len(all_filtered_bookings)}")
            # print(f"Debug - Agent assigned count from filtered results: {booking_count['AGENT_ASSIGNED']}")
        else:
            # If no search, get total counts from database
            booking_count = {
                "REQUESTED": db.query(Booking).filter(Booking.status == BookingStatus.REQUESTED).count(),
                "ASSIGNED": db.query(Booking).filter(Booking.status == BookingStatus.ARTISAN_ASSIGNED).count(),
                "AGENT_ASSIGNED": 0,  # Default value
                "ONGOING": db.query(Booking).filter(Booking.status == BookingStatus.ONGOING).count(),
                "COMPLETED": db.query(Booking).filter(Booking.status == BookingStatus.COMPLETED).count()
            }
            
            # Update AGENT_ASSIGNED count based on role
            if role == "agent":
                agent_assigned_count = db.query(Booking).filter(
                    and_(
                        Booking.status == BookingStatus.AGENT_ASSIGNED,
                        Booking.assigned_agent_id == account_id
                    )
                ).count()
                booking_count["AGENT_ASSIGNED"] = agent_assigned_count
                # print(f"Debug - No Search, Role: {role}, Account ID: {account_id}, Agent Assigned Count: {agent_assigned_count}")
        # Apply sorting
        if sort:
            if sort.lower() == "asc":
                query = query.order_by(Booking.requested_time.asc())
            elif sort.lower() == "desc":
                query = query.order_by(Booking.requested_time.desc())
            else:
                # Invalid sort parameter, return error
                return ErrorResponse(status_code=400, message="Invalid sort parameter. Use 'asc' or 'desc'")
        else:
            # Default sorting: descending (newest first)
            query = query.order_by(Booking.requested_time.desc())
                
        # Get total count after filters but before pagination
        total_count = query.count()
        
        # Pagination logic
        adjusted_skip = (skip - 1) * limit if skip > 0 else 0
        # total_pages = (total_count + limit - 1) // limit
        current_page = (skip // limit) + 1
        
        # Execute query with pagination FOR RESPONSE DATA ONLY
        service_requests = query.offset(adjusted_skip).limit(limit).all()
        
        # Build response list
        bookings_list = []
        
        # Process bookings with complete details
        for booking_row in service_requests:
            booking = booking_row[0]  # Booking object
            user_profile = booking_row[1]  # UserProfiles object
            
            # Initialize fresh objects for each booking to prevent data bleeding
            user_details = get_empty_user_details()
            service_details = []
            invoice_details = get_empty_invoice_details()
            invoice_items = []
            assigned_artisans_list = []
            
            # Build user details object
            if user_profile:
                user_details.update({
                    "id": str(user_profile.id),
                    "first_name": user_profile.first_name,
                    "last_name": user_profile.last_name,
                    "email": user_profile.email,
                    "phone_number": user_profile.phone_number,
                    "profile_image_url": user_profile.profile_image_url,
                    "country_code": user_profile.country_code,
                    "ratings": get_avg_rating_by_user(db, user_profile.id)
                })
            
            # # Build service details array from service_list with optimized queries
            # if booking.service_list:
            #     # Extract all service IDs first
            #     service_ids = []
            #     service_metadata = {}  # Store quantity, description, images by service_id
                
            #     for service_item in booking.service_list:
            #         service_id = None
            #         quantity = 1
            #         item_description = None
            #         item_images = []
                    
            #         # Handle different service_list formats
            #         if isinstance(service_item, dict):
            #             # Format: [{"service_id": "uuid", "qty": 1, "description": "...", "images": [...]}]
            #             service_id = service_item.get("service_id")
            #             quantity = service_item.get("qty", 1)
            #             item_description = service_item.get("description")
            #             item_images = service_item.get("images", [])
            #         elif isinstance(service_item, str):
            #             # Format: ["uuid1", "uuid2", ...] - direct UUID strings
            #             service_id = service_item
                    
            #         if service_id:
            #             service_ids.append(service_id)
            #             service_metadata[service_id] = {
            #                 "quantity": quantity,
            #                 "item_description": item_description,
            #                 "item_images": item_images
            #             }
                
            #     # Get all services in one query for better performance
            #     if service_ids:
            #         services = db.query(Services).filter(Services.id.in_(service_ids)).all()
            #         services_dict = {str(service.id): service for service in services}
                    
            #         # Build service details maintaining original order
            #         for service_id in service_ids:
            #             if service_id in services_dict:
            #                 service = services_dict[service_id]
            #                 metadata = service_metadata.get(service_id, {})
                            
            #                 service_details.append({
            #                     "id": str(service.id),
            #                     "name": service.name,
            #                     "description": service.description,
            #                     "price": service.price,
            #                     "service_code": service.service_code,
            #                     "quantity": metadata.get("quantity", 1),
            #                     "item_description": metadata.get("item_description"),
            #                     "item_images": metadata.get("item_images", [])
            #                 })
            
            # Get invoice details if invoice_id exists
            if booking.invoice_id:
                invoice = db.query(Invoice).filter(Invoice.id == booking.invoice_id).first()
                if invoice:
                    payment_status = invoice.payment_status
                    print(payment_status, "invoice payment_status")
                    if payment_status == InvoiceStatusEnum.QUOTATION and booking.payment_type != "CASH":
                        continue
                    elif payment_status == InvoiceStatusEnum.QUOTATION and booking.payment_type == "CASH":
                        payment_status = "UNPAID"
                    invoice_details.update({
                        "id": str(invoice.id),
                        "sp_id": invoice.sp_id,
                        "payment_method": invoice.payment_method,
                        "biller_id": invoice.biller_id,
                        "customer_id": invoice.customer_id,
                        "tax_percentage": invoice.tax_percentage,
                        "tax_amount": invoice.tax_amount,
                        "platform_fee_percentage": invoice.platform_fee_percentage,
                        "platform_fee_amount": invoice.platform_fee_amount,
                        "base_fee": invoice.base_fee,
                        "discount_amount": invoice.discount_amount,
                        "booking_fee": invoice.booking_fee,
                        "total_amount": invoice.total_amount,
                        "pending_amount": invoice.pending_amount,
                        "payment_status": payment_status,
                        "booking_id": invoice.booking_id,
                        "currency": 'GHS',
                        "created_at": invoice.created_at.isoformat() if hasattr(invoice, 'created_at') and invoice.created_at else "",
                        "updated_at": invoice.updated_at.isoformat() if hasattr(invoice, 'updated_at') and invoice.updated_at else ""
                    })

                    # Get invoice items for this invoice
                    # parent_items = db.query(InvoiceItem).filter(
                    #     InvoiceItem.invoice_id == booking.invoice_id,
                    #     InvoiceItem.parent_id == None
                    # ).all()

                    # for parent in parent_items:
                    #     # Get service details for this invoice item
                    #     service = db.query(Services).filter(Services.id == parent.service_id).first()
                    #     service_name = service.name if service else "Unknown Service"

                        # # Aggregate child fields
                        # child_totals = db.query(
                        #     func.coalesce(func.sum(InvoiceItem.quantity), 0),
                        #     func.coalesce(func.sum(InvoiceItem.price), 0),
                        #     func.coalesce(func.sum(InvoiceItem.tax_amount), 0.0),
                        #     func.coalesce(func.sum(InvoiceItem.platform_fee_amount), 0.0),
                        #     func.coalesce(func.sum(InvoiceItem.total_amount), 0.0)
                        # ).filter(InvoiceItem.parent_id == parent.id).first()

                        # # Add child totals to parent
                        # total_quantity = parent.quantity + child_totals[0]
                        # total_price = parent.price + child_totals[1]
                        # total_tax = (parent.tax_amount or 0) + child_totals[2]
                        # total_platform_fee = (parent.platform_fee_amount or 0) + child_totals[3]
                        # total_amount = (parent.total_amount or 0) + child_totals[4]


                        # # Step 3: Count status of parent + children
                        # status_counts = db.query(
                        #     InvoiceItem.status,
                        #     func.count().label("count")
                        # ).filter(
                        #     or_(
                        #         InvoiceItem.id == parent.id,
                        #         InvoiceItem.parent_id == parent.id
                        #     )
                        # ).group_by(InvoiceItem.status).all()

                        # status_summary = {status: count for status, count in status_counts}

                        # invoice_items.append({
                        #     "id": str(parent.id),
                        #     "parent_id": None,
                        #     "service_id": parent.service_id,
                        #     "service_name": service_name,
                        #     "quantity": total_quantity,
                        #     "price": total_price,
                        #     "description": parent.description,
                        #     "tax_percentage": parent.tax_percentage,
                        #     "tax_amount": total_tax,
                        #     "platform_fee_percentage": parent.platform_fee_percentage,
                        #     "platform_fee_amount": total_platform_fee,
                        #     "discount_amount": parent.discount_amount,
                        #     "total_amount": total_amount,
                        #     # "status": parent.status,
                        #     "is_agent_created": parent.is_agent_created,
                        #     "created_at": parent.created_at.isoformat() if parent.created_at else "",
                        #     "updated_at": parent.updated_at.isoformat() if parent.updated_at else "",
                        #     "status_summary": {
                        #         "PENDING": status_summary.get(InvoiceItemStatus.PENDING, 0),
                        #         "PAID": status_summary.get(InvoiceItemStatus.PAID, 0),
                        #         "CANCEL_REQUESTED": status_summary.get(InvoiceItemStatus.CANCEL_REQUESTED, 0),
                        #         "CANCELLED": status_summary.get(InvoiceItemStatus.CANCELLED, 0)
                        #     }
                        # })
                        # if status_summary.get(InvoiceItemStatus.PENDING, 0) > 0:
                        #     invoice_items[-1]["status"] = InvoiceItemStatus.PENDING
                        # else:
                        #     # If all items are paid, set status to PAID
                        #     if status_summary.get(InvoiceItemStatus.PAID, 0) == total_quantity:
                        #         invoice_items[-1]["status"] = InvoiceItemStatus.PAID
                        #     # If all items are cancelled, set status to CANCELLED
                        #     elif status_summary.get(InvoiceItemStatus.CANCELLED, 0) == total_quantity:
                        #         invoice_items[-1]["status"] = InvoiceItemStatus.CANCELLED
                        #     # If all items are cancel requested, set status to CANCEL_REQUESTED
                        #     elif status_summary.get(InvoiceItemStatus.CANCEL_REQUESTED, 0) == total_quantity:
                        #         invoice_items[-1]["status"] = InvoiceItemStatus.CANCEL_REQUESTED
                    
                    invoice_items_query = db.query(InvoiceItem).filter(InvoiceItem.invoice_id == booking.invoice_id).all()
                    print(invoice_items, "invoice_items")
                    for invoice_item in invoice_items_query:
                        # Get service details for this invoice item
                        service = db.query(Services).filter(Services.id == invoice_item.service_id).first()
                        service_name = service.name if service else "Unknown Service"
                        
                        invoice_items.append({
                            "id": str(invoice_item.id),
                            "parent_id": str(invoice_item.parent_id) if invoice_item.parent_id else None,
                            "service_id": invoice_item.service_id,
                            "service_name": service_name,
                            "quantity": invoice_item.quantity,
                            "price": invoice_item.price,
                            "description": invoice_item.description,
                            "tax_percentage": invoice_item.tax_percentage,
                            "tax_amount": invoice_item.tax_amount,
                            "platform_fee_percentage": invoice_item.platform_fee_percentage,
                            "platform_fee_amount": invoice_item.platform_fee_amount,
                            "discount_amount": invoice_item.discount_amount,
                            "total_amount": invoice_item.total_amount,
                            "status": invoice_item.status,
                            "is_agent_created": invoice_item.is_agent_created,
                            "created_at": invoice_item.created_at.isoformat() if hasattr(invoice_item, 'created_at') and invoice_item.created_at else "",
                            "updated_at": invoice_item.updated_at.isoformat() if hasattr(invoice_item, 'updated_at') and invoice_item.updated_at else ""
                        })

            # Get assigned artisan details using invoice_id (filtered by artisan_id if provided)
            if booking.invoice_id:
                artisan_assignments_query = db.query(ArtisanAssigned).filter(
                    ArtisanAssigned.invoice_id == booking.invoice_id
                )
                
                # Filter by specific artisan if artisan_id is provided
                if artisan_id:
                    artisan_assignments_query = artisan_assignments_query.filter(
                        ArtisanAssigned.artisan_id == artisan_id
                    )
                    
                    # Get service_ids assigned to this specific artisan
                    artisan_service_ids = db.query(ArtisanAssigned.service_id).filter(
                        ArtisanAssigned.invoice_id == booking.invoice_id,
                        ArtisanAssigned.artisan_id == artisan_id
                    ).all()
                    artisan_service_ids = [str(service_id[0]) for service_id in artisan_service_ids if service_id[0]]
                    
                    # Filter service_details to only include services assigned to this artisan
                    service_details = [
                        service for service in service_details 
                        if service.get("id") in artisan_service_ids
                    ]
                
                artisan_assignments = artisan_assignments_query.all()
                
                for artisan_assigned in artisan_assignments:
                    # Initialize fresh objects for each artisan
                    assigned_artisan_details = get_empty_assigned_artisan_details()
                    assigned_artisan_profile_details = get_empty_artisan_details()
                    
                    # Build assigned artisan details
                    assigned_artisan_details.update({
                        "id": str(artisan_assigned.id),
                        "invoice_item_id": str(artisan_assigned.invoice_item_id),
                        "invoice_id": str(artisan_assigned.invoice_id),
                        "service_id": artisan_assigned.service_id,
                        "artisan_id": artisan_assigned.artisan_id,
                        "start_time": artisan_assigned.start_time.isoformat() if artisan_assigned.start_time else "",
                        "end_time": artisan_assigned.end_time.isoformat() if artisan_assigned.end_time else "",
                        # "start_otp": artisan_assigned.start_otp,
                        # "end_otp": artisan_assigned.end_otp,
                        "service_start": artisan_assigned.service_start.isoformat() if artisan_assigned.service_start else "",
                        "service_end": artisan_assigned.service_end.isoformat() if artisan_assigned.service_end else "",
                        "service_start_artisan_latitude": artisan_assigned.service_start_artisan_latitude,
                        "service_start_artisan_longitude": artisan_assigned.service_start_artisan_longitude,
                        "service_end_artisan_latitude": artisan_assigned.service_end_artisan_latitude,
                        "service_end_artisan_longitude": artisan_assigned.service_end_artisan_longitude,
                        "status": artisan_assigned.status,
                        "created_at": artisan_assigned.created_at.isoformat() if hasattr(artisan_assigned, 'created_at') and artisan_assigned.created_at else "",
                        "updated_at": artisan_assigned.updated_at.isoformat() if hasattr(artisan_assigned, 'updated_at') and artisan_assigned.updated_at else ""
                    })
                    
                    # Get assigned artisan profile details from UserProfiles table
                    if artisan_assigned.artisan_id:
                        # Check if artisan_id is a valid UUID before querying
                        if is_valid_uuid(artisan_assigned.artisan_id):
                            assigned_artisan_profile = db.query(UserProfiles).filter(
                                UserProfiles.id == artisan_assigned.artisan_id
                            ).first()
                            if assigned_artisan_profile:
                                job_count = db.query(ArtisanAssigned).filter(
                                    ArtisanAssigned.artisan_id == artisan_assigned.artisan_id,
                                    ArtisanAssigned.status == ArtisanAssignStatus.COMPLETED
                                ).count()
                                assigned_artisan_profile_details.update({
                                    "id": str(assigned_artisan_profile.id),
                                    "first_name": assigned_artisan_profile.first_name,
                                    "last_name": assigned_artisan_profile.last_name,
                                    "email": assigned_artisan_profile.email,
                                    "phone_number": assigned_artisan_profile.phone_number,
                                    "profile_image_url": assigned_artisan_profile.profile_image_url,
                                    "country_code": assigned_artisan_profile.country_code,
                                    "ratings": get_avg_rating_by_user(db, artisan_assigned.artisan_id),
                                    "job_count": job_count
                                })
                    
                    # Add this artisan to the list
                    assigned_artisans_list.append({
                        "assignment_details": assigned_artisan_details,
                        "artisan_profile": assigned_artisan_profile_details
                    })
            cart_ids = [
                cart_item['cart_id'] 
                for cart_item in (booking.cart_ids or [])
                if isinstance(cart_item, dict) and 'cart_id' in cart_item
            ] if booking.cart_ids else []


            # Get invoice items for this invoice
            parent_items = (
                db.query(InvoiceItem, Services)
                .join(Services, cast(Services.id, String) == InvoiceItem.service_id)
                .filter(
                    InvoiceItem.invoice_id == booking.invoice_id,
                    InvoiceItem.parent_id == None  # Parent items only
                ).order_by(InvoiceItem.created_at.asc()).all())
            for parent_invoice_item, ser in parent_items:
                # Subquery to get all invoice item IDs (parent + children)
                invoice_item_subq = (
                    db.query(InvoiceItem.id)
                    .filter(or_(
                        InvoiceItem.id == parent_invoice_item.id,
                        InvoiceItem.parent_id == parent_invoice_item.id
                    )).subquery()
                )

                # Main query to count InvoiceItems and ArtisanAssigned rows in one go
                combined_counts = (
                    db.query(
                        func.count(InvoiceItem.id).label("invoice_item_count"),
                        func.count(ArtisanAssigned.id).label("artisan_assigned_count")
                    )
                    .outerjoin(ArtisanAssigned, ArtisanAssigned.invoice_item_id == InvoiceItem.id)
                    .filter(InvoiceItem.id.in_(select(invoice_item_subq)))
                    .one()
                )
                service_details.append(
                        {
                            "id": parent_invoice_item.service_id,
                            "name": ser.name,
                            "description": ser.description,
                            "price": ser.price,
                            "service_code": ser.service_code,
                            "quantity": parent_invoice_item.quantity,
                            "item_description": parent_invoice_item.description,
                            "main_invoice_item_id": str(parent_invoice_item.id),
                            "is_agent_created": parent_invoice_item.is_agent_created,
                            "total_artisan_count": combined_counts.invoice_item_count,
                            "artisan_assigned_count": combined_counts.artisan_assigned_count
                        }
                    )

            booking_data = {
                "id": str(booking.id),
                "booking_order_id": booking.booking_order_id,
                "user_id": str(booking.user_id),
                "sp_id": str(booking.sp_id) if booking.sp_id else None,
                "cart_ids": cart_ids,
                "invoice_id": str(booking.invoice_id) if booking.invoice_id else None,
                "assigned_agent_id": str(booking.assigned_agent_id) if booking.assigned_agent_id else None,
                "booking_date": str(booking.booking_date),
                "preferred_arrival_time": str(booking.preferred_arrival_time) if booking.preferred_arrival_time else None,
                "user_latitude": booking.user_latitude,
                "user_longitude": booking.user_longitude,
                "user_address": booking.user_address,
                "locality": booking.locality,
                "is_rescheduled": booking.is_rescheduled,
                "status": booking.status,
                "request_type": booking.request_type,
                "requested_time": booking.requested_time.isoformat() if booking.requested_time else None,
                "updated_time": booking.updated_time.isoformat() if booking.updated_time else None,
                "service_details": service_details if service_details else [],
                "user_details": user_details,
                "invoice_details": invoice_details,
                "invoice_items": invoice_items,
                "assigned_artisans": assigned_artisans_list,
                # "assigned_artisan_count": len(assigned_artisans_list),
                # "unassigned_artisan_count": len(invoice_items) - len(assigned_artisans_list)
            }
            
            # Add booking to the list (search filtering is now done at database level)
            bookings_list.append(booking_data)

        return StandardResponse(
            status_code=200, 
            message="Service requests fetched successfully",
            data={
                "bookings": bookings_list,
                "booking_count": booking_count,
                "pagination": {
                    "page": current_page,
                    "page_size": limit,
                    # "total_pages": total_pages,
                    "total_count": total_count
                },

            }
        )
    except Exception as e:
        logging.error(f"Error fetching service requests list: {e}")
        return ErrorResponse(status_code=500, message="Error fetching service requests", error=str(e))


@router.get("/booking/details/{booking_order_id}")
async def get_booking_by_order_id(
    booking_order_id: str,
    artisan_id: Optional[str] = Query(None, description="Filter assigned artisans by specific artisan ID"),
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user
        # Initialize all variables to avoid "referenced before assignment" errors
        user_details = get_empty_user_details()
        service_details = []
        invoice_details = get_empty_invoice_details()
        invoice_items = []
        assigned_artisans_list = []
        
        # Query booking with user profile information
        booking_result = db.query(
            Booking,
            UserProfiles
        ).outerjoin(
            UserProfiles, Booking.user_id == UserProfiles.id
        ).filter(
            Booking.booking_order_id == booking_order_id
        ).first()
        
        if not booking_result:
            return ErrorResponse(
                status_code=404, 
                message=f"Booking with order ID '{booking_order_id}' not found"
            )
        
        booking = booking_result[0]  # Booking object
        user_profile = booking_result[1]  # UserProfiles object
        
        # Build user details object
        if user_profile:
            user_details.update({
                "id": str(user_profile.id),
                "first_name": user_profile.first_name,
                "last_name": user_profile.last_name,
                "email": user_profile.email,
                "phone_number": user_profile.phone_number,
                "profile_image_url": user_profile.profile_image_url,
                "country_code": user_profile.country_code,
                "ratings": get_avg_rating_by_user(db, user_profile.id)
            })
        
        # Build service details array from service_list
        # if booking.service_list:
        #     # Extract all service IDs first
        #     service_ids = []
        #     service_metadata = {}  # Store quantity, description, images by service_id
            
            # for service_item in booking.service_list:
            #     service_id = None
            #     quantity = 1
            #     item_description = None
            #     item_images = []
                
            #     # Handle different service_list formats
            #     if isinstance(service_item, dict):
            #         # Format: [{"service_id": "uuid", "qty": 1, "description": "...", "images": [...]}]
            #         service_id = service_item.get("service_id")
            #         quantity = service_item.get("qty", 1)
            #         item_description = service_item.get("description")
            #         item_images = service_item.get("images", [])
            #     elif isinstance(service_item, str):
            #         # Format: ["uuid1", "uuid2", ...] - direct UUID strings
            #         service_id = service_item
                
            #     if service_id:
            #         service_ids.append(service_id)
            #         service_metadata[service_id] = {
            #             "quantity": quantity,
            #             "item_description": item_description,
            #             "item_images": item_images
            #         }
            
            # # Get all services in one query for better performance
            # if service_ids:
            #     services = db.query(Services).filter(Services.id.in_(service_ids)).all()
            #     services_dict = {str(service.id): service for service in services}
                
            #     # Build service details maintaining original order
            #     for service_id in service_ids:
            #         if service_id in services_dict:
            #             service = services_dict[service_id]
            #             metadata = service_metadata.get(service_id, {})
                        
            #             service_details.append({
            #                 "id": str(service.id),
            #                 "name": service.name,
            #                 "description": service.description,
            #                 "price": service.price,
            #                 "service_code": service.service_code,
            #                 "quantity": metadata.get("quantity", 1),
            #                 "item_description": metadata.get("item_description"),
            #                 "item_images": metadata.get("item_images", [])
            #             })
        
        # Get cart description if cart_ids exists
        cart_description = None
        if booking.cart_ids:
            for cart_item in booking.cart_ids:
                # Extract cart_id from the dict structure
                if isinstance(cart_item, dict) and 'cart_id' in cart_item:
                    cart_id = cart_item['cart_id']
                    cart = db.query(Cart).filter(Cart.id == cart_id).first()
                    if cart:
                        cart_description = cart.description
                        break  # Use the first cart description found

        # Get invoice details if invoice_id exists
        if booking.invoice_id:
            invoice = db.query(Invoice).filter(Invoice.id == booking.invoice_id).first()
            if invoice:
                payment_status = invoice.payment_status
                if payment_status == "QUOTATION":
                    payment_status = "UNPAID"
                invoice_details.update({
                    "id": str(invoice.id),
                    "sp_id": invoice.sp_id,
                    "payment_method": invoice.payment_method,
                    "biller_id": invoice.biller_id,
                    "customer_id": invoice.customer_id,
                    "tax_percentage": invoice.tax_percentage,
                    "tax_amount": invoice.tax_amount,
                    "platform_fee_percentage": invoice.platform_fee_percentage,
                    "platform_fee_amount": invoice.platform_fee_amount,
                    "base_fee": invoice.base_fee,
                    "discount_amount": invoice.discount_amount,
                    "booking_fee": invoice.booking_fee,
                    "total_amount": invoice.total_amount,
                    "pending_amount": invoice.pending_amount,
                    "payment_status": payment_status,
                    "booking_id": invoice.booking_id,
                    "currency": 'GHS',
                    "created_at": invoice.created_at.isoformat() if hasattr(invoice, 'created_at') and invoice.created_at else "",
                    "updated_at": invoice.updated_at.isoformat() if hasattr(invoice, 'updated_at') and invoice.updated_at else ""
                })


                # parent_items = db.query(InvoiceItem).filter(
                #     InvoiceItem.invoice_id == booking.invoice_id,
                #     InvoiceItem.parent_id == None
                # ).all()

                # for parent in parent_items:
                #     # Get service details for this invoice item
                #     service = db.query(Services).filter(Services.id == parent.service_id).first()
                #     service_name = service.name if service else "Unknown Service"

                #     # Aggregate child fields
                #     child_totals = db.query(
                #         func.coalesce(func.sum(InvoiceItem.quantity), 0),
                #         func.coalesce(func.sum(InvoiceItem.price), 0),
                #         func.coalesce(func.sum(InvoiceItem.tax_amount), 0.0),
                #         func.coalesce(func.sum(InvoiceItem.platform_fee_amount), 0.0),
                #         func.coalesce(func.sum(InvoiceItem.total_amount), 0.0)
                #     ).filter(InvoiceItem.parent_id == parent.id).first()

                #     # Add child totals to parent
                #     total_quantity = parent.quantity + child_totals[0]
                #     total_price = parent.price + child_totals[1]
                #     total_tax = (parent.tax_amount or 0) + child_totals[2]
                #     total_platform_fee = (parent.platform_fee_amount or 0) + child_totals[3]
                #     total_amount = (parent.total_amount or 0) + child_totals[4]


                #     # Step 3: Count status of parent + children
                #     status_counts = db.query(
                #         InvoiceItem.status,
                #         func.count().label("count")
                #     ).filter(
                #         or_(
                #             InvoiceItem.id == parent.id,
                #             InvoiceItem.parent_id == parent.id
                #         )
                #     ).group_by(InvoiceItem.status).all()

                #     status_summary = {status: count for status, count in status_counts}

                #     invoice_items.append({
                #         "id": str(parent.id),
                #         "parent_id": None,
                #         "service_id": parent.service_id,
                #         "service_name": service_name,
                #         "quantity": total_quantity,
                #         "price": total_price,
                #         "description": parent.description,
                #         "tax_percentage": parent.tax_percentage,
                #         "tax_amount": total_tax,
                #         "platform_fee_percentage": parent.platform_fee_percentage,
                #         "platform_fee_amount": total_platform_fee,
                #         "discount_amount": parent.discount_amount,
                #         "total_amount": total_amount,
                #         # "status": parent.status,
                #         "is_agent_created": parent.is_agent_created,
                #         "created_at": parent.created_at.isoformat() if parent.created_at else "",
                #         "updated_at": parent.updated_at.isoformat() if parent.updated_at else "",
                #         "status_summary": {
                #                 "PENDING": status_summary.get(InvoiceItemStatus.PENDING, 0),
                #                 "PAID": status_summary.get(InvoiceItemStatus.PAID, 0),
                #                 "CANCEL_REQUESTED": status_summary.get(InvoiceItemStatus.CANCEL_REQUESTED, 0),
                #                 "CANCELLED": status_summary.get(InvoiceItemStatus.CANCELLED, 0)
                #             }
                #     })
                #     if status_summary.get(InvoiceItemStatus.PENDING, 0) > 0:
                #             invoice_items[-1]["status"] = InvoiceItemStatus.PENDING
                #     else:
                #         # If all items are paid, set status to PAID
                #         if status_summary.get(InvoiceItemStatus.PAID, 0) == total_quantity:
                #             invoice_items[-1]["status"] = InvoiceItemStatus.PAID
                #         # If all items are cancelled, set status to CANCELLED
                #         elif status_summary.get(InvoiceItemStatus.CANCELLED, 0) == total_quantity:
                #             invoice_items[-1]["status"] = InvoiceItemStatus.CANCELLED
                #         # If all items are cancel requested, set status to CANCEL_REQUESTED
                #         elif status_summary.get(InvoiceItemStatus.CANCEL_REQUESTED, 0) == total_quantity:
                #             invoice_items[-1]["status"] = InvoiceItemStatus.CANCEL_REQUESTED

                # Get invoice items for this invoice
                invoice_items_query = db.query(InvoiceItem).filter(
                    InvoiceItem.invoice_id == booking.invoice_id
                ).all()
                
                for invoice_item in invoice_items_query:
                    # Get service details for this invoice item
                    service = db.query(Services).filter(Services.id == invoice_item.service_id).first()
                    service_name = service.name if service else "Unknown Service"
                    
                    invoice_items.append({
                        "id": str(invoice_item.id),
                        "parent_id": str(invoice_item.parent_id) if invoice_item.parent_id else None,
                        "service_id": invoice_item.service_id,
                        "service_name": service_name,
                        "quantity": invoice_item.quantity,
                        "price": invoice_item.price,
                        "description": invoice_item.description,
                        "tax_percentage": invoice_item.tax_percentage,
                        "tax_amount": invoice_item.tax_amount,
                        "platform_fee_percentage": invoice_item.platform_fee_percentage,
                        "platform_fee_amount": invoice_item.platform_fee_amount,
                        "discount_amount": invoice_item.discount_amount,
                        "booking_fee": invoice_item.booking_fee,
                        "booking_fee_percentage": invoice_item.booking_fee_percentage,
                        "total_amount": invoice_item.total_amount,
                        "status": invoice_item.status,
                        "is_agent_created": invoice_item.is_agent_created,
                        "created_at": invoice_item.created_at.isoformat() if hasattr(invoice_item, 'created_at') and invoice_item.created_at else "",
                        "updated_at": invoice_item.updated_at.isoformat() if hasattr(invoice_item, 'updated_at') and invoice_item.updated_at else ""
                    })

        # Get assigned artisan details using invoice_id (filtered by artisan_id if provided)
        if booking.invoice_id:
            artisan_assignments_query = db.query(ArtisanAssigned).filter(
                ArtisanAssigned.invoice_id == booking.invoice_id
            )
            
            # Filter by specific artisan if artisan_id is provided
            if artisan_id:
                artisan_assignments_query = artisan_assignments_query.filter(
                    ArtisanAssigned.artisan_id == artisan_id
                )
                
                # Get service_ids assigned to this specific artisan
                artisan_service_ids = db.query(ArtisanAssigned.service_id).filter(
                    ArtisanAssigned.invoice_id == booking.invoice_id,
                    ArtisanAssigned.artisan_id == artisan_id
                ).all()
                artisan_service_ids = [str(service_id[0]) for service_id in artisan_service_ids if service_id[0]]
                
                # Filter service_details to only include services assigned to this artisan
                service_details = [
                    service for service in service_details 
                    if service.get("id") in artisan_service_ids
                ]
            
            artisan_assignments = artisan_assignments_query.all()
            
            for artisan_assigned in artisan_assignments:
                # Initialize fresh objects for each artisan
                assigned_artisan_details = get_empty_assigned_artisan_details()
                assigned_artisan_profile_details = get_empty_artisan_details()
                
                # Build assigned artisan details
                assigned_artisan_details.update({
                    "id": str(artisan_assigned.id),
                    "invoice_item_id": str(artisan_assigned.invoice_item_id),
                    "invoice_id": str(artisan_assigned.invoice_id),
                    "service_id": artisan_assigned.service_id,
                    "artisan_id": artisan_assigned.artisan_id,
                    "start_time": artisan_assigned.start_time.isoformat() if artisan_assigned.start_time else "",
                    "end_time": artisan_assigned.end_time.isoformat() if artisan_assigned.end_time else "",
                    # "start_otp": artisan_assigned.start_otp,
                    # "end_otp": artisan_assigned.end_otp,
                    "service_start": artisan_assigned.service_start.isoformat() if artisan_assigned.service_start else "",
                    "service_end": artisan_assigned.service_end.isoformat() if artisan_assigned.service_end else "",
                    "service_start_artisan_latitude": artisan_assigned.service_start_artisan_latitude,
                    "service_start_artisan_longitude": artisan_assigned.service_start_artisan_longitude,
                    "service_end_artisan_latitude": artisan_assigned.service_end_artisan_latitude,
                    "service_end_artisan_longitude": artisan_assigned.service_end_artisan_longitude,
                    "status": artisan_assigned.status,
                    "created_at": artisan_assigned.created_at.isoformat() if hasattr(artisan_assigned, 'created_at') and artisan_assigned.created_at else "",
                    "updated_at": artisan_assigned.updated_at.isoformat() if hasattr(artisan_assigned, 'updated_at') and artisan_assigned.updated_at else ""
                })
                
                # Get assigned artisan profile details from UserProfiles table
                if artisan_assigned.artisan_id:
                    # Check if artisan_id is a valid UUID before querying
                    if is_valid_uuid(artisan_assigned.artisan_id):
                        assigned_artisan_profile = db.query(UserProfiles).filter(
                            UserProfiles.id == artisan_assigned.artisan_id
                        ).first()
                        if assigned_artisan_profile:
                            # Get completed job count for this artisan
                            job_count = db.query(ArtisanAssigned).filter(
                                ArtisanAssigned.artisan_id == artisan_assigned.artisan_id,
                                ArtisanAssigned.status == ArtisanAssignStatus.COMPLETED
                            ).count()
                            
                            assigned_artisan_profile_details.update({
                                "id": str(assigned_artisan_profile.id),
                                "first_name": assigned_artisan_profile.first_name,
                                "last_name": assigned_artisan_profile.last_name,
                                "email": assigned_artisan_profile.email,
                                "phone_number": assigned_artisan_profile.phone_number,
                                "profile_image_url": assigned_artisan_profile.profile_image_url,
                                "country_code": assigned_artisan_profile.country_code,
                                "ratings": get_avg_rating_by_user(db, artisan_assigned.artisan_id),
                                "job_count": job_count
                            })                                
                # Add this artisan to the list
                assigned_artisans_list.append({
                    "assignment_details": assigned_artisan_details,
                    "artisan_profile": assigned_artisan_profile_details
                })
        # Get address type from Address table using booking.address_id
        address_type = None
        if booking.address_id:
            address = db.query(Address).filter(Address.id == booking.address_id).first()

            if address:  
                address_type = address.name

        # Build complete booking data

        cart_ids = [
                cart_item['cart_id'] 
                for cart_item in (booking.cart_ids or [])
                if isinstance(cart_item, dict) and 'cart_id' in cart_item
            ] if booking.cart_ids else []
        
        # Get invoice items for this invoice
        parent_items = (
            db.query(InvoiceItem, Services)
            .join(Services, cast(Services.id, String) == InvoiceItem.service_id)
            .filter(
                InvoiceItem.invoice_id == booking.invoice_id,
                InvoiceItem.parent_id == None  # Parent items only
            ).order_by(InvoiceItem.created_at.asc()).all())
        for parent_invoice_item, ser in parent_items:
            # Subquery to get all invoice item IDs (parent + children)
            invoice_item_subq = (
                db.query(InvoiceItem.id)
                .filter(or_(
                    InvoiceItem.id == parent_invoice_item.id,
                    InvoiceItem.parent_id == parent_invoice_item.id
                )).subquery()
            )

            # Main query to count InvoiceItems and ArtisanAssigned rows in one go
            combined_counts = (
                db.query(
                    func.count(InvoiceItem.id).label("invoice_item_count"),
                    func.count(ArtisanAssigned.id).label("artisan_assigned_count")
                )
                .outerjoin(ArtisanAssigned, ArtisanAssigned.invoice_item_id == InvoiceItem.id)
                .filter(InvoiceItem.id.in_(select(invoice_item_subq)))
                .one()
            )            
            service_details.append(
                    {
                        "id": parent_invoice_item.service_id,
                        "name": ser.name,
                        "description": ser.description,
                        "price": ser.price,
                        "service_code": ser.service_code,
                        "quantity": parent_invoice_item.quantity,
                        "item_description": parent_invoice_item.description,
                        "item_images": ser.banner,
                        "main_invoice_item_id": str(parent_invoice_item.id),
                        "is_agent_created": parent_invoice_item.is_agent_created,
                        "total_artisan_count": combined_counts.invoice_item_count,
                        "artisan_assigned_count": combined_counts.artisan_assigned_count
                    }
                )
        
        booking_data = {
            "id": str(booking.id),
            "booking_order_id": booking.booking_order_id,
            "user_id": str(booking.user_id),
            "sp_id": str(booking.sp_id) if booking.sp_id else None,
            "cart_ids": cart_ids,
            "invoice_id": str(booking.invoice_id) if booking.invoice_id else None,
            "booking_date": str(booking.booking_date),
            "preferred_arrival_time": str(booking.preferred_arrival_time) if booking.preferred_arrival_time else None,
            "user_latitude": booking.user_latitude,
            "user_longitude": booking.user_longitude,
            "user_address": booking.user_address,
            "address_type": address_type,
            "is_rescheduled": booking.is_rescheduled,
            "status": booking.status,
            "request_type": booking.request_type,
            "requested_time": booking.requested_time.isoformat() if booking.requested_time else None,
            "updated_time": booking.updated_time.isoformat() if booking.updated_time else None,
            "created_at": booking.created_at.isoformat() if hasattr(booking, 'created_at') and booking.created_at else None,
            "updated_at": booking.updated_at.isoformat() if hasattr(booking, 'updated_at') and booking.updated_at else None,
            "description": cart_description,
            "locality": booking.locality,
            "assigned_agent_id": str(booking.assigned_agent_id) if booking.assigned_agent_id else None,
            "service_details": service_details if service_details else [],
            "user_details": user_details,
            "invoice_details": invoice_details,
            "invoice_items": invoice_items,
            "assigned_artisans": assigned_artisans_list,
            # "assigned_artisan_count": len(assigned_artisans_list),
            # "unassigned_artisan_count": len(invoice_items) - len(assigned_artisans_list)
        }
        
        return StandardResponse(
            status_code=200,
            message="Booking details retrieved successfully",
            data=booking_data
        )
        
    except Exception as e:
        logging.error(f"Error fetching booking details: {e}")
        return ErrorResponse(status_code=500, message="Error fetching booking details", error=str(e))
    


@router.post("/assign-artisan")
async def assign_artisan(
    req: Request,
    request: AssignArtisanRequest,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    """
    Artisan assign/reassign api by agent
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        authorization = req.headers.get("Authorization")
        if not authorization:
            return ErrorResponse(status_code=401, message="Authorization header is required")

        AA = aliased(ArtisanAssigned)

           
        booking_id = request.booking_id
        # service_id = request.service_id
        invoice_item_id = request.invoice_item_id
        artisan_ids = request.artisan_ids
        assign_type = request.assign_type

        booking = db.query(Booking).filter(Booking.id == booking_id).first()
        if not booking:
            return ErrorResponse(status_code=404, message="Booking not found")
        print(booking.invoice_id, "booking.invoice_id")

        #Checking if there is any unpaid invoice items
        # all_invoice_items = (db.query(InvoiceItem).filter(or_(InvoiceItem.id == invoice_item_id, InvoiceItem.parent_id == invoice_item_id)))
        # unpaid_invoice_items = all_invoice_items.filter(InvoiceItem.status == InvoiceItemStatus.PENDING).all()
        # print(unpaid_invoice_items, "unpaid_invoice_items")
        # if unpaid_invoice_items:
        #     return ErrorResponse(status_code=400, message="There are unpaid invoice items for this booking")
        
        # Getting unassigned invoice items
        invoice_items = (
            db.query(InvoiceItem)
            .outerjoin(AA, AA.invoice_item_id == InvoiceItem.id)
            .filter(
                or_(
                    InvoiceItem.id == invoice_item_id,
                    InvoiceItem.parent_id == invoice_item_id
                ),
                AA.id == None  # ArtisanAssigned entry not present
            )
            .all()
        )
        if not invoice_items:
            return ErrorResponse(status_code=404, message="All Invoice items have been artisan assigned")
        
        print(invoice_items, len(invoice_items), "invoice_item")

        # print(x)

        # if len(artisan_ids) < len(invoice_items):
        #     return ErrorResponse(status_code=400, message="Not enough artisans assigned for the number of pending services")
        

        if len(artisan_ids) > len(invoice_items):
            return ErrorResponse(status_code=400, message="More artisans assigned than the number of services")

        
        for artisan_id, invoice_item in zip(artisan_ids, invoice_items):
            payload = {
                "invoice_id": str(invoice_item.invoice_id),
                "invoice_item_id": str(invoice_item.id),
                "artisan_id": str(artisan_id),
                "service_id": str(invoice_item.service_id),
                "status": "ASSIGNED"
            }
            create_record(db, ArtisanAssigned, payload)

        # Check if all services are assigned
        invoice_item_ids = db.query(InvoiceItem.id).filter(InvoiceItem.invoice_id == invoice_item.invoice_id).all()
        assigned_artisan_invoice_item_ids = db.query(ArtisanAssigned.invoice_item_id).filter(ArtisanAssigned.invoice_id == invoice_item.invoice_id).distinct().all()
        
        # Flatten results into sets of integers
        all_ids = {row[0] for row in invoice_item_ids}
        assigned_ids = {row[0] for row in assigned_artisan_invoice_item_ids}

        # Unassigned IDs
        unassigned_ids = all_ids - assigned_ids

        if not unassigned_ids:
            booking.status = 'ARTISAN_ASSIGNED'
            db.commit()

        # send notification to artisan:

        if artisan_ids:
            for artisan_id in artisan_ids:
                artisan = db.query(UserProfiles).filter(UserProfiles.id == artisan_id).first()
                if artisan:
                    send_push_notification(
                        auth_token=authorization,
                        title="Service Assigned",
                        message="service assigned to you",
                        sender_id=str(artisan_id),
                        type="artisan",
                        data={
                            "booking_id": str(booking.id),
                            "request_type": "booking"
                        }
                    )
        
        log_activity(
            db=db,
            title="Artisan Assigned",
            description="Artisan has been assigned to the booking",
            reference_id=booking_id,
            customer_id=booking.user_id,
            activity_type=ActivityType.BOOKING_CREATED,
        )

        # send notification to user:
        user = db.query(UserProfiles).filter(UserProfiles.id == booking.user_id).first()
        if user:
            send_push_notification(
                auth_token=authorization,
                title="Artisan Assigned",
                message="artisan assigned for your service",
                sender_id=str(booking.user_id),
                type="user",
                data={
                    "booking_id": str(booking.id),
                    "request_type": "booking"
                }

            )
                
        return StandardResponse(status_code=200, message="Artisan assigned")    
    except Exception as e:
        print(e)
        return ErrorResponse(status_code=500, message="Error assigning artisan", error=str(e))
    

@router.post("/artisan-soft-lock")
async def artisan_soft_lock(
    request: ArtisanSoftLockRequest,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user
        is_lock = request.is_lock
        artisan_id = request.artisan_id
        key = f"artisan_lock:{artisan_id}"

        if is_lock:
            result = redis_client.set(key, artisan_id, nx=True, ex=1800)
            if result:
                return StandardResponse(status_code=200, message="Artisan locked")
            else:
                return ErrorResponse(status_code=400, message="Artisan already locked")
        else:
            redis_client.delete(key)
            return StandardResponse(status_code=200, message="Artisan unlocked")
    except Exception as e:
        print(e)
        return ErrorResponse(status_code=500, message="Error creating booking", error=str(e))
    


@router.put("/service-request-update/{id}")
async def service_request_update(
    id: str,
    request: UpdateServiceRequest,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    """
    Api for update service request status and agent self assign
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        booking = db.query(Booking).filter(Booking.id ==id).first()
        if not booking:
            return ErrorResponse(status_code=404, message="Booking not found")
        
        if request.is_exit:
            booking.assigned_agent_id = None
            booking.status = 'REQUESTED'

        if request.assigned_agent_id:
            booking.assigned_agent_id = request.assigned_agent_id
            booking.status = 'AGENT_ASSIGNED'

        if request.status:
            booking.status = request.status
            
        db.commit()
        db.refresh(booking)

        return StandardResponse(status_code=200, message="Service request updated")
    except Exception as e:
        print(e)
        return ErrorResponse(status_code=500, message="Error updating service request", error=str(e))
    
@router.post("/add-service-to-booking", response_model=StandardResponse)
async def add_service_to_booking(
    request: AddServiceCreate,
    req: Request,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        description = request.description
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        authorization = req.headers.get("Authorization")
        if not authorization:
            return ErrorResponse(status_code=401, message="Authorization header is required")
        
        artisan_ids = request.artisan_ids
        # 1. Fetch Booking
        booking = db.query(Booking).filter(Booking.id == request.booking_id).first()
        if not booking:
            return ErrorResponse(status_code=404, message="Booking not found")

        # 2. Fetch Invoice
        invoice = db.query(Invoice).filter(Invoice.booking_id == request.booking_id).first()
        if not invoice:
            return ErrorResponse(status_code=404, message="Invoice not found for booking")

        # 3. Fetch Service
        service = db.query(Services).filter(Services.id == request.service_id).first()
        if not service:
            return ErrorResponse(status_code=404, message="Service not found")

        # Update booking's service_list to include the new service
        try:
            # Create the new service entry
            new_service_entry = {
                "service_id": str(request.service_id),
                "qty": 1,
                "description": service.description if service.description else None,
                "images": []
            }
            
            # Handle existing service_list
            if booking.service_list:
                # Convert to list if it's a string
                if isinstance(booking.service_list, str):
                    try:
                        service_list = json.loads(booking.service_list)
                    except:
                        service_list = []
                else:
                    service_list = booking.service_list
                
                # Ensure service_list is a list
                if not isinstance(service_list, list):
                    service_list = []
                
                # Add the new service
                service_list.append(new_service_entry)
                booking.service_list = service_list
            else:
                # Create new service_list with the service
                service_list = [new_service_entry]
                booking.service_list = service_list
            
            # Force the update by marking the field as modified
            from sqlalchemy.orm.attributes import flag_modified
            flag_modified(booking, "service_list")
            
            # Commit the service_list update
            db.commit()
            db.refresh(booking)
            
        except Exception as e:
            db.rollback()
            return ErrorResponse(
                status_code=500,
                message="Error updating booking service list",
                error=str(e)
            )
        
        # 4. Per-item Fee Calculations
        base_fee = service.price or 0
        booking_fee_percentage = service.booking_fee_percentage or 0
        booking_fee = get_booking_fee(base_fee, booking_fee_percentage) or 0
        platform_fee = get_surcharges(base_fee) or 0
        tax = get_tax(base_fee) or 0
        total_amount = base_fee + booking_fee + platform_fee + tax

        artisan_count = request.artisan_count or 1
        created_items = []

        # 5. Create Invoice Items (Before total calculation)
        new_items_total = []
        for _ in range(artisan_count):
            val = {
                "invoice_id": invoice.id,
                "quantity": 1,
                "service_id": request.service_id,
                "price": base_fee,
                "description": description,
                "booking_fee_percentage": booking_fee_percentage,
                "booking_fee": booking_fee,
                "platform_fee_percentage": 2.5,
                "platform_fee_amount": platform_fee,
                "tax_percentage": 17.5,
                "tax_amount": tax,
                "total_amount": total_amount,
                "is_agent_created": True,
                "is_active": True
            }
            if _ != 0:
                val["parent_id"] = created_items[0]
            invoice_item = InvoiceItem(**val)
            db.add(invoice_item)
            db.flush()
            created_items.append(str(invoice_item.id))
            new_items_total.append(total_amount)

        db.flush()  # Ensure new items are available in the next query

        # Assigning new services to existing artisans
        if artisan_ids:
            for artisan_id, invoice_item_id in zip(artisan_ids, created_items):
                payload = {
                    "invoice_id": str(booking.invoice_id),
                    "invoice_item_id": str(invoice_item_id),
                    "artisan_id": str(artisan_id),
                    "service_id": str(request.service_id),
                    "status": "ASSIGNED"
                }
                create_record(db, ArtisanAssigned, payload)


        # 6. Fetch all active invoice items (existing + newly added)
        all_items = db.query(InvoiceItem).filter(
            InvoiceItem.invoice_id == invoice.id,
            InvoiceItem.is_active == True
        ).all()

        # 7. Recalculate Totals
        total_base_fee = sum(item.price or 0 for item in all_items)
        total_booking_fee = sum(item.booking_fee or 0 for item in all_items)
        total_platform_fee = sum(item.platform_fee_amount or 0 for item in all_items)
        total_tax = sum(item.tax_amount or 0 for item in all_items)
        total_discount = invoice.discount_amount or 0

        # Update invoice totals
        invoice.base_fee = total_base_fee
        invoice.booking_fee_percentage = booking_fee_percentage
        invoice.booking_fee = total_booking_fee
        invoice.platform_fee_amount = total_platform_fee
        invoice.tax_amount = total_tax

        total_invoice_amount = (total_base_fee + total_booking_fee + total_platform_fee + total_tax) - total_discount
        invoice.total_amount = total_invoice_amount
        invoice.pending_amount = invoice.pending_amount + sum(new_items_total)
        invoice.payment_status = "PARTIAL_PAID" if booking.payment_type != "CASH" else "QUOTATION"
        db.commit()
        user_id = booking.user_id

        log_activity(
            db=db,
            title="Additional Service Added",
            description="New service added to booking",
            reference_id=request.booking_id,
            customer_id=booking.user_id,
            activity_type=ActivityType.ADDITIONAL_SERVICE_ADDED,
        )
        
        if user_id:
            send_push_notification(
                auth_token=authorization,
                title="Service Added",
                message="service added to your booking",
                sender_id=str(user_id),
                type="user",
                data={
                    "booking_id": str(booking.id),
                    "service_id": str(request.service_id),
                    "service_name": str(service.name),
                    "service_price": str(service.price),
                    "platform_fee_percentage":str(invoice.platform_fee_percentage),
                    "platform_fee_amount" :str(total_platform_fee),
                    "tax_amount" : str(total_tax),
                    "tax_percentage": str(invoice.tax_percentage),
                    "currency": "GHS",
                    "pending_amount": str(total_invoice_amount),
                    "base_fee": str(total_base_fee),
                    "booking_fee": str(total_booking_fee),
                    "invoice_id": str(invoice.id),
                    "invoice_status": str(invoice.payment_status),
                    "request_type": "quotation"

                }
            )

        return StandardResponse(
            status_code=201,
            message=f"{artisan_count} service(s) added to booking and invoice updated",
            data={
                "invoice_item_ids": created_items,
                "base_fee_per_item": base_fee,
                "booking_fee_per_item": booking_fee,
                "platform_fee_per_item": platform_fee,
                "tax_per_item": tax,
                "total_amount_per_item": total_amount,
                "invoice_total": total_invoice_amount,
                "updated_service_list": booking.service_list
            }
        )

    except Exception as e:
        db.rollback()
        return ErrorResponse(
            status_code=500,
            message="Error adding service(s) to booking and updating invoice",
            error=str(e)
        )
