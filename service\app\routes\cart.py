import uuid
from fastapi import APIRouter, Depends, File, UploadFile, Header
from app.utils.s3_upload import upload_file_direct, s3_delete_file, S3_IMAGES_FOLDER
from app.schemas import cart as schemas
from app.database import get_db
from sqlalchemy.orm import Session
from app.utils.auth import permission_checker
from typing import Optional, List, Union
from app.utils.crud import get_record_by_id, create_record, delete_record, update_record
from sqlalchemy.future import select
from sqlalchemy import func, and_
from typing import Annotated
from app.schemas.helper import StandardResponse, ErrorResponse
from app.models import Cart, Services , Category
import json
from fastapi.responses import JSONResponse
router = APIRouter(tags=["Cart"])

@router.post("/cart-create")
async def create_cart_item(
    request: schemas.CreateCart = Depends(),
    attachments: Optional[Union[UploadFile, List[UploadFile]]] = File(None),
    db: Session = Depends(get_db),
    user = Depends(permission_checker),
):
    try:
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user
            
        # Validate service exists
        service = get_record_by_id(db, Services, uuid.UUID(request.service_id))
        print(service)
        if isinstance(service, str):
            return ErrorResponse(
                status_code=404,
                message="Service not found"
            )

        request_dict = request.to_dict()

        # Handle file attachments upload to S3
        attachment_filenames = []
        if attachments:
            # Convert single file to list for uniform processing
            files_to_process = attachments if isinstance(attachments, list) else [attachments]
            
            for attachment in files_to_process:
                if attachment.filename:  # Check if file is not empty
                    # Calling S3 upload function
                    upload_result = upload_file_direct(attachment, path=S3_IMAGES_FOLDER)
                    attachment_filenames.append(upload_result['filename'])
            
            if attachment_filenames:
                request_dict["attachments"] = json.dumps(attachment_filenames)
        
        new_cart_item = create_record(db, Cart, request_dict)
        if isinstance(new_cart_item, str):
            return ErrorResponse(status_code=400, message=new_cart_item)
        
        return StandardResponse(
            status_code=200,
            message="Cart item created successfully",
            data={
                "cart_item_id": str(new_cart_item.id),
                "user_id": str(new_cart_item.user_id),
                "service_id": str(new_cart_item.service_id),
                "qty": new_cart_item.qty,
                "description": new_cart_item.description,
                "attachments": attachment_filenames,
                "attachments_count": len(attachment_filenames),
                "status": new_cart_item.status,
                "created_at": new_cart_item.created_at.isoformat() if new_cart_item.created_at else None
            }
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="cart item creation failed", error=str(e)
        )


@router.get("/cart-read/{id}")
async def read_cart_item(
    id: str,
    db: Session = Depends(get_db),
    user = Depends(permission_checker),
):
    """
    Get a specific cart item by ID
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
            
        cart_item = get_record_by_id(db, Cart, uuid.UUID(id))
        if isinstance(cart_item, str):
            return ErrorResponse(status_code=404, message="Cart item not found")
        
        # Parse attachments JSON if exists
        attachments = []
        if cart_item.attachments:
            try:
                # Check if attachments is already a list/dict or a JSON string
                if isinstance(cart_item.attachments, str):
                    attachments = json.loads(cart_item.attachments)
                elif isinstance(cart_item.attachments, (list, dict)):
                    attachments = cart_item.attachments
                else:
                    attachments = []
            except (json.JSONDecodeError, TypeError):
                attachments = []
        
        cart_data = {
            "cart_item_id": str(cart_item.id),
            "user_id": str(cart_item.user_id),
            "service_id": str(cart_item.service_id),
            "qty": cart_item.qty,
            "description": cart_item.description,
            "attachments": attachments,
            "attachments_count": len(attachments) if isinstance(attachments, list) else 0,
            "status": cart_item.status,
            "is_active": cart_item.is_active,
            "created_at": cart_item.created_at.isoformat() if cart_item.created_at else None,
            "updated_at": cart_item.updated_at.isoformat() if cart_item.updated_at else None
        }
        
        return StandardResponse(
            status_code=200,
            message="Cart item read successfully",
            data=cart_data
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="cart item reading failed", error=str(e)
        )


@router.get("/cart-list")
async def list_cart_items(
    user_id: Optional[str] = None,
    status: Optional[str] = None,
    service_id: Optional[str] = None,
    skip: int = 1,
    limit: int = 10,
    db: Session = Depends(get_db),
    user = Depends(permission_checker),
):
    """
    Get list of cart items with optional filtering by user ID, status, and service ID
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user

        # Build query with filters
        query = select(Cart).filter(Cart.is_active == True)
        count_query = select(func.count()).select_from(Cart).filter(Cart.is_active == True)

        if user_id:
            query = query.filter(Cart.user_id == uuid.UUID(user_id))
            count_query = count_query.filter(Cart.user_id == uuid.UUID(user_id))

        if status:
            query = query.filter(Cart.status == status)
            count_query = count_query.filter(Cart.status == status)

        if service_id:
            query = query.filter(Cart.service_id == uuid.UUID(service_id))
            count_query = count_query.filter(Cart.service_id == uuid.UUID(service_id))

        # Apply pagination
        adjusted_skip = (skip - 1) * limit
        query = query.offset(adjusted_skip).limit(limit)
        
        # Execute queries
        result = db.execute(query)
        cart_items = result.scalars().all()

        # Parse attachments for each cart item
        cart_items_data = []
        for item in cart_items:
            attachments = []
            if item.attachments:
                try:
                    # Check if attachments is already a list/dict or a JSON string
                    if isinstance(item.attachments, str):
                        attachments = json.loads(item.attachments)
                    elif isinstance(item.attachments, (list, dict)):
                        attachments = item.attachments
                    else:
                        attachments = []
                except (json.JSONDecodeError, TypeError):
                    attachments = []
            
            cart_item_data = {
                "cart_item_id": str(item.id),
                "user_id": str(item.user_id),
                "service_id": str(item.service_id),
                "qty": item.qty,
                "description": item.description,
                "attachments": attachments,
                "attachments_count": len(attachments) if isinstance(attachments, list) else 0,
                "status": item.status,
                "is_active": item.is_active,
                "created_at": item.created_at.isoformat() if item.created_at else None,
                "updated_at": item.updated_at.isoformat() if item.updated_at else None
            }
            cart_items_data.append(cart_item_data)

        total_result = db.execute(count_query)
        total_count = total_result.scalar()

        return StandardResponse(
            status_code=200,
            message="Cart items retrieved successfully",
            data={
                "cart_items": cart_items_data,
                "page": skip,
                "limit": limit,
                "total": total_count
            }
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="cart items listing failed", error=str(e)
        )

@router.get("/cart-user/{user_id}")
async def get_cart_by_user(
    user_id: str,
    db: Session = Depends(get_db),
    user = Depends(permission_checker),
):
    """
    Get all cart items for a specific user with service and category details
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user

        # Query cart items with service and category details using joins
        query = db.query(Cart, Services, Category).outerjoin(
            Services, Cart.service_id == Services.id
        ).outerjoin(
            Category, Services.parent_id == Category.id
        ).filter(
            and_(Cart.user_id == uuid.UUID(user_id), Cart.status == "PENDING")
        )
        
        cart_results = query.all()

        # Parse cart items with service and category details
        cart_items_data = []
        for cart_item, service, category in cart_results:
            attachments = []
            if cart_item.attachments:
                try:
                    # Check if attachments is already a list/dict or a JSON string
                    if isinstance(cart_item.attachments, str):
                        attachments = json.loads(cart_item.attachments)
                    elif isinstance(cart_item.attachments, (list, dict)):
                        attachments = cart_item.attachments
                    else:
                        attachments = []
                except (json.JSONDecodeError, TypeError):
                    attachments = []
            
            cart_item_data = {
                "cart_item_id": str(cart_item.id),
                "user_id": str(cart_item.user_id),
                "service_id": str(cart_item.service_id),
                "qty": cart_item.qty,
                "description": cart_item.description,
                "attachments": attachments,
                "attachments_count": len(attachments) if isinstance(attachments, list) else 0,
                "status": cart_item.status,
                "is_active": cart_item.is_active,
                "created_at": cart_item.created_at.isoformat() if cart_item.created_at else None,
                "updated_at": cart_item.updated_at.isoformat() if cart_item.updated_at else None,
                # Service details
                "service_name": service.name if service else None,
                "service_image": service.banner if service else None,
                "service_price": service.price if service else None,
                "service_description": service.description if service else None,
                # Category details
                "category_name": category.name if category else None,
                "category_id": str(category.id) if category else None
            }
            cart_items_data.append(cart_item_data)

        return StandardResponse(
            status_code=200,
            message="User cart items retrieved successfully",
            data=cart_items_data
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="cart getting failed", error=str(e)
        )

@router.put("/cart-update/{id}")
async def update_cart_item(
    id: str,
    data: schemas.UpdateCart = Depends(),
    attachments: Optional[Union[UploadFile, List[UploadFile]]] = File(None),
    db: Session = Depends(get_db),
    user = Depends(permission_checker),
):
    """
    Update a specific cart item with optional new attachments
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user

        get_cart_item = get_record_by_id(db, Cart, uuid.UUID(id))
        if isinstance(get_cart_item, str):
            return ErrorResponse(status_code=404, message="Cart item not found")

        new_attachment_filenames = []
        # Handle new attachment uploads
        if attachments:
            # Parse existing attachments
            existing_attachments = []
            if get_cart_item.attachments:
                try:
                    # Check if attachments is already a list/dict or a JSON string
                    if isinstance(get_cart_item.attachments, str):
                        existing_attachments = json.loads(get_cart_item.attachments)
                    elif isinstance(get_cart_item.attachments, (list, dict)):
                        existing_attachments = get_cart_item.attachments if isinstance(get_cart_item.attachments, list) else []
                    else:
                        existing_attachments = []
                except (json.JSONDecodeError, TypeError):
                    existing_attachments = []

            # Convert single file to list for uniform processing
            files_to_process = attachments if isinstance(attachments, list) else [attachments]

            # Upload new attachments
            for attachment in files_to_process:
                if attachment.filename:
                    upload_result = upload_file_direct(attachment, path=S3_IMAGES_FOLDER)
                    new_attachment_filenames.append(upload_result['filename'])

            # Combine existing and new attachments
            all_attachments = existing_attachments + new_attachment_filenames
            setattr(get_cart_item, "attachments", json.dumps(all_attachments))

        res = update_record(db, data, get_cart_item)
        if isinstance(res, str):
            return ErrorResponse(status_code=400, message=res)
        
        # Parse final attachments for response
        final_attachments = []
        if get_cart_item.attachments:
            try:
                # Check if attachments is already a list/dict or a JSON string
                if isinstance(get_cart_item.attachments, str):
                    final_attachments = json.loads(get_cart_item.attachments)
                elif isinstance(get_cart_item.attachments, (list, dict)):
                    final_attachments = get_cart_item.attachments if isinstance(get_cart_item.attachments, list) else []
                else:
                    final_attachments = []
            except (json.JSONDecodeError, TypeError):
                final_attachments = []
        
        return StandardResponse(
            status_code=200,
            message="Cart item updated successfully",
            data={
                "cart_item_id": str(get_cart_item.id),
                "user_id": str(get_cart_item.user_id),
                "service_id": str(get_cart_item.service_id),
                "qty": get_cart_item.qty,
                "description": get_cart_item.description,
                "attachments": final_attachments,
                "attachments_count": len(final_attachments) if isinstance(final_attachments, list) else 0,
                "new_attachments_added": len(new_attachment_filenames),
                "status": get_cart_item.status,
                "is_active": get_cart_item.is_active,
                "created_at": get_cart_item.created_at.isoformat() if get_cart_item.created_at else None,
                "updated_at": get_cart_item.updated_at.isoformat() if get_cart_item.updated_at else None
            }
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="cart item updating failed", error=str(e)
        )

@router.delete("/cart-delete/{id}")
async def delete_cart_item(
    id: str,
    db: Session = Depends(get_db),
    user = Depends(permission_checker),
):
    """
    Hard delete a specific cart item (permanently removes from database)
    Also removes associated S3 files
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user

        get_cart_item = get_record_by_id(db, Cart, uuid.UUID(id))
        if isinstance(get_cart_item, str):
            return ErrorResponse(status_code=404, message="Cart item not found")

        if get_cart_item is None:
            return ErrorResponse(
                status_code=404,
                message="Cart item not found"
            )
        
        # Parse attachments before deletion
        deleted_attachments = []
        if get_cart_item.attachments:
            try:
                # Check if attachments is already a list/dict or a JSON string
                if isinstance(get_cart_item.attachments, str):
                    deleted_attachments = json.loads(get_cart_item.attachments)
                elif isinstance(get_cart_item.attachments, (list, dict)):
                    deleted_attachments = get_cart_item.attachments if isinstance(get_cart_item.attachments, list) else []
                else:
                    deleted_attachments = []
                    
                # Delete associated S3 files
                for attachment_filename in deleted_attachments:
                    s3_delete_file(attachment_filename)
            except (json.JSONDecodeError, TypeError):
                pass  # Continue even if S3 deletion fails
        
        delete_record(db, get_cart_item)
        return StandardResponse(
            status_code=200,
            message="Cart item deleted successfully"
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="cart item deleting failed", error=str(e)
        )

