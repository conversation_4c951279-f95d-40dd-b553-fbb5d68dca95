from fastapi import UploadFile, HTTPException
from typing import Optional, <PERSON><PERSON>
import io

# Constants for image validation
MAX_IMAGE_SIZE = 5 * 1024 * 1024  # 5MB in bytes
ALLOWED_IMAGE_TYPES = {
    "image/jpeg",
    "image/jpg",
    "image/png",
    "application/pdf"
}

def validate_image_sync(
    file: UploadFile,
    max_size: int = MAX_IMAGE_SIZE,
    allowed_types: set = ALLOWED_IMAGE_TYPES
) -> Tuple[bool, Optional[str]]:
    """
    Synchronous version of validate_image for use with sync database operations.
    
    Args:
        file (UploadFile): The image file to validate
        max_size (int): Maximum allowed file size in bytes (default: 5MB)
        allowed_types (set): Set of allowed MIME types
        
    Returns:
        Tuple[bool, Optional[str]]: (is_valid, error_message)
        - is_valid: True if validation passes, False otherwise
        - error_message: Error message if validation fails, None if validation passes
    """
    try:
        # Validate file type first (this doesn't require reading the file)
        if file.content_type not in allowed_types:
            allowed_extensions = []
            if "image/jpeg" in allowed_types or "image/jpg" in allowed_types:
                allowed_extensions.append("JPG/JPEG")
            if "image/png" in allowed_types:
                allowed_extensions.append("PNG")
            if "application/pdf" in allowed_types:
                allowed_extensions.append("PDF")
            
            return False, f"Upload failed: Only {', '.join(allowed_extensions)} files are allowed"
        
        # Validate file size
        file_size = 0
        for chunk in file.file:
            file_size += len(chunk)
            if file_size > max_size:
                return False, "Upload failed: File size must be less than 5MB"
        
        # Reset file pointer after reading - use the underlying file object
        file.file.seek(0)
        
        return True, None
        
    except Exception as e:
        return False, f"Error validating file: {str(e)}" 

def validate_image(file: UploadFile, max_size_mb: int = 5):
    """Validate file is a supported image and within size limit."""
    if not file.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="Invalid file type. Only images are allowed.")

    ext = file.filename.split(".")[-1].lower()
    if ext not in {"jpg", "jpeg", "png", "webp"}:
        raise HTTPException(status_code=400, detail="Unsupported image format.")

    file.file.seek(0, 2)  # Go to end of file
    size_mb = file.file.tell() / (1024 * 1024)
    file.file.seek(0)

    if size_mb > max_size_mb:
        raise HTTPException(status_code=400, detail=f"File too large. Max allowed size is {max_size_mb}MB.")
