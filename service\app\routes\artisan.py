import uuid
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form,Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy.future import select
from app.database import get_db
from typing import Optional
from app.models import ArtisanDetail, KycDetails, UserProfiles, Roles,ServiceProviderServiceMapping,Services,ArtisanAssigned, Address, InvoiceItem,Invoice,Account
from app.models_enum import KYCTypes,ValidationStatus,Gender,UserStatusType,ArtisanAssignStatus,InvoiceStatusEnum,AccountTypeType
from app.schemas.artisan import ArtisanListRequest, ServiceProviderStatusUpdate
from app.utils.s3_upload import validate_image, upload_file_sync,upload_file_direct,S3_IMAGES_FOLDER
from app.schemas.helper import StandardResponse, ErrorResponse
from app.config import  get_settings
from app.utils.crud import create_record
import json
from sqlalchemy import delete,func
from sqlalchemy import select, or_, cast, String
from app.utils.auth import permission_checker
from fastapi.responses import JSONResponse
from app.utils.helper import redis_update, update_service_provider_status
from app.utils.redis_config import redis_client
from app.utils.auth import cognito_sign_up , update_cognito_attributes , admin_get_user

from datetime import datetime

router = APIRouter(prefix="/artisan", tags=["artisan-details"])
# similarly like this code i need not kyc hear this is working code i need to fetch the artisan using user id and kyc id if is there in kyc table i hve to return kyc details 
@router.put("/update-artisan-detail", response_model=StandardResponse)
def update_artisan_detail(
    first_name: Optional[str] = Form(None),
    last_name: Optional[str] = Form(None),
    email: Optional[str] = Form(None),
    country_code: Optional[str] = Form(None),
    profile: Optional[UploadFile] = File(None),

    skill: Optional[str] = Form(None),
    skill_level: Optional[str] = Form(None),
    experience: Optional[float] = Form(None),
    about_us: Optional[str] = Form(None),
    reg_code: Optional[str] = Form(None),
    live_status: Optional[str] = Form(None),
    work_from_hrs: Optional[str] = Form(None),
    work_to_hrs: Optional[str] = Form(None),
    break_from_hrs: Optional[str] = Form(None),
    break_to_hrs: Optional[str] = Form(None),
    weekdays: Optional[str] = Form(None),
    is_confirmed: Optional[str] = Form(None),
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):
            return user
        
        user_id = user.get("user_id") if isinstance(user, dict) else None

        user_profile = db.query(UserProfiles).filter(UserProfiles.id == user_id).first()
        artisan = db.query(ArtisanDetail).filter(ArtisanDetail.user_id == user_id).first()

        if not user_profile or not artisan:
            return ErrorResponse(status_code=404, message="User or artisan not found")

        # --- Update User Profile ---
        if profile:
            validate_image(profile)
            profile_url = upload_file_sync(profile, folder="user/profile")
            user_profile.profile_image_url = profile_url

        if first_name:
            user_profile.first_name = first_name
        if last_name:
            user_profile.last_name = last_name
        if email:
            user_profile.email = email
        if country_code:
            user_profile.country_code = country_code

        # Convert to bool
        if is_confirmed is not None:
            is_confirmed = is_confirmed.lower() == "true"

        # Parse Time fields safely
        def parse_time(t):
            return datetime.strptime(t.strip(), "%H:%M").time() if t else None

        artisan.skill = skill or artisan.skill
        artisan.skill_level = skill_level or artisan.skill_level
        artisan.experience = experience if experience is not None else artisan.experience
        artisan.about_us = about_us or artisan.about_us
        artisan.reg_code = reg_code or artisan.reg_code
        artisan.live_status = live_status or artisan.live_status
        artisan.work_from_hrs = parse_time(work_from_hrs) or artisan.work_from_hrs
        artisan.work_to_hrs = parse_time(work_to_hrs) or artisan.work_to_hrs
        artisan.break_from_hrs = parse_time(break_from_hrs) or artisan.break_from_hrs
        artisan.break_to_hrs = parse_time(break_to_hrs) or artisan.break_to_hrs
        artisan.weekdays = [int(day.strip()) for day in weekdays.split(",")] if weekdays else artisan.weekdays
        artisan.is_confirmed = is_confirmed if is_confirmed is not None else artisan.is_confirmed

        db.commit()
        db.refresh(user_profile)
        db.refresh(artisan)

        return StandardResponse(
            status_code=200,
            message="Artisan and user profile updated successfully"
        )

    except Exception as e:
        db.rollback()
        return ErrorResponse(status_code=500, message=f"Failed to update artisan details: {str(e)}")

@router.get("/get-artisan-details", response_model=StandardResponse)
def get_artisan_details(
    db=Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        # Fetch User Profile
        user_id = user.get('user_id')
        user = db.query(UserProfiles).filter(UserProfiles.id == user_id).first()
        if not user:
            return ErrorResponse(status_code=404, message="User not found")
        role = db.query(Roles).filter(Roles.id == user.role_id).first()
        if not role or role.role_name != "artisan":
            return ErrorResponse(status_code=404, message="This user is not an artisan", error="NOT_FOUND")

        com_count = (
            db.query(func.count())
            .select_from(ArtisanAssigned)
            .filter(
                ArtisanAssigned.artisan_id == user_id,
                ArtisanAssigned.status == "COMPLETED"
            ).scalar()
        )

        # Service Info
        services_list = (
            db.query(Services)
            .join(ServiceProviderServiceMapping, ServiceProviderServiceMapping.services_id == Services.id)
            .filter(ServiceProviderServiceMapping.service_provider_id == user_id)
            .all()
        )

        service_info = None
        if services_list:
            service_info = [{
                "service_id": str(service.id),
                "parent_id": str(service.parent_id),
                "name": service.name,
                "banner": service.banner,
                "duration": service.duration,
                "price": service.price,
                "description": service.description,
                "is_multiple": service.is_multiple,
                "service_code": service.service_code,
                "booking_fee_percentage": service.booking_fee_percentage,
            } for service in services_list]

        user_basic_info = {
            "user_id": str(user.id),
            "first_name": user.first_name,
            "last_name": user.last_name,
            "country_code": user.country_code,
            "phone_number": user.phone_number,
            "email": user.email,
            "status": user.status,
            "profile_image_url": user.profile_image_url,
            "role": role.role_name,
            "no_of_Jobs_completed": com_count,
            "date_of_joining": user.created_at
        }

        artisan = db.query(ArtisanDetail).filter(ArtisanDetail.user_id == user_id).first()
        if not artisan:
            return ErrorResponse(status_code=404, message="Artisan details not found")

        artisan_data = {
            "artisan_id": str(artisan.id),
            "skill": artisan.skill,
            "skill_level": artisan.skill_level,
            "experience": artisan.experience,
            "about_us": artisan.about_us,
            "reg_code": artisan.reg_code,
            "live_status": artisan.live_status,
            "license": artisan.license,
            "work_from_hrs": str(artisan.work_from_hrs) if artisan.work_from_hrs else None,
            "work_to_hrs": str(artisan.work_to_hrs) if artisan.work_to_hrs else None,
            "break_from_hrs": str(artisan.break_from_hrs) if artisan.break_from_hrs else None,
            "break_to_hrs": str(artisan.break_to_hrs) if artisan.break_to_hrs else None,
            "weekdays": artisan.weekdays,
            "is_confirmed": artisan.is_confirmed,
        }

        # KYC Data
        kyc_list = (
            db.query(KycDetails)
            .filter(KycDetails.user_id == user_id)
            .order_by(KycDetails.created_at.desc())
            .all()
        )

        kyc_data = [{
            "kyc_id": str(kyc.id),
            "kyc_type": kyc.KYC_type,
            "document": kyc.document,
            "validation_status": kyc.validation_status,
            "validation_comments": kyc.validation_comments,
            "created_at": kyc.created_at
        } for kyc in kyc_list]

        # Profile completeness check logic
        basic_info_complete = all([
            user.first_name, user.last_name, user.email,
            user.phone_number, user.dob, user.location
        ])

        artisan_info_complete = all([
            artisan.work_from_hrs, artisan.work_to_hrs,
            artisan.weekdays
        ])

        # kyc_approved = any(k.validation_status == "approved" for k in kyc_list)

        service_complete = services_list is not None

        profile_complete = all([
            basic_info_complete,
            artisan_info_complete,
            service_complete
        ])

        return StandardResponse(
            status_code=200,
            message="Artisan, User, KYC details fetched successfully",
            data={
                "user_basic_info": user_basic_info,
                "artisan_details": artisan_data,
                "kyc_details": kyc_data,
                "service_list": service_info,
                "is_profile_complete": profile_complete
            }
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Failed to fetch artisan details",
            error=str(e)
        )


@router.post("/artisan-list", response_model=StandardResponse)
def get_artisan_list(
    request: ArtisanListRequest,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user

        query = (
            db.query(UserProfiles, ArtisanDetail)
            .join(ArtisanDetail, ArtisanDetail.user_id == UserProfiles.id)
            .join(Roles, Roles.id == UserProfiles.role_id)
            .filter(Roles.role_name == "artisan")
        )

        if request.user_id:
            query = query.filter(UserProfiles.id == request.user_id)
        if request.status:
            query = query.filter(UserProfiles.status == request.status)
        if request.skill:
            query = query.filter(ArtisanDetail.skill.ilike(f"%{request.skill}%"))
        if request.live_status:
            query = query.filter(ArtisanDetail.live_status == request.live_status)
        if request.is_confirmed is not None:
            query = query.filter(ArtisanDetail.is_confirmed == request.is_confirmed)
        if request.q:
            search_term = f"%{request.q.lower()}%"
            full_name_concat = func.lower(func.concat(UserProfiles.first_name, ' ', UserProfiles.last_name))

            query = query.filter(
                or_(
                    full_name_concat.ilike(search_term),
                    func.lower(UserProfiles.email).ilike(search_term),
                    func.lower(UserProfiles.phone_number).ilike(search_term),
                    func.lower(ArtisanDetail.reg_code).ilike(search_term),
                )
            )

        total_count = query.count()

        if request.order_by and request.order_by.upper() == "ASC":
            query = query.order_by(UserProfiles.created_at.asc())
        else:
            query = query.order_by(UserProfiles.created_at.desc())

        if not request.user_id:
            offset_value = (request.skip - 1) * request.limit if request.skip > 0 else 0
            query = query.offset(offset_value).limit(request.limit)

        results = query.all()

        data = []
        for user_obj, artisan in results:
            user_id = user_obj.id

            # Jobs Completed Count
            completed_jobs = (
                db.query(func.count())
                .select_from(ArtisanAssigned)
                .filter(
                    cast(ArtisanAssigned.artisan_id, String) == str(user_id),
                    ArtisanAssigned.status == "COMPLETED"
                )
                .scalar()
            )

            # Average Rating
            # avg_rating = (
            #     db.query(func.avg(ArtisanRating.rating))
            #     .filter(cast(ArtisanRating.artisan_id, String) == str(user_id))
            #     .scalar()
            # )
            # avg_rating = round(avg_rating, 2) if avg_rating else 0.0
            avg_rating = 0.0

            # KYC Records
            kyc_records = (
                db.query(KycDetails)
                .filter(KycDetails.user_id == user_id)
                .order_by(KycDetails.created_at.desc())
                .all()
            )
            kyc_data = [{
                "kyc_id": str(kyc.id),
                "kyc_type": kyc.KYC_type,
                "document": kyc.document,
                "validation_status": kyc.validation_status,
                "validation_comments": kyc.validation_comments,
                "created_at": kyc.created_at
            } for kyc in kyc_records]

            # Address Records
            address_records = (
                db.query(Address)
                .filter(Address.user_id == user_id)
                .all()
            )
            addresses = [{
                "address_id": str(addr.id),
                "name": addr.name,
                "details": addr.details,
                "is_primary": addr.is_primary
            } for addr in address_records]
            primary_address = next((addr for addr in addresses if addr["is_primary"]), None)

            # Services List
            services = (
                db.query(Services)
                .join(ServiceProviderServiceMapping, ServiceProviderServiceMapping.services_id == Services.id)
                .filter(ServiceProviderServiceMapping.service_provider_id == user_id)
                .all()
            )
            services_list = [{
                "service_id": str(s.id),
                "parent_id": str(s.parent_id),
                "name": s.name,
                "banner": s.banner,
                "duration": s.duration,
                "price": s.price,
                "description": s.description,
                "is_multiple": s.is_multiple,
                "service_code": s.service_code,
                "booking_fee_percentage": s.booking_fee_percentage,
            } for s in services]

            # Profile Completeness Check
            basic_info_complete = all([
                user_obj.first_name, user_obj.last_name, user_obj.email,
                user_obj.phone_number
            ])
            artisan_info_complete = all([
                artisan.work_from_hrs, artisan.work_to_hrs,
                artisan.weekdays
            ])
            kyc_approved = any(k["validation_status"] == "approved" for k in kyc_data)
            services_exist = bool(services_list)

            profile_complete = all([
                basic_info_complete,
                artisan_info_complete,
                kyc_approved,
                services_exist
            ])

            data.append({
                "user_id": str(user_obj.id),
                "first_name": user_obj.first_name,
                "last_name": user_obj.last_name,
                "email": user_obj.email,
                "phone_number": user_obj.phone_number,
                "profile_image_url": user_obj.profile_image_url,
                "date_of_joining": user_obj.created_at,
                "no_of_jobs_completed": completed_jobs,
                "average_rating": avg_rating,
                "gender": user_obj.gender,
                "location": user_obj.location,
                "status": user_obj.status,
                "dob": user_obj.dob,
                "artisan_details": {
                    "skill": artisan.skill,
                    "skill_level": artisan.skill_level,
                    "live_status": artisan.live_status,
                    "experience": artisan.experience,
                    "reg_code": artisan.reg_code,
                    "is_confirmed": artisan.is_confirmed,
                    "about_us": artisan.about_us,
                    "license": artisan.license,
                    "work_from_hrs": str(artisan.work_from_hrs) if artisan.work_from_hrs else None,
                    "work_to_hrs": str(artisan.work_to_hrs) if artisan.work_to_hrs else None,
                    "break_from_hrs": str(artisan.break_from_hrs) if artisan.break_from_hrs else None,
                    "break_to_hrs": str(artisan.break_to_hrs) if artisan.break_to_hrs else None,
                    "weekdays": artisan.weekdays,
                },
                "kyc_details": kyc_data,
                "primary_address": primary_address,
                "all_addresses": addresses,
                "services_list": services_list,
                "is_profile_complete": profile_complete
            })

        return StandardResponse(
            status_code=200,
            message="Artisan list fetched successfully",
            data={
                "artisan_list": data,
                "pagination": {
                    "total": total_count,
                    "page": request.skip,
                    "limit": request.limit,
                    "pages": (total_count + request.limit - 1) // request.limit if not request.user_id else 1,
                }
            }
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Failed to fetch artisan list",
            error=str(e)
        )

    
@router.put("/onboarding-artisan", response_model=StandardResponse)
def artisan_onboarding(
    # user_id: str = Form(...),

    # User Profile fields
    first_name: Optional[str] = Form(None),
    last_name: Optional[str] = Form(None),
    email: Optional[str] = Form(None),
    mobile: Optional[str] = Form(None),
    gender: Optional[str] = Form(None),
    dob: Optional[str] = Form(None),
    location: Optional[str] = Form(None),

    # KYC uploads
    license: Optional[UploadFile] = File(None),
    certification: Optional[UploadFile] = File(None),
    govtid: Optional[UploadFile] = File(None),
    policecertificate: Optional[UploadFile] = File(None),
    gaurantordocument: Optional[UploadFile] = File(None),
    profile_pic: Optional[UploadFile] = File(None),


    # Artisan timing fields
    work_from_hrs: Optional[str] = Form(None),
    work_to_hrs: Optional[str] = Form(None),
    weekdays: Optional[str] = Form(None),

    # Services
    services_list: Optional[str] = Form(None),

    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        user_id = user.get("user_id") if isinstance(user, dict) else None

        if not user_id:
            return ErrorResponse(status_code=400, message="User ID is missing")
        # print("line304")
        artisan = db.query(ArtisanDetail).filter(ArtisanDetail.user_id == user_id).first()
        if not artisan:
            return ErrorResponse(status_code=404, message="Artisan not found")
        print("line307")
        user_profile = db.query(UserProfiles).filter(UserProfiles.id == user_id).first()
        if not user_profile:
            return ErrorResponse(status_code=404, message="User profile not found")
        
        upload_profile = upload_file_direct(profile_pic, path=S3_IMAGES_FOLDER)
        file_name = upload_profile.get("filename")

        # Update User Profile
        user_fields = {
            "first_name": first_name,
            "last_name": last_name,
            "email": email,
            "phone_number": mobile,
            "gender": Gender[gender.upper()] if gender else None,
            "dob": dob,
            "location": location,
            "profile_image_url":file_name
        }

        for key, value in user_fields.items():
            if value is not None:
                setattr(user_profile, key, value)

        # ☁️ Upload and Save KYC Documents
        # BASE_BLOB_ACCESS_URL = get_settings().BE_BLOB_API_URL + "/"

        kyc_files = {
            KYCTypes.LICENCE: license,
            KYCTypes.CERTIFICATE: certification,
            KYCTypes.GOVT_ID: govtid,
            KYCTypes.POLICE_REPORT: policecertificate,
            KYCTypes.GUARANTEED_DOC: gaurantordocument,
        }

        for kyc_type, file in kyc_files.items():
            if file:
                upload_result = upload_file_direct(file, path=f"users/kyc/{kyc_type.value}")
                file_key = upload_result.get("filename")

                if not file_key:
                    raise HTTPException(status_code=400, detail=f"Failed to upload {kyc_type.value} document")

                # file_url = BASE_BLOB_ACCESS_URL + file_key

                existing_kyc = db.query(KycDetails).filter(
                    KycDetails.user_id == user_id,
                    KycDetails.KYC_type == kyc_type
                ).first()

                if existing_kyc:
                    existing_kyc.document = file_key
                    existing_kyc.validation_status = ValidationStatus.PENDING
                else:
                    new_kyc = KycDetails(
                        user_id=user_id,
                        KYC_type=kyc_type,
                        document=file_key,
                        validation_status=ValidationStatus.PENDING
                    )
                    db.add(new_kyc)

        # 🕒 Update Artisan Work Timings
        artisan_fields = {
            "work_from_hrs": work_from_hrs,
            "work_to_hrs": work_to_hrs,
            "weekdays": [int(x) for x in weekdays.split(",")] if weekdays else None,
        }

        for key, value in artisan_fields.items():
            if value is not None:
                setattr(artisan, key, value)

        # 🔁 Update Artisan Services List
        if services_list:
            try:
                print("line379 in if servi")
                services_list_json = json.loads(services_list)
                if not isinstance(services_list_json, list):
                    raise ValueError("services_list must be a list of service IDs")

                stmt = delete(ServiceProviderServiceMapping).where(
                    ServiceProviderServiceMapping.service_provider_id == user_profile.id
                )
                db.execute(stmt)
                db.commit()

                for service_id in services_list_json:
                    if service_id:
                        val = {
                            "service_provider_id": user_profile.id,
                            "services_id": service_id
                        }
                        print("line396")
                        create_record(db, ServiceProviderServiceMapping, val)
            except Exception as e:
                db.rollback()
                return ErrorResponse(status_code=400, message=f"Invalid services list: {str(e)}")

        db.commit()

        db.query(UserProfiles).filter(UserProfiles.id == user_id).update({UserProfiles.is_profile_complete: True})
        db.commit()

        return StandardResponse(
            status_code=200,
            message="Artisan onboarding info updated successfully",
            data={"user_id": user_id,
        "is_profile_complete": True}
        )

    except Exception as e:
        db.rollback()
        return ErrorResponse(status_code=500, message=f"Update failed: {str(e)}")


# Api for live location update & status update
@router.put("/status")
async def update_artisan_status(
    status_data: ServiceProviderStatusUpdate,
    db: Session = Depends(get_db)):
    """
    Update the service provider's status and store location & skills efficiently in Redis.
    Retrieves service IDs from the ServiceProviderServiceMapping table.
    """
    try:
        artisan_id = status_data.artisan_id
        redis_key = f"artisan:{artisan_id}"

        service_provider = update_service_provider_status(db, artisan_id, status_data)
        if not service_provider:
            return ErrorResponse(status_code=404, message="Service Provider not found")
        
        # Fetch service IDs from ServiceProviderServiceMapping table
        service_ids = (
            db.query(ServiceProviderServiceMapping.services_id)
            .filter(
                ServiceProviderServiceMapping.service_provider_id == uuid.UUID(artisan_id)
            )
            .all()
        )

        # Convert list of tuples to list of strings
        service_ids = [str(service_id[0]) for service_id in service_ids]

        if not service_ids:
            return ErrorResponse(
                status_code=400, message="No services found for this artisan."
            )

        print(service_ids, "service_ids")

        if not redis_client.exists(redis_key):
            # Store artisan data in Redis Hash
            redis_client.hset(
                redis_key,
                mapping={
                    "base_latitude": str(service_provider.latitude),
                    "base_longitude": str(service_provider.longitude),
                    "live_latitude": str(status_data.current_latitude),
                    "live_longitude": str(status_data.current_longitude),
                    "service_ids": ",".join(service_ids),
                    "weekdays": str(service_provider.weekdays)
                },
            )
            # Set expiration (optional, refresh every 24 hrs)
            # redis_client.expire(redis_key, 86400)

        if redis_client.exists(redis_key) and status_data.live_status == "live":
            print(f"Key '{redis_key}' exists.")
            live_location_updates = {
                "live_latitude": str(status_data.current_latitude),
                "live_longitude": str(status_data.current_longitude)
            }
            redis_update(redis_key, live_location_updates)
            return StandardResponse(
                status_code=200,
                message="Artisan live location updated successfully"
            )

        if status_data.live_status == "online":
            redis_update(redis_key, {"status": "online"})

            for service_id in service_ids:
                service_geo_key = f"geo:service:{service_id}"

                redis_client.geoadd(
                    service_geo_key,
                    (
                        status_data.current_longitude,
                        status_data.current_latitude,
                        artisan_id,
                    ),
                )
                # Add artisan ID under service-based keys
                redis_client.sadd(f"service:{service_id}", artisan_id)
        elif status_data.live_status == "offline":
            redis_update(redis_key, {"status": "offline"})

            # Remove from all stored services if offline
            # for service_id in service_ids:
            #     service_geo_key = f"geo:service:{service_id}"
            #     redis_client.zrem(service_geo_key, artisan_id)
            #     redis_client.srem(f"service:{service_id}", artisan_id)

            # redis_client.delete(redis_key)

        return StandardResponse(
            status_code=200,
            message=f"Status updated to {status_data.live_status}",
            data={"service_provider_id": artisan_id, "status": status_data.live_status},
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message="Error", error=str(e))
        
@router.post("/admin/create-artisan", response_model=StandardResponse)
async def admin_create_artisan(
    # user_id: str = Form(...),

    # User Profile fields
    first_name: Optional[str] = Form(None),
    last_name: Optional[str] = Form(None),
    email: str = Form(None),
    mobile: str = Form(None),
    gender: Optional[Gender] = Form(None),
    dob: Optional[str] = Form(None),
    location: Optional[str] = Form(None),
    country_code: Optional[str] = Form(None),

    # KYC uploads
    license: Optional[UploadFile] = File(None),
    certification: Optional[UploadFile] = File(None),
    govtid: Optional[UploadFile] = File(None),
    policecertificate: Optional[UploadFile] = File(None),
    gaurantordocument: Optional[UploadFile] = File(None),
    profile_pic: Optional[UploadFile] = File(None),

    # Artisan timing fields
    work_from_hrs: Optional[str] = Form(None),
    work_to_hrs: Optional[str] = Form(None),
    weekdays: Optional[str] = Form(None),

    # Services
    services_list: Optional[str] = Form(None),

    db: Session = Depends(get_db),
    # user=Depends(permission_checker)
):
    try:
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user
        # user_id = user.get("user_id") if isinstance(user, dict) else None
        # if not user_id:
        #     return ErrorResponse(status_code=400, message="User ID is missing")

        # === Uniqueness checks ===
        db_user_query = db.execute(
            select(UserProfiles).where(UserProfiles.phone_number == mobile)
        )
        db_user = db_user_query.scalars().first()
        phone_number = country_code + mobile
        cognito_user = admin_get_user(phone_number)
        role_name = "artisan"

        if cognito_user and not db_user:
            try:
                # Extract auth_id
                print("cognito_user found:", cognito_user)
                # auth_id = cognito_user.get("Username")
                auth_id = next(attr["Value"] for attr in cognito_user["UserAttributes"] if attr["Name"] == "sub")
                
                # Check if user already exists with this auth_id
                # existing_auth_user = db.query(UserProfiles).filter(UserProfiles.auth_id == auth_id).first()
                # if existing_auth_user:
                #     return ErrorResponse(
                #         status_code=409,
                #         message="User already exists with this auth_id",
                #         error="A user with this authentication ID already exists in the database."
                #     )

                role_query = db.execute(select(Roles).where(Roles.role_name == role_name))
                role = role_query.scalars().first()

                if not role:
                    return ErrorResponse(status_code=404, message=f"Role '{role_name}' not found")

                if profile_pic:
                    upload_profile = upload_file_direct(profile_pic, path=S3_IMAGES_FOLDER)
                    file_name = upload_profile.get("filename")

                user_obj = UserProfiles(
                    phone_number=mobile,
                    auth_id=auth_id,
                    status=UserStatusType.CREATED,
                    role_id=role.id,
                    hash_password=None,
                    country_code=country_code,
                    first_name=first_name,
                    last_name=last_name,
                    email=email,
                    gender=gender,
                    dob=dob,
                    location=location,
                    profile_image_url=file_name if file_name else None,
                )
                db.add(user_obj)
                db.commit()
                db.refresh(user_obj)

                user_id = user_obj.id

                # Optional: update back Cognito with DB ID if not already there
                # await update_cognito_attributes(
                #     username=phone_number,
                #     attributes={"custom:account_id": str(user_id)},
                #     replace_all=False,
                # )

                # Create artisan detail
                artisan_obj = ArtisanDetail(
                    user_id=user_id,
                    work_from_hrs=work_from_hrs if work_from_hrs else None,
                    work_to_hrs=work_to_hrs if work_to_hrs else None,
                    weekdays=[int(x) for x in weekdays.split(",")] if weekdays else None,
                )
                db.add(artisan_obj)
                db.commit()
                db.refresh(artisan_obj)

                print(f"[KYC] Starting KYC upload for user_id: {user_id}")

                kyc_files = {
                    KYCTypes.LICENCE: license,
                    KYCTypes.CERTIFICATE: certification,
                    KYCTypes.GOVT_ID: govtid,
                    KYCTypes.POLICE_REPORT: policecertificate,
                    KYCTypes.GUARANTEED_DOC: gaurantordocument,
                }
                print("license:", license)
                print("certification:", certification)
                print("govtid:", govtid)
                print("policecertificate:", policecertificate)
                print("gaurantordocument:", gaurantordocument)
                for kyc_type, file in kyc_files.items():
                    if file:
                        try:
                            print(f"[KYC] Processing {kyc_type.value}")
                            upload_result = upload_file_direct(file, path=f"users/kyc/{kyc_type.value}")
                            print(f"[KYC] Upload result: {upload_result}")
                            file_key = upload_result.get("filename")

                            if not file_key:
                                raise HTTPException(status_code=400, detail=f"Failed to upload {kyc_type.value} document")

                            new_kyc = KycDetails(
                                user_id=user_id,
                                KYC_type=kyc_type,
                                document=file_key,
                                validation_status=ValidationStatus.PENDING
                            )
                            db.add(new_kyc)
                            print(f"[KYC] Added new KYC record for {kyc_type.value}")
                        except Exception as e:
                            print(f"[KYC] Error uploading {kyc_type.value}: {e}")

                db.commit()
                db.refresh(new_kyc)

                # Update Artisan Services List
                if services_list:
                    try:
                        services_list_json = json.loads(services_list)
                        if not isinstance(services_list_json, list):
                            raise ValueError("services_list must be a list of service IDs")

                        # Remove existing mappings
                        stmt = delete(ServiceProviderServiceMapping).where(
                            ServiceProviderServiceMapping.service_provider_id == user_id
                        )
                        db.execute(stmt)
                        db.commit()

                        # Add new service mappings
                        for service_id in services_list_json:
                            if service_id:
                                print(f"Mapping service_id {service_id} to user_id {user_id}")
                                val = {
                                    "service_provider_id": user_id,
                                    "services_id": service_id
                                }
                                create_record(db, ServiceProviderServiceMapping, val)
                        db.commit()
                    except Exception as e:
                        db.rollback()
                        return ErrorResponse(status_code=400, message=f"Invalid services list: {str(e)}")

                db.query(UserProfiles).filter(UserProfiles.id == user_id).update({UserProfiles.is_profile_complete: True})
                db.commit()

                return StandardResponse(
                    status_code=200,
                    message="Artisan created successfully",
                    data={
                        "user_id": user_id,
                        "is_profile_complete": True
                    }
                )

            except Exception as e:
                return ErrorResponse(
                    status_code=500,
                    message="User exists in Cognito",
                    error=str(e),
                )
        elif not db_user and not cognito_user:
            # === Uniqueness checks ===
            existing_email = db.query(UserProfiles).filter(UserProfiles.email == email).first()
            if existing_email:
                return ErrorResponse(status_code=409, message="Email already exists", error="This email is already registered.")

            existing_phone = db.query(UserProfiles).filter(UserProfiles.phone_number == mobile, UserProfiles.country_code == country_code).first()
            if existing_phone:
                return ErrorResponse(status_code=409, message="Phone number already exists", error="This phone number is already registered.")

            phone_number = country_code + mobile
            cognito_resp, encrypted_password = cognito_sign_up(phone_number)
            auth_id = cognito_resp.get("UserSub")
            if not auth_id:
                return ErrorResponse(status_code=500, message="Error creating Cognito user", error="Missing UserSub from Cognito")
            
            # Check if user already exists with this auth_id (double-check)
            # existing_auth_user = db.query(UserProfiles).filter(UserProfiles.auth_id == auth_id).first()
            # if existing_auth_user:
            #     return ErrorResponse(
            #         status_code=409,
            #         message="User already exists with this auth_id",
            #         error="A user with this authentication ID already exists in the database."
            #     )

            role_query = db.execute(select(Roles).where(Roles.role_name == 'artisan'))
            role = role_query.scalars().first()
            if not role:
                return ErrorResponse(status_code=404, message=f"Role artisan not found")

            if profile_pic:
                upload_profile = upload_file_direct(profile_pic, path=S3_IMAGES_FOLDER)
                file_name = upload_profile.get("filename")

            user_obj = UserProfiles(
                phone_number=mobile,
                auth_id=auth_id,
                status=UserStatusType.CREATED,
                role_id=role.id,
                hash_password=encrypted_password,
                country_code=country_code,
                first_name=first_name,
                last_name=last_name if last_name else None,
                email=email,
                gender=gender,
                dob=dob,
                location=location,
                profile_image_url=file_name if file_name else None,
            )
            db.add(user_obj)
            db.commit()
            db.refresh(user_obj)

            user_id = user_obj.id
            artisan_obj = ArtisanDetail(
                user_id=user_id,
                work_from_hrs=work_from_hrs if work_from_hrs else None,
                work_to_hrs=work_to_hrs if work_to_hrs else None,
                weekdays=[int(x) for x in weekdays.split(",")] if weekdays else None,
            )
            db.add(artisan_obj)
            db.commit()
            db.refresh(artisan_obj)

            print(f"[KYC] Starting KYC upload for user_id: {user_id}")

            kyc_files = {
                KYCTypes.LICENCE: license,
                KYCTypes.CERTIFICATE: certification,
                KYCTypes.GOVT_ID: govtid,
                KYCTypes.POLICE_REPORT: policecertificate,
                KYCTypes.GUARANTEED_DOC: gaurantordocument,
            }
            print("license:", license)
            print("certification:", certification)
            print("govtid:", govtid)
            print("policecertificate:", policecertificate)
            print("gaurantordocument:", gaurantordocument)
            for kyc_type, file in kyc_files.items():
                if file:
                    try:
                        print(f"[KYC] Processing {kyc_type.value}")
                        upload_result = upload_file_direct(file, path=f"users/kyc/{kyc_type.value}")
                        print(f"[KYC] Upload result: {upload_result}")
                        file_key = upload_result.get("filename")

                        if not file_key:
                            raise HTTPException(status_code=400, detail=f"Failed to upload {kyc_type.value} document")

                        new_kyc = KycDetails(
                            user_id=user_id,
                            KYC_type=kyc_type,
                            document=file_key,
                            validation_status=ValidationStatus.PENDING
                        )
                        db.add(new_kyc)
                        print(f"[KYC] Added new KYC record for {kyc_type.value}")
                    except Exception as e:
                        print(f"[KYC] Error uploading {kyc_type.value}: {e}")

            db.commit()
            db.refresh(new_kyc)

            # Update Artisan Services List
            if services_list:
                try:
                    services_list_json = json.loads(services_list)
                    if not isinstance(services_list_json, list):
                        raise ValueError("services_list must be a list of service IDs")

                    # Remove existing mappings
                    stmt = delete(ServiceProviderServiceMapping).where(
                        ServiceProviderServiceMapping.service_provider_id == user_id
                    )
                    db.execute(stmt)
                    db.commit()

                    # Add new service mappings
                    for service_id in services_list_json:
                        if service_id:
                            print(f"Mapping service_id {service_id} to user_id {user_id}")
                            val = {
                                "service_provider_id": user_id,
                                "services_id": service_id
                            }
                            create_record(db, ServiceProviderServiceMapping, val)
                    db.commit()
                except Exception as e:
                    db.rollback()
                    return ErrorResponse(status_code=400, message=f"Invalid services list: {str(e)}")

            db.query(UserProfiles).filter(UserProfiles.id == user_id).update({UserProfiles.is_profile_complete: True})
            db.commit()

            return StandardResponse(
                status_code=200,
                message="Artisan created successfully",
                data={
                    "user_id": user_id,
                    "is_profile_complete": True
                }
            )

        else:
            return ErrorResponse(
                status_code=409,
                message="User already exists",
                error="User already exists"
            )

    except Exception as e:
        db.rollback()
        return ErrorResponse(status_code=500, message=f"Update failed", error=str(e))

@router.get("/artisan-earnings-list")
def get_artisan_earnings(
    db: Session = Depends(get_db),user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        user_id = user.get("user_id") if isinstance(user, dict) else None
        print(user_id, "user_id from token")
        # Total earning from completed invoice items
        total_earning = (
            db.query(func.sum(InvoiceItem.total_amount))
            .join(ArtisanAssigned, ArtisanAssigned.invoice_item_id == InvoiceItem.id)
            .filter(
                ArtisanAssigned.artisan_id == user_id,
                ArtisanAssigned.status == ArtisanAssignStatus.COMPLETED
            )
            .scalar()
        )

        # List of items
        # item_list = (
        #     db.query(InvoiceItem)
        #     .join(ArtisanAssigned, ArtisanAssigned.invoice_item_id == InvoiceItem.id)
        #     .filter(
        #         ArtisanAssigned.artisan_id == artisan_id,
        #         ArtisanAssigned.status == ArtisanAssignStatus.COMPLETED
        #     )
        #     .all()
        # )

        # Balance details from account
        balance_detail = (
            db.query(Account)
            .filter(
                Account.user_id == user_id,
                Account.account_type == AccountTypeType.APP  # Enum usage
            )
            .first()
        )
        if not balance_detail:
            return ErrorResponse(status_code=404, message="Account balance not found")

        # Safe balance formatting
        # balance_data = {
        #     "balance": balance_detail.balance if balance_detail else 0.0,
        #     "currency": balance_detail.currency if balance_detail else "UNKNOWN"
        # }

        return StandardResponse(
            status_code=200,
            message="Total Earnings with Balance Detail",
            data={
                "artisan_id": user_id,
                "total_earning": total_earning or 0.0,
                "account_balance": balance_detail.balance,
                "currency": balance_detail.currency
            }
        )

    except Exception as e:
        return ErrorResponse(status_code=400, message=f"Failed: {str(e)}")
    
@router.get("/artisan-earnings-details/{item_id}")
def get_artisan_earnings_detail(
    item_id: str,
    db: Session = Depends(get_db)
):
    try:
        item_list=db.query(InvoiceItem).join(ArtisanAssigned, InvoiceItem.id == item_id).filter(
            ArtisanAssigned.status == ArtisanAssignStatus.COMPLETED).all()

        return StandardResponse(
            status_code=200,
            message="item details",
            data={
                "item_id": item_id,
                "details": item_list
            
        })

    except Exception as e:
        return ErrorResponse(status_code=400, message=f"Failed: {str(e)}")
    
@router.get("/user-spends-list/{user_id}")
def get_user_spends(
    user_id: str,
    db: Session = Depends(get_db)
):
    try:
        total_spends = (
            db.query(func.sum(Invoice.total_amount))
            .filter(
                Invoice.customer_id == user_id,
                Invoice.payment_status == InvoiceStatusEnum.PAID.value
            )
            .scalar()
        )
        invoice_list= (
            db.query(Invoice)
            .filter(
                Invoice.customer_id == user_id,
                Invoice.payment_status == InvoiceStatusEnum.PAID.value
            )
            .all()
        )
        return StandardResponse(
            status_code=200,
            message="Total spends",
            data={
                "list":invoice_list,
            "user_id": user_id,
            "total_spends": total_spends or 0.0
        })

    except Exception as e:
        return ErrorResponse(status_code=400, message=f"Failed: {str(e)}")
    
@router.get("/user-spends-details/{invioice_id}")
def get_user_spends_detail(
    invioice_id: str,
    db: Session = Depends(get_db)
):
    try:
        invoice_list= (
            db.query(Invoice)
            .filter(
                Invoice.id == invioice_id,
                Invoice.payment_status == InvoiceStatusEnum.PAID.value
            )
            .all()
        )
        return StandardResponse(
            status_code=200,
            message="Invoice details",
            data={
                "invioice_id": invioice_id,
                "detail":invoice_list
            })

    except Exception as e:
        return ErrorResponse(status_code=400, message=f"Failed: {str(e)}")