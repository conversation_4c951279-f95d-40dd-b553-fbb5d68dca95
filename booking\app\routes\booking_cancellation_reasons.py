from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query, Header
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from app.database import get_db
from app.schemas.booking_cancellation_reasons import BookingCancellationReasonCreate, BookingCancellationReasonUpdate, BookingCancellationReasonResponse
from app.schemas.helper import StandardResponse, ErrorResponse
from app.utils.booking_cancellation_reason_crud import create_booking_cancellation_reason, get_booking_cancellation_reasons, get_booking_cancellation_reason, update_booking_cancellation_reason, delete_booking_cancellation_reason
from typing import List, Optional, Annotated
from app.utils.auth import get_id_header

router = APIRouter(prefix="/booking-cancellation-reason", tags=["Booking Cancellation Reasons"])


@router.post("/create")
async def create_cancellation_reason(data: BookingCancellationReasonCreate, db: Session = Depends(get_db), Authorization: Annotated[str, Header()] = None):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        db_booking_cancellation_reason = create_booking_cancellation_reason(db, data)
        return StandardResponse(
            status_code=200,
            message="Booking Cancellation Reason created successfully",
            data=BookingCancellationReasonResponse.model_validate(db_booking_cancellation_reason)
        )
    except Exception as e:
        return ErrorResponse(
            status_code=400,
            message="Failed to create booking cancellation reason",
            error=str(e)
        )

@router.get("/read")
async def read_cancellation_reasons(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
    user_type: Optional[str] = None,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")

        booking_cancellation_reasons = get_booking_cancellation_reasons(db=db, skip=skip, limit=limit, is_active=is_active, user_type=user_type)
        return StandardResponse(
            status_code=200,
            message="Booking Cancellation Reasons retrieved successfully",
            data=[BookingCancellationReasonResponse.model_validate(booking_cancellation_reason) for booking_cancellation_reason in booking_cancellation_reasons]
        )
    except Exception as e:
        return ErrorResponse(
            status_code=400,
            message="Failed to retrieve Booking Cancellation Reasons",
            error=str(e)
        )


@router.get("/{id}", response_model=dict)
async def read_cancellation_reasons(id: UUID, db: Session = Depends(get_db), Authorization: Annotated[str, Header()] = None):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        db_booking_cancellation_reason = get_booking_cancellation_reason(db, id)
        return StandardResponse(
            status_code=200,
            message="Booking Cancellation Reason retrieved successfully",
            data=BookingCancellationReasonResponse.model_validate(db_booking_cancellation_reason)
        )
    except Exception as e:
        return ErrorResponse(
            status_code=404,
            message="Booking Cancellation Reason not found",
            error=str(e)
        )

@router.put("/{id}", response_model=dict)
async def update_existing_cancellation_reasons(
    id: UUID,
    data: BookingCancellationReasonUpdate,
    db: Session = Depends(get_db), Authorization: Annotated[str, Header()] = None):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")

        db_booking_cancellation_reason = update_booking_cancellation_reason(db, id, data)
        return StandardResponse(
            status_code=200,
            message="Booking Cancellation Reason updated successfully",
            data=BookingCancellationReasonResponse.model_validate(db_booking_cancellation_reason)
        )
    except Exception as e:
        return ErrorResponse(
            status_code=400,
            message="Failed to update booking_cancellation_reason",
            error=str(e)
        )

@router.delete("/{id}", response_model=dict)
async def delete_existing_cancellation_reasons(id: UUID, db: Session = Depends(get_db), Authorization: Annotated[str, Header()] = None):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")

        delete_booking_cancellation_reason(db, id)
        return StandardResponse(
            status_code=200,
            message="Booking Cancellation Reason deleted successfully",
            data=None
        )
    except Exception as e:
        return ErrorResponse(
            status_code=400,
            message="Failed to delete booking_cancellation_reason",
            error=str(e)
        )