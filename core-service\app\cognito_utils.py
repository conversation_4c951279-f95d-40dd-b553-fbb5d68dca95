from typing import Optional
import boto3
import hmac
import hashlib
import base64
from app.config import get_settings
from app.utils import generate_cognito_password
import secrets
import string

from app.helper import ErrorResponse
from cryptography.fernet import Fernet


settings = get_settings()

cognito_client = boto3.client(
    "cognito-idp",
    region_name=settings.COGNITO_REGION,
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,  # Use Client ID as access key
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,  # Use Client Secret as secret key
)

print(get_settings().COGNITO_USER_POOL_ID, get_settings().COGNITO_CLIENT_ID, "settings")


# Generate a key (store this securely, do it only once)
def generate_key() -> bytes:
    x = Fernet.generate_key()
    print(x, "xxxxxxxxxxxxxxxx")
    return x


# Create Fernet instance
fernet = Fernet(
    b"Ys-6MQcPzFEEBB-ES6G-WVo3zEbqGozrB5m1Rd2Qxqk="
)  # In production, load key from a secure file or vault


# Encrypt password
def encrypt_password(password: str) -> str:
    return fernet.encrypt(password.encode()).decode()


# Decrypt password
def decrypt_password(encrypted_password: str) -> str:
    return fernet.decrypt(encrypted_password.encode()).decode()


def generate_secret_hash(username, client_id, client_secret):
    """
    Generates a secret hash required for AWS Cognito API calls.
    """
    message = username + client_id
    dig = hmac.new(client_secret.encode(), message.encode(), hashlib.sha256).digest()
    return base64.b64encode(dig).decode()


def create_cognito_user(
    phone_number: str,
    temporary_password: str,
    # email: Optional[str] = None,
):
    """
    Creates a user in AWS Cognito **without sending SMS/Email** using `admin_create_user()`.
    """
    try:

        response = cognito_client.admin_create_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=phone_number,
            UserAttributes=[
                # {"Name": "email", "Value": email},
                {"Name": "phone_number", "Value": phone_number},
                # {"Name": "email_verified", "Value": "false"},  # Now allowed
                {"Name": "phone_number_verified", "Value": "true"},  # Now allowed
            ],
            ForceAliasCreation=True,
            TemporaryPassword=temporary_password,
            MessageAction="SUPPRESS",  # Prevents Cognito from sending verification emails/SMS
        )
        set_password_response = cognito_client.admin_set_user_password(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=phone_number,
            Password="Hello@123",
            Permanent=True,  # This makes the password permanent, without requiring a password change on first login
        )
        return response

    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


async def update_cognito_attributes(
    username: str, attributes: dict, replace_all: bool = False
):
    """
    Updates Cognito user attributes using admin privileges.
    Can be used for both regular users and admins.

    Args:
        username: The username/email of the user
        attributes: Dictionary of attributes to update
                   (e.g., {"custom:local_user_id": "123"} or {"custom:local_admin_id": "456"})
        replace_all: If True, replaces all attributes. If False, only updates/adds specified attributes
    """
    try:
        if not replace_all:
            user_info = cognito_client.admin_get_user(
                UserPoolId=get_settings().COGNITO_USER_POOL_ID, Username=username
            )

            existing_attributes = {
                attr["Name"]: attr["Value"]
                for attr in user_info.get("UserAttributes", [])
                if attr["Name"]
                != "sub"  # Exclude the 'sub' attribute as it's immutable
            }

            merged_attributes = {**existing_attributes, **attributes}
        else:
            merged_attributes = attributes

        # Ensure 'sub' is not in the attributes to update
        if "sub" in merged_attributes:
            del merged_attributes["sub"]

        user_attributes = [
            {"Name": key, "Value": value} for key, value in merged_attributes.items()
        ]

        response = cognito_client.admin_update_user_attributes(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=username,
            UserAttributes=user_attributes,
        )
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


async def cognito_admin_login(username: str, password: str):
    try:

        auth_parameters = {"USERNAME": username, "PASSWORD": password}

        # If client secret is configured, add secret hash
        if get_settings().COGNITO_CLIENT_SECRET:
            from app.hash import cognito_secret_hash

            secret_hash = cognito_secret_hash(username)
            auth_parameters["SECRET_HASH"] = secret_hash

        response = cognito_client.initiate_auth(
            ClientId=get_settings().COGNITO_CLIENT_ID,
            AuthFlow="USER_PASSWORD_AUTH",
            AuthParameters=auth_parameters,
        )
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


async def delete_cognito_user(username: str):
    """
    Deletes a user from AWS Cognito.

    Args:
        username: The username/email of the user to delete

    Returns:
        Response from Cognito API
    """
    return cognito_client.admin_delete_user(
        UserPoolId=get_settings().USER_POOL_ID, Username=username
    )


def add_user_to_group(username: str, group_name: str):
    """
    Add a user to a Cognito user group.

    Args:
        username: The Cognito username (UUID)
        group_name: The name of the group to add the user to

    Returns:
        The response from Cognito
    """
    try:
        client = boto3.client(
            "cognito-idp",
            region_name=get_settings().AWS_REGION,
            aws_access_key_id=get_settings().AWS_ACCESS_KEY_ID,
            aws_secret_access_key=get_settings().AWS_SECRET_ACCESS_KEY,
        )

        response = client.admin_add_user_to_group(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=username,
            GroupName=group_name,
        )

        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def cognito_sign_up(phonenumber):
    try:
        password = generate_cognito_password()
        encrypted_password = encrypt_password(password)
        response = cognito_client.sign_up(
            ClientId=get_settings().CLIENT_ID,
            Username=phonenumber,
            UserAttributes=[
                {"Name": "email", "Value": ""},
                {"Name": "phone_number", "Value": phonenumber},
            ],
            Password=password,
            SecretHash=generate_secret_hash(
                phonenumber, get_settings().CLIENT_ID, get_settings().CLIENT_SECRET
            ),
        )
        print(response, "respppppppppppppppp")
        return response, encrypted_password
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def cognito_renew_token(refresh_token, role, phone_number):
    try:
        response = cognito_client.initiate_auth(
            AuthFlow="REFRESH_TOKEN_AUTH",
            ClientId=get_settings().COGNITO_CLIENT_ID,
            AuthParameters={
                "REFRESH_TOKEN": refresh_token,
                "SECRET_HASH": generate_secret_hash(
                    phone_number,
                    get_settings().COGNITO_CLIENT_ID,
                    get_settings().CLIENT_SECRET,
                ),
            },
        )
        print(response, "respppppppppppppppp")
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def confirm_signup(phonenumber, confirmation_code):
    try:
        response = cognito_client.confirm_sign_up(
            ClientId=get_settings().CLIENT_ID,
            Username=phonenumber,
            ConfirmationCode=confirmation_code,
            SecretHash=generate_secret_hash(
                phonenumber, get_settings().CLIENT_ID, get_settings().CLIENT_SECRET
            ),
        )
        print(response, "respppppppppppppppp")
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def resend_confirmation_code(phonenumber):
    try:
        response = cognito_client.resend_confirmation_code(
            ClientId=get_settings().CLIENT_ID,
            Username=phonenumber,
            SecretHash=generate_secret_hash(
                phonenumber, get_settings().CLIENT_ID, get_settings().CLIENT_SECRET
            ),
        )
        print(response, "respppppppppppppppp")
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def get_access_token(phone_number, otp):
    try:
        auth_response = cognito_client.initiate_auth(
            AuthFlow="CUSTOM_AUTH",
            AuthParameters={
                "USERNAME": phone_number,
                "SECRET_HASH": generate_secret_hash(
                    phone_number, get_settings().CLIENT_ID, get_settings().CLIENT_SECRET
                ),
            },
            ClientId=get_settings().CLIENT_ID,
        )

        print(auth_response, "auth_response")

        session = auth_response["Session"]

        token_response = cognito_client.respond_to_auth_challenge(
            ClientId=get_settings().CLIENT_ID,
            ChallengeName="CUSTOM_CHALLENGE",
            Session=session,
            ChallengeResponses={"USERNAME": phone_number, "ANSWER": otp},
        )
        print(token_response, "token_response")
        return token_response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def get_token_after_signup(phonenumber, encrypted_password):
    try:
        password = decrypt_password(encrypted_password)
        auth_response = cognito_client.initiate_auth(
            ClientId=get_settings().CLIENT_ID,
            AuthFlow="USER_PASSWORD_AUTH",
            AuthParameters={
                "USERNAME": phonenumber,
                "PASSWORD": password,
                "SECRET_HASH": generate_secret_hash(
                    phonenumber, get_settings().CLIENT_ID, get_settings().CLIENT_SECRET
                ),
            },
        )
        # auth_response = cognito_client.initiate_auth(
        #         AuthFlow="USER_SRP_AUTH",
        #         ClientId=get_settings().COGNITO_CLIENT_ID,
        #         AuthParameters={"USERNAME": phonenumber}
        #     )
        print(auth_response, "token responseeeeeeeeeeee")
        return auth_response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


def admin_get_user(phonenumber):
    try:
        response = cognito_client.admin_get_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID,
            Username=phonenumber,
        )
        print(response, "respppppppppppppppp")
        return response
    except Exception as e:
        if (
            hasattr(e, "response")
            and e.response.get("Error", {}).get("Code") == "UserNotFoundException"
        ):
            return None
        raise Exception(f"Cognito Error: {str(e)}")


async def disable_cognito_user(username: str):
    """
    Deletes a user from AWS Cognito.

    Args:
        username: The username/email of the user to delete

    Returns:
        Response from Cognito API
    """
    try:
        cognito_entry = admin_get_user(username)
        print(cognito_entry, "cognitttttto")
        if cognito_entry is None:
            print("noppeeee")
            return None
        response = cognito_client.admin_disable_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID, Username=username
        )
        print("dissssabled")
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


async def enable_cognito_user(username: str):
    """
    Deletes a user from AWS Cognito.

    Args:
        username: The username/email of the user to delete

    Returns:
        Response from Cognito API
    """
    try:
        cognito_entry = admin_get_user(username)
        if cognito_entry is None:
            print("noppeeee")
            return None
        response = cognito_client.admin_enable_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID, Username=username
        )
        print("enabledabled")
        return response
    except Exception as e:
        raise Exception(f"Cognito Error: {e}")


async def get_user_attributes(username: str):
    """
    Retrieves all user attributes from Cognito using an access token.

    Args:
        token: The Cognito access token

    Returns:
        Dictionary of user attributes
    """
    try:
        print(username, "username")
        user_info = cognito_client.admin_get_user(
            UserPoolId=get_settings().COGNITO_USER_POOL_ID, Username=username
        )

        # Convert attributes list to dictionary for easier access
        attributes = {}
        print(user_info, "user_info")
        for attr in user_info.get("UserAttributes", []):
            attributes[attr["Name"]] = attr["Value"]

        # Add username to attributes
        attributes["username"] = user_info.get("Username")
        print(attributes, "attributes")

        return attributes

    except Exception as e:
        # print(e)
        raise Exception(f"Cognito Error: {e}")


def password_login(identifier: str, password: str):
    """
    Login using Cognito with username (email/phone) and password.
    Returns tokens if successful, raises exception otherwise.
    """
    try:
        auth_params = {"USERNAME": identifier, "PASSWORD": password}

        # Only add SECRET_HASH if client secret exists
        # if get_settings().COGNITO_CLIENT_SECRET:
        #     auth_params["SECRET_HASH"] = generate_secret_hash(
        #         identifier,
        #         get_settings().COGNITO_CLIENT_ID,
        #         get_settings().COGNITO_CLIENT_SECRET,
        #     )

        response = cognito_client.initiate_auth(
            ClientId=get_settings().COGNITO_CLIENT_ID,
            AuthFlow="USER_PASSWORD_AUTH",
            AuthParameters=auth_params,
        )

        return response
    except Exception as e:
        # print(e)
        raise Exception(f"Cognito Error: {e}")


def signup(phone_number: str, password: str,email:str):
    try:
        cognito_resp = cognito_client.admin_create_user(
            UserPoolId=get_settings().USER_POOL_ID,
            Username=phone_number,
            UserAttributes=[
                {"Name": "email", "Value": email},
                {"Name": "email_verified", "Value": "false"},
                {"Name": "phone_number", "Value": phone_number},
                {"Name": "phone_number_verified", "Value": "true"},
            ],
            ForceAliasCreation=True,
            MessageAction="SUPPRESS",
        )
        cognito_client.admin_set_user_password(
            UserPoolId=get_settings().USER_POOL_ID,
            Username=phone_number,
            Password=password,
            Permanent=True,
        )
        # auth_id = cognito_resp.get("User", {}).get("Username")
        return cognito_resp
    except Exception as e:
        # print(e)
        raise Exception(f"Cognito Error: {e}")
