import ast
from datetime import datetime
import json
import uuid
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, Header, Query
from app.dependencies import role_required
from app.s3_upload import (
    upload_file_direct,
    s3_delete_file,
    S3_IMAGES_FOLDER,
    S3_DOCS_FOLDER,
)
from sqlalchemy.orm import Session, selectinload
from sqlalchemy import or_, delete, desc, and_, asc
from pathlib import Path
from app import schemas
from app.database import get_db
from app.utils import (
    AdminRole,
    ServiceProviderStatusType,
    check_file_exists,
    create_record,
    delete_record,
    get_id_header,
    is_valid_uuid,
    send_push_notification,
    update_record,
)
from app.cognito_utils import (
    enable_cognito_user,
    update_cognito_attributes,
    disable_cognito_user,
)
from typing import Optional
from app.hash import cognito_secret_hash
from sqlalchemy.future import select
from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Annotated, List
from app.file_uploader import upload_file
from app.config import get_settings
from geoalchemy2.functions import ST_Point
from sqlalchemy import cast, Time
from app.schemas import CreateServiceProvider
from app.models import ServiceProvider, ServiceProviderServiceMapping
from sqlalchemy.types import String

import uuid
import re

# from hash import generate_salt, hash_password
from fastapi.encoders import jsonable_encoder
from app.validation import check_if_account_exists
from app.helper import StandardResponse, ErrorResponse
import requests
from fastapi.responses import StreamingResponse
import pandas as pd
from io import BytesIO
from app.image_processor import generate_thumbnail
from app.image_validator import validate_image
import traceback
import sys
import logging

# Configure logger
logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/sp-signup")
async def create_serviceprovider(
    request: CreateServiceProvider,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            auth_id = resp_json.get("id")
            email = resp_json.get("email")
        else:
            return ErrorResponse(status_code=401, message=resp.json())

        exists = await check_if_account_exists(
            db, email=request.email, phone_number=request.phone_number
        )
        print(exists, "exists")
        if exists:
            return ErrorResponse(status_code=400, message="Account already exists")

        request_dict = request.dict()
        if account_id is not None:
            query = select(ServiceProvider).filter(ServiceProvider.id == account_id)
            result = await db.execute(query)
            sp_obj = result.scalars().first()

            if sp_obj:
                return ErrorResponse(
                    status_code=400, message="Service Provider already exists"
                )

        request_dict["id"] = account_id
        if request_dict.get("latitude") == None:
            request_dict["latitude"] = 0
        if request_dict.get("longitude") == None:
            request_dict["longitude"] = 0

        new_sp = await create_record(db, ServiceProvider, request_dict)
        if isinstance(new_sp, str):
            if "ix_service_provider_email" in new_sp:
                return ErrorResponse(status_code=400, message="Email already registerd")
            elif "ix_service_provider_phone_number" in new_sp:
                return ErrorResponse(
                    status_code=400, message="Phone Number already registerd"
                )
            else:
                return ErrorResponse(status_code=400, message=new_sp)
        print(new_sp.id, "new_sp.id")
        await update_cognito_attributes(
            request.phone_number, {"custom:account_id": str(new_sp.id)}, False
        )
        return StandardResponse(
            status_code=200,
            data=new_sp,
            message="Service Provider created successfully",
        )

    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/sp-read")
async def read_sp(
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        # ✅ Get Auth ID from Token
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message=resp.json())

        account_id = resp.json().get("account_id")
        user_attributes = resp.json().get("user_attributes")

        print(account_id, "account_id")

        # ✅ Select All Fields Except `location` and `profile_pic`
        excluded_fields = {"location"}
        selected_columns = [
            col
            for col in ServiceProvider.__table__.c
            if col.name not in excluded_fields
        ]

        query = select(*selected_columns).filter(ServiceProvider.id == account_id)
        result = await db.execute(query)
        sp_obj = result.mappings().first()

        if not sp_obj:
            return ErrorResponse(status_code=404, message="Service Provider not found")

        sp_obj = jsonable_encoder(sp_obj)
        print(sp_obj, "sp_obj")

        # ✅ Get service mappings
        service_mapping_query = select(ServiceProviderServiceMapping).where(
            ServiceProviderServiceMapping.service_provider_id
            == uuid.UUID(sp_obj.get("id"))
        )
        service_mapping_result = await db.execute(service_mapping_query)

        # ✅ Extract service IDs as strings
        service_mappings = service_mapping_result.scalars().all()
        print(service_mappings, "service_mappings")
        print(len(service_mappings), "service_mappings")
        for mapping in service_mappings:
            print(mapping.id, "mapping.services_id")
        service_ids = (
            [str(mapping.services_id) for mapping in service_mappings]
            if service_mappings
            else []
        )

        # ✅ Add services to response
        final_response = jsonable_encoder(sp_obj)
        final_response["services"] = service_ids

        return StandardResponse(
            status_code=200,
            message="Service Provider read successfully",
            data=final_response,
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


# Get by Id
@router.get(
    "/sp-read/{id}",
    # response_model=schemas.ServiceProviderResponse
)
async def read_sp_by_id(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            auth_id = resp_json.get("id")
        else:
            return ErrorResponse(status_code=401, message=resp.json())
        # ✅ Select All Fields Except `location` and `profile_pic`
        excluded_fields = {"location"}
        selected_columns = [
            col
            for col in ServiceProvider.__table__.c
            if col.name not in excluded_fields
        ]

        query = select(*selected_columns).filter(ServiceProvider.id == uuid.UUID(id))
        result = await db.execute(query)
        sp_obj = result.mappings().first()

        if not sp_obj:
            return ErrorResponse(status_code=404, message="Service Provider not found")

        sp_obj = jsonable_encoder(sp_obj)
        print(sp_obj, "sp_obj")

        # ✅ Get service mappings
        service_mapping_query = select(ServiceProviderServiceMapping).where(
            ServiceProviderServiceMapping.service_provider_id
            == uuid.UUID(sp_obj.get("id"))
        )
        service_mapping_result = await db.execute(service_mapping_query)

        # ✅ Extract service IDs as strings
        service_mappings = service_mapping_result.scalars().all()
        print(service_mappings, "service_mappings")
        print(len(service_mappings), "service_mappings")

        # Create a list to store service details (id and name)
        services_with_names = []

        # Fetch service details for each service ID
        for mapping in service_mappings:
            service_id = str(mapping.services_id)

            # Call the service-management API to get service details
            try:
                service_url = (
                    f"{get_settings().BE_SERVICE_API_URL}/services-read/{service_id}"
                )
                service_response = requests.get(
                    service_url, headers={"Authorization": Authorization}
                )

                if service_response.status_code == 200:
                    service_data = service_response.json().get("data", {})
                    services_with_names.append(
                        {
                            "id": service_id,
                            "name": service_data.get("name", "Unknown Service"),
                        }
                    )
                else:
                    # If service details can't be fetched, just add the ID
                    services_with_names.append(
                        {"id": service_id, "name": "Unknown Service"}
                    )
            except Exception as e:
                print(f"Error fetching service details: {e}")
                # If there's an error, just add the ID
                services_with_names.append(
                    {"id": service_id, "name": "Unknown Service"}
                )

        # ✅ Add services to response
        final_response = jsonable_encoder(sp_obj)
        final_response["services"] = services_with_names

        return StandardResponse(
            status_code=200,
            message="Service Provider read successfully",
            data=final_response,
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/sp-list")
async def list_sp(
    q: Optional[str] = Query(None, description="Search query"),
    status: Optional[ServiceProviderStatusType] = Query(
        None, description="Filter by status"
    ),
    sort: Optional[str] = Query(
        None, description="Sort by created_at ('asc' or 'dsc')"
    ),
    skip: int = 0,
    limit: int = 10000,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        # ✅ Authenticate User
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message=resp.json())

        account_id = resp.json().get("account_id")

        # ✅ Exclude Fields That Cause Encoding Issues
        excluded_fields = {"location"}
        selected_columns = [
            col
            for col in ServiceProvider.__table__.c
            if col.name not in excluded_fields
        ]

        # ✅ Initialize Query
        query = select(*selected_columns).select_from(ServiceProvider)

        # ✅ Apply Search Filter
        filters = []
        if q:
            filters.append(
                or_(
                    ServiceProvider.id.cast(String) == q,
                    ServiceProvider.phone_number.ilike(f"%{q}%"),
                    ServiceProvider.email.ilike(f"%{q}%"),
                    ServiceProvider.first_name.ilike(f"%{q}%"),
                    ServiceProvider.last_name.ilike(f"%{q}%"),
                )
            )

        # ✅ Apply Status Filter
        if status:
            filters.append(ServiceProvider.status == status)

        if filters:
            query = query.where(and_(*filters))  # Apply all filters

        # ✅ Apply Sorting
        if sort:
            if sort.lower() == "dsc":
                query = query.order_by(ServiceProvider.created_at.desc())
            elif sort.lower() == "asc":
                query = query.order_by(ServiceProvider.created_at.asc())

        # ✅ Apply Pagination
        query = query.offset(skip).limit(limit)

        # ✅ Execute Query
        result = await db.execute(query)
        sps = result.mappings().all()  # ✅ Converts ORM objects to dictionaries

        # ✅ Convert to JSON Format
        json_data = jsonable_encoder(sps)

        # ✅ Get Total Service Provider Count
        count_query = select(func.count()).select_from(ServiceProvider)
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()

        total_status_count = None
        if status:
            count_query = select(func.count()).select_from(ServiceProvider).where(ServiceProvider.status == status)
            total_result = await db.execute(count_query)
            total_status_count = total_result.scalar()

        return StandardResponse(
            status_code=200,
            message="Service Provider list read successfully",
            data={"total": total_count, "data": json_data, "total_status_count": total_status_count},
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.put("/sp-update")
async def update_sp(
    data: schemas.UpdateServiceProvider = Depends(),
    profile_pic: Optional[UploadFile] = File(None),
    license: Optional[UploadFile] = File(None),
    certificate: Optional[UploadFile] = File(None),
    govt_id: Optional[UploadFile] = File(None),
    police_report: Optional[UploadFile] = File(None),
    guaranteed_doc: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        print(data.__dict__, "data.__dict__")
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            print(resp_json, "resp_json")
            account_id = resp_json.get("account_id")
            print(account_id, "account_id")
        else:
            return ErrorResponse(
                status_code=401, message="Unauthorized", error=resp.json()
            )

        services_list = data.__dict__.pop("services_list", None)
        query = select(ServiceProvider).filter(ServiceProvider.id == account_id)
        result = await db.execute(query)
        get_sp = result.scalars().first()
        if get_sp is None:
            return ErrorResponse(
                status_code=404, message="Service Provider Not found", error=None
            )

        sp_id = get_sp.id
        print(sp_id, "sp_id", data.__dict__)

        if profile_pic:
            # Validate profile picture
            try:
                is_valid, error_message = await validate_image(profile_pic)
                if not is_valid:
                    logger.error(f"Image validation failed: {error_message}")
                    return ErrorResponse(
                        status_code=400,
                        message=error_message,
                        error="Profile picture validation failed"   
                    )
            except Exception as e:
                logger.error(f"Error validating image: {str(e)}", exc_info=True)
                return ErrorResponse(
                    status_code=400,
                    message="Failed to validate image",
                    error=str(e)
                )

            # Generate thumbnail
            thumbnail_io, thumbnail_filename = await generate_thumbnail(profile_pic)
            
            # Reset file pointer for main image upload
            await profile_pic.seek(0)

            # Delete existing profile pic and its thumbnail
            if get_sp.profile_pic:
                s3_delete_file(get_sp.profile_pic)
            if get_sp.profile_pic_thumbnail:
                s3_delete_file(get_sp.profile_pic_thumbnail)
            
            # Upload original image
            profile_url = upload_file_direct(profile_pic, path=S3_IMAGES_FOLDER)
            if "message" in profile_url:
                return ErrorResponse(
                    status_code=400,
                    message="Failed to upload image",
                    error=f"Failed to upload image: {profile_url['message']}"
                )
            
            # Upload thumbnail
            thumbnail_url = upload_file_direct(
                UploadFile(
                    filename=thumbnail_filename,
                    file=thumbnail_io
                ),
                path=S3_IMAGES_FOLDER
            )
            if "message" in thumbnail_url:
                return ErrorResponse(
                    status_code=400,
                    message="Failed to upload thumbnail",
                    error=f"Failed to upload thumbnail: {thumbnail_url['message']}"
                )

            # Update service provider record with both main image and thumbnail
            setattr(get_sp, "profile_pic", profile_url["filename"])
            setattr(get_sp, "profile_pic_thumbnail", thumbnail_url["filename"])

        if license:
            if get_sp.license:
                # Delete existing license
                s3_delete_file(get_sp.license)
            # Calling S3 upload function
            profile_url = upload_file_direct(license, path=S3_DOCS_FOLDER)
            setattr(get_sp, "license", profile_url["filename"])

        if certificate:
            if get_sp.certificate:
                # Delete existing certificate
                s3_delete_file(get_sp.certificate)
            # Calling S3 upload function
            profile_url = upload_file_direct(certificate, path=S3_DOCS_FOLDER)
            setattr(get_sp, "certificate", profile_url["filename"])

        if govt_id:
            if get_sp.govt_id:
                # Delete existing govt_id
                s3_delete_file(get_sp.govt_id)
            # Calling S3 upload function
            profile_url = upload_file_direct(govt_id, path=S3_DOCS_FOLDER)
            setattr(get_sp, "govt_id", profile_url["filename"])

        if police_report:
            if get_sp.police_report:
                # Delete existing govt_id
                s3_delete_file(get_sp.police_report)
            # Calling S3 upload function
            police_report = upload_file_direct(police_report, path=S3_DOCS_FOLDER)
            setattr(get_sp, "police_report", police_report["filename"])

        if guaranteed_doc:
            if get_sp.guaranteed_doc:
                # Delete existing govt_id
                s3_delete_file(get_sp.guaranteed_doc)
            # Calling S3 upload function
            guaranteed_doc = upload_file_direct(guaranteed_doc, path=S3_DOCS_FOLDER)
            setattr(get_sp, "guaranteed_doc", guaranteed_doc["filename"])

        if data.weekdays:
            data.weekdays = list(
                map(int, data.weekdays.split(","))
            )  # Convert to list of integers
            setattr(get_sp, "weekdays", data.weekdays)

        print(data.weekdays, "weekdays")

        latitude = data.latitude
        longitude = data.longitude
        print(latitude, longitude, "lat long")
        if latitude and longitude:
            setattr(
                get_sp, "location", ST_Point(longitude, latitude)
            )  # Store as GeoPoint

        if data.work_from_hrs:
            work_from_time = datetime.strptime(data.work_from_hrs, "%H:%M").time()
            setattr(get_sp, "work_from_hrs", cast(work_from_time, Time))

        if data.work_to_hrs:
            work_to_time = datetime.strptime(data.work_to_hrs, "%H:%M").time()
            setattr(get_sp, "work_to_hrs", cast(work_to_time, Time))

        if data.break_from_hrs:
            break_from_time = datetime.strptime(data.break_from_hrs, "%H:%M").time()
            setattr(get_sp, "break_from_hrs", cast(break_from_time, Time))

        if data.break_to_hrs:
            break_to_time = datetime.strptime(data.break_to_hrs, "%H:%M").time()
            setattr(get_sp, "break_to_hrs", cast(break_to_time, Time))

        if data.dob:
            dob_date = datetime.strptime(data.dob, "%Y-%m-%d").date()
            setattr(get_sp, "dob", dob_date)

        if (
            data.first_name
            and data.work_from_hrs
            and data.work_to_hrs
            # and data.bank_name
            # and data.bank_acc_holder_name
            # and data.bank_acc_no
            # and data.bank_branch_code
            # and data.bank_swift_code
            # and data.bank_acc_type
            and data.dob
            and data.gender
            # and data.primary_location
            # and data.services_list
            and license
            and certificate
            and govt_id
            and profile_pic
        ):
            setattr(get_sp, "is_profile_complete", True)

        res = await update_record(db, data, get_sp)

        # Creating services list
        if services_list:
            print(services_list, type(services_list), "service listtttt")
            # Removing existing services before adding new ones
            stmt = delete(ServiceProviderServiceMapping).where(
                ServiceProviderServiceMapping.service_provider_id == sp_id
            )
            await db.execute(stmt)
            await db.commit()
            # Adding new services
            # services_list_json = ast.literal_eval(services_list)
            # services_list_json = json.loads(services_list)
            uuid_pattern = (
                r"[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"
            )
            uuid_strings = re.findall(uuid_pattern, services_list)

            # Step 2: Convert to UUID objects for validation
            uuid_objects = [uuid.UUID(u) for u in uuid_strings]
            print(uuid_objects, "uuid_objects")

            # Step 3: Convert UUID objects back to strings (if needed)
            services_list_json = [str(u) for u in uuid_objects]
            print(services_list_json, "services_list_json")
            for service_id in services_list_json:
                print(service_id, "service_id")
                if service_id:
                    val = {"service_provider_id": sp_id, "services_id": service_id}
                    await create_record(db, ServiceProviderServiceMapping, val)

        print(res, "final ressssssssss")
        return StandardResponse(
            status_code=200, message="Service Provider updated successfully"
        )
    except Exception as e:
        print(e, "error")
        return ErrorResponse(status_code=500, message=f"Error", error=str(e))


@router.delete("/sp-delete")
async def delete_sp(
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(status_code=401, message=resp.json())

        query = select(ServiceProvider).filter(ServiceProvider.id == account_id)
        result = await db.execute(query)
        get_sp = result.scalars().first()

        if get_sp is None:
            return ErrorResponse(status_code=404, message="Service Provider not found")

        if get_sp.profile_pic:
            s3_delete_file(get_sp.profile_pic)

        if get_sp.license:
            s3_delete_file(get_sp.license)

        if get_sp.certificate:
            s3_delete_file(get_sp.certificate)

        if get_sp.govt_id:
            s3_delete_file(get_sp.govt_id)

        res = await delete_record(db, get_sp)
        return StandardResponse(
            status_code=200, message="Service Provider deleted successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.put("/sp-approval/{id}")
async def sp_approval(
    id: str,
    data: schemas.ServiceProviderApproval,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    resp = await get_id_header(Authorization)
    if resp.status_code == 200:
        resp_json = resp.json()

        query = select(ServiceProvider).filter(ServiceProvider.id == uuid.UUID(id))
        result = await db.execute(query)
        get_sp = result.scalars().first()
        request = data.dict()

        # if (
        #     data.status == ServiceProviderStatusType.APPROVED
        #     and get_sp.status == ServiceProviderStatusType.BLACKLIST
        # ):
        #     return ErrorResponse(
        #         status_code=400,
        #         message="Service Provider already blacklisted",
        #     )

        if (
            data.status == ServiceProviderStatusType.BLACKLIST
            or data.status == ServiceProviderStatusType.SUSPENDED
        ):
            if get_sp.is_active:
                setattr(get_sp, "is_active", False)
                disable_response = await disable_cognito_user(get_sp.phone_number)
                if disable_response is None:
                    return ErrorResponse(
                        status_code=400,
                        message="Failed to disable Cognito user, no user in cognito",
                    )
            else:
                return ErrorResponse(
                    status_code=400,
                    message="Service Provider already blacklisted or suspended",
                )

        if (
            data.status == ServiceProviderStatusType.APPROVED
            and (get_sp.status == ServiceProviderStatusType.SUSPENDED or get_sp.status == ServiceProviderStatusType.BLACKLIST)
            and not get_sp.is_active
        ):
            setattr(get_sp, "is_active", True)
            enable_response = await enable_cognito_user(get_sp.phone_number)
            if enable_response is None:
                return ErrorResponse(
                    status_code=400,
                    message="Failed to enable Cognito user, no user in cognito",
                )

        setattr(get_sp, "status", data.status)
        setattr(get_sp, "reason", data.reason)
        await db.commit()
        await db.refresh(get_sp)

        send_push_notification(
            auth_token=Authorization,
            title="Profile Status Updated",
            message=f"Your profile has been {data.status}",
            sender_id=get_sp.notification_uuid,
            type="service_provider",
        )

        return StandardResponse(
            status_code=200, message="Service Provider updated successfully"
        )
    else:
        return ErrorResponse(status_code=401, message=resp.json())


async def create_artisan(request_dict, db):
    try:
        auth_id = request_dict.get("auth_id")
        request_dict["latitude"] = 0
        request_dict["longitude"] = 0
        new_sp = await create_record(db, ServiceProvider, request_dict)
        sp_id = new_sp.id
        if isinstance(new_sp, str):
            if "ix_users_email" in new_sp:
                return ErrorResponse(status_code=400, message="Email already registerd")
            elif "ix_users_phone_number" in new_sp:
                return ErrorResponse(
                    status_code=400, message="Phone Number already registerd"
                )
            else:
                return ErrorResponse(status_code=400, message=new_sp)
        await update_cognito_attributes(
            auth_id, {"custom:account_id": str(sp_id), "custom:artisan_id": str(sp_id)}, False
        )
        return {
            "status_code": 200,
            "message": "Artisan created successfully",
            "data": sp_id,
        }
    except Exception as e:
        return {"status_code": 500, "message": f"Error: {e}"}


async def update_artisan_status(request_dict, db):
    try:
        account_id = request_dict.get("account_id")
        query = select(ServiceProvider).filter(ServiceProvider.id == account_id)
        result = await db.execute(query)
        get_sp = result.scalars().first()
        setattr(get_sp, "is_confirmed", True)
        await db.commit()
        await db.refresh(get_sp)
        return {
            "status_code": 200,
            "message": "Service Provider status updated successfully",
        }
    except Exception as e:
        return {"status_code": 500, "message": f"Error: {e}"}


@router.put("/soft-delete-artisan")
async def soft_delete_artisan(
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Soft deletes a user by marking them as inactive in the database.
    The user remains in the database but cannot log in.

    Args:
        username: The username/email of the user to soft delete

    Returns:
        Success message
    """
    try:
        # Verify Admin Identity
        resp = await get_id_header(Authorization)

        # Check if resp is a dictionary or has status_code
        if hasattr(resp, "status_code") and resp.status_code == 200:
            admin_resp_json = resp.json()
            id = admin_resp_json.get("account_id")
        elif isinstance(resp, dict) and "id" in resp:
            id = resp.get("account_id")
        else:
            error_message = (
                resp if isinstance(resp, dict) else {"error": "Authentication failed"}
            )
            return ErrorResponse(status_code=401, message=error_message)

        # Check if user is a service provider
        query = select(ServiceProvider).filter(ServiceProvider.id == id)
        result = await db.execute(query)
        sp_obj = result.scalars().first()

        if sp_obj:
            # Mark as inactive instead of deleting
            sp_obj.is_active = False
            disable_response = await disable_cognito_user(sp_obj.phone_number)
            if disable_response is None:
                return ErrorResponse(
                    status_code=400,
                    message="Failed to disable Cognito user, no user in cognito",
                )
            await db.commit()
            return StandardResponse(
                status_code=200,
                message=f"Service provider {id} deactivated successfully",
            )

        # If we got here, the user was not found in our database
        return ErrorResponse(
            status_code=404, message=f"User {id} not found in database"
        )

    except Exception as e:
        import traceback

        print(traceback.format_exc())  # Print full traceback for debugging
        return ErrorResponse(
            status_code=500, message=f"Error deactivating user: {str(e)}"
        )


@router.get("/sp-export")
async def export_sp(
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        # Authenticate User
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message=resp.json())

        # Select only required fields
        selected_columns = [
            ServiceProvider.first_name,
            ServiceProvider.last_name,
            ServiceProvider.email,
            ServiceProvider.gender,
            ServiceProvider.phone_number,
            ServiceProvider.primary_location,
            ServiceProvider.skill,
            ServiceProvider.status,
            ServiceProvider.created_at
        ]

        # Initialize Query - Get all service providers
        query = select(*selected_columns).select_from(ServiceProvider).order_by(ServiceProvider.created_at.desc())
        
        # Execute Query
        result = await db.execute(query)
        sps = result.mappings().all()  # Converts ORM objects to dictionaries

        # Convert to JSON Format
        json_data = jsonable_encoder(sps)

        # Convert to DataFrame
        df = pd.DataFrame(json_data)

        # Rename columns for better readability
        df = df.rename(columns={
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'email': 'Email',
            'gender': 'Gender',
            'phone_number': 'Phone Number',
            'primary_location': 'Primary Location',
            'skill': 'Skill',
            'status': 'Status',
            'created_at': 'Created At'
        })

        # Format the created_at column
        df['Created At'] = pd.to_datetime(df['Created At']).dt.strftime('%Y-%m-%d %H:%M:%S')

        # Create Excel in memory
        output = BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, sheet_name='Service Providers')
            
            # Get the xlsxwriter workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Service Providers']
            
            # Add some formatting
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })
            
            # Write the column headers with the defined format
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)
            
            # Auto-adjust columns' width
            for idx, col in enumerate(df):
                series = df[col]
                max_len = max((
                    series.astype(str).map(len).max(),
                    len(str(series.name))
                )) + 1
                worksheet.set_column(idx, idx, max_len)
        
        output.seek(0)
        
        return StreamingResponse(
            output,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": "attachment; filename=service_providers.xlsx"
            }
        )

    except Exception as e:
        tb = traceback.extract_tb(sys.exc_info()[2])[-1]
        logger.error(
            f"Unexpected error: {str(e)} | "
            f"File: {tb.filename}, Line: {tb.lineno}, "
            f"Function: {tb.name}, Code: {tb.line}",
            exc_info=True
        )
        return ErrorResponse(status_code=500, message="Service providers export failed", error=f"Error: {e}")
