from uuid import UUID
from typing import Optional
from fastapi import Depends, APIRouter, Form, File, UploadFile
from app.s3_upload import validate_image, upload_file_sync
from sqlalchemy.future import select
import app.kyc_details.schemas as sc
import app.models as md
from app.models_enum import KYCTypes, ValidationStatus
from sqlalchemy.orm import Session
# from fastapi.responses import JSONResponse
from app.response_models import (
    StandardResponse,
    StandardResponseWithoutSerialize,
    ErrorResponse
)
from app.database import get_db
from app.kyc_details.db_query import (
    db_get_kys_detail_user,
    db_get_kyc_details
)
from app.auth import permission_checker
from fastapi.responses import JSONResponse

router = APIRouter(tags=["KycDketails"])


@router.post("/kyc-details-create", response_model=StandardResponse)
def create_kyc_details(
    user_id: str = Form(...),
    kyc_type: KYCTypes = Form(...),  
    document: UploadFile = File(...),  
    expired: Optional[str] = Form(...),
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        result = db.execute(select(md.ArtisanDetail).where(md.ArtisanDetail.user_id == user_id))
        artisan = result.scalars().first()

        if not artisan:
            return ErrorResponse(status_code=404, message="Artisan not found")

        validate_image(document)
        document_url = upload_file_sync(document, folder="artisan/kyc-details")

        kyc_data = md.KycDetails(
            user_id=user_id,
            KYC_type=kyc_type.value,
            document=document_url,
            validation_status=ValidationStatus.PENDING,
            expired=expired
        )
        db.add(kyc_data)
        db.commit()
        db.refresh(kyc_data)

        return StandardResponse(
            status_code=201,
            status=True,
            message="KYC details created successfully",
            data={"kyc_id": str(kyc_data.id), "document_url": document_url, "kyc_type": kyc_type.value}
        )
    except Exception as e:
        db.rollback()
        return ErrorResponse(
            status=False,
            status_code=500,
            message="Unable to process your request",
            error=str(e)
        )


@router.put("/kyc-details-update/{kyc_id}", response_model=StandardResponse)
def update_kyc_details(
    kyc_id: str,
    kyc_type: Optional[KYCTypes] = Form(None),
    expired: Optional[str] = Form(None),
    document: Optional[UploadFile] = File(None),
    validation_status: Optional[ValidationStatus] = Form(None),
    validation_comments: Optional[str] = Form(None),
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        kyc_info = db.query(md.KycDetails).filter(md.KycDetails.id == kyc_id).first()
        if not kyc_info:
            return ErrorResponse(status_code=404, message="KYC Details not found")

        # Document upload
        if document:
            validate_image(document)
            document_url = upload_file_sync(document, folder="artisan/kyc-details")
            kyc_info.document = document_url

        if kyc_type:
            kyc_info.KYC_type = kyc_type.value
        if expired:
            kyc_info.expired = expired
        if validation_status is not None:
            kyc_info.validation_status = validation_status.value
        if validation_comments is not None:
            kyc_info.validation_comments = validation_comments

        db.commit()
        db.refresh(kyc_info)

        return StandardResponse(
            status=True,
            status_code=200,
            message="KYC details updated successfully",
            data={
                "kyc_id": str(kyc_info.id),
                "kyc_type": kyc_info.KYC_type,
                "document_url": kyc_info.document,
                "validation_status": kyc_info.validation_status,
                "validation_comments": kyc_info.validation_comments,
                "expired": kyc_info.expired,
            }
        )

    except Exception as e:
        db.rollback()
        return ErrorResponse(
            status=False,
            status_code=500,
            message="Failed to update KYC details",
            error=str(e),
        )


@router.get("/kyc-details/{user_id}")
def kyc_details(user_id: UUID, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        kyc_info = db_get_kys_detail_user(user_id, db)
        if not kyc_info:
            return StandardResponse(status=False, status_code=404, message="user id not found")
        res = {
            "kyc_id": kyc_info.id,
            "kyc_type": kyc_info.KYC_type,
            "document": kyc_info.document,
            "validation_status": kyc_info.validation_status
        }
        return StandardResponse(status=True, status_code=200, data={"result":res}, message="user kyc details fetched sucessfully")
    except Exception as e:
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")
    

@router.get("/kyc-info")
def get_kyc_details(page_no:int, page_size:int, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        kyc= db_get_kyc_details(db)
        if not kyc:
            return StandardResponse(status=False, status_code=404, message="kyc details not found")
        total_rec = len(kyc)
        skip = (page_no - 1) * page_size
        limit = page_no * page_size
        result = [
            {
                "kyc_id": kyc_info.id,
                "kyc_type": kyc_info.KYC_type,
                "document": kyc_info.document,
                "validation_status": kyc_info.validation_status
            }
            for kyc_info in kyc[skip:limit]
        ]
        return StandardResponse(status=True, status_code=200, data={"result": result, "page_no": page_no, "page_size": page_size, "total_records": total_rec}, message="kyc details fetched sucessfully")
    except Exception as e:
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")
    
@router.delete("/delete-kyc/{user_id}")
def delete_kyc_details(user_id: UUID, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        kyc_info = db_get_kys_detail_user(user_id, db)
        if not kyc_info:
            return StandardResponse(status=False, status_code=404, message="user id not found")
        db.delete(kyc_info)
        db.commit()
        return StandardResponse(status=True, status_code=200, message="user kyc details deleted sucessfully")
    except Exception as e:
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")