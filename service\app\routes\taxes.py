import uuid
from fastapi import API<PERSON><PERSON><PERSON>, Depen<PERSON>, Header, Request
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from app.schemas import tax as schemas
from app.database import get_db
from app.utils.crud import get_record_by_id, create_record, delete_record, update_record
from app.utils.auth import permission_checker
from typing import Optional, Annotated
from sqlalchemy.future import select
from sqlalchemy import func
from app.models import Tax, ServiceProvider
from app.schemas.helper import StandardResponse, ErrorResponse



router = APIRouter(tags=["Taxes"])


@router.post("/taxes-create")
async def create_tax(
    request: schemas.CreateTax,
    db: Session = Depends(get_db),
    user = Depends(permission_checker),
):
    """
    Create a new tax for a service provider
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
            
        # Get authenticated user info
        # user_id = user.get("user_id")
        # role_name = user.get("role_name")
        
        # Validate service provider exists
        service_provider = get_record_by_id(db, ServiceProvider, request.sp_id)
        if isinstance(service_provider, str):
            return ErrorResponse(
                status_code=404,
                message="Service provider not found"
            )

        request_dict = request.model_dump()
        new_tax = create_record(db, Tax, request_dict)
        
        if isinstance(new_tax, str):
            return ErrorResponse(
                status_code=400,
                message=new_tax
            )
        
        return StandardResponse(
            status_code=201,
            message="Tax created successfully",
            data={"tax_id": str(new_tax.id)}
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="tax creation failed", error=str(e)
        )


@router.get("/taxes-read/{id}")
async def read_tax(
    id: str,
    db: Session = Depends(get_db),
    user = Depends(permission_checker),
):
    """
    Get a specific tax by ID
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
            
        tax_obj = get_record_by_id(db, Tax, uuid.UUID(id))
        if isinstance(tax_obj, str):
            return ErrorResponse(status_code=404, message="Tax not found")
        
        return StandardResponse(
            status_code=200,
            message="Tax read successfully",
            data=tax_obj
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="tax reading failed", error=str(e)
        )


@router.get("/taxes-list")
async def list_taxes(
    sp_id: Optional[str] = None,
    tax_type: Optional[str] = None,
    name: Optional[str] = None,
    skip: int = 1,
    limit: int = 10,
    db: Session = Depends(get_db),
    user = Depends(permission_checker),
):
    """
    Get list of taxes with optional filtering by service provider ID and tax type
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user

        # Build query with filters
        query = select(Tax).filter(Tax.is_active == True)
        count_query = select(func.count()).select_from(Tax).filter(Tax.is_active == True)

        if sp_id:
            query = query.filter(Tax.sp_id == uuid.UUID(sp_id))
            count_query = count_query.filter(Tax.sp_id == uuid.UUID(sp_id))

        if tax_type:
            query = query.filter(Tax.type == tax_type)
            count_query = count_query.filter(Tax.type == tax_type)

        if name:
            query = query.filter(Tax.name == name)
            count_query = count_query.filter(Tax.name == name)

        # Apply pagination
        adjusted_skip = (skip - 1) * limit
        query = query.offset(adjusted_skip).limit(limit)
        
        # Execute queries
        result = db.execute(query)
        taxes = result.scalars().all()

        total_result = db.execute(count_query)
        total_count = total_result.scalar()

        return StandardResponse(
            status_code=200,
            message="Taxes retrieved successfully",
            data={
                "taxes": taxes,
                "page": skip,
                "limit": limit,
                "total": total_count
            }
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="tax listing failed", error=str(e)
        )


@router.get("/taxes-sp/{sp_id}")
async def get_taxes_by_service_provider(
    sp_id: str,
    db: Session = Depends(get_db),
    user = Depends(permission_checker),
):
    """
    Get all taxes for a specific service provider
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user


        # Validate service provider exists
        service_provider = get_record_by_id(db, ServiceProvider, uuid.UUID(sp_id))
        if isinstance(service_provider, str):
            return ErrorResponse(
                status_code=404,
                message="Service provider not found"
            )

        query = select(Tax).filter(
            and_(Tax.sp_id == uuid.UUID(sp_id), Tax.is_active == True)
        )
        result = db.execute(query)
        taxes = result.scalars().all()

        return StandardResponse(
            status_code=200,
            message="Service provider taxes retrieved successfully",
            data=taxes
        )

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="tax getting failed", error=str(e)
        )


@router.put("/taxes-update/{id}")
async def update_tax(
    id: str,
    data: schemas.UpdateTax,
    db: Session = Depends(get_db),
    user = Depends(permission_checker),
):
    """
    Update a specific tax
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        

        get_tax = get_record_by_id(db, Tax, uuid.UUID(id))
        if isinstance(get_tax, str):
            return ErrorResponse(status_code=404, message="Tax not found")

        res = update_record(db, data, get_tax)
        if isinstance(res, str):
            return ErrorResponse(status_code=400, message=res)
        
        return StandardResponse(
            status_code=200,
            message="Tax updated successfully",
            data=res
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="tax updating failed", error=str(e)
        )


@router.delete("/taxes-delete/{id}")
async def delete_tax(
    id: str,
    db: Session = Depends(get_db),
    user = Depends(permission_checker),
):
    """
    Delete a specific tax (soft delete by setting is_active to False)
    """
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
            
        # Get authenticated user info
        # user_id = user.get("user_id")
        # role_name = user.get("role_name")

        get_tax = get_record_by_id(db, Tax, uuid.UUID(id))
        if isinstance(get_tax, str):
            return ErrorResponse(status_code=404, message="Tax not found")

        res = delete_record(db, get_tax)
        if isinstance(res, str):
            return ErrorResponse(status_code=400, message=res)
        
        return StandardResponse(
            status_code=200,
            message="Tax deleted successfully",
            data={"deleted_tax_id": id}
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="tax deleting failed", error=str(e)
        )


