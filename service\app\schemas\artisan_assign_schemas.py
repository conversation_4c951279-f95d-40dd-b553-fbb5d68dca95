from uuid import UUID
from pydantic import BaseModel, UUID4, Field
from typing import Optional, Any,List
from datetime import datetime
from fastapi import Form
from app.models_enum import BookingStatus,ArtisanAssignStatus



class ArtisanAssignedBase(BaseModel):
    invoice_item_id: Optional[UUID] = None
    invoice_id: Optional[UUID] = None
    service_id:Optional[UUID] = None
    artisan_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    start_otp: Optional[str] = None
    end_otp: Optional[str] = None
    service_start: Optional[datetime] = None
    service_end: Optional[datetime] = None
    service_start_artisan_latitude: Optional[float] = None
    service_start_artisan_longitude: Optional[float] = None
    service_end_artisan_latitude: Optional[float] = None
    service_end_artisan_longitude: Optional[float] = None
    status: Optional[str] = None

    class Config:
        from_attributes = True


class ArtisanAssignedCreate(ArtisanAssignedBase):
    pass

class ArtisanAssignedUpdate(ArtisanAssignedBase):
    pass

class ArtisanAssignedResponse(ArtisanAssignedBase):
    id: UUID