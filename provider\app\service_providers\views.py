from uuid import UUID
from fastapi import Depends, APIRouter, Form, File, UploadFile, Query
import app.service_providers.schemas as sc
import app.models as md
from sqlalchemy.orm import Session
from fastapi.responses import JSONResponse
from app.database import get_db
# from app.response_models import (
#     StandardResponse,
#     StandardResponseWithoutSerialize,
#     ErrorResponse
# )
from app.service_providers.qb_query import (
    db_get_phone_and_email_info,
    db_get_service_provider_info,
    db_get_service_providers,
)
from app.auth import permission_checker
# from fastapi.responses import JSONResponse

# from fastapi import APIRouter, Depends, File, UploadFile, Query
from app.helper import StandardResponse, ErrorResponse
from sqlalchemy.future import select
from app.models import ServiceProviders , BusinessServiceMapping, BusinessArea, Services, UserProfiles
# from app.models_enum import BusinessType, BusinessProviderStatus
import uuid
from app.s3_upload import (
    upload_file_direct,
    s3_delete_file,
    S3_IMAGES_FOLDER,
    S3_DOCS_FOLDER
)
from sqlalchemy import delete, or_, func , and_
from app.service_providers.utills import (
    create_record,
    update_record
)
from typing import Optional, List, Union
from app.service_providers.schemas import UpdateBusiness, BusinessResponse, BusinessAreaCreate, BusinessAreaUpdate
from app.image_validator import validate_image_sync
import json
import logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

router = APIRouter(tags=["ServiceProvider-business"])


@router.put("/business-onboarding")
async def update_business(
    data: UpdateBusiness = Depends(),
    portfolio: List[UploadFile] = File(None),
    business_logo: Optional[UploadFile] = File(None),
    business_registration_document: Optional[UploadFile] = File(None),
    id_proof: Optional[UploadFile] = File(None),
    signature: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        account_id = user.get("user_id")
        role = user.get("role_name")

        if not account_id or not role:
            return ErrorResponse(status_code=401, message="Account ID and role are required")

        # check the account id in user profiles table
        query = select(UserProfiles).filter(UserProfiles.id == account_id)
        result = db.execute(query)
        user_profile = result.scalars().first()
        if user_profile is None:
            return ErrorResponse(status_code=404, message="User profile not found")

        # Update UserProfiles table fields
        user_profile_updated = False
        if data.first_name:
            user_profile.first_name = data.first_name
            user_profile_updated = True
        if data.last_name:
            user_profile.last_name = data.last_name
            user_profile_updated = True
        if data.email:
            user_profile.email = data.email
            user_profile_updated = True
            
        # Commit user profile changes if any were made
        if user_profile_updated:
            db.commit()
            
        services_offered = data.__dict__.pop("services_offered", None)
        query = select(ServiceProviders).filter(ServiceProviders.user_id == account_id)
        result = db.execute(query)
        get_business = result.scalars().first()
        if get_business is None:
            return ErrorResponse(
                status_code=404, message="Business Not found", error=None
            )

        # business_id = get_business.id
        # print(business_id, "business_id", data.__dict__)
        # Handle file uploads
        supporting_docs = []
        if portfolio:
            # Delete existing portfolio images if they exist
            if get_business.portfolio_image:
                try:
                    existing_images = get_business.portfolio_image.split(",")
                    for image in existing_images:
                        if image.strip():
                            try:
                                s3_delete_file(image.strip())
                            except Exception as e:
                                logger.warning(f"Failed to delete existing portfolio image {image}: {str(e)}")
                except Exception as e:
                    logger.warning(f"Failed to parse existing portfolio images: {str(e)}")

            # Upload new portfolio images
            for attachment in portfolio:
                if attachment is not None and attachment.filename:
                    # Generate a unique filename
                    # file_ext = attachment.filename.split(".")[-1]
                    # unique_filename = f"{uuid.uuid4()}.{file_ext}"

                    # Upload to S3 or your storage service
                    file_url = upload_file_direct(attachment, S3_IMAGES_FOLDER)
                    print(file_url["filename"])
                    supporting_docs.append(file_url["filename"])

        if supporting_docs:
            get_business.portfolio_image = ",".join(supporting_docs)
            logger.info(f"Updated business with {len(supporting_docs)} portfolio images")


        if business_logo:
            # Validate business logo
            try:
                is_valid, error_message = validate_image_sync(business_logo)
                if not is_valid:
                    logger.error(f"Image validation failed: {error_message}")
                    return ErrorResponse(
                        status_code=400,
                        message=error_message,
                        error="Business logo validation failed"   
                    )
            except Exception as e:
                logger.error(f"Error validating image: {str(e)}", exc_info=True)
                return ErrorResponse(
                    status_code=400,
                    message="Failed to validate image",
                    error=str(e)
                )

            # Delete existing business logo
            if get_business.business_logo:
                try:
                    s3_delete_file(get_business.business_logo)
                except Exception as e:
                    logger.warning(f"Failed to delete existing business logo: {str(e)}")
            
            # Upload original image
            logo_url = upload_file_direct(business_logo, path=S3_IMAGES_FOLDER)
            if "message" in logo_url:
                return ErrorResponse(
                    status_code=400,
                    message="Failed to upload image",
                    error=f"Failed to upload image: {logo_url['message']}"
                )

            # Update business record with logo
            setattr(get_business, "business_logo", logo_url["filename"])

        if business_registration_document:
            # Validate business registration document
            try:
                is_valid, error_message = validate_image_sync(business_registration_document)
                if not is_valid:
                    logger.error(f"Image validation failed: {error_message}")
                    return ErrorResponse(
                        status_code=400,
                        message=error_message,
                        error="Business registration document validation failed"   
                    )
            except Exception as e:
                logger.error(f"Error validating image: {str(e)}", exc_info=True)
                return ErrorResponse(
                    status_code=400,
                    message="Failed to validate image",
                    error=str(e)
                )

            if get_business.business_registration_document:
                # Delete existing document
                try:
                    s3_delete_file(get_business.business_registration_document)
                except Exception as e:
                    logger.warning(f"Failed to delete existing business registration document: {str(e)}")
            # Calling S3 upload function
            doc_url = upload_file_direct(business_registration_document, path=S3_DOCS_FOLDER)
            setattr(get_business, "business_registration_document", doc_url["filename"])

        if id_proof:
            # Validate ID proof
            try:
                is_valid, error_message = validate_image_sync(id_proof)
                if not is_valid:
                    logger.error(f"Image validation failed: {error_message}")
                    return ErrorResponse(
                        status_code=400,
                        message=error_message,
                        error="ID proof validation failed"   
                    )
            except Exception as e:
                logger.error(f"Error validating image: {str(e)}", exc_info=True)
                return ErrorResponse(
                    status_code=400,
                    message="Failed to validate image",
                    error=str(e)
                )

            if get_business.id_proof:
                # Delete existing id_proof
                try:
                    s3_delete_file(get_business.id_proof)
                except Exception as e:
                    logger.warning(f"Failed to delete existing ID proof: {str(e)}")
            # Calling S3 upload function
            id_proof_url = upload_file_direct(id_proof, path=S3_DOCS_FOLDER)
            setattr(get_business, "id_proof", id_proof_url["filename"])
            

        if signature:
            # Validate signature
            try:
                is_valid, error_message = validate_image_sync(signature)
                if not is_valid:
                    logger.error(f"Signature validation failed: {error_message}")
                    return ErrorResponse(
                        status_code=400,
                        message=error_message,
                        error="Signature validation failed"   
                    )
            except Exception as e:
                logger.error(f"Error validating signature: {str(e)}", exc_info=True)
                return ErrorResponse(
                    status_code=400,
                    message="Failed to validate signature",
                    error=str(e)
                )

            if get_business.signature:
                # Delete existing signature
                try:
                    s3_delete_file(get_business.signature)
                except Exception as e:
                    logger.warning(f"Failed to delete existing signature: {str(e)}")
            
            # Upload signature
            signature_url = upload_file_direct(signature, path=S3_DOCS_FOLDER)
            if "message" in signature_url:
                return ErrorResponse(
                    status_code=400,
                    message="Failed to upload signature",
                    error=f"Failed to upload signature: {signature_url['message']}"
                )

            # Update business record with signature
            setattr(get_business, "signature", signature_url["filename"])

        # Creating services list
        if services_offered:
            # Removing existing services before adding new ones
            stmt = delete(BusinessServiceMapping).where(
                BusinessServiceMapping.business_user_id == account_id
            )

            db.execute(stmt)
            db.commit()
            
            # Just validate each UUID and create mappings
            for service_id in services_offered:
                if service_id:
                    try:
                        # Validate UUID format
                        uuid.UUID(service_id)
                        val = {"business_user_id": account_id, "service_id": service_id, "service_provider_id": get_business.id}
                        create_record(db, BusinessServiceMapping, val)
                    except ValueError:
                        logger.error(f"Invalid UUID format: {service_id}")
                        continue

        # Update ServiceProviders table with remaining fields
        res = update_record(db, data, get_business)
        
        # Final commit to ensure all changes are saved
        db.commit()
        
        return StandardResponse(
            status_code=200, message="Business registration completed and currently under review" , data={
                "user_id": str(get_business.user_id),
            }
        )
    except Exception as e:
        db.rollback()
        return ErrorResponse(status_code=500, message="Business updation failed", error=str(e))


@router.get("/business-list")
async def get_all_businesses(
    page: int = Query(1),
    limit: int = Query(10),
    user_id: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    business_type: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    try:
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user
        # account_id = user.get("user_id")
        # role = user.get("role_name")

        # if not account_id or not role:
        #     return ErrorResponse(status_code=401, message="Account ID and role are required")
        
        # Build the base query with join to UserProfiles
        query = select(ServiceProviders, UserProfiles).join(
            UserProfiles, ServiceProviders.user_id == UserProfiles.id
        )
        if user_id:
            try:
                business_uuid = uuid.UUID(user_id)
                query = query.filter(ServiceProviders.user_id == business_uuid)
            except ValueError:
                return ErrorResponse(
                    status_code=400,
                    message="Invalid business ID format",
                    error="Business ID must be a valid UUID"
                )
        # Apply filters
        if status:
            query = query.filter(ServiceProviders.status == status)
        
        if business_type:
            query = query.filter(ServiceProviders.business_type == business_type)
        
        if search:
            search_filter = or_(
                ServiceProviders.business_name.ilike(f"%{search}%"),
                # ServiceProviders.business_registration_number.ilike(f"%{search}%"),
                # ServiceProviders.ghana_post_gps_address.ilike(f"%{search}%"),
                # ServiceProviders.business_location.ilike(f"%{search}%"),
                # ServiceProviders.tax_identification_number.ilike(f"%{search}%"),
                UserProfiles.first_name.ilike(f"%{search}%"),
                UserProfiles.last_name.ilike(f"%{search}%"),
                UserProfiles.email.ilike(f"%{search}%"),
                UserProfiles.phone_number.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # Get total count for pagination
        count_query = select(func.count()).select_from(query.subquery())
        total_count = db.execute(count_query)
        total = total_count.scalar()
        
        # Apply pagination
        offset = (page - 1) * limit
        query = query.offset(offset).limit(limit).order_by(ServiceProviders.created_at.desc())
        
        # Execute query
        result = db.execute(query)
        business_user_pairs = result.all()
        
        # Convert to BusinessResponse objects
        business_list = []
        for business, user_profile in business_user_pairs:
            # Get services for this business
            services_query = select(BusinessServiceMapping.service_id).filter(
                BusinessServiceMapping.business_user_id== business.user_id
            )
            services_result = db.execute(services_query)
            service_ids = [row.service_id for row in services_result]
            portfolio_images = []
            if business.portfolio_image:
                try:
                    # Handle comma separated string format
                    if isinstance(business.portfolio_image, str):
                        portfolio_images = [img.strip() for img in business.portfolio_image.split(",") if img.strip()]
                    elif isinstance(business.portfolio_image, list):
                        portfolio_images = business.portfolio_image
                    else:
                        portfolio_images = []
                except Exception:
                    logger.warning(f"Failed to parse portfolio_image for business {business.id}")
                    portfolio_images = []
            business_response = BusinessResponse(
                id=business.id,
                user_id=str(business.user_id),  # Convert UUID to string
                first_name=user_profile.first_name,
                last_name=user_profile.last_name,
                full_name=f"{user_profile.first_name} {user_profile.last_name}",
                email=user_profile.email,
                country_code=user_profile.country_code,
                phone_number=user_profile.phone_number,
                business_name=business.business_name,
                business_type=business.business_type,
                business_registration_number=business.business_registration_number,
                ghana_post_gps_address=business.ghana_post_gps_address,
                business_location=business.business_location,
                tax_identification_number=business.tax_identification_number,
                services_offered=service_ids,  # Add services to response
                service_area_ids=business.service_area_ids,
                business_registration_document=business.business_registration_document,
                business_logo=business.business_logo,
                portfolio_image=portfolio_images,
                id_proof=business.id_proof,
                signature=business.signature,
                submit_date=business.submit_date,
                page_position=business.page_position,
                status=business.status,
                # reason=business.reason,
                created_at=business.created_at,

            )
            business_list.append(business_response)
        
        # Calculate pagination info
        total_pages = (total + limit - 1) // limit

        
        return StandardResponse(
            status_code=200,
            message="Businesses list retrieved successfully",
            data={
                "businesses": business_list,
                "pagination": {
                    "current_page": page,
                    "total_pages": total_pages,
                    "total_items": total,
                    "items_per_page": limit


                },
            }
        )
    except Exception as e:
        logger.error(f"Error retrieving businesses: {str(e)}", exc_info=True)
        return ErrorResponse(
            status_code=500,
            message="Failed to retrieve businesses",
            error=str(e)
        )



@router.get("/business-details/{business_id}")
async def get_business_by_id(
    business_id: str,
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    """Get a specific business by ID with full details"""
    try:
 
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user
        
        # Convert string to UUID
        try:
            business_uuid = uuid.UUID(business_id)
        except ValueError:
            return ErrorResponse(
                status_code=400,
                message="Invalid business ID format",
                error="Business ID must be a valid UUID"
            )
        
        # Get business with user profile join
        result = db.execute(
            select(ServiceProviders, UserProfiles).join(
                UserProfiles, ServiceProviders.user_id == UserProfiles.id
            ).filter(ServiceProviders.id == business_uuid)
        )
        business_user_pair = result.first()
        
        if not business_user_pair:
            return ErrorResponse(
                status_code=404,
                message="Business not found",
                error="Business ID not found"
            )
        
        business, user_profile = business_user_pair
        
        # Get service mappings for this business
        service_query = select(BusinessServiceMapping).filter(
            BusinessServiceMapping.business_user_id == business.user_id
        )
        service_result = db.execute(service_query)
        service_mappings = service_result.scalars().all()
        
        # Get service details with names
        service_details = []
        for mapping in service_mappings:
            # Get service name from services table
            service_query = select(Services).filter(Services.id == mapping.service_id)
            service_result = db.execute(service_query)
            service = service_result.scalars().first()
            if service:
                service_details.append({
                    "id": str(service.id),
                    "name": service.name
                })
        
        # Get business areas if service_area_ids exist
        service_area_details = []
        if business.service_area_ids:
            try:
                # Ensure service_area_ids is a list of valid UUIDs
                area_ids = business.service_area_ids
                if isinstance(area_ids, list) and area_ids:
                    # Convert string UUIDs to UUID objects if needed
                    valid_area_ids = []
                    for area_id in area_ids:
                        try:
                            if isinstance(area_id, str):
                                valid_area_ids.append(uuid.UUID(area_id))
                            else:
                                valid_area_ids.append(area_id)
                        except (ValueError, TypeError):
                            logger.warning(f"Invalid area ID format: {area_id}")
                            continue
                    
                    if valid_area_ids:
                        area_query = select(BusinessArea).filter(
                            BusinessArea.id.in_(valid_area_ids)
                        )
                        area_result = db.execute(area_query)
                        business_areas = area_result.scalars().all()
                        
                        # Create area objects with ID and name
                        for area in business_areas:
                            service_area_details.append({
                                "id": str(area.id),
                                "name": area.area_name
                            })
            except Exception as e:
                logger.error(f"Error retrieving business areas: {str(e)}")

        portfolio_images = []
        if business.portfolio_image:
            try:
                # Handle comma separated string format
                if isinstance(business.portfolio_image, str):
                    portfolio_images = [img.strip() for img in business.portfolio_image.split(",") if img.strip()]
                elif isinstance(business.portfolio_image, list):
                    portfolio_images = business.portfolio_image
                else:
                    portfolio_images = []
            except Exception:
                logger.warning(f"Failed to parse portfolio_image for business {business.id}")
                portfolio_images = []
        business_data = {
            "id": str(business.id),
            "user_id": str(business.user_id),
            "first_name": user_profile.first_name,
            "last_name": user_profile.last_name,
            "full_name": f"{user_profile.first_name} {user_profile.last_name}",
            "email": user_profile.email,
            "country_code": user_profile.country_code,
            "phone_number": user_profile.phone_number,
            "business_name": business.business_name,
            "business_type": business.business_type,
            "business_registration_number": business.business_registration_number,
            "ghana_post_gps_address": business.ghana_post_gps_address,
            "business_location": business.business_location,
            "tax_identification_number": business.tax_identification_number,
            "business_registration_document": business.business_registration_document,
            "business_logo": business.business_logo,
            "portfolio_image": portfolio_images,
            "id_proof": business.id_proof,
            "signature": business.signature,
            "submit_date": business.submit_date,
            "page_position": business.page_position,
            "status": business.status,
            "reason": business.reason,
            "services_offered": service_details,
            "service_area_ids": service_area_details
        }
        
        return StandardResponse(
            status_code=200,
            message="Business details retrieved successfully",
            data=business_data
        )
    except Exception as e:
        logger.error(f"Error retrieving business: {str(e)}", exc_info=True)
        return ErrorResponse(
            status_code=500,
            message="Failed to retrieve business",
            error=str(e)
        )


@router.post("/business-area")
def create_business_area(
    data: BusinessAreaCreate,
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    """Create a new business area"""
    try:

        # if isinstance(user, JSONResponse):
        #     return user
        # Validate area name
        if not data.area_name or not data.area_name.strip():
            return ErrorResponse(
                status_code=400,
                message="Area name cannot be empty",
                error="Area name is required"
            )
        
        # Check if area name already exists
        existing_area = db.execute(
            select(BusinessArea).filter(BusinessArea.area_name == data.area_name.strip())
        )
        if existing_area.scalars().first():
            return ErrorResponse(
                status_code=400,
                message="Business area with this name already exists",
                error="Duplicate area name"
            )
        
        # Create new business area
        business_area_data = {"area_name": data.area_name.strip()}
        result = create_record(db, BusinessArea, business_area_data)
        
        return StandardResponse(
            status_code=201,
            message="Business area created successfully"
        
        )
    except Exception as e:
        logger.error(f"Error creating business area: {str(e)}", exc_info=True)
        return ErrorResponse(
            status_code=500,
            message="Failed to create business area",
            error=str(e)
        )


@router.get("/business-area")
def get_all_business_areas(
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    """Get all business areas"""
    try:
        # if isinstance(user, JSONResponse):  # Permission failed
        #     return user
        
        result = db.execute(select(BusinessArea))
        business_areas = result.scalars().all()
        
        return StandardResponse(
            status_code=200,
            message="Business areas retrieved successfully",
            data=business_areas
        )
    except Exception as e:
        logger.error(f"Error retrieving business areas: {str(e)}", exc_info=True)
        return ErrorResponse(
            status_code=500,
            message="Failed to retrieve business areas",
            error=str(e)
        )


@router.put("/business-area/{area_id}")
def update_business_area(
    area_id: str,
    data: BusinessAreaUpdate,
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    """Update a business area"""
    try:

        if isinstance(user, JSONResponse):  # Permission failed
            return user
        # Convert string to UUID
        try:
            area_uuid = uuid.UUID(area_id)
        except ValueError:
            return ErrorResponse(
                status_code=400,
                message="Invalid area ID format",
                error="Area ID must be a valid UUID"
            )
        
        # Check if business area exists
        result = db.execute(
            select(BusinessArea).filter(BusinessArea.id == area_uuid)
        )
        business_area = result.scalars().first()
        
        if not business_area:
            return ErrorResponse(
                status_code=404,
                message="Business area not found",
                error="Area ID not found"
            )
        
        # Check if any fields are provided for update
        update_data = {k: v for k, v in data.dict().items() if v is not None}
        if not update_data:
            return ErrorResponse(
                status_code=400,
                message="No fields provided for update",
                error="At least one field must be provided"
            )
        
        # Check if new area name already exists (if updating name)
        if data.area_name and data.area_name != business_area.area_name:
            existing_area = db.execute(
                select(BusinessArea).filter(
                    BusinessArea.area_name == data.area_name,
                    BusinessArea.id != area_uuid
                )
            )
            if existing_area.scalars().first():
                return ErrorResponse(
                    status_code=400,
                    message="Business area with this name already exists",
                    error="Duplicate area name"
                )
        
        # Update business area directly
        for key, value in update_data.items():
            setattr(business_area, key, value)
        
        db.commit()
        db.refresh(business_area)
        
        return StandardResponse(
            status_code=200,
            message="Business area updated successfully",
            data=business_area
        )
    except Exception as e:
        logger.error(f"Error updating business area: {str(e)}", exc_info=True)
        return ErrorResponse(
            status_code=500,
            message="Failed to update business area",
            error=str(e)
        )


@router.delete("/business-area/{area_id}")
def delete_business_area(
    area_id: str,
    db: Session = Depends(get_db),
    user=Depends(permission_checker),
):
    """Delete a business area"""
    try:

        if isinstance(user, JSONResponse):  # Permission failed
            return user
        # Convert string to UUID
        try:
            area_uuid = uuid.UUID(area_id)
        except ValueError:
            return ErrorResponse(
                status_code=400,
                message="Invalid area ID format",
                error="Area ID must be a valid UUID"
            )
        
        # Check if business area exists
        result = db.execute(
            select(BusinessArea).filter(BusinessArea.id == area_uuid)
        )
        business_area = result.scalars().first()
        
        if not business_area:
            return ErrorResponse(
                status_code=404,
                message="Business area not found",
                error="Area ID not found"
            )
        
        # Delete business area
        db.delete(business_area)
        db.commit()
        
        return StandardResponse(
            status_code=200,
            message="Business area deleted successfully"
        )
    except Exception as e:
        logger.error(f"Error deleting business area: {str(e)}", exc_info=True)
        return ErrorResponse(
            status=False, status_code=500,
            message="Failed to delete business area",
            error=str(e)
        )











# @router.post("/service-provider-create")
# def create_service_providers(payload: sc.ServiceProviders, db: Session = Depends(get_db),user=Depends(permission_checker)):
#     try:
#         if isinstance(user, JSONResponse):  # Permission failed
#             return user
#         email_info, phn_info = db_get_phone_and_email_info(payload.business_email, payload.business_phone, db)
#         if email_info or phn_info:
#             return StandardResponse(status=False, status_code=400, message="email or phn already present with another business")
#         new_data = md.ServiceProviders(
#             Business_Name=payload.business_name,
#             Business_Phone=payload.business_phone,
#             Business_Email=payload.business_email,
#             Address=payload.adress
#         )
#         db.add(new_data)
#         db.commit()
#         return StandardResponse(status=True, status_code=200, message="service provider created sucessfully")
#     except Exception as e:
#         db.rollback()
#         return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")

# @router.put("/service-provider-update/{sp_id}")
# def update_service_providers(sp_id: UUID, payload: sc.UpdateServiceProviders, db: Session = Depends(get_db),user=Depends(permission_checker)):
#     try:
#         if isinstance(user, JSONResponse):  # Permission failed
#             return user
#         sp_info = db_get_service_provider_info(sp_id, db)
#         if not sp_info:
#             return StandardResponse(status=False, status_code=400, message="service provider id not found")
#         if payload.business_email:
#             sp_info.Business_Email = payload.business_email
#         if payload.business_name:
#             sp_info.Business_Name = payload.business_name
#         if payload.business_phone:
#             sp_info.Business_Phone = payload.business_phone
#         if payload.adress:
#             sp_info.Address = payload.adress
#         db.commit()
#         return StandardResponse(status=True, status_code=200, message="service provider details updated sucessfully")
#     except Exception as e:
#         db.rollback()
#         return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")
    

# @router.get("/get-service-provider/{sp_id}")
# def get_service_provider(sp_id: UUID, db: Session = Depends(get_db),user=Depends(permission_checker)):
#     try:
#         if isinstance(user, JSONResponse):  # Permission failed
#             return user
#         sp_info = db_get_service_provider_info(sp_id, db)
#         if not sp_info:
#             return StandardResponse(status=False, status_code=400, message="service provider id not found")
#         res = {
#             "sp_id": sp_info.id,
#             "business_email": sp_info.Business_Email,
#             "business_name": sp_info.Business_Name,
#             "business_phone": sp_info.Business_Phone,
#             "adress": sp_info.Address
#         }
#         return StandardResponse(status=True, status_code=200, data={"result":res}, message="service provider details fetched sucessfully")
#     except Exception as e:
#         return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")
    

# @router.get("/get-service-providers-list")
# def get_service_providers(page_no:int, page_size:int, db: Session = Depends(get_db),user=Depends(permission_checker)):
#     try:
#         if isinstance(user, JSONResponse):  # Permission failed
#             return user
#         sp_infos= db_get_service_providers(db)
#         if not sp_infos:
#             return StandardResponse(status=False, status_code=400, message="service provider id not found")
#         total_rec = len(sp_infos)
#         skip = (page_no - 1) * page_size
#         limit = page_no * page_size
#         result = [
#             {
#                 "sp_id": sp.id,
#                 "business_email": sp.Business_Email,
#                 "business_name": sp.Business_Name,
#                 "business_phone": sp.Business_Phone,
#                 "address": sp.Address,
#             }
#             for sp in sp_infos[skip:limit]
#         ]
#         return StandardResponse(status=True, status_code=200, data={"result": result, "page_no": page_no, "page_size": page_size, "total_records": total_rec}, message="service provider details fetched sucessfully")
#     except Exception as e:
#         return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")
    
# @router.delete("/delete-servise-provider/{sp_id}")
# def delete_service_provider(sp_id: UUID, db: Session = Depends(get_db),user=Depends(permission_checker)):
#     try:
#         if isinstance(user, JSONResponse):  # Permission failed
#             return user
#         sp_info = db_get_service_provider_info(sp_id, db)
#         if not sp_info:
#             return StandardResponse(status=False, status_code=400, message="service provider id not found")
#         db.delete(sp_info)
#         db.commit()
#         return StandardResponse(status=True, status_code=200, message="service provider details deleted sucessfully")
#     except Exception as e:
#         return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")