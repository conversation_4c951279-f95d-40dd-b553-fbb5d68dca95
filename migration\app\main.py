from fastapi import FastAPI
from contextlib import asynccontextmanager
from app.alembic_autogenerate import autogenerate_and_upgrade
import app.models as models
from app.database import engine

# Create tables in the database
models.Base.metadata.create_all(bind=engine)

@asynccontextmanager
async def lifespan(app: FastAPI):
    autogenerate_and_upgrade()
    yield

# Pass lifespan to FastAPI
# app = FastAPI(debug=True, lifespan=lifespan)
app = FastAPI(debug=True, lifespan=lifespan)

@app.get("/__health")
def read_root():
    return {"status": "online"}



