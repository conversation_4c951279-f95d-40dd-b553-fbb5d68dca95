import json
from app.email_template import render_email_template
from app.enums import NotificationType
from app.config import get_settings
import requests
import boto3
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.text import MIMEText
from jinja2 import Template
from pyfcm import FCMNotification
from fastapi import HTTPException

# Initialize FCM client
# push_service = FCMNotification(api_key=get_settings().FIREBASE_SERVER_KEY)
# try:

# except:
#     print('Fcm Connection failed...')


class StringEncoder:
    def __init__(self, data):
        self.data = data

    def encode(self):
        # Iterate over dictionary and convert all values to strings
        return {key: str(value) for key, value in self.data.items()}


async def send_notification(notification_data: dict):
    try:
        notification_type = notification_data.get("notification_type")
        print(
            "hiiiiiz",
            notification_data,
        )
        # return notification_data
        if notification_type == NotificationType.SMS:
            response = requests.post(
                f"https://{get_settings().SMS_API_ID}.execute-api.{get_settings().AWS_REGION}.amazonaws.com/dev/send-sms",
                headers={
                    "Accept": "application/json",
                    "Content-Type": "text/plain",
                    "x-api-key": get_settings().SMS_API_KEY,
                },
                json={
                    "sender_id": get_settings().SMS_SENDER_ID,
                    "message": notification_data.get("message"),
                    "phone_number": notification_data.get("phone_number"),
                },
            )
        elif notification_type == NotificationType.OTP:
            response = requests.post(
                f"https://{get_settings().SMS_API_ID}.execute-api.{get_settings().AWS_REGION}.amazonaws.com/dev/send-sms",
                headers={
                    "Accept": "application/json",
                    "Content-Type": "text/plain",
                    "x-api-key": get_settings().SMS_API_KEY,
                },
                json={
                    "sender_id": get_settings().SMS_SENDER_ID,
                    "message": f"OTP request for {get_settings().APP_NAME} is {notification_data.get('message')}",
                    "phone_number": notification_data.get("phone_number"),
                },
            )
        elif notification_type == NotificationType.PUSH:
            sender_id = notification_data.get("sender_id")
            title = notification_data.get("title")
            body = notification_data.get("message")
            fcm = FCMNotification(
                service_account_file="app/fcm/fcm.json",
                project_id="jobconnectz",
            )
            if not notification_data.get("data"):
                response = fcm.notify(
                    fcm_token=sender_id,
                    notification_title=title,
                    notification_body=body,
                    android_config={"notification": {"sound": "job_notification"}},
                    apns_config={
                        "payload": {"aps": {"sound": "job_notification.wav"}}
                    },
                )
            else:
                print("checkpoint1", type(notification_data.get("data")))
                encoder = StringEncoder(notification_data.get("data"))
                data_payload = encoder.encode()
                # data_payload = convert_dict_values_to_strings(notification_data.get('data'))
                print(type(data_payload))
                response = fcm.notify(
                    fcm_token=sender_id,
                    notification_title=title,
                    # notification_body=body,
                    data_payload=data_payload,
                    android_config={"notification": {"sound": "job_notification"}},
                    apns_config={
                        "payload": {"aps": {"sound": "job_notification.wav"}}
                    },
                )

        elif notification_type == NotificationType.EMAIL:
            ses = boto3.client(
                "ses",
                region_name=get_settings().AWS_REGION,
                aws_access_key_id=get_settings().SES_ACCESS_KEY,
                aws_secret_access_key=get_settings().SES_SECRET_KEY,
            )
            sender_email = get_settings().FROM_EMAIL
            receiver_email = notification_data.get("email")

            template_name = "email_template.html"
            context = {
                "message": notification_data.get("message"),
                "app_name": get_settings().APP_NAME,
            }
            email_body = render_email_template(template_name, context)

            msg = MIMEMultipart()
            msg["From"] = sender_email
            msg["To"] = receiver_email
            msg["Subject"] = f"Email from {get_settings().APP_NAME}"
            msg.attach(MIMEText(email_body, "html"))

            response = ses.send_raw_email(
                Source=sender_email,
                Destinations=[receiver_email],
                RawMessage={"Data": msg.as_string()},
            )
            print(response["MessageId"])
            print("mail senddddddddddddddd")
            return response["MessageId"]
        else:
            raise ValueError("Unsupported notification type")

        print(f"Notification sent: {response}")
        return response
    except Exception as e:
        raise Exception(f"Error sending notification: {e}")
