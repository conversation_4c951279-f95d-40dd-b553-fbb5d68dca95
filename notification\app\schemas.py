import re
from typing import Optional
from pydantic import BaseModel, FutureDatetime, field_validator

from app.enums import NotificationType


class CustomBaseModel(BaseModel):
    class Config:
        from_attributes = True
        use_enum_values = True

    @field_validator("phone_number", mode="before", check_fields=False)
    @classmethod
    def phone_validation(cls, v):
        if not v:
            return v
        regex = r"^\+?[1-9]{1,4}-?[0-9]{9,15}$"
        if not re.fullmatch(regex, v):
            raise ValueError(
                "Phone number is invalid. It should start with '+' followed by the country code, "
                "optionally include '-', and contain 9-15 digits."
            )
        return v


class NotificationRequest(CustomBaseModel):
    phone_number: Optional[str] = None
    email: Optional[str] = None
    sender_id: Optional[str] = None
    message: str
    notification_type: NotificationType
    send_at: Optional[FutureDatetime] = None  # this should be UTC
    # fcm_token: Optional[str] = None
    title: Optional[str] = None
    data: Optional[dict] = None