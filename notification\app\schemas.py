import re
from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel, FutureDatetime, field_validator
from datetime import datetime

from app.enums import NotificationType


class CustomBaseModel(BaseModel):
    class Config:
        from_attributes = True
        use_enum_values = True

    @field_validator("phone_number", mode="before", check_fields=False)
    @classmethod
    def phone_validation(cls, v):
        if not v:
            return v
        regex = r"^\+?[1-9]{1,4}-?[0-9]{9,15}$"
        if not re.fullmatch(regex, v):
            raise ValueError(
                "Phone number is invalid. It should start with '+' followed by the country code, "
                "optionally include '-', and contain 9-15 digits."
            )
        return v


class NotificationRequest(CustomBaseModel):
    phone_number: Optional[str] = None
    email: Optional[str] = None
    sender_id: Optional[str] = None
    message: str
    notification_type: NotificationType
    send_at: Optional[FutureDatetime] = None  # this should be UTC
    # fcm_token: Optional[str] = None
    title: Optional[str] = None
    data: Optional[dict] = None
    fcm_request_type: Optional[str] = "default"  # 'booking', 'service', payment, etc.
    user_id: Optional[str] = None


class NotificationResponse(CustomBaseModel):
    id: UUID
    title: str
    message: str
    notification_type: NotificationType
    phone_number: Optional[str] = None
    email: Optional[str] = None
    sender_id: Optional[str] = None
    send_at: Optional[datetime] = None
    data: Optional[dict] = None
    fcm_request_type: Optional[str] = None
    user_id: Optional[UUID] = None
    is_read: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_active: bool


# Notification Preferences Schemas
class NotificationPreferencesBase(CustomBaseModel):
    enable_push_notifications: Optional[bool] = True
    enable_email_notifications: Optional[bool] = True
    enable_sms_notifications: Optional[bool] = True


class NotificationPreferencesCreate(NotificationPreferencesBase):
    user_id: UUID
    device_id: UUID


class NotificationPreferencesUpdate(CustomBaseModel):
    enable_push_notifications: Optional[bool] = None
    enable_email_notifications: Optional[bool] = None
    enable_sms_notifications: Optional[bool] = None


class NotificationPreferencesOut(CustomBaseModel):
    id: UUID
    user_id: UUID
    device_id: UUID
    enable_push_notifications: bool
    enable_email_notifications: bool
    enable_sms_notifications: bool