"""thumbnail field added in users

Revision ID: 846776567c08
Revises: e870212a95e9
Create Date: 2025-06-10 15:17:03.095360

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '846776567c08'
down_revision: Union[str, None] = 'e870212a95e9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('service_provider', sa.Column('profile_pic_thumbnail', sa.String(), nullable=True))
    op.add_column('users', sa.Column('profile_thumbnail', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'profile_thumbnail')
    op.drop_column('service_provider', 'profile_pic_thumbnail')
    # ### end Alembic commands ###
