import boto3
from app.config import get_settings
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from app.models import ServiceProvider
from typing import List, Dict
import asyncio

logger = logging.getLogger(__name__)

async def bulk_update_cognito_groups(db: AsyncSession, old_group: str, new_group: str) -> Dict[str, any]:
    """
    Bulk update Cognito user groups for service providers.
    
    Args:
        db: Database session
        old_group: The group to remove (e.g., "artisan")
        new_group: The group to add (e.g., "service-provider")
        
    Returns:
        Dict with success count and errors
    """
    try:
        # Initialize Cognito client
        cognito_client = boto3.client(
            'cognito-idp',
            region_name=get_settings().AWS_REGION,
            aws_access_key_id=get_settings().AWS_ACCESS_KEY_ID,
            aws_secret_access_key=get_settings().AWS_SECRET_ACCESS_KEY
        )
        
        # Get all service providers
        query = select(ServiceProvider)
        result = await db.execute(query)
        service_providers = result.scalars().all()
        
        success_count = 0
        errors = []
        
        # Process each service provider
        for sp in service_providers:
            try:
                # Remove old group
                try:
                    cognito_client.admin_remove_user_from_group(
                        UserPoolId=get_settings().COGNITO_USER_POOL_ID,
                        Username=sp.auth_id,
                        GroupName=old_group
                    )
                except Exception as e:
                    logger.warning(f"Could not remove group {old_group} from user {sp.auth_id}: {str(e)}")
                
                # Add new group
                try:
                    cognito_client.admin_add_user_to_group(
                        UserPoolId=get_settings().COGNITO_USER_POOL_ID,
                        Username=sp.auth_id,
                        GroupName=new_group
                    )
                    
                    # Optionally add a second group if needed
                    # cognito_client.admin_add_user_to_group(
                    #     UserPoolId=get_settings().COGNITO_USER_POOL_ID,
                    #     Username=sp.auth_id,
                    #     GroupName="another-group"
                    # )
                    
                    success_count += 1
                    
                except Exception as e:
                    errors.append({
                        "user_id": sp.auth_id,
                        "error": f"Failed to add group {new_group}: {str(e)}"
                    })
                    
            except Exception as e:
                errors.append({
                    "user_id": sp.auth_id,
                    "error": str(e)
                })
        
        return {
            "success": True,
            "message": f"Updated {success_count} users",
            "updated": success_count,
            "total": len(service_providers),
            "errors": errors
        }
        
    except Exception as e:
        logger.error(f"Bulk update failed: {e}")
        return {
            "success": False,
            "message": str(e),
            "updated": 0,
            "errors": [{"error": str(e)}]
        }

# Add an endpoint to trigger the bulk update
from fastapi import APIRouter, Depends
from app.database import get_db

router = APIRouter()

@router.post("/bulk-update-groups")
async def update_user_groups(
    old_group: str,
    new_group: str,
    db: AsyncSession = Depends(get_db)
):
    """
    Endpoint to bulk update Cognito user groups.
    """
    result = await bulk_update_cognito_groups(db, old_group, new_group)
    return result 