from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import os, uvicorn
import logging
from contextlib import asynccontextmanager
from app.database import create_tables
from app.router import user_profiles, address, roles, permissions, blacklist, signup, user_preferences, regions, locality , artisan_upload

# from app.router import user_profiles, address, roles, permissions, blacklist, signup, user_preferences, regions, artisan_upload
from app.kafka_producer.config import kafka_producer_config
from app.kafka_consumer.consumer import cognito_consumer,db_consumer, consume_cognito_consumer, consume_db_consumer
import asyncio

log = logging.getLogger("uvicorn")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await create_tables()
    log.info("Starting up...")
    
    # Start Kafka
    kafka_conn = False
    while not kafka_conn: 
        try:
            await kafka_producer_config.start()
            await cognito_consumer.start()
            await db_consumer.start()
            kafka_conn = True
        except Exception as e:
            log.error(f"Kafka connection failed, retrying: {e}")
            await asyncio.sleep(1)
    
    log.info("Kafka Started___core___service...")
    
    # Start consumer tasks
    cognito_task = asyncio.create_task(consume_cognito_consumer())
    db_task = asyncio.create_task(consume_db_consumer())
    
    yield
    
    # Shutdown
    log.info("Kafka_Shutting down..._core___service")
    cognito_task.cancel()
    db_task.cancel()
    await cognito_consumer.stop()
    await db_consumer.stop()
    await kafka_producer_config.stop()
    log.info("Shutting down...")


app = FastAPI(lifespan=lifespan, 
              debug=True, 
              docs_url="/core/docs",           # Swagger UI
              redoc_url=None,                     # Disable ReDoc (optional)
              openapi_url="/core/openapi.json" # OpenAPI schema path
      )

# Filter out None values to avoid CORS errors
allowed_orgins = list(filter(None, [
    os.getenv("LOCALHOST_URL"),
    os.getenv("WEBAPP_URL"),
    os.getenv("UAT_WEBAPP_URL")
]))

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_orgins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(user_profiles.router)
app.include_router(address.router)
app.include_router(roles.router)
app.include_router(permissions.router)
app.include_router(blacklist.router)
app.include_router(signup.router)
app.include_router(user_preferences.router)
app.include_router(regions.router)
app.include_router(locality.router)
app.include_router(artisan_upload.router)
# app.include_router(
#     bulk_update_router,
#     tags=["upload"],
# )

@app.get("/__health")
def read_root():
    return {"status": "online"}

@app.get("/")
def read_root():
    return {"message": "Welcome to Core Service"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8006)