import logging
from fastapi import <PERSON><PERSON><PERSON>
# from starlette.websockets import WebSocketState
# import asyncio
from app.kafka_consumer import KafkaAssignmentConsumer
from app.database import get_db, engine
import app.models as md
from app.redis_cache import RedisCache
from app.kafka_producer import producer
from app.websocket_manager import WebSocketManager
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
from app.routes import chat
# from app.app_loggers import logger

log = logging.getLogger("uvicorn")

md.Base.metadata.create_all(bind=engine)
app = FastAPI(debug=True, 
              docs_url="/chat/docs",           # Swagger UI
              redoc_url=None,                     # Disable ReDoc (optional)
              openapi_url="/chat/openapi.json" # OpenAPI schema path
              )
redis_cache = RedisCache()
ws_manager = WebSocketManager()
# producer = KafkaProducerClient()


app.add_middleware(
    middleware_class=CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/health")
def read_root():
    return {"message": "Service is Up and running"}

app.include_router(chat.router, prefix="/chatservice")



@app.on_event("startup")
async def startup_event():
    log.info("Starting up application...")
    await producer.start()
    global consumer
    consumer = KafkaAssignmentConsumer("assignment_notifications", ws_manager)
    await consumer.start()
    log.info("Kafka consumer and producer started.")


@app.on_event("shutdown")
async def shutdown_event():
    log.info("Shutting down application...")
    await producer.stop()
    await consumer.stop()
    log.info("Kafka consumer and producer stopped.")
