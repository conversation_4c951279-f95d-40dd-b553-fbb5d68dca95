import logging
import os
from functools import lru_cache
from pydantic_settings import BaseSettings

log = logging.getLogger("uvicorn")


class Settings(BaseSettings):
    """Class for storing settings."""

    POSTGRES_DB: str = os.getenv("POSTGRES_DB")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD")
    POSTGRES_HOST: str = os.getenv("POSTGRES_HOST")
    POSTGRES_PORT: int = os.getenv("POSTGRES_PORT")
    AWS_REGION: str = os.getenv("AWS_REGION", "")
    KAFKA_HOST: str = os.getenv("KAFKA_HOST", "")
    KAFKA_PORT: str = os.getenv("KAFKA_PORT", "")
    NOTIFICATION_REDIS_CELERY_URL: str = os.getenv("NOTIFICATION_REDIS_CELERY_URL", "")
    SMS_API_ID: str = os.getenv("SMS_API_ID", "")
    SMS_API_KEY: str = os.getenv("SMS_API_KEY", "")
    SMS_SENDER_ID: str = os.getenv("SMS_SENDER_ID", "")
    SES_ACCESS_KEY: str = os.getenv("SES_ACCESS_KEY", "")
    SES_SECRET_KEY: str = os.getenv("SES_SECRET_KEY", "")
    FROM_EMAIL: str = os.getenv("FROM_EMAIL", "")
    APP_NAME: str = os.getenv("APP_NAME", "")
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "")
    FIREBASE_SERVER_KEY: str = os.getenv("FIREBASE_SERVER_KEY", "")
    BE_BLOB_API_URL : str = os.getenv("BE_BLOB_API_URL", "")
    S3_IMAGES_FOLDER : str = os.getenv("S3_IMAGES_FOLDER", "")
    S3_DOCS_FOLDER : str = os.getenv("S3_DOCS_FOLDER", "")
    BE_AUTH_API_URL : str = os.getenv("BE_AUTH_API_URL", "")

@lru_cache()
def get_settings() -> BaseSettings:
    """Get application settings usually stored as environment variables.

    Returns:
        Settings: Application settings.
    """
    log.info("Loading config settings from the environment...")
    return Settings()
