# import asyncio
# import logging
# from app.kafka_producer.config import kafka_producer_config
# from app.kafka_consumer.consumer import cognito_consumer,db_consumer, consume_cognito_consumer, consume_db_consumer
# from aiokafka.errors import KafkaConnectionError  # Import the exception

# log = logging.getLogger(__name__)

# async def startup_event():
#     """Start up event for FastAPI application."""
#     log.info("Starting up...")
#     kafka_ready = False
#     max_retries = 30  # Or however many retries you want
#     retry_delay = 5  # Seconds

#     for attempt in range(max_retries):
#         try:
#             log.info(f"Attempting to connect to Kafka (attempt {attempt + 1}/{max_retries})...")
#             await kafka_producer_config.start()
#             await cognito_consumer.start()
#             await db_consumer.start()
#             kafka_ready = True
#             log.info("Successfully connected to Kafka.")
#             break  # Exit the loop if successful
#         except KafkaConnectionError as e:
#             log.warning(f"Kafka connection failed: {e}")
#             if attempt < max_retries - 1:
#                 log.info(f"Retrying in {retry_delay} seconds...")
#                 await asyncio.sleep(retry_delay)
#             else:
#                 log.error("Max Kafka connection retries reached. Exiting.")
#                 raise  # Re-raise the exception to signal failure

#     if kafka_ready:
#         asyncio.create_task(consume_cognito_consumer())
#         asyncio.create_task(consume_db_consumer())
#         log.info("Kafka consumer started.")
#     else:
#         log.error("Kafka consumer failed to start.")


# async def shutdown_event():
#     """Shutdown event for FastAPI application."""
#     log.info("Shutting down...")
#     try:
#         await kafka_producer_config.stop()
#         await cognito_consumer.stop()
#         await db_consumer.stop()
#         log.info("Kafka producer and consumer stopped.")
#     except Exception as e:
#         log.error(f"Error during shutdown: {e}")
