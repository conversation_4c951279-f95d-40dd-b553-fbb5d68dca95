import os
import uuid
import jwt
import requests
from fastapi import Depends, HTTPException, Header, status
from typing import Annotated, List, Optional
import requests
from enum import Enum
from pydantic import BaseModel
from app.enum import NotificationType

# from app.schemas import NotificationRequest


# from database import ClanSessionLocal, RecruiterSessionLocal
class NotificationRequest(BaseModel):
    phone_number: Optional[str] = None
    email: Optional[str] = None
    sender_id: Optional[str] = None
    message: str
    notification_type: NotificationType
    title: Optional[str] = None
    data: Optional[dict] = None


class ServiceProviderStatusType(str, Enum):
    CREATED = "created"
    APPROVED = "approved"
    REJECTED = "rejected"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"


class UserStatusType(str, Enum):
    APPROVED = "approved"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"


class AdminRole(str, Enum):
    ADMIN = "admin"
    SUPERADMIN = "superadmin"
    AGENT = "agent"


class Gender(str, Enum):
    MALE = "male"
    FEMALE = "female"


class DeviceType(str, Enum):
    ANDROID = "android"
    IOS = "ios"


class CognitoRole(str, Enum):
    ARTISAN = "artisan"
    USER = "user"
    BUSINESS_OWNER = "business_owner"


async def get_id_header(Authorization):
    if not Authorization:
        return {"error": "Token required"}
    try:
        # print(Authorization, "auth")
        jwt_token = Authorization.split(" ")[1]
        baseurl = os.getenv("BE_AUTH_API_URL")
        response = requests.post(
            f"{baseurl}/validate-token/", json={"token": jwt_token}
        )
        return response
    except Exception as e:
        print(e, "error")
        return {"error": f"Error: {e}"}


def is_valid_uuid(val: str) -> bool:
    try:
        uuid.UUID(val)
        return True
    except ValueError:
        return False


# Common create function using sqlalchemy orm
async def create_record(db, model, request_dict):
    print("calling create record functionnnnnnnn")
    try:
        print("go database: ", request_dict)
        obj = model(**request_dict)
        db.add(obj)
        await db.commit()
        await db.refresh(obj)
        return obj
    except Exception as e:
        error_message = f"Database error: {str(e)}"
        print(error_message, "errrrrrrrrrrrrrrrrrrrrrrrrrrr")
        await db.rollback()
        # raise HTTPException(status_code=500, detail=error_message)
        return error_message


# async def update_record(db, data, obj):
#     for key, value in data.__dict__.items():
#         if value is None or (type(value) == str and len(value) == 0):
#             continue
#         else:
#             setattr(obj, key, value)

#     await db.commit()
#     await db.refresh(obj)
#     return obj


async def update_record(db, data, obj):
    for key, value in data.__dict__.items():
        # Skip the time fields
        if key in [
            "work_from_hrs",
            "work_to_hrs",
            "break_from_hrs",
            "break_to_hrs",
            "dob",
            "locations",
            "weekdays",
        ]:
            continue
        # Skip None or empty string values
        if value is None or (type(value) == str and len(value) == 0):
            continue
        # If the value is valid, update the corresponding object field
        setattr(obj, key, value)

    # Commit the transaction
    await db.commit()
    await db.refresh(obj)
    return obj


async def delete_record(db, obj):
    await db.delete(obj)
    await db.commit()
    return {"data": "Record deleted successfully"}


def check_file_exists(file):
    if file:
        return True
    return False


import random
import string
import time


def generate_cognito_password(length=12):
    """
    Generates a Cognito-compliant random password using epoch time for uniqueness.
    """
    if length < 8:
        raise ValueError("Password length must be at least 8 characters.")

    # ✅ Get last 5 digits of the current epoch time
    epoch_part = str(int(time.time()))[-5:]

    # ✅ Define character groups
    uppercase = random.choice(string.ascii_uppercase)  # At least 1 uppercase
    lowercase = random.choice(string.ascii_lowercase)  # At least 1 lowercase
    digit = random.choice(string.digits)  # At least 1 digit
    special = random.choice("!@#$%^&*")  # At least 1 special character

    # ✅ Generate remaining random characters
    remaining_length = length - 5 - 4  # 5 from epoch + 4 required chars
    random_chars = "".join(
        random.choices(
            string.ascii_letters + string.digits + "!@#$%^&*", k=remaining_length
        )
    )

    # ✅ Combine all parts
    password = uppercase + lowercase + digit + special + random_chars + epoch_part

    # ✅ Shuffle the password to avoid predictable patterns
    password = "".join(random.sample(password, len(password)))

    return password


# Function to send push notification
def send_push_notification(
    auth_token: str,
    title: str,
    message: str,
    sender_id: str,
    type: str,
    data: dict = {},
):
    """
    Sends a push notification using the API.

    :param auth_token: The authorization token (JWT or other)
    :param message: The message for the notification
    :param sender_id: The ID of the sender
    :return: Response from the API
    """
    print("Sender ID:", sender_id, type, title, message)
    # if type == "user":
    #     sender_id = get_user_by_id(user_id=sender_id, auth_token=auth_token)
    #     print(sender_id, "sender_id")
    # if type == "service_provider":
    #     sender_id = get_service_provider_by_id(sp_id=sender_id, auth_token=auth_token)
    #     print(sender_id, "sender_id")
    print("Sender ID:", sender_id)
    # Get API URL from settings
    BASE_URL = os.getenv("BE_NOTIFICATION_API_URL")
    ENDPOINT = "/send_notification"
    API_URL = f"{BASE_URL}{ENDPOINT}"

    # Prepare notification request
    notification_data = NotificationRequest(
        message=message,
        title=title,
        notification_type=NotificationType.PUSH,
        sender_id=str(sender_id),
        data=data,
    )

    # Headers
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {auth_token}",
    }
    print(notification_data, "notification_data")
    print(title, message, sender_id)
    print("Sending push notification:", message, "Sender ID:", sender_id)

    # Make API request
    try:
        response = requests.post(
            API_URL, headers=headers, json=notification_data.dict()
        )  # ✅ Fix JSON payload
        response_data = response.json()  # ✅ Ensure response.json() is properly called

        print("Response Code:", response.status_code)
        print("Response Data:", response_data)

        return response.status_code, response_data

    except requests.RequestException as e:
        print("Error sending notification:", str(e))
        return 500, {"error": "Failed to send notification"}


# class IssueStatus(str, Enum) :
#     OPEN = "OPEN"
#     IN_PROGRESS = "IN_PROGRESS"
#     RESOLVED = "RESOLVED"


# class IssuePriority(str, Enum):
#     LOW = "LOW"
#     MEDIUM = "MEDIUM"
#     HIGH = "HIGH"
#     CRITICAL = "CRITICAL"


class UserType(str, Enum):
    ARTISAN = "ARTISAN"
    USER = "USER"
    AGENT = "AGENT"
    ADMIN= "ADMIN"
    SUPERADMIN= "SUPERADMIN"


class AccountStatusType(str, Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"


class AccountTypeType(str, Enum):
    USER = "USER"
    AGENT = "ARTISAN"
    BUSINESS = "BUSINESS"


class CurrencyType(str, Enum):
    GHS = "GHS"
    EUR = "EUR"
    USD = "USD"
    AED = "AED"


class AgentStatusType(str, Enum):
    APPROVED = "approved"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"
