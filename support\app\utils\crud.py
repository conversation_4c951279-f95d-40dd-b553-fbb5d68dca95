def create_record(db, model, request_dict):
    print('calling create record functionnnnnnnn')
    try:
        obj = model(**request_dict)
        db.add(obj)
        db.commit()
        db.refresh(obj)
        return obj
    except Exception as e:
        db.rollback()
        error_message = f"Database error: {str(e)}"
        print(error_message, 'errrrrrrrrrrrrrrrrrrrrrrrrrrr')
        # raise HTTPException(status_code=500, detail=error_message)
        return error_message
