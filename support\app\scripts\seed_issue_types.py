from app.database import get_db
from app.models import IssueTypes, IssueCategory
import uuid
from app.utils.enums import DisputePriority

def seed_issue_types():
    db = next(get_db())
    
    # Clear existing issue types and categories
    db.query(IssueTypes).delete()
    db.query(IssueCategory).delete()
    db.commit()
    
    # Define categories
    categories = [
        {
            "name": "Booking Issues",
            "code": "BOOKING_ISSUES",
            "description": "Issues related to booking process and scheduling",
            "default_priority": DisputePriority.HIGH
        },
        {
            "name": "Payment and Refund",
            "code": "PAYMENT_AND_REFUND",
            "description": "Issues related to payments, charges, and refunds",
            "default_priority": DisputePriority.CRITICAL
        },
        {
            "name": "Service Issues",
            "code": "SERVICE_ISSUES",
            "description": "Issues related to service quality and professional conduct",
            "default_priority": DisputePriority.MEDIUM
        },
        {
            "name": "Others",
            "code": "OTHERS",
            "description": "Other miscellaneous issues",
            "default_priority": DisputePriority.LOW
        }
    ]
    
    # Insert categories and store their IDs
    category_ids = {}
    for category in categories:
        category_obj = IssueCategory(
            id=uuid.uuid4(),
            name=category["name"],
            code=category["code"],
            description=category["description"],
            default_priority=category["default_priority"]
        )
        db.add(category_obj)
        db.flush()  # Flush to get the ID
        category_ids[category["code"]] = category_obj.id
    
    # Define issue types
    issue_types_data = {
        "BOOKING_ISSUES": [
            {
                "code": "PROFESSIONAL_NO_SHOW",
                "description": "The professional did not show up for the service."
            },
            {
                "code": "SERVICE_INCOMPLETE",
                "description": "The service was incomplete or not as expected."
            },
            {
                "code": "CHARGED_FOR_CANCELED_BOOKING",
                "description": "I was charged for a canceled booking."
            }
        ],
        "PAYMENT_AND_REFUND": [
            {
                "code": "INCORRECT_AMOUNT_CHARGED",
                "description": "Incorrect amount charged for the service."
            },
            {
                "code": "REFUND_NOT_RECEIVED",
                "description": "Refund not received for a canceled booking."
            },
            {
                "code": "PAYMENT_NOT_CONFIRMED",
                "description": "Payment deducted, but booking not confirmed."
            }
        ],
        "SERVICE_ISSUES": [
            {
                "code": "SERVICE_UNSATISFACTORY",
                "description": "The service provided was unsatisfactory."
            },
            {
                "code": "PROFESSIONAL_UNPROFESSIONAL",
                "description": "The professional was unprofessional or rude."
            },
            {
                "code": "WORK_NOT_COMPLETED",
                "description": "The work was not completed as agreed."
            },
            {
                "code": "UNABLE_TO_CONTACT_PROFESSIONAL",
                "description": "Unable to contact the professional after booking."
            }
        ]
    }
    
    # Insert issue types with category IDs
    for category_code, issues in issue_types_data.items():
        category_id = category_ids[category_code]
        for issue in issues:
            issue_type = IssueTypes(
                id=uuid.uuid4(),
                category_id=category_id,
                code=issue["code"],
                description=issue["description"]
            )
            db.add(issue_type)
    
    db.commit()
    print("Issue categories and types seeded successfully")

if __name__ == "__main__":
    seed_issue_types() 