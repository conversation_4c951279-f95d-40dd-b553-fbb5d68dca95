import os
from uuid import UUID
import uuid
import random
import string
import time
import jwt
import requests
from fastapi import Depends, HTTPException, Header, status
from typing import Annotated, List
import requests
from enum import Enum
from datetime import datetime, timedelta, timezone
from app.config import settings
from app.helper import StandardResponse,ErrorResponse
from app.models import Invoice,InvoiceItem
# from app.schemas import InvoiceCreate
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db
from sqlalchemy.future import select
from app.models import ActivityLog
from app.models_enum import ActivityType
from sqlalchemy.orm import Session
# from app.schemas import InvoiceCreate


# from database import ClanSessionLocal, RecruiterSessionLocal


class PaymentMethodType(str, Enum):
    CARD = "CARD"
    WALLET = "WALLET"
    CASH = "CASH"
    BANK = "BANK"  # Using for artisan withdrawal


class StatusType(str, Enum):
    INIT = "INIT"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"


class CurrencyType(str, Enum):
    GHS = "GHS"
    EUR = "EUR"
    USD = "USD"
    AED = "AED"


class TransactionTypeType(str, Enum):
    PAYMENT = "PAYMENT"
    PAYOUT = "PAYOUT"
    REFUND = "REFUND"


class AccountStatusType(str, Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"


class AccountTypeType(str, Enum):
    USER = "USER"
    AGENT = "ARTISAN"
    BUSINESS = "BUSINESS"




async def get_id_header(Authorization):
    if not Authorization:
        return {"error": "Token required"}
    try:
        jwt_token = Authorization.split(" ")[1]
        baseurl = os.getenv("BE_AUTH_API_URL")
        response = requests.post(
            f"{baseurl}/validate-token/", json={"token": jwt_token}
        )
        return response
    except Exception as e:
        return {"error": f"Error: {e}"}


def is_valid_uuid(val: str) -> bool:
    try:
        uuid.UUID(val)
        return True
    except ValueError:
        return False


# Common create function using sqlalchemy orm
async def create_record(db, model, request_dict):
    print("calling create record functionnnnnnnn")
    try:
        obj = model(**request_dict)
        db.add(obj)
        await db.commit()
        await db.refresh(obj)
        return obj
    except Exception as e:
        await db.rollback()
        error_message = f"Database error: {str(e)}"
        print(error_message, "errrrrrrrrrrrrrrrrrrrrrrrrrrr")
        # raise HTTPException(status_code=500, detail=error_message)
        return error_message


async def update_record(db, data, obj):
    for key, value in data.__dict__.items():
        if value is None or (type(value) == str and len(value) == 0):
            continue
        else:
            setattr(obj, key, value)

    await db.commit()
    await db.refresh(obj)
    return obj


async def delete_record(db, obj):
    await db.delete(obj)
    await db.commit()
    return {"data": "Record deleted successfully"}


def check_file_exists(file):
    if file:
        return True
    return False


async def update_payment_rec(db, data, obj):
    print("Data updating")
    for key, value in data.items():
        if value is None or (type(value) == str and len(value) == 0):
            continue
        else:
            setattr(obj, key, value)

    await db.commit()
    await db.refresh(obj)
    print("Data Updated")
    return obj


def check_datetime(given_datetime):
    # Given datetime with corrected format
    # given_datetime = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S.%f%z")
    # Current datetime in UTC using timezone-aware object
    current_datetime = datetime.now(timezone.utc)
    # Check if the given datetime exceeds 24 hours from now
    exceeds_24_hours = (current_datetime - given_datetime) > timedelta(hours=24)
    print(exceeds_24_hours, "exceeds_24_hours")
    return exceeds_24_hours


def get_gtipay_transaction_detail(p_order_id):
    # Call GTI API to get transaction details
    url = f"{settings.CARD_API_URL}/open/orders/transaction-details?p_order_id={p_order_id}"

    headers = {
        "merchant-key": settings.CARD_MERCHANT_KEY,
        "merchant-secret": settings.CARD_MERCHANT_SECRET,
        "Content-Type": "application/json",
    }

    response = requests.get(url, headers=headers)
    transaction_data = response.json()
    return transaction_data


class Gender(str, Enum):
    MALE = "male"
    FEMALE = "female"


class ServiceProviderStatusType(str, Enum):
    CREATED = "created"
    APPROVED = "approved"
    REJECTED = "rejected"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"


class DeviceType(str, Enum):
    ANDROID = "android"
    IOS = "ios"


class UserStatusType(str, Enum):
    APPROVED = "approved"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"


class PaymentType(str, Enum):
    CASH = "CASH"
    WALLET = "WALLET"
    CARD = "CARD"


class BookingStatus(str, Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed" #when artisan accepts booking
    STARTED = "started"
    ARRIVED = "arrived" #when artisan arrives at user location
    ACCEPTED = "accepted" #when user accepts booking
    ONGOING = "ongoing" #when user starts service
    ENDED = "ended" #when artisan ends service
    COMPLETED = "completed" #when user pays for service, should be update in webhook
    CANCELLED = "cancelled"
    REJECTED = "rejected"

class BookingCancellationStatus(str, Enum):
    INITIATED = "initiated"
    PENDING = "pending"     # Cancellation accepted by agent
    # APPROVED = "approved"
    COMPLETED = "completed" # Once penalty is applied or agent approves the cancellation

class UserType(str, Enum):
    ARTISAN = "ARTISAN"
    USER = "USER"
    AGENT = "AGENT"

SURCHARGES = 2.5
TAX = 17.5


def get_surcharges(base_service_fee):
    return round(base_service_fee * SURCHARGES / 100, 2)


def get_tax(base_service_fee):
    return round(base_service_fee * TAX / 100, 2)

# # async def create_invoice(
# #     invoice,
# #     db: AsyncSession = Depends(get_db)
# # ):
# #     try:
# #         db_invoice = Invoice(
# #             sp_id=invoice.sp_id,
# #             payment_method=invoice.payment_method,
# #             biller_id=invoice.biller_id,
# #             customer_id=invoice.customer_id,
# #             tax_percentage=invoice.tax_percentage,
# #             tax_amount=invoice.tax_amount,
# #             discount_amount=invoice.discount_amount,
# #             total_amount=invoice.total_amount,
# #             payment_status=invoice.payment_status,
# #             booking_id=invoice.booking_id,
# #         )
# #         await db.add(db_invoice)
# #         await db.commit()
# #         await db.refresh(db_invoice)

# #         for item in invoice.items:
# #             db_item = InvoiceItem(
# #                 invoice_id=db_invoice.id,
# #                 catalogue_id=item.catalogue_id,
# #                 quantity=item.quantity,
# #                 price=item.price,
# #                 tax_percentage=item.tax_percentage,
# #                 tax_amount=item.tax_amount,
# #                 discount_amount=item.discount_amount,
# #                 total_amount=item.total_amount,
# #             )
# #             await db.add(db_item)

# #         await db.commit()
# #         await db.refresh(db_invoice)

# #         return StandardResponse(
# #             status_code=200,
# #             message="Invoice created successfully",
# #             data=db_invoice
# #         )


#     except Exception as e:
#         # await db.rollback()  
#         return ErrorResponse(
#             status_code=500,
#             message="Error creating invoice",
#             error=str(e)
#         )

        
        
# async def get_invoice(invoice_id: UUID,db: AsyncSession=Depends(get_db)):
#     result = await db.execute(select(Invoice).where(Invoice.id == invoice_id))
#     invoice = result.scalars().first()

#     if not invoice:
#         return ErrorResponse(
#                 status_code=404,
#                 message="Invoice not found"
#             )
        
#     return StandardResponse(
#         status_code=200,
#         message="Invoice fetched successfully",
#         data=invoice
#     )
async def get_invoices(db: AsyncSession=Depends(get_db), skip: int = 0, limit: int = 10):
    result = await db.execute(select(Invoice).offset(skip).limit(limit))
    invoices = result.scalars().all()

    return StandardResponse(
        status_code=200,
        message="Invoices fetched successfully",
        data=invoices
    )

async def delete_invoice(invoice_id: int,db: AsyncSession=Depends(get_db)):
    result = await db.execute(select(Invoice).where(Invoice.id == invoice_id))
    invoice = result.scalars().first()

    if not invoice:
        return ErrorResponse(
                status_code=404,
                message="Invoice not found"
            )
        

    try:
        await db.delete(invoice)
        await db.commit()
        return StandardResponse(
            status_code=200,
            message="Invoice deleted successfully"
        )
    except Exception as e:
        await db.rollback()
        return ErrorResponse(
                status_code=500,
                message="Error deleting invoice",
                data=str(e)
            )
        
def generate_cognito_password(length=12):
    """
    Generates a Cognito-compliant random password using epoch time for uniqueness.
    """
    if length < 8:
        raise ValueError("Password length must be at least 8 characters.")

    # Get last 5 digits of the current epoch time
    epoch_part = str(int(time.time()))[-5:]

    # Define character groups
    uppercase = random.choice(string.ascii_uppercase)  # At least 1 uppercase
    lowercase = random.choice(string.ascii_lowercase)  # At least 1 lowercase
    digit = random.choice(string.digits)  # At least 1 digit
    special = random.choice("!@#$%^&*")  # At least 1 special character

    # Generate remaining random characters
    remaining_length = length - 5 - 4  # 5 from epoch + 4 required chars
    random_chars = "".join(
        random.choices(
            string.ascii_letters + string.digits + "!@#$%^&*", k=remaining_length
        )
    )

    # Combine all parts
    password = uppercase + lowercase + digit + special + random_chars + epoch_part

    # Shuffle the password to avoid predictable patterns
    password = "".join(random.sample(password, len(password)))

    return password

async def log_activity(
    db: AsyncSession,
    title: str,
    description: str,
    reference_id: str,
    customer_id: str,
    activity_type: ActivityType,
):
    try:
        activity = ActivityLog(
            title=title,
            description=description,
            reference_id=reference_id,
            customer_id=customer_id,
            activity_type=activity_type,
        )
        db.add(activity)
        await db.commit()
    except Exception as e:
        await db.rollback()
        print(f"Failed to log activity: {e}")