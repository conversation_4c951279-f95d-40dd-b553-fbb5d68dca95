import os
import uuid
import jwt
import requests
from fastapi import Depends, HTTPException, Head<PERSON>, status
from typing import Annotated, List
import requests
from enum import Enum
from datetime import datetime, timedelta, timezone
from app.config import settings

# from database import ClanSessionLocal, RecruiterSessionLocal


class PaymentMethodType(str, Enum):
    CARD = "CARD"
    WALLET = "WALLET"
    CASH = "CASH"
    BANK = "BANK"  # Using for artisan withdrawal


class StatusType(str, Enum):
    INIT = "INIT"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"


class CurrencyType(str, Enum):
    GHS = "GHS"
    EUR = "EUR"
    USD = "USD"
    AED = "AED"


class TransactionTypeType(str, Enum):
    PAYMENT = "PAYMENT"
    PAYOUT = "PAYOUT"
    REFUND = "REFUND"


class AccountStatusType(str, Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"


class AccountTypeType(str, Enum):
    USER = "USER"
    AGENT = "ARTISAN"
    BUSINESS = "BUSINESS"


async def get_id_header(Authorization):
    if not Authorization:
        return {"error": "Token required"}
    try:
        jwt_token = Authorization.split(" ")[1]
        baseurl = os.getenv("BE_AUTH_API_URL")
        response = requests.post(
            f"{baseurl}/validate-token/", json={"token": jwt_token}
        )
        return response
    except Exception as e:
        return {"error": f"Error: {e}"}


def is_valid_uuid(val: str) -> bool:
    try:
        uuid.UUID(val)
        return True
    except ValueError:
        return False


# Common create function using sqlalchemy orm
async def create_record(db, model, request_dict):
    print("calling create record functionnnnnnnn")
    try:
        obj = model(**request_dict)
        db.add(obj)
        await db.commit()
        await db.refresh(obj)
        return obj
    except Exception as e:
        await db.rollback()
        error_message = f"Database error: {str(e)}"
        print(error_message, "errrrrrrrrrrrrrrrrrrrrrrrrrrr")
        # raise HTTPException(status_code=500, detail=error_message)
        return error_message


async def update_record(db, data, obj):
    for key, value in data.__dict__.items():
        if value is None or (type(value) == str and len(value) == 0):
            continue
        else:
            setattr(obj, key, value)

    await db.commit()
    await db.refresh(obj)
    return obj


async def delete_record(db, obj):
    await db.delete(obj)
    await db.commit()
    return {"data": "Record deleted successfully"}


def check_file_exists(file):
    if file:
        return True
    return False


async def update_payment_rec(db, data, obj):
    print("Data updating")
    for key, value in data.items():
        if value is None or (type(value) == str and len(value) == 0):
            continue
        else:
            setattr(obj, key, value)

    await db.commit()
    await db.refresh(obj)
    print("Data Updated")
    return obj


def check_datetime(given_datetime):
    # Given datetime with corrected format
    # given_datetime = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S.%f%z")
    # Current datetime in UTC using timezone-aware object
    current_datetime = datetime.now(timezone.utc)
    # Check if the given datetime exceeds 24 hours from now
    exceeds_24_hours = (current_datetime - given_datetime) > timedelta(hours=24)
    print(exceeds_24_hours, "exceeds_24_hours")
    return exceeds_24_hours


def get_gtipay_transaction_detail(p_order_id):
    # Call GTI API to get transaction details
    url = f"{settings.CARD_API_URL}/open/orders/transaction-details?p_order_id={p_order_id}"

    headers = {
        "merchant-key": settings.CARD_MERCHANT_KEY,
        "merchant-secret": settings.CARD_MERCHANT_SECRET,
        "Content-Type": "application/json",
    }

    response = requests.get(url, headers=headers)
    transaction_data = response.json()
    return transaction_data


class Gender(str, Enum):
    MALE = "male"
    FEMALE = "female"


class ServiceProviderStatusType(str, Enum):
    CREATED = "created"
    APPROVED = "approved"
    REJECTED = "rejected"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"


class DeviceType(str, Enum):
    ANDROID = "android"
    IOS = "ios"


class UserStatusType(str, Enum):
    APPROVED = "approved"
    SUSPENDED = "suspended"
    BLACKLIST = "blacklist"


class PaymentType(str, Enum):
    CASH = "CASH"
    WALLET = "WALLET"
    CARD = "CARD"


class BookingStatus(str, Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed" #when artisan accepts booking
    STARTED = "started"
    ARRIVED = "arrived" #when artisan arrives at user location
    ACCEPTED = "accepted" #when user accepts booking
    ONGOING = "ongoing" #when user starts service
    ENDED = "ended" #when artisan ends service
    COMPLETED = "completed" #when user pays for service, should be update in webhook
    CANCELLED = "cancelled"
    REJECTED = "rejected"

class BookingCancellationStatus(str, Enum):
    INITIATED = "initiated"
    PENDING = "pending"     # Cancellation accepted by agent
    # APPROVED = "approved"
    COMPLETED = "completed" # Once penalty is applied or agent approves the cancellation

class UserType(str, Enum):
    ARTISAN = "ARTISAN"
    USER = "USER"
    AGENT = "AGENT"

SURCHARGES = 2.5
TAX = 17.5


def get_surcharges(base_service_fee):
    return round(base_service_fee * SURCHARGES / 100, 2)


def get_tax(base_service_fee):
    return round(base_service_fee * TAX / 100, 2)
