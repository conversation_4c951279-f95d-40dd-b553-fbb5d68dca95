from uuid import UUID
from fastapi import Depends, APIRouter, Form, File, UploadFile
import app.sp_branches.schemas as sc
import app.models as md
from sqlalchemy.orm import Session
# from fastapi.responses import JSONResponse
from app.database import get_db
from app.response_models import (
    StandardResponse,
    StandardResponseWithoutSerialize,
    ErrorResponse
)
from app.sp_branches.db_query import(
    db_get_phone_info,
    db_get_service_provider_info,
    db_get_sp_branch_info,
    db_get_sp_branchs,
)
from app.auth import permission_checker
from fastapi.responses import JSONResponse

router = APIRouter(tags=["SP_Branches"])
@router.post("/sp-branch-create")
def create_sp_branche(payload: sc.ServiceProvidersBranch, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        phn_info = db_get_phone_info(payload.phone, db)
        if phn_info:
            return StandardResponse(status=False, status_code=400, message="phn number already present with another branch")
        sp_info = db_get_service_provider_info(payload.sp_id, db)
        if not sp_info:
            return StandardResponse(status=False, status_code=400, message="service provider id not found")
        new_data = md.SPBranches(
            SP_ID=payload.sp_id,
            Branch_Location=payload.branch_location,
            Phone=payload.phone,
            Address=payload.address,
            is_main=payload.is_main,
            Region=payload.Region
        )
        db.add(new_data)
        db.commit()
        return StandardResponse(status=False, status_code=200, message="service provider branch created sucessfully")
    except Exception as e:
        db.rollback()
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")

@router.put("/sp-branch-update/{spb_id}")
def update_sp_branche(spb_id: UUID, payload: sc.BranchUpdate, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        spb_info = db_get_sp_branch_info(spb_id, db)
        if not spb_info:
            return StandardResponse(status=False, status_code=400, message="service provider branch id not found")
        if payload.branch_location:
            spb_info.Branch_Location = payload.branch_location
        if payload.phone:
            spb_info.Phone = payload.phone
        if payload.address:
            spb_info.Address = payload.address
        if payload.is_main:
            spb_info.is_main = payload.is_main
        db.commit()
        return StandardResponse(status=True, status_code=200, message="service provider branch details updated sucessfully")
    except Exception as e:
        db.rollback()
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")
    

@router.get("/get-sp-branch/{spb_id}")
def sp_branch(spb_id: UUID, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        spb_info = db_get_sp_branch_info(spb_id, db)
        if not spb_info:
            return StandardResponse(status=False, status_code=400, message="service provider branch id not found")
        res = {
            "spb_id": spb_info.id,
            "sp_id": spb_info.SP_ID,
            "branch_location": spb_info.Branch_Location,
            "phone": spb_info.Phone,
            "address": spb_info.Address,
            "is_main": spb_info.is_main
        }
        return StandardResponse(status=True, status_code=200, data={"result": res}, message="service provider branch details fetched sucessfully")
    except Exception as e:
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")
    

@router.get("/get-service-provider-branch-list")
def service_providers_branch(page_no:int, page_size:int, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        spb_infos= db_get_sp_branchs(db)
        if not spb_infos:
            return StandardResponse(status=False, status_code=400, message="service provider branchs not found")
        total_rec = len(spb_infos)
        skip = (page_no - 1) * page_size
        limit = page_no * page_size
        result = [
            {
                "spb_id": spb_info.id,
                "sp_id": spb_info.SP_ID,
                "branch_location": spb_info.Branch_Location,
                "phone": spb_info.Phone,
                "address": spb_info.Address,
                "is_main": spb_info.is_main
            }
            for spb_info in spb_infos[skip:limit]
        ]
        return StandardResponse(status=True, status_code=200, data={"result": result, "page_no": page_no, "page_size": page_size, "total_records": total_rec}, message="service provider branchs details fetched sucessfully")
    except Exception as e:
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")
    
@router.delete("/delete-service-provider-branch/{spb_id}")
def delete_spb(spb_id: UUID, db: Session = Depends(get_db),user=Depends(permission_checker)):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        spb_info = db_get_sp_branch_info(spb_id, db)
        if not spb_info:
            return StandardResponse(status=False, status_code=400, message="service provider branch id not found")
        db.delete(spb_info)
        db.commit()
        return StandardResponse(status=True, status_code=200, message="service provider branch deleted sucessfully")
    except Exception as e:
        return ErrorResponse(status=False, status_code=500, error=str(e), message="Exception occred Unable to process your request, please try again")