from aiokafka import AIOKafkaConsumer
from app.config import get_settings


def create_cognito_consumer() -> AIOKafkaConsumer:
    return AIOKafkaConsumer(
        "cognito",
        bootstrap_servers=f"{get_settings().KAFKA_HOST}:{get_settings().KAFKA_PORT}",
    )

def create_db_consumer() -> AIOKafkaConsumer:
    return AIOKafkaConsumer(
        "db",
        bootstrap_servers=f"{get_settings().KAFKA_HOST}:{get_settings().KAFKA_PORT}",
    )

