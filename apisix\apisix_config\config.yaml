plugins:
  - openid-connect
  - cors
  - key-auth
  - jwt-auth
  - prometheus
  - limit-req
  - limit-count
  - limit-conn
  - proxy-rewrite
  - request-id
  - real-ip
  - basic-auth
  - file-logger
  - http-logger
  - response-rewrite

deployment:
  admin:
    enable_admin: true
    admin_key:
      - name: admin
        key: rKPdiZQpsHDgyCiDSLXOfJTmyxGafZZu #env
        role: admin
    allow_admin:
      - 0.0.0.0/0
    admin_listen:
      ip: 0.0.0.0
      port: 9180

  role: traditional
  role_traditional:
    config_provider: etcd
  etcd:
    host:
      - http://etcd:2379
    prefix: /apisix
    timeout: 30
    watch_timeout: 50
    startup_retry: 2
