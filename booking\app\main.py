from fastapi import FastAP<PERSON>, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from app.routes import service_provider, booking, disputes,artisan_rating,booking_cancellation,booking_cancellation_reasons, invoice #artisan_search
# from app.utils.notification import notification_manager
from app.database import init_db, enable_postgis
from app.seed import seed_data
from app.scripts.seed_issue_types import seed_issue_types
from app.scripts.seed_dispute_event_types import seed_dispute_event_types
import uvicorn
from app.audit_log import log_requests

app = FastAPI(
    title="API",
    description="FastAPI-based backend for booking artisans",
    version="1.0.0"
)

app.middleware("http")(log_requests)


# CORS Middleware (Allow all origins for frontend integration)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Change to specific frontend domain in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database and seed data
@app.on_event("startup")
async def startup_event():
    init_db()  # Ensure tables exist
    enable_postgis()  # Enable PostGIS extension
    # seed_data()  # Seed data on startup
    try:
        # seed_issue_types()
        # seed_dispute_event_types()
        print("Database seeded successfully")
    except Exception as e:
        print(f"Error seeding database: {str(e)}")

@app.get("/")
def read_root():
    return {"message": "Welcome to Urban Company Clone API!"}


# Registering Routes
app.include_router(service_provider.router)
app.include_router(booking.router)
app.include_router(disputes.router)
app.include_router(artisan_rating.router)
app.include_router(booking_cancellation.router)
app.include_router(booking_cancellation_reasons.router)
app.include_router(invoice.router)


# WebSocket Endpoint for Real-time Notifications
# @app.websocket("/ws/{user_id}")
# async def websocket_endpoint(websocket: WebSocket, user_id: str):
#     await notification_manager.connect(websocket, user_id)
#     try:
#         while True:
#             data = await websocket.receive_text()
#             print(f"Received message from {user_id}: {data}")
#     except Exception as e:
#         print(f"WebSocket Error: {e}")
#     finally:
#         await notification_manager.disconnect(user_id)
        


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
