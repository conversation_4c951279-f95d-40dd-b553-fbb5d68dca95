def validate_artisan_profile(artisan_profile):
    # Check specified columns are not None
    columns_to_check = ['first_name', 'last_name', 'email', 'phone_number', 'gender', 'dob', 'primary_location',
                        'license', 'certificate', 'govt_id', 'work_from_hrs', 'work_to_hrs', 'bank_acc_holder_name', 'bank_name', 'bank_acc_no', 'bank_branch_code', 'bank_swift_code', 'bank_acc_type']
    none_columns = [column for column in columns_to_check if getattr(artisan_profile, column) is None]
    if none_columns:
        return {'error': f"Please complete your profile to go online. Missing columns: {', '.join(none_columns)}"}
