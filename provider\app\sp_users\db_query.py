import app.models as md

def db_get_service_provider_info(sp_id, db):

    sp_info = db.query(md.ServiceProviders).filter(md.ServiceProviders.id == sp_id).first()
    return sp_info

def db_get_sp_branch_info(spb_id, db):
    spb_info = db.query(md.SPBranches).filter(md.SPBranches.id == spb_id).first()
    return spb_info

def db_get_user_info(user_id, db):
    user_info = db.query(md.SPUsers).filter(md.SPUsers.User_ID == user_id).first()
    return user_info

def db_get_users(db):
    user_info = db.query(md.SPUsers)
    return user_info