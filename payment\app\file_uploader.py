# import json
# from minio import Minio, commonconfig
# from minio.error import S3Error
# from fastapi import HTTPException
# from app.config import get_settings
# from fastapi import UploadFile

# client = Minio(
#     endpoint=get_settings().MINIO_URL.replace("https://", "").replace("http://", ""),
#     access_key=get_settings().MINIO_ACCESS_KEY,
#     secret_key=get_settings().MINIO_SECRET_KEY,
#     secure=get_settings().MINIO_URL.startswith("https://"),
# )

# content_type = ["image/png", "image/jpeg", "application/pdf"]


# def set_bucket_public(bucket_name: str):
#     policy = {
#         "Version": "2012-10-17",
#         "Statement": [
#             {
#                 "Effect": "Allow",
#                 "Principal": "*",
#                 "Action": "s3:GetObject",
#                 "Resource": f"arn:aws:s3:::{bucket_name}/*",
#             }
#         ],
#     }

#     try:
#         client.set_bucket_policy(bucket_name, json.dumps(policy))
#         print(f"Bucket '{bucket_name}' set to public.")
#     except S3Error as e:
#         raise HTTPException(status_code=500, detail=f"Failed to set bucket policy: {e}")


# def upload_file(file: UploadFile | None, bucket_name: str, content_type: list):
#     try:
#         if file is None:
#             raise HTTPException(status_code=400, detail="File to be uploaded not found")

#         # if not file.content_type.__contains__(content_type):
#         #     raise HTTPException(
#         #         status_code=400, detail=f"Only {content_type} is accepted"
#         #     )

#         if not client.bucket_exists(bucket_name):
#             client.make_bucket(bucket_name)
#             set_bucket_public(bucket_name)

#         client.put_object(
#             bucket_name=bucket_name,
#             object_name=file.filename,
#             data=file.file,
#             length=file.size,
#             content_type=file.content_type,
#         )

#         image_url = f"/{bucket_name}/{file.filename}"
#         print(image_url, 'image_url')
#         return image_url

#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error: {e}")


# def delete_file(bucket_name: str, file_name: str):
#     try:
#         if not client.bucket_exists(bucket_name):
#             client.make_bucket(bucket_name)

#         client.remove_object(bucket_name, file_name)
#         return {"result": "file deleted successfully"}

#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error: {e}")
    

