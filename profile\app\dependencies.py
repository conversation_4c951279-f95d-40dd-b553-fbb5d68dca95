from typing import Annotated, List
from app.utils import <PERSON>minR<PERSON>
from fastapi import Depends, HTTPException, Header, status
from app.utils import get_id_header

def role_required(allowed_roles: List[AdminRole], Authorization):
    print(Authorization, 'token')
    async def role_dependency():
        try:
            resp = await get_id_header(token)
            if resp.status_code == 200:
                if resp.get('role') not in allowed_roles:
                    return {"error": "You do not have the necessary permissions"}
                return {'allow': True}
            else:
                return {'error': resp.json()}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error: {e}")
    return role_dependency
