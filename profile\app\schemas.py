from pydantic import BaseModel, EmailStr, <PERSON>son, UUID4, PastDate, <PERSON>, validator
from datetime import datetime, date, time
from typing import Optional, Union, List
from fastapi import Form
from pydantic.types import conint
from app.utils import CognitoRole, DeviceType, Gender, ServiceProviderStatusType , AgentStatusType
import json
from app.enum import NotificationType
from uuid import UUID
from app.models_enum import *
from app.models import BusinessProviderStatus

class CreateUser(BaseModel):
    # auth_id: str
    # id: Optional[UUID4] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    country_code: Optional[str] = None
    primary_location: Optional[str] = None
    # rating: Optional[int] = None


class AddressEntry(BaseModel):
    address: str
    latitude: str
    longitude: str
    is_default: bool = False  # Default flag to indicate primary address


class UsersResponse(CreateUser):
    id: UUID4
    auth_id: Optional[str] = None
    profile_pic: Optional[str] = None
    profile_thumbnail: Optional[str] = None
    notification_uuid: Optional[str] = None
    locations: Optional[List] = None


class UpdateUser:
    def __init__(
        self,
        first_name: Optional[str] = Form(None),
        last_name: Optional[str] = Form(None),
        email: Optional[str] = Form(None),
        phone_number: Optional[str] = Form(None),
        country_code: Optional[str] = Form(None),
        primary_location: Optional[str] = Form(None),
        rating: Optional[int] = Form(None),
        locations: Optional[str] = Form(None),
        notification_uuid: Optional[str] = Form(None),
    ):
        self.first_name = first_name
        self.last_name = last_name
        self.email = email
        self.phone_number = phone_number
        self.country_code = country_code
        self.primary_location = primary_location
        self.rating = rating
        self.locations = json.loads(locations) if locations else []
        self.notification_uuid = notification_uuid


class CreateAdmin(BaseModel):
    first_name: str
    last_name: str
    email: str
    phone_number: str
    gender: Optional[Gender] = None
    role: str
    status: Optional[ServiceProviderStatusType] = None


class CreateServiceProvider(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    country_code: Optional[str] = None
    primary_location: Optional[str] = None
    rating: Optional[int] = None
    skill: Optional[str] = None
    skill_level: Optional[str] = None
    experience: Optional[float] = None
    about_us: Optional[str] = None
    status: Optional[ServiceProviderStatusType] = None


class ServiceProviderResponse(CreateServiceProvider):
    id: Optional[UUID4] = None
    auth_id: Optional[str] = None
    profile_pic: Optional[str] = None
    profile_pic_thumbnail: Optional[str] = None
    gender: Optional[Gender] = None
    dob: Optional[date] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    work_from_hrs: Optional[time] = None
    work_to_hrs: Optional[time] = None
    break_from_hrs: Optional[time] = None
    break_to_hrs: Optional[time] = None
    weekdays: Optional[List[int]] = None
    bank_acc_holder_name: Optional[str] = None
    bank_name: Optional[str] = None
    bank_acc_no: Optional[str] = None
    bank_branch_code: Optional[str] = None
    bank_swift_code: Optional[str] = None
    bank_acc_type: Optional[str] = None
    services: Optional[list] = None
    notification_uuid: Optional[str] = None


class UpdateServiceProvider:

    def __init__(
        self,
        first_name: Optional[str] = Form(None),
        last_name: Optional[str] = Form(None),
        # email: Optional[str] = Form(None),
        # phone_number: Optional[str] = Form(None),
        country_code: Optional[str] = Form(None),
        primary_location: Optional[str] = Form(None),
        rating: Optional[int] = Form(None),
        skill: Optional[str] = Form(None),
        skill_level: Optional[str] = Form(None),
        experience: Optional[float] = Form(None),
        about_us: Optional[str] = Form(None),
        status: Optional[ServiceProviderStatusType] = Form(None),
        gender: Optional[str] = Form(None),
        dob: Optional[str] = Form(None),
        latitude: Optional[float] = Form(None),
        longitude: Optional[float] = Form(None),
        work_from_hrs: Optional[str] = Form(None),
        work_to_hrs: Optional[str] = Form(None),
        break_from_hrs: Optional[str] = Form(None),
        break_to_hrs: Optional[str] = Form(None),
        weekdays: Optional[str] = Form(None),
        bank_acc_holder_name: Optional[str] = Form(None),
        bank_name: Optional[str] = Form(None),
        bank_acc_no: Optional[str] = Form(None),
        bank_branch_code: Optional[str] = Form(None),
        bank_swift_code: Optional[str] = Form(None),
        bank_acc_type: Optional[str] = Form(None),
        services_list: Optional[str] = Form(None),
        notification_uuid: Optional[str] = Form(None),
    ):
        self.first_name = first_name
        self.last_name = last_name
        # self.email = email
        # self.phone_number = phone_number
        self.country_code = country_code
        self.primary_location = primary_location
        self.rating = rating
        self.skill = skill
        self.skill_level = skill_level
        self.experience = experience
        self.about_us = about_us
        self.status = status
        self.gender = gender
        self.dob = dob
        self.latitude = latitude
        self.longitude = longitude
        self.work_from_hrs = work_from_hrs
        self.work_to_hrs = work_to_hrs
        self.break_from_hrs = break_from_hrs
        self.break_to_hrs = break_to_hrs
        self.weekdays = weekdays
        self.bank_acc_holder_name = bank_acc_holder_name
        self.bank_name = bank_name
        self.bank_acc_no = bank_acc_no
        self.bank_branch_code = bank_branch_code
        self.bank_swift_code = bank_swift_code
        self.bank_acc_type = bank_acc_type
        self.services_list = services_list
        self.notification_uuid = notification_uuid


class AdminCreateServiceProvider:
    def __init__(
        self,
        first_name: Optional[str] = Form(None),
        last_name: Optional[str] = Form(None),
        email: Optional[str] = Form(None),
        phone_number: Optional[str] = Form(None),
        primary_location: Optional[str] = Form(None),
        rating: Optional[int] = Form(None),
        skill: Optional[str] = Form(None),
        skill_level: Optional[str] = Form(None),
        experience: Optional[float] = Form(None),
        about_us: Optional[str] = Form(None),
        status: Optional[ServiceProviderStatusType] = Form(None),
        gender: Optional[str] = Form(None),
        dob: Optional[str] = Form(None),
        latitude: Optional[float] = Form(None),
        longitude: Optional[float] = Form(None),
        work_from_hrs: Optional[str] = Form(None),
        work_to_hrs: Optional[str] = Form(None),
        break_from_hrs: Optional[str] = Form(None),
        break_to_hrs: Optional[str] = Form(None),
        weekdays: Optional[str] = Form(None),
        bank_acc_holder_name: Optional[str] = Form(None),
        bank_name: Optional[str] = Form(None),
        bank_acc_no: Optional[str] = Form(None),
        bank_branch_code: Optional[str] = Form(None),
        bank_swift_code: Optional[str] = Form(None),
        bank_acc_type: Optional[str] = Form(None),
        services_list: Optional[str] = Form(None),
        notification_uuid: Optional[str] = Form(None),
    ):
        self.first_name = first_name
        self.last_name = last_name
        self.email = email
        self.phone_number = phone_number
        self.primary_location = primary_location
        self.rating = rating
        self.skill = skill
        self.skill_level = skill_level
        self.experience = experience
        self.about_us = about_us
        self.status = status
        self.gender = gender
        self.dob = dob
        self.latitude = latitude
        self.longitude = longitude
        self.work_from_hrs = work_from_hrs
        self.work_to_hrs = work_to_hrs
        self.break_from_hrs = break_from_hrs
        self.break_to_hrs = break_to_hrs
        self.weekdays = weekdays
        self.bank_acc_holder_name = bank_acc_holder_name
        self.bank_name = bank_name
        self.bank_acc_no = bank_acc_no
        self.bank_branch_code = bank_branch_code
        self.bank_swift_code = bank_swift_code
        self.bank_acc_type = bank_acc_type
        self.services_list = services_list
        self.notification_uuid = notification_uuid


class UserApproval(BaseModel):
    status: ServiceProviderStatusType
    reason: Optional[str] = None


class ServiceProviderApproval(BaseModel):
    status: ServiceProviderStatusType
    reason: Optional[str] = None

    # def __init__(
    #     self,
    #     status: Optional[ServiceProviderStatusType] = Form(None),
    # ):
    #     self.status = status


class CreateDevice(BaseModel):
    user_id: Optional[UUID4] = None
    service_provider_id: Optional[UUID4] = None
    type: DeviceType
    notification_token: str
    info: dict


class UpdateDevice(BaseModel):
    id: UUID4
    type: Optional[DeviceType] = None
    notification_token: Optional[str] = None
    info: Optional[dict] = None
    is_active: Optional[bool] = None


class DeleteDevice(BaseModel):
    id: UUID4


class DeviceResponse(BaseModel):
    id: UUID4
    user_id: Optional[UUID4] = None
    service_provider_id: Optional[UUID4] = None
    type: DeviceType
    notification_token: str
    info: dict
    created_at: datetime
    updated_at: datetime
    is_active: bool


class AdminSignupRequest(BaseModel):
    email: str
    password: str
    phone_number: str  # This will be used as the username
    first_name: str
    last_name: str


class AdminLoginRequest(BaseModel):
    phone_number: str  # Changed from email to phone_number
    password: str


class RefreshRequest(BaseModel):
    refresh_token: str
    role: CognitoRole


class SignupRequest(BaseModel):
    phone_number: Optional[str] = None
    country_code: str
    role: CognitoRole


class OtpVerifyRequest(BaseModel):
    phone_number: str
    otp: str
    role: CognitoRole


class ResendOtpRequest(BaseModel):
    phone_number: str


class CognitoGetUserRequest(BaseModel):
    phone_number: Optional[str] = None
    email: Optional[str] = None
    role: CognitoRole
    country_code: str


class AgentApproval(BaseModel):
    status: AgentStatusType
    reason: Optional[str] = None


class BusinessResponse(BaseModel):
    id: Optional[UUID4] = None
    auth_id: Optional[str] = None
    email: Optional[str] = None
    country_code: Optional[str] = None
    phone_number: Optional[str] = None
    full_name: Optional[str] = None
    business_name: Optional[str] = None
    business_type: Optional[str] = None
    business_registration_number: Optional[str] = None
    ghana_post_gps_address: Optional[str] = None
    business_location: Optional[str] = None
    tax_identification_number: Optional[str] = None
    services_offered: Optional[list] = None
    service_area_ids: Optional[list] = None
    business_registration_document: Optional[str] = None
    business_logo: Optional[str] = None
    portfolio_image: Optional[list] = None
    id_proof: Optional[str] = None
    signature: Optional[str] = None
    submit_date: Optional[datetime] = None
    page_position: Optional[str] = None
    status: Optional[str] = None
    reason: Optional[str] = None


class UpdateBusiness:
    def __init__(
        self,
        full_name: Optional[str] = Form(None),
        business_name: Optional[str] = Form(None),
        business_type: Optional[str] = Form(None),
        business_registration_number: Optional[str] = Form(None),
        ghana_post_gps_address: Optional[str] = Form(None),
        business_location: Optional[str] = Form(None),
        tax_identification_number: Optional[str] = Form(None),
        services_offered: Optional[str] = Form(None),
        service_area_ids: Optional[str] = Form(None),
        page_position: Optional[str] = Form(None),
        status: BusinessProviderStatus = Form(BusinessProviderStatus.IN_PROGRESS),
        reason: Optional[str] = Form(None),
        submit_date: Optional[datetime] = Form(None)
        
    ):  
        self.full_name = full_name
        self.business_name = business_name
        self.business_type = business_type
        self.business_registration_number = business_registration_number
        self.ghana_post_gps_address = ghana_post_gps_address
        self.business_location = business_location
        self.tax_identification_number = tax_identification_number
        self.services_offered = json.loads(services_offered) if services_offered else []
        self.service_area_ids = json.loads(service_area_ids) if service_area_ids else []
        self.page_position = page_position
        self.status = status 
        self.reason = reason
        self.submit_date = submit_date

class BusinessAreaCreate(BaseModel):
    area_name: str

class BusinessAreaUpdate(BaseModel):
    area_name:str

class BusinessSignUp(BaseModel):
    full_name: str
    country_code: str
    phone_number: str
    email: str
class SigninRequest(BaseModel):
    phone_number: Optional[str] = None
    session: Optional[str] = None
    otp: Optional[str] = None
