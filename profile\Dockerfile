# Use the official Python image as the base image
FROM python:3.10-slim
# Set the working directory in the container
WORKDIR /app

# set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# install system dependencies
# RUN apt-get update && \
#     apt-get install -y --no-install-recommends \
#     gcc \
#     libpq-dev gcc kafkacat && \
#     apt-get clean && \
#     rm -rf /var/lib/apt/lists/*

# Copy the requirements file into the container
COPY ./requirements.txt .

# Copy the FastAPI application code into the container

# Install FastAPI and any other required packages
RUN pip install -r requirements.txt

COPY . .

# Ensure AWS config directory exists
RUN mkdir -p /root/.aws

# Copy AWS config and credentials if available
COPY ./aws/config /root/.aws/config
COPY ./aws/credentials /root/.aws/credentials

# Ensure correct permissions (ignore errors if files are missing)
RUN chmod -R 600 /root/.aws/ || true


# Expose the FastAPI application port
EXPOSE 8001

# RUN aws sts get-caller-identity

# Define the command to start the FastAPI application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]