from datetime import datetime
from sqlalchemy.orm import Session
from typing import Any, Dict, Optional
from uuid import UUID
from app.schemas.service_request_artisan import ServiceStart,UpdateArtisanStatusRequest
from app.models import  Invoice,InvoiceItem,ArtisanAssigned, Booking, Account
from app.models_enum import ArtisanAssignStatus
from app.utils.notification import send_push_notification


def create_record(db: Session, model, request_dict: Dict[str, Any]):
    """Create a new record in the database"""
    print('calling create record functionnnnnnnn')
    try:
        obj = model(**request_dict)
        db.add(obj)
        db.commit()
        db.refresh(obj)
        return obj
    except Exception as e:
        print(e, 'eeeeeeeeeeeeee')
        db.rollback()
        error_message = f"Database error: {str(e)}"
        print(error_message, 'errrrrrrrrrrrrrrrrrrrrrrrrrrr')
        # raise HTTPException(status_code=500, detail=error_message)
        return error_message


def get_record_by_id(db: Session, model, record_id: UUID):
    """Get a record by its ID"""
    try:
        record = db.query(model).filter(model.id == record_id).first()
        if not record:
            return f"Record not found for id {record_id}"
        return record
    except Exception as e:
        error_message = f"Database error: {str(e)}"
        print(error_message, 'errrrrrrrrrrrrrrrrrrrrrrrrrrr')
        return error_message


def update_record(db: Session, data, obj):
    """Update an existing record"""
    try:
        for key, value in data.__dict__.items():
            # Skip None or empty string values
            if value is None or (isinstance(value, str) and not value.strip()):
                continue
            # Update the object attribute
            setattr(obj, key, value)

        db.commit()
        db.refresh(obj)
        return obj
    except Exception as e:
        db.rollback()
        error_message = f"Database error: {str(e)}"
        print(error_message, 'errrrrrrrrrrrrrrrrrrrrrrrrrrr')
        return error_message


def delete_record(db: Session, obj):
    """Delete a record from the database"""
    try:
        db.delete(obj)
        db.commit()
        return
    except Exception as e:
        db.rollback()
        error_message = f"Database error: {str(e)}"
        print(error_message, 'errrrrrrrrrrrrrrrrrrrrrrrrrrr')
        return error_message
    
def service_start(db: Session, data: UpdateArtisanStatusRequest):
    # print("line75")
    artisan_assign = db.query(ArtisanAssigned).filter(ArtisanAssigned.id == data.id).first()
    if artisan_assign:
        artisan_assign.status = ArtisanAssignStatus.STARTED
        artisan_assign.start_time = datetime.utcnow()
        artisan_assign.service_start_artisan_latitude = data.service_start_artisan_latitude
        artisan_assign.service_start_artisan_longitude = data.service_start_artisan_longitude
        # print({"lat":artisan_assign.service_start_artisan_latitude,"lon":artisan_assign.service_start_artisan_longitude})
        db.commit()
        db.refresh(artisan_assign)
        # print("line85 in crud dbupdated")
    # Creating booking history
    # create_booking_history(db, booking_obj=booking)
    return artisan_assign

def service_ongoing(db: Session, data: UpdateArtisanStatusRequest):
    # print("line75")
    artisan_assign = db.query(ArtisanAssigned).filter(ArtisanAssigned.id == data.id).first()
    if artisan_assign:
        artisan_assign.status = ArtisanAssignStatus.ONGOING
        artisan_assign.service_start = datetime.utcnow()
        db.commit()
        db.refresh(artisan_assign)
    return artisan_assign

def service_end(db: Session, data: UpdateArtisanStatusRequest):
    artisan_assign = db.query(ArtisanAssigned).filter(ArtisanAssigned.id == data.id).first()
    if artisan_assign:
        booking_obj = db.query(Booking).filter(Booking.id == data.booking_id).first()
        # if paymnt_method.payment_type != "CASH":
        #     artisan_assign.status = ArtisanAssignStatus.COMPLETED
        artisan_assign.status = ArtisanAssignStatus.COMPLETED
        artisan_assign.service_end = datetime.utcnow()
        artisan_assign.end_time = datetime.utcnow()
        artisan_assign.service_end_artisan_latitude = data.service_end_artisan_latitude
        artisan_assign.service_end_artisan_longitude = data.service_end_artisan_longitude
        db.commit()
        db.refresh(artisan_assign)
    try:
        service_fee = 0
        other_charges = 0
        invoice_item_obj = db.query(InvoiceItem).filter(InvoiceItem.id == artisan_assign.invoice_item_id).first()
        if invoice_item_obj:
            service_fee = invoice_item_obj.price
            other_charges = invoice_item_obj.booking_fee + invoice_item_obj.platform_fee_amount + invoice_item_obj.tax_amount
        print(service_fee, "service_fee")

        if booking_obj.payment_type in ['WALLET', 'CARD']:
            # Updating Artisan Wallet once service is completed
            a_query = db.query(Account).filter(Account.user_id == artisan_assign.artisan_id, Account.account_type == 'APP').first()
            if a_query:
                a_account_balance = a_query.balance
                setattr(a_query, "balance", a_account_balance + service_fee)
                db.commit()
                db.refresh(a_query)
                print("Artisan Wallet Updated in end service")
        elif booking_obj.payment_type == 'CASH':
            # Updating Artisan Wallet once service is completed
            a_query = db.query(Account).filter(Account.user_id == artisan_assign.artisan_id, Account.account_type == 'APP').first()
            if a_query:
                a_account_balance = a_query.balance
                setattr(a_query, "balance", a_account_balance - other_charges)
                db.commit()
                db.refresh(a_query)
                print("Artisan Wallet Updated in end service")
    except Exception as e:
        print(e, "error in wallet update")
    return artisan_assign

def start_service(
    db: Session, data: UpdateArtisanStatusRequest, artisan_data: ArtisanAssigned, Authorization: str, user_id: UUID
):
    # print("inline107")
    booking = service_start(db, data)
    booking_id = data.booking_id
    send_push_notification(
        auth_token=Authorization,
        title="Artisan started",
        message=f"Artisan started to your location",
        sender_id=str(user_id),
        type="user",
        data={
            "booking_id": str(booking_id),
            "request_type": "booking"
        }
    )

    return booking
def start_ongoing(
    db: Session, data: UpdateArtisanStatusRequest, artisan_data: ArtisanAssigned, Authorization: str, user_id: UUID
):
    # print("inline107")
    booking = service_ongoing(db, data)
    booking_id = data.booking_id
    send_push_notification(
        auth_token=Authorization,
        title="service started",
        message=f"Your service started successfully",
        sender_id=str(user_id),
        type="user",
        data={
            "booking_id": str(booking_id),
            "request_type": "booking"
        }
    )

    return booking


def end_service(
    db: Session, data: UpdateArtisanStatusRequest, artisan_data: ArtisanAssigned, Authorization: str, user_id: UUID
):
    booking = service_end(db, data)
    booking_id = data.booking_id
    send_push_notification(
        auth_token=Authorization,
        title="Service Completed",
        message=f"Your requested service has ended",
        sender_id=str(user_id),
        type="user",
        data={
            "booking_id": str(booking_id),
            "request_type": "booking"
        }
    )

    return booking