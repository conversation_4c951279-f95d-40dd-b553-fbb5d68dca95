from pydantic import BaseModel, EmailStr
from typing import Optional
from uuid import UUID

class BookingCancellationReasonBase(BaseModel):
    reason: str
    user_type: Optional[str] = None
    penalty_needed: Optional[bool] = None
    apply_penalty_on: Optional[str] = None

class BookingCancellationReasonCreate(BookingCancellationReasonBase):
    pass

class BookingCancellationReasonUpdate(BaseModel):
    reason: Optional[str] = None
    user_type: Optional[str] = None
    penalty_needed: Optional[bool] = None
    apply_penalty_on: Optional[str] = None

class BookingCancellationReasonResponse(BookingCancellationReasonBase):
    id: UUID
    is_active: bool

    class Config:
        from_attributes = True
