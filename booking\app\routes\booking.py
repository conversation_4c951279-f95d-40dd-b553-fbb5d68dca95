from datetime import datetime, <PERSON><PERSON><PERSON>
import json
from typing import Annotated
import uuid
import asyncio
from app.utils.helper import get_surcharges, get_tax
from sqlalchemy.orm import Session
from typing import List, Optional, Annotated
from fastapi import APIRouter, Depends, HTTPException, Header
from app.models import Booking
from app.models import Services
from app.database import get_db
from app.schemas.helper import StandardResponse, ErrorResponse
from app.utils.notification import send_push_notification  # Dummy notification function
from app.utils.auth import get_id_header
from app.services.booking import (
    reschedule_service_request,
    start_service,
    end_service,
    reschedule_service,
)
from app.schemas.booking import (
    LatestBookingRequest,
    ServiceStart,
    ServiceEnd,
    ServiceReschedule,
    ServiceRescheduleRequest,
    BookingRequest,
    BookingResponse,
    BookingHistoryRequest,
    UpdateBookingStatusRequest,
    NegotiationRequest,
    NegotiationResponseRequest,
    BookingIdRequest,
    BookingUpdateRequest,
    RescheduleResponseRequest
)
from sqlalchemy.ext.asyncio import AsyncSession
from app.utils.enums import BookingStatus, PricingType
import random
from sqlalchemy import text, desc, func, select, extract, or_, cast, String

from app.models import ServiceProvider, Users, Services, BookingCancellation, BookingCancellationReason, Account, ServiceRateSheet
from app.repositories.booking import create_booking_history
from app.tasks import cancel_booking_if_no_response
import requests
from app.config import settings
from app.utils.notification import get_user, get_service_provider_by_id

router = APIRouter(prefix="/booking", tags=["booking"])

# @router.post("/request")
# async def request_booking(
#     user_id: str,
#     artisan_id: str,
#     service_id: str,
#     db: Session = Depends(get_db)
# ):
#     # Check if artisan exists
#     artisan = db.query(ServiceProvider).filter(ServiceProvider.id == artisan_id).first()
#     if not artisan:
#         raise HTTPException(status_code=404, detail="Artisan not found")

#     # Create new booking
#     new_booking = Booking(
#         id=uuid.uuid4(),
#         user_id=user_id,
#         artisan_id=artisan_id,
#         service_id=service_id,
#         status="pending"
#     )
#     db.add(new_booking)
#     db.commit()

#     # Send notification to artisan
#     send_notification(artisan_id, "New Booking Request", "You have a new booking request.")

#     # Start background task to monitor response
#     asyncio.create_task(wait_for_artisan_response(new_booking.id, db))

#     return StandardResponse(status_code=200, message="Booking request sent", detail={"booking_id": new_booking.id})


async def generate_booking_order_id(db: Session, service_code: str) -> str:
    """
    Generate a unique booking order ID in the format: {SERVICE_CODE}-MMYY-COUNT
    where:
    - SERVICE_CODE: The service code (e.g., JACCC)
    - MMYY: 2-digit month + Last 2 digits of current year
    - COUNT: Sequential number for the service in the current year and month (without leading zeros)
    """
    try:
        now = datetime.now()
        year_suffix = str(now.year)[-2:]  # Last 2 digits of year
        month_suffix = f"{now.month:02d}"  # 2-digit month
        
        # Use the service code directly
        service_prefix = str(service_code).upper()
        
        prefix_pattern = f"{service_prefix}-{month_suffix}{year_suffix}%"
        
        # Use SQLAlchemy ORM to query the latest booking with this prefix
        latest_booking = (
            db.query(Booking.booking_order_id)
            .filter(
                Booking.booking_order_id.like(prefix_pattern),
                extract('year', Booking.requested_time) == now.year,
                extract('month', Booking.requested_time) == now.month
            )
            .order_by(Booking.requested_time.desc())
            .first()
        )
        
        if latest_booking and latest_booking.booking_order_id:
            try:
                last_count = int(latest_booking.booking_order_id.split("-")[-1])
            except (ValueError, IndexError):
                last_count = 0
        else:
            last_count = 0
            
        new_count = last_count + 1
        
        # Format without leading zeros, just the raw count number
        return f"{service_prefix}-{month_suffix}{year_suffix}-{new_count}"
    except Exception as e:
        print(f"Error generating booking order ID: {str(e)}")
        # Fallback to a simpler ID
        return f"{str(service_code).upper()}-{month_suffix}{year_suffix}-1"


@router.post("/request")
async def request_booking(
    request: BookingRequest,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    # Convert date and time
    resp = await get_id_header(Authorization)
    if resp.status_code != 200:
        return ErrorResponse(status_code=401, message="Unauthorized", error={})

    account_id = resp.json().get("account_id")

    try:
        print(account_id, "account_id")
        # Execute native query to get user_id from profile service's database
        query = text(
            """
            SELECT id
            FROM users
            WHERE id = :account_id
        """
        )
        result = db.execute(query, {"account_id": account_id}).first()

        if not result:
            return ErrorResponse(status_code=404, message="User not found")

        user_id = result[0]

        booking_date = datetime.strptime(request.booking_date, "%Y-%m-%d").date()

        # service = requests.get(f"{settings.BE_SERVICE_API_URL}/services-read/{request.service_id}",
        #                        headers={"Authorization": f'Bearer {Authorization}'}
        #                        )
        # print(service.json(), "service")

        start_time = datetime.strptime(request.start_time, "%H:%M").time()
        end_time = datetime.strptime(request.end_time, "%H:%M").time()

        # if request.start_time:
        #     start_time = datetime.strptime(request.start_time, "%H:%M").time()
        # else:
        #     start_time = datetime.now + timedelta(minutes=90)

        # end_time = (datetime.combine(datetime.min, start_time) + timedelta(minutes=service.json().get("duration"))).time()

        # Generate random 4-digit OTPs
        start_otp = str(random.randint(1000, 9999))
        end_otp = str(random.randint(1000, 9999))
        while end_otp == start_otp:  # Ensure they're different
            end_otp = str(random.randint(1000, 9999))

        # Check if service exists using ORM
        service = db.query(Services).filter(Services.id == request.service_id).first()
        
        if not service:
            return ErrorResponse(status_code=404, message="Service not found")

        # Make sure service has a service_code
        if not service.service_code:
            return ErrorResponse(status_code=400, message="Service code is missing for this service")
        # Check if artisan exists in service_rate_sheet
        rate_sheet = db.query(ServiceRateSheet).filter(
            ServiceRateSheet.service_id == request.service_id,
            ServiceRateSheet.artisan_id == request.artisan_id
        ).first()

        if not rate_sheet:
            return ErrorResponse(status_code=404, message="Service Provider not available for this service")

        # Generate booking order ID
        booking_order_id = await generate_booking_order_id(db, service.service_code)
        
        # Create new booking with user_id
        surcharges = get_surcharges(rate_sheet.price_per_unit)
        tax = get_tax(rate_sheet.price_per_unit)

        # Default booking_fee and final_amount
        booking_fee = 0.0
        final_amount = 0.0

        # Apply logic for FIXED pricing
        if rate_sheet.pricing_type == PricingType.FIXED:
            final_amount = booking_fee + rate_sheet.price_per_unit + rate_sheet.price_per_unit + surcharges + tax

        new_booking = Booking(
            id=uuid.uuid4(),
            user_id=user_id,  # Use user_id from profile service
            booking_order_id=booking_order_id,
            artisan_id=request.artisan_id,
            service_id=request.service_id,
            description=request.description,
            booking_date=booking_date,
            start_time=start_time,
            end_time=end_time,
            user_latitude=request.user_latitude,
            user_longitude=request.user_longitude,
            user_address=request.user_address,
            service_fee=rate_sheet.price_per_unit, # base fee service fee per unit
            base_service_fee=rate_sheet.price_per_unit, #after negosiation
            surcharges=surcharges,
            tax=tax,
            status=BookingStatus.PENDING,
            start_otp=start_otp,
            end_otp=end_otp,
            pricing_type=request.pricing_type,
            booking_fee_status=False,
            final_payment_status=False,
            booking_fee_amount=rate_sheet.booking_fee,
            final_amount=final_amount
            # payment_type=request.payment_type,
            # payment_id=request.payment_id,
        )
        db.add(new_booking)
        db.commit()
        create_booking_history(db, booking_obj=new_booking)

        print("Sending first notification", new_booking.id)
        user = get_user(user_id, Authorization)
        print(user.get("data", {}).get("first_name"), "user")

        service_query = text(
            """
            SELECT name
            FROM services
            WHERE id = :service_id
        """
        )
        service_result = db.execute(
            service_query, {"service_id": request.service_id}
        ).first()
        print(service_result, "service_result")

        base_service_fee = new_booking.base_service_fee
        surcharges = new_booking.surcharges
        if not base_service_fee:
            base_service_fee = 0
        if not surcharges:
            surcharges = 0
        data = {
            "booking_date": str(new_booking.booking_date),
            "booking_order_id": str(new_booking.booking_order_id),
            "booking_id": str(new_booking.id),
            "user_name": f"{user.get('data', {}).get('first_name', '')} {user.get('data', {}).get('last_name', '')}".strip(),
            "user_profile": user.get("data", {}).get("profile_pic", ""),
            "user_latitude": new_booking.user_latitude,
            "user_longitude": new_booking.user_longitude,
            "service": service_result[0],
            "start_time": str(new_booking.start_time),
            "end_time": str(new_booking.end_time),
            "amount": base_service_fee + surcharges + tax,
        }
        print(data, "push dataaaaaa")
        send_push_notification(
            auth_token=Authorization,
            title="New Booking Request",
            message=f"You have a new booking request - {new_booking.id}",
            sender_id=request.artisan_id,
            type="service_provider",
            data=data,
        )
        # Start background task to monitor response
        # asyncio.create_task(wait_for_artisan_response(str(new_booking.id), db, Authorization, str(request.artisan_id), str(user_id)))

        # # ✅ Use Celery instead of asyncio
        # wait_for_artisan_response.apply_async(
        #     args=[
        #         str(new_booking.id),
        #         str(request.artisan_id),
        #         str(user_id),
        #         Authorization,
        #         data,
        #         db
        #     ],
        #     countdown=300,
        # )

        # After booking is created:
        # send_artisan_reminder.apply_async(
        #     args=[
        #         str(new_booking.id),
        #         str(request.artisan_id),
        #         Authorization,
        #         data,
        #     ],
        #     countdown=60
        # )
        cancel_booking_if_no_response.apply_async(
            args=[
                str(new_booking.id),
                str(request.artisan_id),
                str(user_id),
                Authorization,
                data,
            ],
            queue="booking_queue",
            countdown=2 * 60 * 60,  # 2 hours
        )

        return StandardResponse(
            status_code=200,
            message="Booking request sent",
            data={
                "booking_id": new_booking.id,
                "booking_order_id": new_booking.booking_order_id,
                "start_otp": start_otp,  # Include OTPs in response
                "end_otp": end_otp,
                "payment_type": new_booking.payment_type,
            },
        )
    except Exception as e:
        print(e)
        db.rollback()
        return ErrorResponse(
            status_code=500, message="Error creating booking", error=str(e)
        )


async def wait_for_artisan_response_background(
    booking_id: str, db: Session, token: str, artisan_id: str, user_id: str
):
    """
    Waits for the artisan's response with two notification attempts.
    If no response is given within 5 minutes, the booking is auto-cancelled.
    """
    await asyncio.sleep(180)  # Wait 3 minutes
    # Check booking status
    booking = db.query(Booking).filter(Booking.id == booking_id).first()
    if not booking or booking.status != BookingStatus.PENDING:
        return  # If already handled, exit

    print("Sending second notification")
    # Send second notification
    # await send_push_notification(auth_token=token, message="Reminder: Booking Request, Please respond to the booking request.", sender_id=artisan_id)
    # await send_push_notification(
    #     auth_token=token,
    #     title="Remainder",
    #     message=f"You have a new booking request - {booking_id} ",
    #     sender_id=artisan_id,
    #     type="service_provider",
    # )
    # await asyncio.sleep(120)  # Wait 2 more minutes
    # Check again before auto-cancelling
    booking = db.query(Booking).filter(Booking.id == booking_id).first()
    if booking and booking.status == BookingStatus.PENDING:
        booking.status = BookingStatus.CANCELLED
        booking.cancellation_reason = "Artisan Not Responded"
        db.commit()
        create_booking_history(db, booking_obj=booking)
        print("Sending auto-cancelled notification")

        # Notify both user and artisan
        send_push_notification(
            auth_token=token,
            title="Booking Cancelled",
            message=f"The artisan did not respond in time - {booking_id}",
            sender_id=user_id,
            type="user",
        )
        send_push_notification(
            auth_token=token,
            title="Booking Cancelled",
            message=f"Your booking request has been auto-cancelled - {booking_id}",
            sender_id=artisan_id,
            type="service_provider",
        )


@router.post("/response")
async def artisan_response(
    request: BookingResponse,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")

        booking = db.query(Booking).filter(Booking.id == request.booking_id).first()

        if not booking:
            return ErrorResponse(status_code=404, message="Booking not found")

        if booking.status == BookingStatus.ONGOING:
            return ErrorResponse(
                status_code=400, message="Booking is already processed"
            )

        if request.response == "accept":
            booking.status = BookingStatus.CONFIRMED
            message = "Booking confirmed"
            print("Booking Status: ", booking.status)
            send_push_notification(
                auth_token=Authorization,
                title="Booking Confirmed",
                message=f"Booking Confirmed, The artisan has accepted your booking - Start OTP:{booking.start_otp}",
                sender_id=str(booking.user_id),
                type="user",
                data={
                    "start_otp": booking.start_otp,
                },
            )

        elif request.response == "reject":
            booking.status = BookingStatus.REJECTED
            message = "Booking rejected"
            send_push_notification(
                auth_token=Authorization,
                title="Booking Rejected",
                message=f"Booking Rejected, The artisan has Rejected your booking - {request.booking_id}",
                sender_id=str(booking.user_id),
                type="user",
            )

        elif request.response == "cancelled":
            booking.status = BookingStatus.CANCELLED
            message = "Booking cancelled"
            booking.cancellation_reason = request.reason
            send_push_notification(
                auth_token=Authorization,
                title="Booking Cancelled",
                message=f"Booking Cancelled, Your Booking has been Cancelled - {request.booking_id}",
                sender_id=str(booking.user_id),
                type="user",
            )

        else:
            return ErrorResponse(status_code=400, message="Invalid response")
        print("response about to commit")
        db.commit()
        create_booking_history(db, booking_obj=booking)
        return StandardResponse(status_code=200, message=message)
    except Exception as e:
        return ErrorResponse(
            status_code=500, message="Error processing booking", error=str(e)
        )


@router.post("/start")
async def start_booked_service(
    request: ServiceStart,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            email = resp_json.get("email")
        else:
            # return {"error": resp.json()}
            return ErrorResponse(status_code=401, message="Unauthorized")
        print(request.id, "request.id", type(request.id))
        booking_data = db.query(Booking).filter(Booking.id == request.id).first()
        print(booking_data, "booking_data")
        print(request, "request")
        if not booking_data:
            return ErrorResponse(status_code=404, message="Booking not found")

        if booking_data.service_start is not None:
            return ErrorResponse(status_code=400, message="Service already started")
        print(booking_data.start_otp, request.start_otp)
        if booking_data.start_otp != request.start_otp:  # Check against start_otp
            return ErrorResponse(status_code=400, message="Invalid start OTP")

        booking = start_service(db, request, booking_data, Authorization)
        # return {"status_code": 200, "data": "Booking started successfully"}
        return StandardResponse(status_code=200, message="Booking started successfully")

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error", error=str(e))


@router.post("/end")
async def end_booked_service(
    request: ServiceEnd,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            email = resp_json.get("email")
        else:
            # return {"error": resp.json()}
            return ErrorResponse(status_code=401, message="Unauthorized")

        booking_data = db.query(Booking).filter(Booking.id == request.id).first()
        if not booking_data:
            return ErrorResponse(status_code=404, message="Booking not found")

        if booking_data.service_start is None:
            return ErrorResponse(status_code=400, message="Service not started")

        if booking_data.service_end is not None:
            return ErrorResponse(status_code=400, message="Service already ended")

        if booking_data.end_otp != request.stop_otp:  # Check against end_otp
            return ErrorResponse(status_code=400, message="Invalid end OTP")

        booking = end_service(db, request, Authorization)
        # return {"status_code": 200, "data": "Booking Ended successfully"}
        return StandardResponse(status_code=200, message="Booking Ended successfully")

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error", error=str(e))


@router.post("/reschedule-request")
async def reschedule_request_booked_service(
    request: ServiceRescheduleRequest,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            email = resp_json.get("email")
        else:
            # return {"error": resp.json()}
            return ErrorResponse(status_code=401, message="Unauthorized")

        booking_data = db.query(Booking).filter(Booking.id == request.id).first()

        start_time = datetime.strptime(request.start_time, "%H:%M").time()
        end_time = datetime.strptime(request.end_time, "%H:%M").time()
        booking_date = datetime.strptime(request.booking_date, "%Y-%m-%d").date()

        booking_data.is_notify = False
        booking_data.booking_date = booking_date
        booking_data.start_time = start_time
        booking_data.end_time = end_time

        db.commit()
        db.refresh(booking_data)

        # booking = reschedule_service_request(db, booking_data, request, Authorization)

        send_push_notification(
            title=f"Reschedule Request for {booking_data.booking_order_id}",
            auth_token=Authorization,
            message="Reschedule Request hase been accepted by user.",
            sender_id=str(booking_data.artisan_id),
            type="service_provider",
        )


        # return {"data": "Reschedule request raised successfully"}
        return StandardResponse(
            status_code=200, message="Reschedule request has been accepted successfully"
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error", error=str(e))


@router.post("/reschedule-confirm")
async def reschedule_confirm_booked_service(
    request: ServiceReschedule,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
            email = resp_json.get("email")
        else:
            # return {"error": resp.json()}
            return ErrorResponse(status_code=401, message="Unauthorized")

        booking = reschedule_service(db, request)
        # return {"data": "Rescheduled successfully"}
        return StandardResponse(status_code=200, message="Rescheduled successfully")

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error", error=str(e))


@router.post("/history")
async def get_booking_history(
    request: BookingHistoryRequest,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Get booking history based on either artisan_id or user_id.
    At least one of artisan_id or user_id must be provided.
    """
    try:
        # Validate authorization
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")

        # if not request.artisan_id and not request.user_id:
        #     return ErrorResponse(
        #         status_code=400, message="Either artisan_id or user_id must be provided"
        #     )

        # Build the query based on provided parameters
        query = (
            db.query(
                Booking, Users, ServiceProvider, Services, BookingCancellation  # Added service table join
            )
            .outerjoin(Users, Booking.user_id == Users.id)
            .outerjoin(ServiceProvider, Booking.artisan_id == ServiceProvider.id)
            .outerjoin(Services, Booking.service_id == Services.id)
            .outerjoin(BookingCancellation, Booking.id == BookingCancellation.booking_id)
        )

        if request.artisan_id:
            query = query.filter(Booking.artisan_id == request.artisan_id)

        elif request.user_id:
            query = query.filter(Booking.user_id == request.user_id)
        
        if request.q:
            query = query.filter(
                or_(
                    cast(Booking.id, String).ilike(f"%{request.q}%"),
                    Booking.booking_order_id.ilike(f"%{request.q}%"),
                )
            )
        if request.booking_id is not None:
            query = query.filter(Booking.id == request.booking_id)

        if request.booking_status != "all":
            query = query.filter(Booking.status == request.booking_status)

        # Order by booking_date and start_time descending (most recent first)
        query = query.order_by(
            desc(Booking.booking_date),
            desc(Booking.start_time)
        )

        # Execute query and fetch results
        total_count = query.count()
        offset = (request.skip - 1) * request.limit if request.skip > 0 else 0
        booking_history = query.offset(offset).limit(request.limit).all()

        if not booking_history:
            return StandardResponse(
                status_code=200, message="No booking history found", data=[]
            )

        # Format the response
        history_data = []
        # print(json.dumps(booking_history), "booking_history")

        for history, user, artisan, service, booking_cancellation in booking_history:
            history_data.append(
                {
                    "id": str(history.id),
                    "booking_id": str(history.id),
                    "service_id": str(history.service_id),
                    "status": history.status,
                    "description": history.description,
                    "booking_date": history.booking_date,
                    "start_time": history.start_time,
                    "end_time": history.end_time,
                    "start_otp": history.start_otp,
                    "end_otp": history.end_otp,
                    "user_latitude": history.user_latitude,
                    "user_longitude": history.user_longitude,
                    "user_address": history.user_address,
                    "base_service_fee": history.base_service_fee,
                    "surcharges": history.surcharges,
                    "tax": history.tax,
                    "payment_type": history.payment_type,
                    "booking_order_id": history.booking_order_id,
                    "description_for_negotiation": history.description_for_negotiation,
                    # "negotiation_price": history.base_service_fee,
                    "pricing_type": history.pricing_type,
                    "booking_fee_status": history.booking_fee_status,
                    "final_payment_status": history.final_payment_status,
                    "booking_fee_amount": history.booking_fee_amount,
                    "final_amount": history.final_amount,
                    # "total_price": history.base_service_fee + history.surcharges,
                    # "cancellation_reason": history.cancellation_reason,
                    # User Details
                    "user": (
                        {
                            "id": str(user.id),
                            "first_name": user.first_name,
                            "last_name": user.last_name,
                            "profile_pic": user.profile_pic,
                        }
                        if user
                        else None
                    ),
                    # Service Provider (Artisan) Details
                    "artisan": (
                        {
                            "id": str(artisan.id),
                            # "auth_id": artisan.auth_id,
                            "profile_pic": artisan.profile_pic,
                            "first_name": artisan.first_name,
                            "last_name": artisan.last_name,
                            "latitude": artisan.latitude,
                            "longitude": artisan.longitude,
                        }
                        if artisan
                        else None
                    ),
                    # Service Details
                    "service": (
                        {
                            "id": str(service.id),
                            "name": service.name,
                            "duration": service.duration,
                            "price": service.price,
                            "category_name": service.category.name,
                            # "description": service.description,
                            # "is_multiple": service.is_multiple
                        }
                        if service
                        else None
                    ),
                    "is_cancelled": True if booking_cancellation else False
                }
            )

        return StandardResponse(
            status_code=200,
            message="Booking history retrieved successfully",
            data={
                "bookings": history_data,
                "pagination": {
                    "total": total_count,
                    "page": request.skip,
                    "limit": request.limit,
                    "pages": (total_count + request.limit - 1) // request.limit,
                }
            },
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error", error=str(e))


@router.post("/latest_booking")
async def get_latest_booking(
    request: LatestBookingRequest,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Get latest booking based on either artisan_id or user_id.
    At least one of artisan_id or user_id must be provided.
    """
    try:
        # Validate authorization
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")

        query = (
            # db.query(Booking, Users, ServiceProvider, Services, Payment)
            db.query(Booking, Users, ServiceProvider, Services)
            .outerjoin(Users, Booking.user_id == Users.id)
            .outerjoin(ServiceProvider, Booking.artisan_id == ServiceProvider.id)
            .outerjoin(Services, Booking.service_id == Services.id)
            # .outerjoin(Payment, Booking.payment_id == Payment.id)
        )

        if request.artisan_id:
            query = query.filter(Booking.artisan_id == request.artisan_id)
        elif request.user_id:
            query = query.filter(Booking.user_id == request.user_id)

        if request.booking_status != "all":
            query = query.filter(Booking.status == request.booking_status)

        query = query.order_by(
            # Booking.created_at.desc(),
            Booking.requested_time.desc(),
        ).limit(1)

        latest_record = query.first()

        if not latest_record:
            return StandardResponse(
                status_code=200, message="No booking history found", data=None
            )

        # history, user, artisan, service, payment = latest_record
        history, user, artisan, service = latest_record

        # Get account information for penalty amount
        penalty_amount = 0
        if user:
            account = db.query(Account).filter(
                Account.user_id == user.id,
                Account.is_active == True
            ).first()
            
            if account and account.balance < 0:
                penalty_amount = abs(account.balance)

        history_data = {
            "id": str(history.id),
            "booking_id": str(history.id),
            "service_id": str(history.service_id),
            "status": history.status,
            "description": history.description,
            "booking_date": history.booking_date,
            "start_time": history.start_time,
            "end_time": history.end_time,
            "start_otp": history.start_otp,
            "end_otp": history.end_otp,
            "user_latitude": history.user_latitude,
            "user_longitude": history.user_longitude,
            "user_address": history.user_address,
            "base_service_fee": history.base_service_fee,
            "surcharges": history.surcharges,
            "tax": history.tax,
            "payment_type": history.payment_type,
            "booking_order_id": history.booking_order_id,
            "service_start_time": history.service_start,
            "service_end_time": history.service_end,
            "penalty_amount": penalty_amount,
            "user": (
                {
                    "id": str(user.id),
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "profile_pic": user.profile_pic,
                }
                if user
                else None
            ),
            "artisan": (
                {
                    "id": str(artisan.id),
                    "profile_pic": artisan.profile_pic,
                    "first_name": artisan.first_name,
                    "last_name": artisan.last_name,
                    "latitude": artisan.latitude,
                    "longitude": artisan.longitude,
                }
                if artisan
                else None
            ),
            "service": (
                {
                    "id": str(service.id),
                    "name": service.name,
                    "duration": service.duration,
                    "price": service.price,
                    "category_name": service.category.name,
                }
                if service
                else None
            ),
            # "payment": payment if payment else None,
        }

        return StandardResponse(
            status_code=200,
            message="Latest booking retrieved successfully",
            data=history_data,
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error", error=str(e))


@router.get("/{id}")
async def get_booking_by_id(
    id: str,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Get booking based on id.
    """
    try:
        # Validate authorization
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")

        query = (
            db.query(Booking, Users, ServiceProvider, Services)
            .outerjoin(Users, Booking.user_id == Users.id)
            .outerjoin(ServiceProvider, Booking.artisan_id == ServiceProvider.id)
            .outerjoin(Services, Booking.service_id == Services.id)
            # .outerjoin(Payment, Booking.id == Payment.service_request_id)
        )

        query = query.filter(Booking.id == id)

        record = query.first()

        if not record:
            return StandardResponse(
                status_code=200, message="No booking history found", data=None
            )

        history, user, artisan, service = record

        # Get account information for penalty amount
        penalty_amount = 0
        if user:
            account = db.query(Account).filter(
                Account.user_id == user.id,
                Account.is_active == True
            ).first()
            
            if account and account.balance < 0:
                penalty_amount = abs(account.balance)

        history_data = {
            "id": str(history.id),
            "booking_id": str(history.id),
            "service_id": str(history.service_id),
            "status": history.status,
            "description": history.description,
            "booking_date": history.booking_date,
            "start_time": history.start_time,
            "end_time": history.end_time,
            "start_otp": history.start_otp,
            "end_otp": history.end_otp,
            "user_latitude": history.user_latitude,
            "user_longitude": history.user_longitude,
            "user_address": history.user_address,
            "base_service_fee": history.base_service_fee,
            "surcharges": history.surcharges,
            "tax": history.tax,
            "payment_type": history.payment_type,
            "booking_order_id": history.booking_order_id,
            "penalty_amount": penalty_amount,
            "user": (
            {
                "id": str(user.id),
                # "first_name": user.first_name,
                "last_name": user.last_name,
                "profile_pic": user.profile_pic,
                "phone_number": history.phone_number if history.phone_number else user.phone_number,
                "first_name": history.name if history.name else user.first_name,
            }
            if user
            else None
            ),
            "artisan": (
                {
                    "id": str(artisan.id),
                    "profile_pic": artisan.profile_pic,
                    "first_name": artisan.first_name,
                    "last_name": artisan.last_name,
                    "latitude": artisan.latitude,
                    "longitude": artisan.longitude,
                    "phone_number": artisan.phone_number,
                }
                if artisan
                else None
            ),
            "service": (
                {
                    "id": str(service.id),
                    "name": service.name,
                    "duration": service.duration,
                    "price": service.price,
                    "category_name": service.category.name,
                }
                if service
                else None
            ),
        }

        return StandardResponse(
            status_code=200,
            message="Booking retrieved successfully",
            data=history_data,
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error", error=str(e))


@router.put("/status-update")
async def update_booking_status(
    request: UpdateBookingStatusRequest,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")

        booking_result = db.query(Booking).filter(Booking.id == request.id).first()

        if not booking_result:
            return ErrorResponse(status_code=404, message="Booking not found")

        if booking_result.status == request.status:
            return ErrorResponse(
                status_code=400, message="Booking status is already updated"
            )

        setattr(booking_result, "status", request.status)

        db.commit()
        db.refresh(booking_result)

        if request.status == BookingStatus.ARRIVED:
            send_push_notification(
                auth_token=Authorization,
                title=f"Artisan Arrived - {booking_result.start_otp}",
                message=f"Start OTP:{booking_result.start_otp} - Artisan has arrived to your location!",
                sender_id=str(booking_result.user_id),
                type="user",
                data={
                    "start_otp": booking_result.start_otp,
                },
            )

        if request.status == BookingStatus.STARTED:
            send_push_notification(
                auth_token=Authorization,
                title="Service Started",
                message=f"Service has started",
                sender_id=str(booking_result.user_id),
                type="user",
                data={
                    "end_otp": booking_result.end_otp,
                },
            )

        return StandardResponse(
            status_code=200,
            message="Booking status updated successfully",
            data=booking_result,
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error", error=str(e))

# Update negotiate price by Artisan not user
@router.put("/negotiate-price")
async def negotiate_price(
    request: NegotiationRequest,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        # Validate Authorization
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")
        resp = resp.json()
        role = resp.get('user_role')

        print("role", role)

        # Get booking from DB
        booking = db.query(Booking).filter(Booking.id == request.id).first()
        if not booking:
            return ErrorResponse(status_code=404, message="Booking not found")
        
        # Get the service name
        service_data = db.query(Services).filter(Services.id == booking.service_id).first()

        if not service_data:
            return ErrorResponse(
                status_code=400, message="Service provider data was not found"
            )
        
        service_name = service_data.name
        
        if booking.status == "ACCEPTED":
            return ErrorResponse(
                status_code=400, message="Booking status is already updated, now you can't negotiation"
            )
        
        if request.status == BookingStatus.REJECTED:
            # Calculate surcharges and tax based on the base service fee for estimation
            surcharges = get_surcharges(booking.base_service_fee)
            tax = get_tax(booking.base_service_fee)

            # Update booking status to Rejected
            booking.status = BookingStatus.REJECTED
            # booking.tax = tax # Check with vignesh for this updation
            # booking.surcharges = surcharges  # Check with vignesh for this updation

            db.commit()
            db.refresh(booking)

            # Notify the user that the booking has been Rejected before negotiation took place
            send_push_notification(
                auth_token=Authorization,
                title="Booking Rejected",
                message="Your booking has been Rejected.",
                sender_id=str(booking.user_id),
                type="user",
                data={
                    "booking_id": str(booking.id),
                    "service_name": service_name,
                    "booking_order_id": booking.booking_order_id,
                    "platform_fees": surcharges,
                    "tax": tax,
                    "total_amount": booking.base_service_fee + float(surcharges or 0) + float(tax or 0)
                },
            )

            return StandardResponse(
                status_code=200,
                message="Booking Rejected and notification sent to the user.",
                data=booking,
            )

        elif request.status == BookingStatus.CANCELLED:
            # Create cancellation reason record
            cancellation_reason = BookingCancellationReason(
                reason=request.cancellation_reason,
                user_type="ARTISAN",
                is_negotiate_cancel=True
            )
            db.add(cancellation_reason)
            db.commit()
            db.refresh(cancellation_reason)

            # Update booking with cancellation reason and status
            booking.status = BookingStatus.CANCELLED
            booking.booking_cancellation_reason_id = cancellation_reason.id

            # Estimate surcharges and tax for refund details
            surcharges = get_surcharges(booking.base_service_fee)
            tax = get_tax(booking.base_service_fee)

            # Send notification to user
            send_push_notification(
                auth_token=Authorization,
                title="Booking Cancelled",
                message="Your booking has been cancelled.",
                sender_id=str(booking.user_id),
                type="user",
                data={
                    "booking_id": str(booking.id),
                    "service_name": service_name,
                    "cancellation_reason": request.cancellation_reason,
                    "booking_order_id": booking.booking_order_id,
                    "platform_fees": surcharges,
                    "tax": tax,
                    "total_amount": booking.base_service_fee + float(surcharges or 0) + float(tax or 0)
                },
            )

            db.commit()
            db.refresh(booking)

            return StandardResponse(
                status_code=200,
                message="Booking cancelled successfully and notification sent.",
                data=booking,
            )

        if not request.negotiation_price:
            return ErrorResponse(status_code=422, message="Doing negotiation need negotiation_price")

        # Get the rengotiation price
        surcharges = get_surcharges(request.negotiation_price)
        tax = get_tax(request.negotiation_price)

        # Update the base_service_fee
        booking.base_service_fee = request.negotiation_price
        booking.description_for_negotiation = request.description_for_negotiation
        booking.surcharges = surcharges
        booking.final_amount = tax + surcharges + request.negotiation_price
        booking.tax = tax
        booking.end_time = str(request.end_time)
        
        db.commit()
        db.refresh(booking)

        # Send push notification to User
        send_push_notification(
            auth_token=Authorization,
            title="Negotiation Request Sent",
            message="Negotiation request sent to user.",
            sender_id=str(booking.user_id),
            type="user",
            data={
                "booking_id": str(booking.id),
                "service_name": service_name,
                "negotiation_price": request.negotiation_price,
                "description_for_negotiation": request.description_for_negotiation,
                "booking_order_id": booking.booking_order_id,
                "platform_fees": surcharges,
                "tax": tax,
                "total_amount": float(request.negotiation_price or 0) + float(surcharges or 0) + float(tax or 0)
            },
        )

        return StandardResponse(
            status_code=200,
            message="Negotiation price updated and notifications sent successfully.",
            data=booking,
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message="Internal Server Error", error=str(e))
    
@router.put("/negotiate-response")
async def respond_to_negotiation(
    request: NegotiationResponseRequest,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        # Auth check
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")

        user_info = resp.json()
        role = user_info.get("user_role")
        user_id = user_info.get("account_id")

        print('roleeeeeeeeeeeee', role)
        print('user_i', user_id)

        # Fetch booking
        booking = db.query(Booking).filter(Booking.id == request.booking_id).first()
        if not booking:
            return ErrorResponse(status_code=404, message="Booking not found")
        
        
        user_details = db.query(Users).filter(Users.id == user_id).first()
        if not user_details:
            return ErrorResponse(status_code=404, message="User not found")
        
        user_name = user_details.first_name
        phone_number = user_details.phone_number
        locations = user_details.locations

        if locations and isinstance(locations, list) and isinstance(locations[0], dict):
            address = locations[0].get("address") or None
        else:
            address = None

        print("address",address)

        if request.status == BookingStatus.REJECTED:
            # Calculate surcharges and tax based on the base service fee for estimation
            surcharges = get_surcharges(booking.base_service_fee)
            tax = get_tax(booking.base_service_fee)

            # Update booking status to Rejected
            booking.status = BookingStatus.REJECTED
            # booking.tax = tax # Check with vignesh for this updation
            # booking.surcharges = surcharges  # Check with vignesh for this updation

            db.commit()
            db.refresh(booking)

            # Notify the user that the booking has been Rejected before negotiation took place
            send_push_notification(
                auth_token=Authorization,
                title="Booking Rejected",
                message="Booking rejected and notification sent.",
                sender_id=str(booking.artisan_id),
                type="service_provider",
                data={
                    "booking_id": str(booking.id),
                    "booking_order_id": booking.booking_order_id,
                    "user_name": user_name,
                    "phone_number": phone_number,
                    "address": address,
                    # "cancellation_reason": request.description_for_negotiation,
                    "platform_fees": surcharges,
                    "tax": tax,
                    "total_amount": booking.base_service_fee + float(surcharges or 0) + float(tax or 0)
                },
            )

            return StandardResponse(
                status_code=200,
                message="Booking Rejected and notification sent to the user.",
                data=booking,
            )

        elif request.status == BookingStatus.CANCELLED:
            # Create cancellation reason record
            cancellation_reason = BookingCancellationReason(
                reason=request.cancellation_reason,
                user_type="USER",
                is_negotiate_cancel=True
            )

            db.add(cancellation_reason)
            db.commit()
            db.refresh(cancellation_reason)
            
            # Update booking with cancellation reason and status
            booking.status = BookingStatus.CANCELLED
            booking.booking_cancellation_reason_id = cancellation_reason.id

            # Estimate surcharges and tax for refund details
            surcharges = get_surcharges(booking.base_service_fee)
            tax = get_tax(booking.base_service_fee)

            # Send notification to user
            send_push_notification(
                auth_token=Authorization,
                title="Booking Cancelled",
                message="Your booking has been cancelled.",
                sender_id=str(booking.artisan_id),
                type="service_provider",
                data={
                    "booking_id": str(booking.id),
                    "user_name": user_name,
                    "phone_number": phone_number,
                    "address": address,
                    "cancellation_reason": request.cancellation_reason,
                    "booking_order_id": booking.booking_order_id,
                    "platform_fees": surcharges,
                    "tax": tax,
                    "total_amount": booking.base_service_fee + float(surcharges or 0) + float(tax or 0)
                },
            )

            db.commit()
            db.refresh(booking)

            return StandardResponse(
                status_code=200,
                message="Booking cancelled successfully and notification sent.",
                data=booking,
            )


        # Get the rengotiation price
        base_service_fee = booking.base_service_fee
        surcharges = get_surcharges(base_service_fee)
        tax = get_tax(base_service_fee)

        # update the value once user will accept the service
        booking.tax = tax
        booking.surcharges = surcharges

        db.commit()
        db.refresh(booking)


        # Send push notification to Artisan
        send_push_notification(
            auth_token=Authorization,
            title="The user has responded to the negotiation request",
            message="The user has responded to the negotiation request.",
            sender_id=str(booking.artisan_id),
            type="service_provider",
            data={
                "booking_id": str(booking.id),
                "user_name": user_name,
                "phone_number": phone_number,
                "booking_order_id": booking.booking_order_id,
                "address": address,
                "negotiation_price": base_service_fee,
                "platform_fees": surcharges,
                "tax": tax,
                "total_amount": float(base_service_fee or 0) + float(surcharges or 0) + float(tax or 0)
            },
        )

        return StandardResponse(
            status_code=200,
            message="User accepted the service and notifications sent artisan successfully.",
            data=booking,
        )
    
    except Exception as e:
        db.rollback()
        return ErrorResponse(status_code=500, message="Internal Server Error", error=str(e))


# It's used to reschedule booking by artisan.
@router.put("/reschedule-booking-notify-to-user")
async def reschedule_booking_notify_to_user(
    request: BookingIdRequest,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        # Authorization check
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        user_info = resp.json()
        role = user_info.get("user_role")
        # user_id = user_info.get("account_id")

        if role != "artisan":
            return ErrorResponse(status_code=403, message="User does not have rights to access the reschedule booking.")


        # Fetch booking
        booking = db.query(Booking).filter(Booking.id == request.booking_id).first()
        if not booking:
            return ErrorResponse(status_code=404, message="Booking does't exit")
        
        # Get the service name
        service_data = db.query(Services).filter(Services.id == booking.service_id).first()

        if not service_data:
            return ErrorResponse(
                status_code=400, message="Service provider details are not found."
            )
        
        service_name = service_data.name

        result = (
            db.query(ServiceProvider.first_name, ServiceProvider.last_name, ServiceProvider.rating)
            .join(Booking, Booking.artisan_id == ServiceProvider.id)
            .filter(Booking.id == request.booking_id)
            .first()
        )

        if not result:
            return ErrorResponse(status_code=404, message="service provider details are not found")

        first_name, last_name, service_provider_rating = result
        service_provider_name = f"{first_name} {last_name}"


        # Update status for notify the reschedule booking
        booking.is_notify = True
        db.commit()
        db.refresh(booking)

        # Send push notification to artisan
        send_push_notification(
            auth_token=Authorization,
            title="Request to Reschedule Booking",
            message="would like to request a reschedule for my current booking.",
            sender_id=str(booking.user_id),
            type="user",
            data={
                "booking_id": str(booking.id),
                "service_provider_name": service_provider_name,
                "rating": service_provider_rating,
                "start_time": str(booking.start_time),
                "end_time": str(booking.end_time),
                "booking_date": str(booking.booking_date),
                "service_name": service_name
            },
        )

        return StandardResponse(
            status_code=200,
            message="The Reschedule notification has been sent to user successfully.",
            data= booking.id
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message="Internal Server Error, issue in reschedule_booking_notify_to_user", error=str(e))

# Update the booking cancel for user side and collect the reason and add that details
@router.put("/reschedule-booking-cancel")
async def respond_to_negotiation(
    request: RescheduleResponseRequest,
    db: Session = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        # Auth check
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")

        user_info = resp.json()
        role = user_info.get("user_role")
        user_id = user_info.get("account_id")

        print('roleeeeeeeeeeeee', role)
        print('user_i', user_id)

        if role != "user":
            return ErrorResponse(status_code=403, message="Artisan does not have rights to access cancel booking.")

        # Fetch booking
        booking = db.query(Booking).filter(Booking.id == request.booking_id).first()
        if not booking:
            return ErrorResponse(status_code=404, message="Only users are allowed to cancel bookings.")
        
        
        user_details = db.query(Users).filter(Users.id == user_id).first()
        if not user_details:
            return ErrorResponse(status_code=404, message="User not found")
        
        user_name = user_details.first_name
        phone_number = user_details.phone_number

        if request.status == BookingStatus.CANCELLED:
            # Create cancellation reason record
            cancellation_reason = BookingCancellationReason(
                reason=request.cancellation_reason,
                user_type="USER",
                is_notify_cancel=True
            )

            db.add(cancellation_reason)
            db.commit()
            db.refresh(cancellation_reason)
            
            # Update booking with cancellation reason and status
            booking.status = BookingStatus.CANCELLED
            booking.booking_cancellation_reason_id = cancellation_reason.id

            # Send notification to user
            send_push_notification(
                auth_token=Authorization,
                title="Booking Cancelled",
                message="Your booking has been cancelled.",
                sender_id=str(booking.artisan_id),
                type="service_provider",
                data={
                    "booking_id": str(booking.id),
                    "user_name": user_name,
                    "phone_number": phone_number,
                    "cancellation_reason": request.cancellation_reason,
                    "booking_order_id": booking.booking_order_id,
                },
            )

            db.commit()
            db.refresh(booking)

            return StandardResponse(
                status_code=200,
                message="Booking has been cancelled and sent notification to artisan.",
                data=booking,
            )
    
    except Exception as e:
        db.rollback()
        return ErrorResponse(status_code=500, message="Something went wrong", error=str(e))


@router.put("/update-customer/{booking_id}")
async def update_booking_customer(
    booking_id: str,
    data: BookingUpdateRequest,
    Authorization: Annotated[str, Header()] = None,
    db: Session = Depends(get_db)
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message="Unauthorized")
        
        user_info = resp.json()
        role = user_info.get("user_role")

        if role != "user":
            return ErrorResponse(status_code=403, message="Artisan does not have rights to modify user details.")

        booking = db.query(Booking).filter(Booking.id == booking_id).first()
        if not booking:
            return ErrorResponse(status_code=404, message="Booking is not found")

        # Only update if values are provided
        if data.name is not None:
            booking.name = data.name
        if data.phone_number is not None:
            booking.phone_number = data.phone_number

        db.commit()

        return {
            "message": "Customer information has been updated successfully",
            "user": {
                "id": booking.id,
                "name": booking.name,
                "phone_number": booking.phone_number
            }
        }

    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Internal Server Error, issue in update_booking_customer",
            error=str(e)
        )
