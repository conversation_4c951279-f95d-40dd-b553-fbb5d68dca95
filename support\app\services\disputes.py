# from app.repositories.disputes import (
#     create_issue,
#     update_issue,
#     read_issue,
#     issue_list,
#     delete_issue,
# )
# from app.schemas.disputes import IssuesCreate, IssuesUpdate
from sqlalchemy.orm import Session

# from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import func
from sqlalchemy.future import select

from datetime import datetime
import requests
import json
from app.models import Disputes, IssueCategory
from app.config import get_settings
import logging
from sqlalchemy import desc

logger = logging.getLogger(__name__)


# def create_issue_service(db: Session, data: IssuesCreate):
#     issue = create_issue(db, data)
#     return issue


# def update_issue_service(db: Session, data: IssuesUpdate):
#     issue = update_issue(db, data)
#     return issue


# def read_issue_service(db: Session, issue_id: str):
#     issue = read_issue(db, issue_id)
#     return issue


# def list_issues_service(db: Session):
#     issues = issue_list(db)
#     return issues


# def delete_issue_service(db: Session, issue_id: str):
#     issue = delete_issue(db, issue_id)
#     return issue


# def generate_case_number(db: Session) -> str:
#     """Generate a unique case number for a new dispute"""
#     current_year = datetime.now().year

#     # Get the count of disputes for the current year
#     count = db.query(Disputes).filter(
#         func.extract('year', Disputes.created_at) == current_year
#     ).count()

#     # Format: CASE-YYYY-XXXX (where XXXX is a zero-padded sequential number)
#     return f"CASE-{current_year}-{(count + 1):04d}"


def generate_case_number(db: Session, category_code: str) -> str:
    """
    Generate a unique case number for a new issue in the format: {CATEGORY_CODE}-YYMM-COUNT
    where:
    - CATEGORY_CODE: The code from the category (e.g., JCACC)
    - YYMM: Last 2 digits of current year + 2-digit month
    - COUNT: Sequential number for the category in the current year and month
    """
    try:
        now = datetime.now()
        year_suffix = str(now.year)[-2:]
        month_suffix = f"{now.month:02d}"

        query = (
            select(Disputes.case_number)
            .join(IssueCategory, Disputes.issue_category_id == IssueCategory.id)
            .where(
                IssueCategory.code == category_code,
                func.extract("year", Disputes.created_at) == now.year,
                func.extract("month", Disputes.created_at) == now.month,
            )
            .order_by(desc(Disputes.created_at))
            .limit(1)
        )

        result = db.execute(query)
        last_case = result.scalar()

        if last_case:
            try:
                last_count = int(last_case.split("-")[-1])
            except ValueError:
                last_count = 0
        else:
            last_count = 0

        new_count = last_count + 1

        return f"{category_code}-{month_suffix}{year_suffix}-{new_count}"
    except Exception as e:
        logger.error(f"Error generating case number: {str(e)}")
        raise


def get_user(user_id, authorization):
    """
    Get user details from the profile service

    Args:
        user_id: UUID of the user
        authorization: Authorization header token

    Returns:
        User details as a dictionary or None if not found
    """
    try:
        settings = get_settings()
        # Updated endpoint to a more likely path
        profile_service_url = f"{settings.BE_PROFILE_API_URL}/user-read/{user_id}"

        headers = {"Authorization": authorization, "Content-Type": "application/json"}

        response = requests.get(profile_service_url, headers=headers)

        if response.status_code == 200:
            return response.json()
        else:
            print(
                f"Error getting user details: {response.status_code} - {response.text}"
            )
            return None
    except Exception as e:
        print(f"Exception in get_user: {str(e)}")
        return None


def get_admin(user_id, authorization):
    """
    Get user details from the profile service

    Args:
        user_id: UUID of the user
        authorization: Authorization header token

    Returns:
        User details as a dictionary or None if not found
    """
    try:
        settings = get_settings()
        # Updated endpoint to a more likely path
        profile_service_url = f"{settings.BE_PROFILE_API_URL}/admin-read/{user_id}"

        headers = {"Authorization": authorization, "Content-Type": "application/json"}

        response = requests.get(profile_service_url, headers=headers)

        if response.status_code == 200:
            return response.json()
        else:
            print(
                f"Error getting user details: {response.status_code} - {response.text}"
            )
            return None
    except Exception as e:
        print(f"Exception in get_user: {str(e)}")
        return None


def get_service_provider(artisan_id, authorization):
    """
    Get service provider (artisan) details from the profile service

    Args:
        artisan_id: UUID of the artisan
        authorization: Authorization header token

    Returns:
        Artisan details as a dictionary or None if not found
    """
    try:
        settings = get_settings()
        # Updated endpoint to a more likely path
        profile_service_url = f"{settings.BE_PROFILE_API_URL}/sp-read/{artisan_id}"

        headers = {"Authorization": authorization, "Content-Type": "application/json"}

        response = requests.get(profile_service_url, headers=headers)

        if response.status_code == 200:
            return response.json()
        else:
            print(
                f"Error getting artisan details: {response.status_code} - {response.text}"
            )
            return None
    except Exception as e:
        print(f"Exception in get_service_provider: {str(e)}")
        return None
 