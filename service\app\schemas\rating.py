from pydantic import BaseModel, UUID4
from typing import Optional,Dict, List
from uuid import UUID
from app.models_enum import UserType

class RatingRequest(BaseModel):
    # user_id : Optional[UUID4] = None
    booking_id : Optional[UUID4]= None
    service_id : Optional[UUID4] = None
    invoice_item_id : Optional[UUID4] = None
    rating : int
    rating_tags : Optional[str] = None
    feedback : Optional[str] = None
    user_type : UserType

class ReviewItem(BaseModel):
    user_id: UUID
    first_name: str
    last_name: str 
    profile_picture: Optional[str] = None
    rating: float
    time_ago: str
    feedback: str

class RatingSummaryResponse(BaseModel):
    average_rating: float
    total_reviews: int
    rating_distribution: Dict[int, int]
    reviews: List[ReviewItem]
    pagination: Dict[str, int]


class RatingRequestagent(BaseModel):
    user_id : UUID
    dispute_id : Optional[UUID] = None
    issue_id : Optional[UUID] = None
    rating : int
    user_type : UserType