from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import os
from app.routers import blob_storage


app = FastAPI(debug=True)


origins = [os.getenv("LOCALHOST_URL"), os.getenv("WEBAPP_URL"), os.getenv("UAT_WEBAPP_URL")]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # Allows specified origins
    allow_credentials=True,  # Allows cookies to be included in requests
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)


@app.get("/blob")
def read_root():
    return {"Hello": "Blob"}


app.include_router(blob_storage.router)
