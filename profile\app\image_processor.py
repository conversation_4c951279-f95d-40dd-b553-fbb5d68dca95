from PIL import Image
from io import <PERSON>tes<PERSON>
from fastapi import UploadFile
import uuid
import os

async def generate_thumbnail(file: UploadFile, size: tuple = (100, 100)) -> tuple[BytesIO, str]:
    """
    Generate a thumbnail from an uploaded image file.
    
    Args:
        file (UploadFile): The uploaded image file
        size (tuple): The desired thumbnail size (width, height)
        
    Returns:
        tuple[BytesIO, str]: A tuple containing the thumbnail as Bytes<PERSON> and the filename
    """
    # Read the image file
    contents = await file.read()
    image = Image.open(BytesIO(contents))
    
    # Convert to RGB if necessary (for PNG with transparency)
    if image.mode in ('RGBA', 'LA'):
        background = Image.new('RGB', image.size, (255, 255, 255))
        background.paste(image, mask=image.split()[-1])
        image = background
    
    # Generate thumbnail
    image.thumbnail(size, Image.Resampling.LANCZOS)
    
    # Create BytesIO object
    thumbnail_io = BytesIO()
    
    # Save thumbnail to BytesIO
    image.save(thumbnail_io, format='JPEG', quality=85)
    thumbnail_io.seek(0)
    
    # Generate thumbnail filename
    if file.filename:
        name, _ = os.path.splitext(file.filename)
        thumbnail_filename = f"{name}_thumb.jpeg"
    else:
        # Generate a unique filename if original filename is not available
        thumbnail_filename = f"{uuid.uuid4()}_thumb.jpeg" 
    
    return thumbnail_io, thumbnail_filename