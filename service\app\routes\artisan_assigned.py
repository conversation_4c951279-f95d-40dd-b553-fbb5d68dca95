from datetime import datetime
from uuid import UUID
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, Header,status
from sqlalchemy.orm import Session
from sqlalchemy import or_
from pathlib import Path
# from app import models, schemas
from app.models import ArtisanAssigned,InvoiceItem,Invoice
# from app import schemas
from app.schemas.artisan_assign_schemas import ArtisanAssignedCreate, ArtisanAssignedUpdate
from app.database import get_db
from typing import Optional
# from app.hash import cognito_secret_hash
from sqlalchemy.future import select
from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Annotated, List
# from app.file_uploader import delete_file, upload_file
from app.config import get_settings
from sqlalchemy.orm import selectinload
from app.schemas.helper import StandardResponse,ErrorResponse
from app.routes.services import create_record,update_record
from app.utils.auth import permission_checker
from fastapi.responses import JSONResponse


router = APIRouter(prefix="/request-mapping", tags=["request-mapping"])



# Artisan-assigned
@router.post("/create-artisan-assigned")
def create_artisan_assigned(
    data: ArtisanAssignedCreate,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        
        item_exists = db.get(InvoiceItem, data.invoice_item_id)
        if not item_exists:
            return ErrorResponse(
                status_code=404,
                message="Invoice item not found",
                error="Invalid invoice_item_id"
            )

        record = create_record(db, ArtisanAssigned, data.dict())
        if isinstance(record, str):  # Error string returned
            raise Exception(record)

        return StandardResponse(
            status_code=200,
            message="Artisan assignment created successfully",
            data=record
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Failed to create artisan assignment",
            error=str(e)
        )

# GET BY ID with invoice and invoice_item
@router.get("/get-artisan-assigned")
def get_artisan_assigned(
    artisan_id: str,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        result = db.execute(
            select(ArtisanAssigned, Invoice, InvoiceItem)
            .join(Invoice, ArtisanAssigned.invoice_id == Invoice.id)
            .join(InvoiceItem, ArtisanAssigned.invoice_item_id == InvoiceItem.id)
            .where(ArtisanAssigned.artisan_id == artisan_id)
        ).fetchone()

        if not result:
            raise HTTPException(status_code=404, detail="Artisan assignment not found")

        artisan, invoice, invoice_item = result
        return StandardResponse(
            status_code=200,
            message="Artisan assignment fetched successfully",
            data={
                "artisan_assignment": artisan,
                "invoice": invoice,
                "invoice_item": invoice_item
            }
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Failed to fetch artisan assignment",
            error=str(e)
        )

@router.get("/get-artisan-assigned-list")
def get_all_artisan_assigned(
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        records = db.query(ArtisanAssigned).all()

        return StandardResponse(
            status_code=200,
            message="All artisan assignments fetched successfully",
            data=records
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Failed to fetch artisan assignments",
            error=str(e)
        )

@router.put("/update-artisan-assigned/{id}")
def update_artisan_assigned(
    id: UUID,
    data: ArtisanAssignedUpdate,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        record = db.get(ArtisanAssigned, id)
        if not record:
            raise HTTPException(status_code=404, detail="Artisan assignment not found")
        
        if data.artisan_id:
            status = record.status
            if status != 'ASSIGNED':
                return ErrorResponse(
                    status_code=400,
                    message=f"Artisan already {status}"
                )

        updated = update_record(db, data, record)
        if isinstance(updated, str):
            raise Exception(updated)

        return StandardResponse(
            status_code=200,
            message="Artisan assignment updated successfully",
            data=updated
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Failed to update artisan assignment",
            error=str(e)
        )

@router.delete("/delete-artisan-assigned")
def delete_artisan_assigned(
    artisan_id: UUID,
    db: Session = Depends(get_db),
    user=Depends(permission_checker)
):
    try:
        if isinstance(user, JSONResponse):  # Permission failed
            return user
        record = db.get(ArtisanAssigned, artisan_id)
        if not record:
            raise HTTPException(status_code=404, detail="Artisan assignment not found")

        db.delete(record)
        db.commit()

        return StandardResponse(
            status_code=200,
            message="Artisan assignment deleted successfully",
            data={"id": str(artisan_id)}
        )
    except Exception as e:
        return ErrorResponse(
            status_code=500,
            message="Failed to delete artisan assignment",
            error=str(e)
        )