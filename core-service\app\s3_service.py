import boto3
from uuid import uuid4
from botocore.exceptions import ClientError
from fastapi import UploadFile, HTTPException
from app.config import settings
import requests
from app.config import get_settings


def validate_image(file: UploadFile, max_size_mb: int = 5):
    """Validate file is a supported image and within size limit."""
    if not file.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="Invalid file type. Only images are allowed.")

    ext = file.filename.split(".")[-1].lower()
    if ext not in {"jpg", "jpeg", "png", "webp"}:
        raise HTTPException(status_code=400, detail="Unsupported image format.")

    file.file.seek(0, 2)  # Go to end of file
    size_mb = file.file.tell() / (1024 * 1024)
    file.file.seek(0)

    if size_mb > max_size_mb:
        raise HTTPException(status_code=400, detail=f"File too large. Max allowed size is {max_size_mb}MB.")


# async def upload_file(file: UploadFile, folder: str = "user_profiles") -> str:
#     """Upload validated file to S3 and return public URL."""
#     ext = file.filename.split('.')[-1]
#     key = f"{folder}/{uuid4()}.{ext}"

#     s3 = boto3.client(
#         "s3",
#         region_name=settings.AWS_REGION,
#         aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
#         aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
#     )

#     try:
#         s3.upload_fileobj(
#             file.file,
#             settings.S3_BUCKET,  # or settings.S3_BUCKET if your config uses that
#             key
#         )
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error uploading file to S3: {str(e)}")

#     return f"https://{settings.S3_BUCKET}.s3.{settings.AWS_REGION}.amazonaws.com/{key}"

# def delete_file_from_s3(s3_key: str):
#     try:
#         s3 = boto3.client(
#             "s3",
#             region_name=settings.AWS_REGION,
#             aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
#             aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
#         )
#         s3.delete_object(Bucket=settings.S3_BUCKET, Key=s3_key)
#     except ClientError as e:
#         raise HTTPException(status_code=500, detail=f"Error deleting file from S3: {e}")


s3_upload_url = get_settings().BE_BLOB_API_URL
S3_IMAGES_FOLDER = get_settings().S3_IMAGES_FOLDER
S3_DOCS_FOLDER = get_settings().S3_DOCS_FOLDER

def upload_file_direct(file, path):
    try:
        files = {"file": (file.filename, file.file, file.content_type)}
        data = {'path': path}
        response = requests.post(s3_upload_url+'/upload-file-direct', files=files, data=data)
        return response.json()
    except Exception as e:
        return {"message": f"Error: {e}"}
    

def s3_delete_file(file_name):
    data = {
        "file_path": file_name,
    }
    response = requests.delete(s3_upload_url+'/delete-file', json=data)
    return response.json()