import boto3
import time
import random
import string
from typing import Optional
from app.config import get_settings
from app.helper import ErrorResponse, StandardResponseModel


settings = get_settings()

cognito_client = boto3.client(
    "cognito-idp",
    region_name=settings.BUSINESS_COGNITO_REGION
    # aws_access_key_id=settings.AWS_ACCESS_KEY_ID,  # Use Client ID as access key
    # aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,  # Use Client Secret as secret key
)


def generate_cognito_password(length=12):
    """
    Generates a Cognito-compliant random password using epoch time for uniqueness.
    """
    if length < 8:
        raise ValueError("Password length must be at least 8 characters.")

    # Get last 5 digits of the current epoch time
    epoch_part = str(int(time.time()))[-5:]

    # Define character groups
    uppercase = random.choice(string.ascii_uppercase)  # At least 1 uppercase
    lowercase = random.choice(string.ascii_lowercase)  # At least 1 lowercase
    digit = random.choice(string.digits)  # At least 1 digit
    special = random.choice("!@#$%^&*")  # At least 1 special character

    # Generate remaining random characters
    remaining_length = length - 5 - 4  # 5 from epoch + 4 required chars
    random_chars = "".join(
        random.choices(
            string.ascii_letters + string.digits + "!@#$%^&*", k=remaining_length
        )
    )

    # Combine all parts
    password = uppercase + lowercase + digit + special + random_chars + epoch_part

    # Shuffle the password to avoid predictable patterns
    password = "".join(random.sample(password, len(password)))

    return password

def create_cognito_user(email: str, phone_number: str) -> Optional[str]:
    try:
        cognito_resp = cognito_client.admin_create_user(
            UserPoolId=get_settings().BUSINESS_COGNITO_USER_POOL_ID,
            Username=phone_number,
            UserAttributes=[
                {"Name": "email", "Value": email},
                {"Name": "phone_number", "Value": phone_number},
                {"Name": "email_verified", "Value": "false"},
                {"Name": "phone_number_verified", "Value": "true"},
            ],
            ForceAliasCreation=True,
            MessageAction="SUPPRESS",
        )

        cognito_client.admin_set_user_password(
            UserPoolId=get_settings().BUSINESS_COGNITO_USER_POOL_ID,
            Username=phone_number,
            Password=generate_cognito_password(),
            Permanent=True,
        )
        print("cognito user created:", cognito_resp)

        attributes = cognito_resp.get("User", {}).get("Attributes", [])
        auth_id = next(
            (attr["Value"] for attr in attributes if attr["Name"] == "sub"),
            None,
        )
        return auth_id  # <-- return just the UUID

    except Exception as e:
        print(f"Cognito user creation failed: {e}")
        return None
