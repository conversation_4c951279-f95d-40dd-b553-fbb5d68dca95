import json
import requests
import os
import uuid
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, Header
from app.s3_upload import (
    upload_file_direct,
    s3_delete_file,
    S3_IMAGES_FOLDER,
    S3_DOCS_FOLDER,
)
from sqlalchemy.orm import Session
from sqlalchemy import or_
from pathlib import Path
from app import schemas
from app.database import get_db
from app.utils import (
    create_record,
    delete_record,
    get_id_header,
    is_valid_uuid,
    send_push_notification,
    update_record,
)
from typing import Optional
from app.hash import cognito_secret_hash
from sqlalchemy.future import select
from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Annotated, List
from app.file_uploader import upload_file
from app.config import get_settings
from app.models import *
from sqlalchemy.types import String
from app.cognito_utils import (
    enable_cognito_user,
    update_cognito_attributes,
    disable_cognito_user,
)
from app.validation import check_if_account_exists
from app.image_validator import validate_image
from app.image_processor import generate_thumbnail

# from hash import generate_salt, hash_password
from app.helper import StandardResponse, ErrorResponse

import logging
logger = logging.getLogger("user_log")
logging.basicConfig(level=logging.INFO)

router = APIRouter()

@router.post("/user-signup")
async def create_user(
    request: schemas.CreateUser,
    db: AsyncSession = Depends(get_db),
    # Authorization: Annotated[str, Header()] = None,
):
    try:
        # resp = await get_id_header(Authorization)
        # if resp.status_code == 200:
        #     resp_json = resp.json()
        #     account_id = resp_json.get("account_id")
        #     auth_id = resp_json.get("id")
        #     email = resp_json.get("email")
        # else:
        #     return ErrorResponse(
        #         status_code=401,
        #         message=resp.json()
        #     )

        # exists = await check_if_account_exists(db, email=request.email, phone_number=request.phone_number)
        # print(exists, 'exists')
        # if exists:
        #     return ErrorResponse(
        #         status_code=400,
        #         message="Account already exists"
        #     )

        request_dict = request.dict()
        print(request_dict, "request_dict")
        if account_id is not None:
            query = select(Users).filter(Users.id == account_id)
            result = await db.execute(query)
            user_obj = result.scalars().first()

            if user_obj:
                return ErrorResponse(status_code=400, message="User already exists")

        request_dict["id"] = account_id
        new_user = await create_record(db, Users, request_dict)
        if isinstance(new_user, str):
            if "ix_users_email" in new_user:
                return ErrorResponse(status_code=400, message="Email already registerd")
            elif "ix_users_phone_number" in new_user:
                return ErrorResponse(
                    status_code=400, message="Phone Number already registerd"
                )
            else:
                return ErrorResponse(status_code=400, message=new_user)
        print(new_user.id, "new_user.id")
        # print(auth_id, 'auth_id')
        await update_cognito_attributes(
            request.phone_number, {"custom:account_id": str(new_user.id)}, False
        )
        return StandardResponse(
            status_code=200, data=new_user, message="User created successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/user-read")
async def read_user(
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(status_code=401, message=resp.json())

        query = select(Users).filter(Users.id == account_id)
        result = await db.execute(query)
        user_obj = result.scalars().first()
        return StandardResponse(
            status_code=200, message="User read successfully", data=user_obj
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


# Get by Id
@router.get("/user-read/{id}")
async def read_user(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(status_code=401, message=resp.json())

        query = select(Users).filter(Users.id == uuid.UUID(id))
        result = await db.execute(query)
        user_obj = result.scalars().first()
        return StandardResponse(
            status_code=200, message="User read successfully", data=user_obj
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.get("/user-list")
async def read_users(
    q: Optional[str] = None,
    skip: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(status_code=401, message=resp.json())

        # Total users
        query = select(Users)

        # Apply search filter
        if q:
            query = query.where(
                or_(
                    Users.id.cast(String) == q,
                    Users.phone_number.ilike(f"%{q}%"),
                    Users.email.ilike(f"%{q}%"),
                    Users.first_name.ilike(f"%{q}%"),
                    Users.last_name.ilike(f"%{q}%"),
                )
            )

        # Apply pagination
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        users = result.scalars().all()

        # Total count
        count_query = select(func.count()).select_from(Users)
        total_result = await db.execute(count_query)
        total_count = total_result.scalar()

        return StandardResponse(
            status_code=200,
            message="User list read successfully",
            data={"total": total_count, "data": users},
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")

def remove_country_code(phone: str, country_code: str) -> str:
    clean_code = country_code.replace("+", "")
    if phone.startswith("+" + clean_code):
        return phone[len(clean_code)+1:]  # + and country code
    elif phone.startswith(clean_code):
        return phone[len(clean_code):]
    return phone


@router.put("/user-update")
async def update_user(
    data: schemas.UpdateUser = Depends(),
    profile: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):

    try:
        # print(profile, 'fileeeeeeeeeeeeeeee')
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(status_code=401, message=resp.json())
        query = select(Users).filter(Users.id == account_id)
        result = await db.execute(query)
        get_user = result.scalars().first()
        # print(get_user, 'userrrrrrrrrrrrr')
        if not get_user:
            return ErrorResponse(status_code=401, message="User not found")

        if profile:
            # Validate image using utility function
            is_valid, error_message = await validate_image(profile)
            if not is_valid:
                logger.error(f"image validation failed for user {account_id}: {error_message}")
                return ErrorResponse(
                    status_code=400,
                    message=error_message,
                    error="Profile picture validation failed"
                )

            # Generate thumbnail
            thumbnail_io, thumbnail_filename = await generate_thumbnail(profile)
            
            # Reset file pointer for main image upload
            await profile.seek(0)

            # Delete old profile pic and thumbnail if they exist
            if get_user.profile_pic:
                s3_delete_file(get_user.profile_pic)
            if get_user.profile_thumbnail:
                s3_delete_file(get_user.profile_thumbnail)

            # Upload main profile image
            profile_url = upload_file_direct(profile, path=S3_IMAGES_FOLDER)
            if "message" in profile_url:
                return ErrorResponse(
                    status_code=400,
                    message="Failed to upload image",
                    error=f"Failed to upload image: {profile_url['message']}"
                )

            # Upload thumbnail
            thumbnail_url = upload_file_direct(
                UploadFile(
                    filename=thumbnail_filename,
                    file=thumbnail_io
                ),
                path=S3_IMAGES_FOLDER
            )
            if "message" in thumbnail_url:
                return ErrorResponse(
                    status_code=400,
                    message="Failed to upload thumbnail",
                    error=f"Failed to upload thumbnail: {thumbnail_url['message']}"
                )

            # Update user record with both main image and thumbnail
            setattr(get_user, "profile_pic", profile_url["filename"])
            setattr(get_user, "profile_thumbnail", thumbnail_url["filename"])

        if data.locations:
            print(data.locations, type(data.locations), "locations")
            for loc in data.locations:
                if isinstance(loc, str):
                    loc = json.loads(loc)
                loc["latitude"] = float(loc["latitude"])
                loc["longitude"] = float(loc["longitude"])
            setattr(get_user, "locations", data.locations)

        if data.first_name and data.last_name and data.email and data.phone_number:
            setattr(get_user, "is_profile_complete", True)

        res = await update_record(db, data, get_user)

        if data.email or data.phone_number:
            phone_number = data.phone_number
            if get_user.country_code and data.phone_number:
                phone_number = remove_country_code(data.phone_number, get_user.country_code)

            contact_payload = {
                "email": data.email,
                "mobile": phone_number,
                "account_id": account_id
            }

            BE_PAYMENT_API_URL = os.getenv("BE_PAYMENT_API_URL")

            contact_response = requests.patch(
                f"{BE_PAYMENT_API_URL}/account/contact-info",
                json=contact_payload
            )

            if contact_response.status_code not in (200, 204):
                logger.warning("Contact info update failed: %s", contact_response.text)

        return StandardResponse(
            status_code=200, message="User updated successfully", data=res
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


@router.delete("/user-delete")
async def delete_user(
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(status_code=401, message=resp.json())

        query = select(Users).filter(Users.id == account_id)
        result = await db.execute(query)
        get_user = result.scalars().first()

        if get_user is None:
            return ErrorResponse(status_code=404, message="User not found")

        if get_user.profile_pic:
            s3_delete_file(get_user.profile_pic)

        res = await delete_record(db, get_user)
        return StandardResponse(
            status_code=200, message="User deleted successfully", data=res
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


# @router.get("/user-read/{id}", response_model=schemas.UsersResponse)
@router.get("/user-read/{id}")
async def read_user_by_id(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        resp = await get_id_header(Authorization)
        if resp.status_code == 200:
            resp_json = resp.json()
            account_id = resp_json.get("account_id")
        else:
            return ErrorResponse(status_code=401, message=resp.json())

        query = select(Users).filter(Users.id == uuid.UUID(id))
        result = await db.execute(query)
        user_obj = result.scalars().first()
        return StandardResponse(
            status_code=200, message="User read successfully", data=user_obj
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")


async def create_users(request_dict, db):
    try:
        auth_id = request_dict.get("auth_id")
        new_user = await create_record(db, Users, request_dict)
        user_id = new_user.id
        if isinstance(new_user, str):
            if "ix_users_email" in new_user:
                return {"status_code": 400, "message": "Email already registerd"}
            elif "ix_users_phone_number" in new_user:
                return {"status_code": 400, "message": "Phone Number already registerd"}
            else:
                return {"status_code": 400, "message": str(new_user)}
        await update_cognito_attributes(
            auth_id, {"custom:account_id": str(user_id), "custom:user_id": str(user_id)}, False
        )
        return {
            "status_code": 200,
            "message": "User created successfully",
            "data": user_id,
        }
    except Exception as e:
        return {"status_code": 500, "message": f"Error: {e}"}


async def update_user_status(request_dict, db):
    try:
        account_id = request_dict.get("account_id")
        query = select(Users).filter(Users.id == account_id)
        result = await db.execute(query)
        get_user = result.scalars().first()
        setattr(get_user, "is_confirmed", True)
        await db.commit()
        await db.refresh(get_user)
        return {
            "status_code": 200,
            "message": "User status updated successfully",
        }
    except Exception as e:
        return {"status_code": 500, "message": f"Error: {e}"}


@router.put("/soft-delete-user")
async def soft_delete_user(
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    """
    Soft deletes a user by marking them as inactive in the database.
    The user remains in the database but cannot log in.

    Args:
        username: The username/email of the user to soft delete

    Returns:
        Success message
    """
    try:
        # Verify Admin Identity
        resp = await get_id_header(Authorization)

        # Check if resp is a dictionary or has status_code
        if hasattr(resp, "status_code") and resp.status_code == 200:
            resp_json = resp.json()
            id = resp_json.get("account_id")
        elif isinstance(resp, dict) and "id" in resp:
            id = resp.get("account_id")
        else:
            error_message = (
                resp if isinstance(resp, dict) else {"error": "Authentication failed"}
            )
            return ErrorResponse(status_code=401, message=error_message)

        # Check if user is a regular user
        query = select(Users).filter(Users.id == id)
        result = await db.execute(query)
        user_obj = result.scalars().first()

        if user_obj:
            # Mark as inactive instead of deleting
            user_obj.is_active = False
            disable_response = await disable_cognito_user(user_obj.phone_number)
            if disable_response is None:
                return ErrorResponse(
                    status_code=400,
                    message="Failed to disable Cognito user, no user in cognito",
                )
            await db.commit()
            return StandardResponse(
                status_code=200, message=f"User {id} deactivated successfully"
            )

        # If we got here, the user was not found in our database
        return ErrorResponse(
            status_code=404, message=f"User {id} not found in database"
        )

    except Exception as e:
        import traceback

        print(traceback.format_exc())  # Print full traceback for debugging
        return ErrorResponse(
            status_code=500, message=f"Error deactivating user: {str(e)}"
        )


@router.put("/user-approval/{id}")
async def user_approval(
    id: str,
    data: schemas.UserApproval,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    resp = await get_id_header(Authorization)
    if resp.status_code == 200:
        resp_json = resp.json()

        query = select(Users).filter(Users.id == uuid.UUID(id))
        result = await db.execute(query)
        get_user = result.scalars().first()
        request = data.dict()
        if (
            data.status == UserStatusType.BLACKLIST
            or data.status == UserStatusType.SUSPENDED
        ):
            if get_user.is_active:
                setattr(get_user, "is_active", False)
                disable_response = await disable_cognito_user(get_user.phone_number)
                if disable_response is None:
                    return ErrorResponse(
                        status_code=400,
                        message="Failed to disable Cognito user, no user in cognito",
                    )
            else:
                return ErrorResponse(
                    status_code=400,
                    message="User already blacklisted or suspended",
                )

        if (
            data.status == UserStatusType.APPROVED
            and (get_user.status == UserStatusType.SUSPENDED or get_user.status == UserStatusType.BLACKLIST)
            and not get_user.is_active
        ):
            setattr(get_user, "is_active", True)
            enable_response = await enable_cognito_user(get_user.phone_number)
            if enable_response is None:
                return ErrorResponse(
                    status_code=400,
                    message="Failed to enable Cognito user, no user in cognito",
                )

        setattr(get_user, "status", data.status)
        setattr(get_user, "reason", data.reason)
        await db.commit()
        await db.refresh(get_user)

        send_push_notification(
            auth_token=Authorization,
            title="Profile Status Updated",
            message=f"Your profile has been {data.status}",
            sender_id=get_user.notification_uuid,
            type="service_provider",
        )

        return StandardResponse(
            status_code=200, message="Service Provider updated successfully"
        )
    else:
        return ErrorResponse(status_code=401, message=resp.json())


@router.get("/user-address-status/{id}")
async def check_user_address_status(
    id: str,
    db: AsyncSession = Depends(get_db),
    Authorization: Annotated[str, Header()] = None,
):
    try:
        # Authorization validation
        resp = await get_id_header(Authorization)
        if resp.status_code != 200:
            return ErrorResponse(status_code=401, message=resp.json())

        # Fetch user by ID
        query = select(Users).filter(Users.id == uuid.UUID(id))
        result = await db.execute(query)
        user_obj = result.scalars().first()

        if not user_obj:
            return ErrorResponse(status_code=404, message="User not found")

        # Initialize flags
        home_found = False
        office_found = False
        others_found = False

        # Safely load location (JSON list)
        location_data = user_obj.locations or []

        # Check each address
        for address in location_data:
            category = address.get("address_category")
            if category:
                category_lower = category.lower()
                if category_lower == "home":
                    home_found = True
                elif category_lower == "office":
                    office_found = True
                elif category_lower == "others":
                    others_found = True

        response = {
            "home_is_disable": home_found,
            "office_is_disable": office_found,
            "others_is_disable": others_found,
        }

        return StandardResponse(
            status_code=200, message="Address status fetched successfully", data=response
        )

    except Exception as e:
        return ErrorResponse(status_code=500, message=f"Error: {e}")
