from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.config import settings
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.ext.asyncio import async_sessionmaker  # ✅


SQLALCHEMY_DATABASE_URL = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, echo=True, pool_size=500, max_overflow=1000
)
# SessionLocal = sessionmaker(class_=AsyncSession, autocommit=False, autoflush=False, bind=engine)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


Base = declarative_base()


# Dependency to get a session for async use
async def get_db():
    async with SessionLocal() as db:
        yield db


# Function to create all tables asynchronously
def create_tables():
    with engine.begin() as conn:
        conn.run_sync(Base.metadata.create_all)


async def get_db_session():
    """Get an async database session for use in non-endpoint functions"""
    db = SessionLocal()
    try:
        return db
    except Exception as e:
        await db.close()
        raise e