from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import os

# from .alembic_autogenerate import autogenerate_and_upgrade
import uvicorn
from app.database import create_tables
from app.routers import notification
from app.event import shutdown_event, startup_event


@asynccontextmanager
async def lifespan(app: FastAPI):
    await create_tables()
    await startup_event()
    # autogenerate_and_upgrade()
    yield
    await shutdown_event()


app = FastAPI(lifespan=lifespan)


# List of allowed origins (the frontend URLs that are allowed to access the backend)
origins = [os.getenv("LOCALHOST_URL"), os.getenv("WEBAPP_URL"), os.getenv("UAT_WEBAPP_URL")]

# Add CORSMiddleware to the application
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # Allows specified origins
    allow_credentials=True,  # Allows cookies to be included in requests
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)


@app.get("/notification")
def read_root():
    return {"Hello": "Notification"}


app.include_router(notification.router)
