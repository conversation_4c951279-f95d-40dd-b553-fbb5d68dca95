import os
from sqlalchemy.orm import Session
from app.repositories.booking import (
    create_booking,
    service_start,
    service_end,
    service_reschedule,
    service_reschedule_request,
)
from app.schemas.booking import (
    BookingCreate,
    ServiceStart,
    ServiceEnd,
    ServiceReschedule,
)
from app.utils.notification import get_user, send_push_notification
import requests
from app.utils.enums import PaymentType
from app.models import Booking
from app.models import Account


def book_service(db: Session, booking_data: BookingCreate):
    booking = create_booking(db, booking_data)

    # Notify the artisan about the booking
    # NotificationManager.send_notification(
    #     booking.artisan_id, f"New booking request: {booking.id}"
    # )

    return booking


def start_service(
    db: Session, data: ServiceStart, booking_data: Booking, Authorization: str
):
    booking = service_start(db, data)

    send_push_notification(
        auth_token=Authorization,
        title="Service Ongoing",
        message=f"Your requested service has started successfully! - End OTP:{booking.end_otp}",
        sender_id=str(booking.user_id),
        type="user",
    )

    return booking


def end_service(db: Session, data: ServiceEnd, Authorization: str):
    booking = service_end(db, data)
    # user = get_user(user_id, Authorization)
    #     print(user.get("data", {}).get("first_name"), "user")
    #     data = {
    #         "booking_date": str(new_booking.booking_date),
    #         "booking_id": str(new_booking.id),
    #         "user_name": f"{user.get('data', {}).get('first_name', '')} {user.get('data', {}).get('last_name', '')}".strip(),
    #         "user_profile": user.get('data', {}).get('profile_pic',''),
    #         "user_latitude": new_booking.user_latitude,
    #         "user_longitude": new_booking.user_longitude,
    #     }
    if booking.payment_type == PaymentType.CASH:
        user = get_user(booking.user_id, Authorization)

        # Get service details to include price
        baseurl = os.getenv("BE_SERVICE_API_URL")
        service_response = requests.get(
            f"{baseurl}/services-read/{booking.service_id}",
            headers={"Authorization": f"Bearer {Authorization}"},
        )

        service_data = service_response.json()
        service_price = service_data.get("price", 0)

        print(user.get("data", {}).get("first_name"), "user")
        responseData = {
            "booking_date": str(booking.booking_date),
            "booking_id": str(booking.id),
            "service_id": str(booking.service_id),
            "artisan_id": str(booking.artisan_id),
            "user_name": f"{user.get('data', {}).get('first_name', '')} {user.get('data', {}).get('last_name', '')}".strip(),
            "user_profile": user.get("data", {}).get("profile_pic", ""),
            "service_price": service_price,
            "base_service_fee": booking.base_service_fee,
            "surcharges": booking.surcharges,
        }
        # Creating Payment Record
        payment_service_url = os.getenv("BE_PAYMENT_API_URL")
        payload = {
            "user_id": str(booking.user_id),
            "artisan_id": str(booking.artisan_id),
            "service_request_id": str(booking.service_id),
            "payment_method": "CASH",
            "base_service_fee": booking.base_service_fee,
            "surcharges": booking.surcharges,
            "description": "Payment description",
            "currency": "GHS",
        }
        resp = requests.post(
            url=f"{payment_service_url}/payment-create",
            json=payload,
            headers={"Authorization": f"Bearer {Authorization}"},
        )
        print(resp, "payment resppppppp")
        if resp.status_code == 200:
            print("Payment created successfully")
            resp_json = resp.json()
            payment_id = resp_json['data']["payment_id"]
            setattr(booking, "payment_id", payment_id)
            db.commit()
            db.refresh(booking)
        else:
            print("Payment creation failed")
    elif booking.payment_type in [PaymentType.WALLET, PaymentType.CARD]:
        # Updating Artisan Wallet once service is completed
        a_query = db.query(Account).filter(Account.user_id == booking.artisan_id).first()
        if a_query:
            a_account_balance = a_query.balance
            setattr(a_query, "balance", a_account_balance + booking.base_service_fee)
            db.commit()
            db.refresh(a_query)
            print("Artisan Wallet Updated in end service")

    # Sending notification to user
    result = send_push_notification(
        auth_token=Authorization,
        title="Service Ended",
        message=f"Your requested service has completed successfully! - Booking Id:{data.id}",
        sender_id=str(booking.user_id),
        type="user",
        data=responseData,
    )

    print(result, "result --------")
    return booking


def reschedule_service_request(
    db: Session, booking_data: Booking, data: ServiceReschedule, jwt_token: str
):
    baseurl = os.getenv("BE_SERVICE_API_URL")

    response = requests.get(
        f"{baseurl}/services-read/{booking_data.service_id}",
        headers={"Authorization": f"Bearer {jwt_token}"},
    )
    print(response.json(), "response")
    booking = service_reschedule_request(db, data, int(response.json().get("duration")))
    # Notify the artisan about the booking
    # NotificationManager.send_notification(
    #     booking.artisan_id, f"New booking request: {booking.id}"
    # )

    return booking


def reschedule_service(db: Session, data: ServiceReschedule):
    booking = service_reschedule(db, data)
    # Notify the artisan about the booking
    # NotificationManager.send_notification(
    #     booking.artisan_id, f"New booking request: {booking.id}"
    # )
    return booking
