import requests
import os

from fastapi import Request
from app.schemas.helper import ErrorResponse, StandardResponse
from fastapi.responses import JSONResponse


async def get_id_header(Authorization):
    if not Authorization:
        return {"error": "Token required"}
    try:
        jwt_token = Authorization.split(" ")[1]
        baseurl = os.getenv("BE_AUTH_API_URL")
        response = requests.post(
            f"{baseurl}/validate-token/", json={"token": jwt_token}
        )
        return response
    except Exception as e:
        print(e, "error")
        return {"error": f"Error: {e}"}


async def permission_checker(request: Request):
    token = request.headers.get("Authorization")
    if not token:
        return ErrorResponse(401, "Missing Token", "Authorization token required")

    try:
        auth_url = os.getenv("BE_AUTH_API_URL")  # e.g. http://auth-service:8000
        headers = {
            "Authorization": token,
            "X-Original-Path": request.url.path,
            "X-Original-Method": request.method
        }

        response = requests.post(f"{auth_url}/validate-token", headers=headers)
        resp_data = response.json()

        if response.status_code != 200:
            return JSONResponse(status_code=resp_data.get("status_code", 403), content=resp_data)

        return resp_data["data"]  # contains user_id, auth_id, role_id, role_name

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(500, "Permission service failed", str(e)).dict()
        )

