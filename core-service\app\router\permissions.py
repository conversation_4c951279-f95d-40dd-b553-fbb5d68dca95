from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from uuid import UUID
from app.database import get_db
from app.models import Permissions, Roles
from app.schemas import PermissionCreate, PermissionOut
from app.utils import create_record, update_record, delete_record
from app.helper import StandardResponse, ErrorResponse

router = APIRouter(prefix="/permissions", tags=["Permissions"])


@router.post("/", response_model=StandardResponse)
async def create_permission(payload: PermissionCreate, db: AsyncSession = Depends(get_db)):
    try:
        new_permission = await create_record(db, Permissions, payload.dict())
        data = PermissionOut.model_validate(new_permission, from_attributes=True)
        return StandardResponse(
            status_code=201,
            data=data,
            message="Permission created successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))



@router.get("/", response_model=StandardResponse)
async def get_permissions(db: AsyncSession = Depends(get_db)):
    try:
        result = await db.execute(select(Permissions))
        permissions = result.scalars().all()
        data = [PermissionOut.model_validate(p, from_attributes=True) for p in permissions]
        return StandardResponse(status_code=200, data=data, message="Permission found")
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))

@router.get("/{permission_id}", response_model=StandardResponse)
async def get_permission(permission_id: UUID, db: AsyncSession = Depends(get_db)):
    try:
        result = await db.execute(select(Permissions).where(Permissions.id == permission_id))
        permission = result.scalars().first()

        if not permission:
            return ErrorResponse(status_code=404, message="Permission not found")

        return StandardResponse(status_code=200, data=PermissionOut.model_validate(permission, from_attributes=True), message="Permission found")
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))

@router.put("/{permission_id}", response_model=StandardResponse)
async def update_permission(permission_id: UUID, payload: PermissionCreate, db: AsyncSession = Depends(get_db)):
    try:
        # Validate if permission exists
        result = await db.execute(select(Permissions).where(Permissions.id == permission_id))
        if not result.scalars().first():
            return ErrorResponse(status_code=404, message="Permission not found")

        # Validate if the role exists (optional but prevents FK crash)
        role_check = await db.execute(select(Roles).where(Roles.id == payload.role_id))
        if not role_check.scalars().first():
            return ErrorResponse(status_code=400, message="Role ID does not exist")

        updated = await update_record(db, Permissions, permission_id, payload.dict(exclude_unset=True))
        return StandardResponse(status_code=200, data=PermissionOut.model_validate(updated, from_attributes=True), message="Permission found")
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))

@router.delete("/{permission_id}", response_model=StandardResponse)
async def delete_permission(permission_id: UUID, db: AsyncSession = Depends(get_db)):
    try:
        result = await db.execute(select(Permissions).where(Permissions.id == permission_id))
        permission = result.scalars().first()

        if not permission:
            return ErrorResponse(status_code=404, message="Permission not found")

        await delete_record(db, Permissions, permission_id)
        return StandardResponse(status_code=200, message="Permission deleted successfully")
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))