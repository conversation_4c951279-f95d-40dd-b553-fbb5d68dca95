from pydantic import BaseModel, field_validator
from uuid import UUID
from datetime import datetime
from typing import Optional, Any, List
from app.models_enum import BookingCancellationStatus


class CancelRequestPayload(BaseModel):
    booking_id: UUID
    invoice_item_id: Optional[UUID] = None
    cancellation_reason_id: UUID
    is_complete_booking_cancel: bool

class BookingCancellationListFilters(BaseModel):
    user_id: Optional[UUID] = None
    artisan_id: Optional[UUID] = None
    status: Optional[BookingCancellationStatus] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    q: Optional[str] = None
    limit: int = 10
    skip: int = 1

    @field_validator('start_date', 'end_date', mode='before')
    @classmethod
    def parse_datetime(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            # Handle the specific format: '2025-08-04 09:21:27.238324+00'
            if '+00' in v and not v.endswith(':00'):
                # Convert +00 to +00:00 for proper ISO format
                v = v.replace('+00', '+00:00')
            elif 'Z' in v:
                # Convert Z to +00:00
                v = v.replace('Z', '+00:00')
            try:
                return datetime.fromisoformat(v)
            except ValueError:
                raise ValueError(f"Unable to parse datetime: {v}")
        return v


class UpdateCancellationStatusPayload(BaseModel):
    cancellation_id: str
    status: BookingCancellationStatus  # should only accept `APPROVED`, or validate manually
    agent_id: Optional[str] = None
