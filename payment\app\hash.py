import hashlib,hmac,base64
import os
from .config import get_settings

PEPPER = b"pepper"


def hash_password(password: str, salt: bytes) -> str:
    combined = password.encode("utf-8") + salt + PEPPER
    hashed = hashlib.pbkdf2_hmac(
        hash_name="sha256", password=combined, salt=salt, iterations=100000
    )
    return hashed.hex()

def generate_salt(length: int = 32) -> bytes:
    return os.urandom(length)

def cognito_secret_hash(username:str) -> str:
    message = bytes(username+get_settings().CLIENT_ID,'utf-8') 
    key = bytes(get_settings().CLIENT_SECRET,'utf-8') 
    secret_hash = base64.b64encode(hmac.new(key, message, digestmod=hashlib.sha256).digest()).decode() 
    return secret_hash
