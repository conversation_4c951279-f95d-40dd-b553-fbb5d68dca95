#!/bin/bash

echo "Started syncing routes"

ADMIN_API_KEY="rKPdiZQpsHDgyCiDSLXOfJTmyxGafZZu"
APISIX_HOST="http://localhost:9180"
ROUTE_FILE="/usr/local/apisix/apisix-config/routes/apisix-routes-dev.json"

jq -c '.[]' "$ROUTE_FILE" | while read -r route; do
    route_name=$(echo "$route" | jq -r '.name')

    echo "→ Syncing route: $route_name"

    response=$(curl -s -o /dev/null -w "%{http_code}" \
        -X PUT "$APISIX_HOST/apisix/admin/routes/$route_name" \
        -H "X-API-KEY: $ADMIN_API_KEY" \
        -H "Content-Type: application/json" \
        -d "$route")

    if [[ "$response" == "200" || "$response" == "201" ]]; then
        echo "Successfully synced: $route_name"
    else
        echo "Failed to sync: $route_name (HTTP $response)"
    fi
done

echo "Route sync completed"
