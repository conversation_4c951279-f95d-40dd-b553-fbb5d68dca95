
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models import Users, Admin, ServiceProvider
from fastapi import HTT<PERSON><PERSON>xception
from typing import Optional


async def check_if_account_exists(
    db: AsyncSession, email: Optional[str] = None, phone_number: Optional[str] = None
) -> bool:
    if not email and not phone_number:
        raise HTTPException(status_code=400, detail="Email or Phone Number is required")

    queries = []
    print(email, phone_number, "email and phone number")

    if phone_number:
        queries.append(select(Users).filter(Users.phone_number == phone_number))
        queries.append(select(Admin).filter(Admin.phone_number == phone_number))
        queries.append(select(ServiceProvider).filter(ServiceProvider.phone_number == phone_number))

    if email and not phone_number:
        queries.append(select(Users).filter(Users.email == email))
        queries.append(select(Admin).filter(Admin.email == email))
        queries.append(select(ServiceProvider).filter(ServiceProvider.email == email))

    for query in queries:
        result = await db.execute(query)  # ✅ Correct async execution
        row = result.first()  # ✅ Extract the first row

        print(f"Executing Query: {query}")
        print(f"Query Result: {row}")

        if row:  # ✅ If any row is found, return True
            return True

    return False  # ❌ If no match, return False