#!/usr/bin/env python3
"""
Simple test script to verify CRUD functions work correctly.
This is a basic test to ensure the functions can be imported and have the correct signatures.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_imports():
    """Test that all CRUD functions can be imported successfully"""
    try:
        from app.utils.crud import create_record, get_record_by_id, update_record, delete_record
        from app.utils import create_record as utils_create_record, get_id_header, is_valid_uuid, check_file_exists
        from app.models import Services, Category
        from app.helper import StandardResponse, ErrorResponse
        
        print("✅ All imports successful!")
        
        # Test function signatures
        print("✅ create_record function available")
        print("✅ get_record_by_id function available") 
        print("✅ update_record function available")
        print("✅ delete_record function available")
        print("✅ get_id_header function available")
        print("✅ is_valid_uuid function available")
        print("✅ check_file_exists function available")
        print("✅ Services model available")
        print("✅ Category model available")
        print("✅ StandardResponse function available")
        print("✅ ErrorResponse function available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_uuid_validation():
    """Test the UUID validation function"""
    try:
        from app.utils import is_valid_uuid
        
        # Test valid UUIDs
        valid_uuid = "123e4567-e89b-12d3-a456-************"
        assert is_valid_uuid(valid_uuid) == True, "Valid UUID should return True"
        
        # Test invalid UUIDs
        invalid_uuid = "not-a-uuid"
        assert is_valid_uuid(invalid_uuid) == False, "Invalid UUID should return False"
        
        print("✅ UUID validation tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ UUID validation test failed: {e}")
        return False

def test_file_check():
    """Test the file existence check function"""
    try:
        from app.utils import check_file_exists
        
        # Test with a file object (mock)
        class MockFile:
            def __init__(self, exists=True):
                self.exists = exists
        
        # Test file exists
        mock_file = MockFile(True)
        assert check_file_exists(mock_file) == True, "File should exist"
        
        # Test no file
        assert check_file_exists(None) == False, "None should return False"
        
        print("✅ File existence check tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ File existence check test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Running CRUD function tests...")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Run tests
    all_tests_passed &= test_imports()
    all_tests_passed &= test_uuid_validation()
    all_tests_passed &= test_file_check()
    
    print("=" * 50)
    if all_tests_passed:
        print("🎉 All tests passed! CRUD functions are ready to use.")
        sys.exit(0)
    else:
        print("💥 Some tests failed. Please check the implementation.")
        sys.exit(1)
