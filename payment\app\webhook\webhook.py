import logging
import os
from fastapi import APIRouter, Request
from app.helper import StandardResponse, ErrorResponse
import json
import requests
# from app.database import get_db_session
from sqlalchemy.future import select
from app.models import Transaction, Account, Payment

logger = logging.getLogger("webhook")
logging.basicConfig(level=logging.INFO)

router = APIRouter()

wb_key = 'qM4sDHkXjkZf3uWCN39srXrGC'
               
# @router.post("/payment-callback")
# async def payment_callback(api_key: str, request: Request):
#     try:
#         db = await get_db_session()
#         print(request.headers, 'request headers')
#         if api_key != wb_key:
#             return ErrorResponse(status_code=401, message="Unauthorized access")
#         body = await request.body()
#         json_body = body.decode('utf-8')
#         json_body = json.loads(json_body)
#         print(json_body, 'request body')
#         if 'externalTransactionId' in json_body:
#             logger.info('updating transaction details for wallet')
#             externalTransactionId = json_body['externalTransactionId']
#             statusCode = json_body['statusCode']
#             transaction_query = select(Transaction).where(Transaction.transaction_id == externalTransactionId).order_by(Transaction.created_at.desc()).limit(1)
#             result = await db.execute(transaction_query)
#             transaction = result.scalars().first()
#             transaction_id = transaction.id
#             if transaction:
#                 transaction.transaction_details = json_body
#                 transaction.status = 'SUCCESS' if statusCode == '200' else 'FAILED'
#                 await db.commit()
#                 await db.refresh(transaction)
#                 logger.info(f"Transaction details updated for transaction_id: {transaction_id}")
#             else:
#                 logger.error(f"Transaction not found for wallet externalTransactionId: {externalTransactionId}")
#         elif 'p_order_id' in json_body:
#             logger.info('updating transaction details for card')
#             p_order_id = json_body['p_order_id']
#             status = json_body['status'].lower()
#             transaction_query = select(Transaction).where(Transaction.transaction_id == p_order_id).order_by(Transaction.created_at.desc()).limit(1)
#             result = await db.execute(transaction_query)
#             transaction = result.scalars().first()
#             transaction_id = transaction.id
#             if transaction:
#                 transaction.transaction_details = json_body
#                 transaction.status = 'SUCCESS' if status == 'success' else 'FAILED'
#                 await db.commit()
#                 await db.refresh(transaction)
#                 logger.info(f"Transaction details updated for transaction_id: {transaction_id}")
#             else:
#                 logger.error(f"Transaction not found for card p_order_id: {p_order_id}")
#         else:
#             logger.error("Invalid request body")
#         return StandardResponse(status_code=200, message="Payment callback received", data={})
#     except Exception as e:
#         print(e, 'error')
#         return ErrorResponse(status_code=500, message=f"Error: {str(e)}")


from app.database import SessionLocal

@router.post("/payment-callback")
async def payment_callback(api_key: str, request: Request):
    try:
        async with SessionLocal() as db:  # ✅ Proper session management
            # print(request.headers, 'request headers')
            
            if api_key != wb_key:
                return ErrorResponse(status_code=401, message="Unauthorized access")

            body = await request.body()
            json_body = json.loads(body.decode('utf-8'))
            print(json_body, 'request body')

            # Wallet
            if 'externalTransactionId' in json_body:
                logger.info('updating transaction details for wallet')
                externalTransactionId = json_body['externalTransactionId']
                statusCode = json_body['statusCode']
                transaction_query = select(Transaction).where(
                    Transaction.transaction_id == externalTransactionId
                ).order_by(Transaction.created_at.desc()).limit(1)

                result = await db.execute(transaction_query)
                transaction = result.scalars().first()

                if transaction:
                    transaction_type = transaction.transaction_type
                    transaction.transaction_details = json_body
                    transaction.status = 'SUCCESS' if statusCode == '200' else 'FAILED'
                    await db.commit()
                    await db.refresh(transaction)
                    logger.info(f"Transaction details updated for transaction_id: {transaction.id}")

                    if (transaction_type in ['PAYMENT', 'PAYOUT'] and statusCode == '200'):
                        # payment_query = select(Payment).filter(Payment.id == transaction.payment_id)
                        # payment_result = await db.execute(payment_query)
                        # payment_obj = payment_result.scalars().first()
                        # base_service_fee = payment_obj.base_service_fee

                        a_query = select(Account).filter(Account.user_id == transaction.to_entity)
                        a_result = await db.execute(a_query)
                        a_account_obj = a_result.scalars().first()
                        a_account_balance = a_account_obj.balance

                        if transaction_type == 'PAYMENT':
                            u_query = select(Account).filter(Account.user_id == transaction.from_entity)
                            u_result = await db.execute(u_query)
                            u_account_obj = u_result.scalars().first()
                            u_account_balance = u_account_obj.balance
                            if u_account_balance < 0:
                                setattr(u_account_obj, "balance", 0)
                                await db.commit()
                                await db.refresh(u_account_obj)
                                logger.info(f"User wallet balance updated successfully after paid negative balance")

                            # setattr(a_account_obj, "balance", a_account_balance + base_service_fee)
                        elif transaction_type == 'PAYOUT':
                            setattr(a_account_obj, "balance", a_account_balance - transaction.amount)
                            await db.commit()
                            await db.refresh(a_account_obj)
                            logger.info(f"Artisan wallet balance updated successfully after withdrawing for transaction_id: {transaction.id}")

                    elif (transaction_type == 'REFUND' and statusCode == '200'):
                        cancellation_id = transaction.cancellation_id
                        resp = requests.put(
                            url=f"{os.getenv('BE_SCHEDULE_API_URL')}/booking-cancellation/{cancellation_id}",
                            json={"status": "completed"})
                        if resp.status_code == 200:
                            logger.info(f"Booking cancellation status updated to completed for cancellation_id: {cancellation_id}")
                        else:
                            logger.error(f"Failed to update booking cancellation status for cancellation_id: {cancellation_id}")
                else:
                    logger.error(f"Transaction not found for wallet externalTransactionId: {externalTransactionId}")
            # Card
            elif 'p_order_id' in json_body:
                logger.info('updating transaction details for card')
                p_order_id = json_body['p_order_id']
                status = json_body['status'].lower()
                transaction_query = select(Transaction).where(
                    Transaction.transaction_id == p_order_id
                ).order_by(Transaction.created_at.desc()).limit(1)

                result = await db.execute(transaction_query)
                transaction = result.scalars().first()

                if transaction:
                    transaction_type = transaction.transaction_type                    
                    transaction.transaction_details = json_body
                    transaction.status = 'SUCCESS' if status == 'success' else 'FAILED'
                    await db.commit()
                    await db.refresh(transaction)
                    logger.info(f"Transaction details updated for transaction_id: {transaction.id}")

                    if (transaction_type in ['PAYMENT', 'PAYOUT'] and status == 'success'):
                        # payment_query = select(Payment).filter(Payment.id == transaction.payment_id)
                        # payment_result = await db.execute(payment_query)
                        # payment_obj = payment_result.scalars().first()
                        # base_service_fee = payment_obj.base_service_fee

                        a_query = select(Account).filter(Account.user_id == transaction.to_entity)
                        a_result = await db.execute(a_query)
                        a_account_obj = a_result.scalars().first()
                        a_account_balance = a_account_obj.balance

                        if transaction_type == 'PAYMENT':
                            u_query = select(Account).filter(Account.user_id == transaction.from_entity)
                            u_result = await db.execute(u_query)
                            u_account_obj = u_result.scalars().first()
                            u_account_balance = u_account_obj.balance
                            if u_account_balance < 0:
                                setattr(u_account_obj, "balance", 0)
                                await db.commit()
                                await db.refresh(u_account_obj)
                                logger.info(f"User wallet balance updated successfully after paid negative balance")

                            # setattr(a_account_obj, "balance", a_account_balance + base_service_fee)
                        elif transaction_type == 'PAYOUT':
                            setattr(a_account_obj, "balance", a_account_balance - transaction.amount)
                            await db.commit()
                            await db.refresh(a_account_obj)
                            logger.info(f"Artisan wallet balance updated successfully after withdrawing for transaction_id: {transaction.id}")

                    elif (transaction_type == 'REFUND' and status == 'success'):
                        cancellation_id = transaction.cancellation_id
                        resp = requests.put(
                            url=f"{os.getenv('BE_SCHEDULE_API_URL', 'http://schedule-service:8004')}/booking-cancellation/{cancellation_id}",
                            json={"status": "completed"})
                        if resp.status_code == 200:
                            logger.info(f"Booking cancellation status updated to completed for cancellation_id: {cancellation_id}")
                        else:
                            logger.error(f"Failed to update booking cancellation status for cancellation_id: {cancellation_id}")
                else:
                    logger.error(f"Transaction not found for card p_order_id: {p_order_id}")

            else:
                logger.error("Invalid request body")

            return StandardResponse(status_code=200, message="Payment callback received", data={})

    except Exception as e:
        logger.exception("Exception during payment callback") 
        return ErrorResponse(status_code=500, message=f"Error: {str(e)}")
