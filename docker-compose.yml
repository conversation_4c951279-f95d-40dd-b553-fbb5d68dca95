version: "3"
 
x-localhost-url: &localhost-url
  WEBAPP_URL: "${WEBAPP_URL}"
 
x-postgres-config: &postgres-env
  POSTGRES_DB: "${POSTGRES_DB}"
  POSTGRES_USER: "${POSTGRES_USER}"
  POSTGRES_PASSWORD: "${POSTGRES_PASSWORD}"
  POSTGRES_HOST: "${POSTGRES_HOST}"
  POSTGRES_PORT: "${POSTGRES_PORT}"
  PGADMIN_DEFAULT_EMAIL: "${PGADMIN_DEFAULT_EMAIL}"
  PGADMIN_DEFAULT_PASSWORD: "${PGADMIN_DEFAULT_PASSWORD}"
 
x-redis-config: &redis-env
  REDIS_HOST: "${REDIS_HOST}"
  REDIS_PORT: "${REDIS_PORT}"
 
x-celery-config: &celery-env
  CELERY_BROKER_URL: "${CELERY_BROKER_URL}"
  CELERY_RESULT_BACKEND: "${CELERY_RESULT_BACKEND}"
  FLOWER_BROKER_API: "${FLOWER_BROKER_API}"
  FLOWER_BASIC_AUTH: "${FLOWER_BASIC_AUTH}"
 
 
x-cognito-config: &cognito-env
  COGNITO_CLIENT_ID: "${COGNITO_CLIENT_ID}"
  COGNITO_CLIENT_SECRET: "${COGNITO_CLIENT_SECRET}"
  COGNITO_REGION: "${COGNITO_REGION}"
  COGNITO_USER_POOL_ID: "${COGNITO_USER_POOL_ID}"
  TEST_USER_PASSWORD: "${TEST_USER_PASSWORD}"

x-business-cognito-config: &business-cognito-env
  BUSINESS_COGNITO_USER_POOL_ID: "${BUSINESS_COGNITO_USER_POOL_ID}"
  BUSINESS_CLIENT_ID: "${BUSINESS_CLIENT_ID}"
  BUSINESS_CLIENT_SECRET: "${BUSINESS_CLIENT_SECRET}"
  BUSINESS_COGNITO_REGION: "${BUSINESS_COGNITO_REGION}"

x-googlemap-config: &googlemap-env
  GOOGLE_API_KEY: "${GOOGLE_API_KEY}"
 
x-aws-config: &aws-env
  CLIENT_ID: "${CLIENT_ID}"
  CLIENT_SECRET: "${CLIENT_SECRET}"
  USER_POOL_ID: "${USER_POOL_ID}"
  AWS_REGION: "${AWS_REGION}"
  S3_ACCESS_KEY_ID: "${STOR_ACCESS_KEY_ID}"
  S3_SECRET_ACCESS_KEY: "${STOR_SECRET_ACCESS_KEY}"
  S3_REGION: "${STOR_REGION}"
  AWS_ACCESS_KEY_ID: "${AWS_ACCESS_KEY_ID}"
  AWS_SECRET_ACCESS_KEY: "${AWS_SECRET_ACCESS_KEY}"
  S3_BUCKET: "${STOR_BUCKET}"
  S3_IMAGES_FOLDER: "${S3_IMAGES_FOLDER}"
  S3_DOCS_FOLDER: "${S3_DOCS_FOLDER}"
  
 
x-be-api-config: &be-api-env
  BE_AUTH_API_URL: "${BE_AUTH_API_URL}"
  BE_PROFILE_API_URL: "${BE_PROFILE_API_URL}"
  BE_NOTIFICATION_API_URL: "${BE_NOTIFICATION_API_URL}"
  BE_SERVICE_API_URL: "${BE_SERVICE_API_URL}"
  BE_BLOB_API_URL: "${BE_BLOB_API_URL}"
  BE_PAYMENT_API_URL: "${BE_PAYMENT_API_URL}"
  BE_AUDIT_LOG_API_URL: "${BE_AUDIT_LOG_API_URL}"
  BE_SCHEDULE_API_URL: "${BE_SCHEDULE_API_URL}"
 
x-minio-config: &minio-env
  MINIO_URL: "${MINIO_URL}"
  MINIO_ROOT_USER: "${MINIO_ROOT_USER}"
  MINIO_ROOT_PASSWORD: "${MINIO_ROOT_PASSWORD}"
  MINIO_ACCESS_KEY: "${MINIO_ACCESS_KEY}"
  MINIO_SECRET_KEY: "${MINIO_SECRET_KEY}"
  USER_BUCKET_NAME: "${USER_BUCKET_NAME}"
  SP_BUCKET_NAME: "${SP_BUCKET_NAME}"
  SUBCATEGORY_BUCKET_NAME : '${SUBCATEGORY_BUCKET_NAME}'
  SERVICES_BUCKET_NAME : '${SERVICES_BUCKET_NAME}'
 
 
x-notification-kafka-config: &notification-kafka-env
  KAFKA_HOST: "${KAFKA_HOST}"
  KAFKA_PORT: "${KAFKA_PORT}"
 
x-sms-config: &sms-env
  SMS_SENDER_ID: "${SMS_SENDER_ID}"
  SMS_API_ID: "${SMS_API_ID}"
  SMS_API_KEY: "${SMS_API_KEY}"
 
x-email-config: &email-env
  SES_ACCESS_KEY: "${SES_ACCESS_KEY}"
  SES_SECRET_KEY: "${SES_SECRET_KEY}"
  FROM_EMAIL: "${FROM_EMAIL}"
 
x-general-config: &general-env
  APP_NAME: "${APP_NAME}"
  ENVIRONMENT: "${ENVIRONMENT}"
 
x-payment-config: &payment-env
  WALLET_API_URL: "${WALLET_API_URL}"
  WALLET_USERNAME: "${WALLET_USERNAME}"
  WALLET_PASSWORD: "${WALLET_PASSWORD}"
  # WALLET_API_URL: "${WALLET_API_URL1}"
  # WALLET_USERNAME: "${WALLET_USERNAME1}"
  # WALLET_PASSWORD: "${WALLET_PASSWORD1}"
  # CARD_API_URL: "${CARD_API_URL1}"
  # CARD_MERCHANT_KEY: "${CARD_MERCHANT_KEY1}"
  # CARD_MERCHANT_SECRET: "${CARD_MERCHANT_SECRET1}"
  CARD_API_URL: "${CARD_API_URL1}"
  CARD_MERCHANT_KEY: "${CARD_MERCHANT_KEY2}"
  CARD_MERCHANT_SECRET: "${CARD_MERCHANT_SECRET2}"
 
x-notification-celery-redis-config: &notification-celery-redis-env
  NOTIFICATION_REDIS_CELERY_URL: "${NOTIFICATION_REDIS_CELERY_URL}"
 
x-apisix-configs: &apisix-config-env
  APISIX_DASHBOARD_SECRET: "${APISIX_DASHBOARD_SECRET}"
  APISIX_DASHBOARD_USERNAME: "${APISIX_DASHBOARD_USERNAME}"
  APISIX_DASHBOARD_PASSWORD: "${APISIX_DASHBOARD_PASSWORD}"
 
services:
  etcd:
    image: bitnami/etcd:latest
    container_name: etcd
    environment:
      - ALLOW_NONE_AUTHENTICATION=yes
      - ETCD_ADVERTISE_CLIENT_URLS=http://etcd:2379
      - ETCD_LISTEN_CLIENT_URLS=http://0.0.0.0:2379
    ports:
      - "2379:2379"
    volumes:
      - ./apisix/data/etcd:/bitnami/etcd
    networks:
      jobconnect_app:
        aliases:
          - etcd
 
  apisix:
    build:
      context: ./apisix
      dockerfile: Dockerfile.apisix
    container_name: apisix
    depends_on:
      - etcd
    ports:
      - "9080:9080" # Gateway port
      - "9180:9180" # Admin port eg(Adding routes in datbase, configure the upstream and plugins configuration)
    volumes:
      - ./apisix/apisix_config/config.yaml:/usr/local/apisix/conf/config.yaml
      - ./apisix/apisix_config/apisix.yaml:/usr/local/apisix/conf/apisix.yaml
      - ./apisix/apisix-config:/usr/local/apisix/apisix-config
    environment:
      <<: *apisix-config-env
    networks:
      - jobconnect_app
 
 
  apisix-dashboard:
    image: apache/apisix-dashboard:2.10.1-centos
    container_name: apisix-dashboard
    depends_on:
      - apisix
    volumes:
      - ./apisix/dashboard/config.yaml:/usr/local/apisix-dashboard/conf/conf.yaml
    ports:
      - "9002:9002"
    environment:
      APISIX_DASHBOARD_USERNAME: ${APISIX_DASHBOARD_USERNAME}
      APISIX_DASHBOARD_PASSWORD: ${APISIX_DASHBOARD_PASSWORD}
      APISIX_DASHBOARD_SECRET: ${APISIX_DASHBOARD_SECRET}
    networks:
      - jobconnect_app
  zookeeper:
    image: bitnami/zookeeper:latest
    ports:
      - 2181:2181
    environment:
      - ALLOW_ANONYMOUS_LOGIN=yes
    networks:
      - jobconnect_app
 
  kafka:
    image: bitnami/kafka:3.9.0
    ports:
      - 9092:9092
      - 9093:9093
    environment:
      - KAFKA_BROKER_ID=1
      - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
      - KAFKA_CFG_ZOOKEEPER_CONNECT=zookeeper:2181
      - ALLOW_PLAINTEXT_LISTENER=yes
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CLIENT:PLAINTEXT
      - KAFKA_CFG_LISTENERS=CLIENT://:9092
      - KAFKA_CFG_ADVERTISED_LISTENERS=CLIENT://kafka:9092
      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=CLIENT
    depends_on:
      - zookeeper
    networks:
      - jobconnect_app
 
  kafka-ui:
    container_name: kafka-ui
    image: provectuslabs/kafka-ui:latest
    ports:
      - 8082:8080
    depends_on:
      - kafka
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
      DYNAMIC_CONFIG_ENABLED: 'true'
    networks:
      - jobconnect_app
 
  core-service:
    build:
      context: ./core-service/
      dockerfile: Dockerfile
    ports:
      - "8006:8006"
    environment:
      <<:
        - *postgres-env
        - *aws-env
        - *be-api-env
        - *cognito-env
        - *localhost-url
        - *redis-env
        - *notification-kafka-env
        - *celery-env
        - *googlemap-env
        - *notification-kafka-env
    volumes:
      - ./core-service/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    depends_on:
      - postgres
      # - migration
    networks:
      - jobconnect_app
 
  redis:
    image: redis:6.2.7-alpine
    networks:
      - jobconnect_app
    volumes:
      - redis_data:/data
    ports:
      - 6379:6379
 
  auth-service:
    build:
      context: ./auth/
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      <<:
        - *postgres-env
        - *cognito-env
        - *aws-env
        # - *be-api-env
    volumes:
      - ./auth/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    depends_on:
      - postgres
      # - migration
    networks:
      - jobconnect_app
  migration-service:
    build:
      context: ./migration/
      dockerfile: Dockerfile
    ports:
      - "8014:8014"
    environment:
      <<:
        - *postgres-env
        - *localhost-url
        # - *be-api-env
    volumes:
      - ./migration/:/app
    depends_on:
      - postgres
    networks:
      - jobconnect_app
  profile-service:
    build:
      context: ./profile/
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      <<:
        - *postgres-env
        - *be-api-env
        - *minio-env
        - *cognito-env
        - *celery-env
        - *aws-env
        - *googlemap-env
        - *notification-kafka-env
        - *localhost-url
    volumes:
      - ./profile/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    depends_on:
      - postgres
      # - migration
    networks:
      - jobconnect_app
 
 
  notification-service:
    build:
      context: ./notification/
      dockerfile: Dockerfile
    ports:
      - "8002:8002"
    environment:
      <<:
        - *postgres-env
        - *be-api-env
        - *notification-kafka-env
        - *notification-celery-redis-env
        - *aws-env
        - *sms-env
        - *email-env
        - *general-env
        - *celery-env
        - *localhost-url
    volumes:
      - ./notification/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    depends_on:
      - postgres
      - kafka
      - redis
      # - migration
    restart: on-failure
    networks:
      - jobconnect_app
 
  service-service:
    build:
      context: ./service/
      dockerfile: Dockerfile
    ports:
      - "8003:8003"
    environment:
      <<:
        - *postgres-env
        - *be-api-env
        - *minio-env
        - *aws-env
        - *notification-kafka-env
        - *localhost-url
        - *redis-env
        - *cognito-env
    volumes:
      - ./service/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    depends_on:
      - postgres
      - redis
      # - migration
    networks:
      - jobconnect_app
 
  schedule-service:
    build:
      context: ./schedule/
      dockerfile: Dockerfile
    ports:
      - "8012:8012"
    environment:
      <<:
        - *postgres-env
        - *be-api-env
        - *redis-env
        - *googlemap-env
        - *celery-env
        - *minio-env
        - *aws-env
        - *localhost-url
    volumes:
      - ./schedule/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    depends_on:
      - postgres
      - redis
      # - migration
    networks:
      - jobconnect_app
 
  support-service:
    build:
      context: ./support/
      dockerfile: Dockerfile
    ports:
      - "8013:8013"
    environment:
      <<:
        - *postgres-env
        - *be-api-env
        - *redis-env
        - *googlemap-env
        - *celery-env
        - *minio-env
        - *aws-env
        - *localhost-url
    volumes:
      - ./support/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    depends_on:
      - postgres
      - redis
      # - migration
    networks:
      - jobconnect_app
  provider-service:
    build:
      context: ./provider/
      dockerfile: Dockerfile
    ports:
      - "8015:8015"
    environment:
      <<:
        - *postgres-env
        - *be-api-env
        - *minio-env
        - *aws-env
        - *notification-kafka-env
        - *localhost-url
        - *redis-env
        - *minio-env
        - *aws-env
        - *business-cognito-env
    volumes:
      - ./provider/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    depends_on:
      - postgres
      - redis
      # - migration
    networks:
      - jobconnect_app
  # in-app-chat-service:
  #   build:
  #     context: ./in-app-chat/
  #     dockerfile: Dockerfile
  #   ports:
  #     - "8015:8015"
  #   environment:
  #     <<:
  #       - *postgres-env
  #       - *be-api-env
  #       - *celery-env
  #   volumes:
  #     - ./in-app-chat/:/app
  #     - ./migration/app/models.py:/app/app/models.py
  #     - ./migration/app/models_enum.py:/app/app/models_enum.py
  #   depends_on:
  #     - postgres
  #     - redis
  #   restart: on-failure
  #   networks:
  #     - jobconnect_app
 
  # core-service:
  #   build:
  #     context: ./core-service/
  #     dockerfile: Dockerfile
  #   ports:
  #     - "8015:8015"
  #   environment:
  #     <<:
  #       - *postgres-env
  #       - *aws-env
  #       - *be-api-env
  #       - *cognito-env
  #   volumes:
  #     - ./core-service/:/app
  #     - ./migration/app/models.py:/app/app/models.py
  #     - ./migration/app/models_enum.py:/app/app/models_enum.py
  #   depends_on:
  #     - postgres
  #     # - migration
  #   networks:
  #     - jobconnect_app
 
  booking-service:
    build:
      context: ./booking/
      dockerfile: Dockerfile
    ports:
      - "8004:8004"
    environment:
      <<:
        - *postgres-env
        - *be-api-env
        - *redis-env
        - *googlemap-env
        - *celery-env
        - *minio-env
        - *aws-env
        - *localhost-url
    volumes:
      - ./booking/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    depends_on:
      - postgres
      - redis
      # - migration
    networks:
      - jobconnect_app
 
 
  payment-service:
    build:
      context: ./payment/
      dockerfile: Dockerfile
    ports:
      - "8005:8005"
    environment:
      <<:
        - *postgres-env
        - *be-api-env
        - *notification-kafka-env
        - *payment-env
        - *localhost-url
        - *aws-env
        - *cognito-env
    volumes:
      - ./payment/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    depends_on:
      - postgres
      # - migration
    networks:
      - jobconnect_app
 
  # Celery Flower for schedule service
  celery-flower:
    build:
      context: ./booking/
      dockerfile: Dockerfile
    container_name: celery_flower
    restart: always
    depends_on:
      - redis
      - booking-service
    ports:
      - "5555:5555"
    environment:
      <<:
        - *celery-env
        - *postgres-env
        - *be-api-env
        - *redis-env
        - *minio-env
    volumes:
      - ./booking:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    networks:
      - jobconnect_app
    command: celery -A celery_config.celery_app flower --port=5555 --address=0.0.0.0
 
  blob-storage:
    build:
      context: ./blob-storage/
      dockerfile: Dockerfile
    ports:
      - "8010:8010"
    # env_file:
    #   - .env
    environment:
      <<:
        - *aws-env
        - *localhost-url
    volumes:
      - ./blob-storage/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    networks:
      - jobconnect_app
 
  audit-log:
    build:
      context: ./audit-log/
      dockerfile: Dockerfile
    ports:
      - "8011:8011"
    environment:
      <<:
        - *postgres-env
        - *localhost-url
    volumes:
      - ./audit-log/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    networks:
      - jobconnect_app
 
  broadcast:
    build:
      context: ./broadcast/
      dockerfile: Dockerfile
    ports:
      - "8016:8016"
    environment:
      <<:
        - *postgres-env
        - *notification-kafka-env
        - *localhost-url
    volumes:
      - ./broadcast/:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    networks:
      - jobconnect_app
 
  postgres:
    container_name: postgres
    image: postgis/postgis:14-3.3 # Use the official PostGIS image
    environment:
      <<: *postgres-env
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - jobconnect_app
 
  pgadmin:
    image: dpage/pgadmin4
    ports:
      - "5050:80"
    environment:
      <<: *postgres-env
    networks:
      - jobconnect_app
 
  gateway:
    image: devopsfaith/krakend:watch
    volumes:
      - ./krakend:/etc/krakend
    ports:
      - "8080:8080"
      - "8090:8090"
    command: ["run", "-dc", "krakend.json"]
    networks:
      - jobconnect_app
 
 
  minio:
    image: minio/minio:latest
    container_name: minio
    command: server --console-address ":9001" /data
    ports:
      - "9000:9000"
      - "9001:9001" # MinIO Console
    environment:
      MINIO_ROOT_USER: "${MINIO_ROOT_USER}"
      MINIO_ROOT_PASSWORD: "${MINIO_ROOT_PASSWORD}"
      MINIO_CONSOLE_ADDR: "0.0.0.0:9001"
    restart: always
    volumes:
      - minio_data:/data
    networks:
      - jobconnect_app
  
  chat-service:
    build:
      context: ./chatservice
      dockerfile: Dockerfile
    ports:
      - "8007:8007"
    volumes:
      - ./chatservice:/app
      - ./migration/app/models.py:/app/app/models.py
      - ./migration/app/models_enum.py:/app/app/models_enum.py
    depends_on:
      - kafka
      - redis
      - postgres
    restart: on-failure
    environment:
      <<: 
        - *postgres-env
        - *be-api-env
        - *redis-env
        - *cognito-env
        - *celery-env
        - *aws-env
        - *notification-kafka-env
        - *localhost-url
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092

    networks:
      - jobconnect_app

volumes:
  pgdata:
  redis_data:
  minio_data:
 
networks:
  jobconnect_app:
    driver: bridge