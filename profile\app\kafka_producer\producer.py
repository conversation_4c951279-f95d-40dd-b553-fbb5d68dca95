import logging
import json
from app.kafka_producer.config import kafka_producer_config

log = logging.getLogger("uvicorn")


async def cognito_producer(data):
    encoded_data = json.dumps(data).encode("ascii")
    await kafka_producer_config.send_and_wait("cognito", encoded_data)

async def db_producer(data):
    encoded_data = json.dumps(data).encode("ascii")
    await kafka_producer_config.send_and_wait("db", encoded_data)

async def delete_producer(data):
    encoded_data = json.dumps(data).encode("ascii")
    await kafka_producer_config.send_and_wait("delete", encoded_data)
