from fastapi import APIRouter, Request, Depends
from jose import jwt, JWTError, ExpiredSignatureError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from app.database import get_db
from app.models import UserProfiles, Roles, Permissions
from app.config import get_settings
from app.helper import ErrorResponse, StandardResponse
import boto3

router = APIRouter(tags=["Auth Utilities"])

settings = get_settings()
ALGORITHM = "HS256"

method_to_action = {
    "GET": "can_view",
    "POST": "can_create",
    "PUT": "can_update",
    "PATCH": "can_update",
    "DELETE": "can_delete"
}

cognito_client = boto3.client(
    "cognito-idp",
    region_name=settings.COGNITO_REGION,
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
)

# Helper: Fetch Cognito user attributes and inject `Username` into the result
async def get_user_attributes(username: str):
    user_info = cognito_client.admin_get_user(
        UserPoolId=settings.COGNITO_USER_POOL_ID,
        Username=username
    )
    attributes = {attr['Name']: attr['Value'] for attr in user_info.get('UserAttributes', [])}
    attributes["Username"] = user_info.get("Username")  # Cognito Username, usually phone number
    return attributes

# Helper: Lookup username by sub using Cognito's list_users (if needed)
def get_username_by_sub(sub: str):
    response = cognito_client.list_users(
        UserPoolId=settings.COGNITO_USER_POOL_ID,
        Filter=f'sub = "{sub}"',
        Limit=1
    )
    users = response.get("Users", [])
    if users:
        return users[0]["Username"]
    return None

def infer_policy_name(path: str) -> str:
    if path.startswith("/"):
        path = path[1:]
    if "/" in path:
        path = path.split("/")[0]
    return path.replace("-", " ").title().replace(" ", "")

@router.post("/validate-token")
async def check_permission(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    token = request.headers.get("Authorization")
    if not token:
        return ErrorResponse(401, "Token missing", "Authorization token required")

    token = token.replace("Bearer ", "")
    try:
        # Decode token without verifying (for sub only)
        payload = jwt.decode(token, "dummy-key", options={"verify_signature": False}, algorithms=[ALGORITHM])
        sub = payload.get("sub")
        print("Sub auth.py file:", sub)
        if not sub:
            return ErrorResponse(401, "Invalid token", "No subject (sub) in token")

        # Find actual Cognito Username (usually phone_number)
        # username = get_username_by_sub(sub)
        # if not username:
        #     return ErrorResponse(404, "User not found", "No Cognito user with this sub")

        # Get full attributes from Cognito
        # attributes = await get_user_attributes(username)
        account_id =  sub # attributes.get("custom:account_id") or
        # username = attributes.get("Username")

        # Fetch DB user based on Cognito Username (usually stored in auth_id)
        result = await db.execute(
            select(UserProfiles)
            .options(selectinload(UserProfiles.role).selectinload(Roles.permissions))
            .where(UserProfiles.auth_id == account_id)
        )
        user = result.scalars().first()
        if not user:
            return ErrorResponse(404, "User not found", "Account not found in database")

        if not user.role_id:
            return ErrorResponse(403, "Access denied", "No role assigned to user")

        if user.is_active == False:
            return ErrorResponse(
                status_code=403,
                message="User is disabled in local database",
                error="DISABLED"
            )

        method = request.headers.get("X-Original-Method", "GET")
        path = request.headers.get("X-Original-Path", "/")
        action = method_to_action.get(method.upper())
        policy_name = infer_policy_name(path)

        if not action or not policy_name:
            return ErrorResponse(403, "Permission inference failed", "Invalid method or path")

        perms = await db.execute(select(Permissions).where(Permissions.role_id == user.role_id))
        permission = perms.scalars().first()

        if not permission or not permission.permission.get(action, False):
            return ErrorResponse(403, "Permission denied", f"No permission to {action} on {policy_name}")

        return StandardResponse(
            status_code=200,
            message="Permission granted",
            data={
                "user_id": str(user.id),
                "auth_id": user.auth_id,
                "role_id": str(user.role_id),
                "role_name": user.role.role_name
            }
        )

    except ExpiredSignatureError:
        return ErrorResponse(401, "Token expired", "JWT token has expired")
    except JWTError as e:
        return ErrorResponse(401, "Token error", str(e))
    except Exception as e:
        return ErrorResponse(500, "Internal error", str(e))
