from fastapi import APIRouter, HTTPException, Request
from app.hash import cognito_secret_hash
from app.utils import get_id_header

router = APIRouter()

@router.get("/generate-hash")
async def generate_hash(username: str):
    try:
        secret_hash = cognito_secret_hash(username)
        return {"result": secret_hash}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error: {e}")
    

@router.post("/validate-token")
async def validate_token(request: Request):
    request = await request.json()
    token = request.get('token')
    # print("Received token:", token[:10] + "..." if token else None)  # Only print first 10 chars for security
    try:
        res = await get_id_header(token)
        print("Auth response:", res)  # Debug log
        if isinstance(res, dict) and "error" in res:
            return {"error": res["error"]}
        return res
    except Exception as e:
        # print("Auth error:", str(e))  # Debug log
        return {'error': f"Error: {e}"}