from sqlalchemy.orm import Session
from app.models.agent import Agent
from app.schemas.agent import AgentCreate, AgentUpdate
from typing import List, Optional
from uuid import UUID
from fastapi import HTTPException
from app.utils.enums import Status

def create_agent(db: Session, agent: AgentCreate) -> Agent:
    db_agent = Agent(**agent.model_dump())
    try:
        db.add(db_agent)
        db.commit()
        db.refresh(db_agent)
        return db_agent
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

def get_agent(db: Session, agent_id: UUID) -> Agent:
    agent = db.query(Agent).filter(Agent.id == agent_id).first()
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    return agent

def get_agents(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    is_active: Optional[bool] = None
) -> List[Agent]:
    query = db.query(Agent)
    if is_active is not None:
        query = query.filter(Agent.is_active == is_active)
    return query.offset(skip).limit(limit).all()

def update_agent(db: Session, agent_id: UUID, agent_update: AgentUpdate) -> Agent:
    db_agent = get_agent(db, agent_id)
    update_data = agent_update.model_dump(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_agent, field, value)
    
    try:
        db.commit()
        db.refresh(db_agent)
        return db_agent
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

def delete_agent(db: Session, agent_id: UUID) -> bool:
    db_agent = get_agent(db, agent_id)
    try:
        db.delete(db_agent)
        db.commit()
        return True
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

def update_agent_status(db: Session, agent_id: UUID, status: Status) -> Agent:
    db_agent = get_agent(db, agent_id)
    db_agent.status = status
    try:
        db.commit()
        db.refresh(db_agent)
        return db_agent
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))
