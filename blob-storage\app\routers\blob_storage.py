from fastapi import APIRouter, File, Form, HTTPException, UploadFile
from app import schemas
from app.service import S3Uploader
from app.config import get_settings
from app.helper import ErrorResponse, StandardResponse

router = APIRouter()

s3_uploader = S3Uploader()


@router.post("/initiate-multipart-upload")
def initiate_upload(
    request: schemas.InitiateBlobUploadModel,
):
    try:
        response = s3_uploader.initiate_multipart_upload(
            request.file_name, request.bucket_name
        )
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error: {e}")


@router.get("/generate-presigned-url")
def get_presigned_url(
    request: schemas.GeneratePresignedURLModel,
):
    try:
        response = s3_uploader.generate_presigned_url(
            request.file_name, request.content_type, request.bucket_name
        )
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error: {e}")


@router.post("/complete-multipart-upload")
def complete_upload(
    request: schemas.CompleteBlobUploadModel,
):
    try:
        response = s3_uploader.complete_multipart_upload(
            request.file_name, request.upload_id, request.parts, request.bucket_name
        )
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error: {e}")


@router.post("/check-file-exists")
def check_file_exists(
    request: schemas.CheckFileStatusModel,
):
    try:
        return s3_uploader.check_file_exists(request.file_name, request.bucket_name)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error: {e}")


@router.delete("/delete-file")
def delete_file(
    request: schemas.DeleteFileStatusModel,
):
    try:
        bucket_name = get_settings().S3_BUCKET
        return s3_uploader.delete_file(
            file_name=request.file_path, bucket_name=bucket_name
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error: {e}")


@router.get("/get-presigned-url")
def get_presigned_url_temp(file_path: str):
    try:
        bucket_name = get_settings().S3_BUCKET
        exists = s3_uploader.check_file_exists(
            file_name=file_path, bucket_name=bucket_name
        )
        if exists["exists"] == False:
            return ErrorResponse(status_code=500, message="File doesnt exists")
        response = s3_uploader.get_presigned_url(
            object_key=file_path, bucket_name=bucket_name
        )
        # return response
        return StandardResponse(
            status_code=200, message="Presigned url created", data={"url": response}
        )
    except Exception as e:
        # raise HTTPException(status_code=500, detail=f"Error: {e}")
        return ErrorResponse(status_code=500, message="Error", error=str(e))


@router.post("/upload-file-direct")
async def upload_file_direct(
    file: UploadFile = File(...),
    path: str = Form(...),
):
    try:
        bucket_name = get_settings().S3_BUCKET
        response = await s3_uploader.upload_file_direct(file, bucket_name, path)
        return response
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error: {e}")
