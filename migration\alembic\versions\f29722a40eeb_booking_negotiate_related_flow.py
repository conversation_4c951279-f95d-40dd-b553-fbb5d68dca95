"""booking negotiate related flow

Revision ID: f29722a40eeb
Revises: 846776567c08
Create Date: 2025-06-13 08:46:48.278443

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f29722a40eeb'
down_revision: Union[str, None] = '846776567c08'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('booking_cancellation_reasons', sa.Column('is_negotiate_cancel', sa.<PERSON>(), nullable=True))
    op.add_column('booking_cancellation_reasons', sa.Column('is_notify_cancel', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('booking_cancellation_reasons', 'is_notify_cancel')
    op.drop_column('booking_cancellation_reasons', 'is_negotiate_cancel')
    # ### end Alembic commands ###
