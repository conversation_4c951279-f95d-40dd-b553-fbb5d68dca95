import googlemaps
from decouple import config
from app.redis_config import redis_client

GOOGLE_API_KEY = config("GOOGLE_API_KEY")
gmaps = googlemaps.Client(key=GOOGLE_API_KEY)

def search_artisans(user_latitude, user_longitude, category, radius=5000):
    artisan_keys = redis_client.keys("artisan:*")
    filtered_artisans = []

    for key in artisan_keys:
        artisan_data = redis_client.get(key).split(",")
        lat, lng, skill = float(artisan_data[0]), float(artisan_data[1]), artisan_data[2]

        if skill != category:
            continue

        distance_result = gmaps.distance_matrix((user_latitude, user_longitude), (lat, lng), mode="driving")
        distance_km = distance_result['rows'][0]['elements'][0]['distance']['value'] / 1000

        if distance_km <= (radius / 1000):
            filtered_artisans.append({"id": key.split(":")[1], "lat": lat, "lng": lng, "distance": distance_km})

    return filtered_artisans
