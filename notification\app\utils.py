import asyncio
import os
import uuid
import requests

from app.kafka_producer.config import kafka_producer_config
from app.kafka_consumer.consumer import vector_consumer,consume_vector_process


async def get_id_header(Authorization):
    if not Authorization:
        return {"error": "Token required"}
    try:
        jwt_token = Authorization.split(" ")[1]
        baseurl = os.getenv("BE_AUTH_API_URL")
        response = requests.post(
            f"{baseurl}/validate-token/", json={"token": jwt_token}
        )
        return response
    except Exception as e:
        return {"error": f"Error: {e}"}


def is_valid_uuid(val: str) -> bool:
    try:
        uuid.UUID(val)
        return True
    except ValueError:
        return False


# Common create function using sqlalchemy orm
async def create_record(db, model, request_dict):
    print("calling create record functionnnnnnnn")
    try:
        print("go database: ", request_dict)
        obj = model(**request_dict)
        db.add(obj)
        await db.commit()
        await db.refresh(obj)
        return obj
    except Exception as e:
        await db.rollback()
        error_message = f"Database error: {str(e)}"
        print(error_message, "errrrrrrrrrrrrrrrrrrrrrrrrrrr")
        # raise HTTPException(status_code=500, detail=error_message)
        return error_message


async def update_record(db, data, obj):
    for key, value in data.__dict__.items():
        if value is None or (type(value) == str and len(value) == 0):
            continue
        else:
            setattr(obj, key, value)

    await db.commit()
    await db.refresh(obj)
    return obj


async def delete_record(db, obj):
    await db.delete(obj)
    await db.commit()
    return {"data": "Record deleted successfully"}


async def startup_event():
    """Start up event for FastAPI application."""
    kafka_conn = False
    while not kafka_conn:
        try:
            await kafka_producer_config.start()
            await vector_consumer.start()
            kafka_conn = True
        except:
            pass
    asyncio.create_task(consume_vector_process())


async def shutdown_event():
    # kafka_producer_config = await producer.create_producer()

    """Shutdown event for FastAPI application."""

    await kafka_producer_config.stop()
    await vector_consumer.stop()
