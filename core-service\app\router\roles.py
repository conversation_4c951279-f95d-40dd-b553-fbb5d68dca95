from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import IntegrityError
from sqlalchemy.future import select
from uuid import UUID
from app.database import get_db
from app.models import Roles
from app.schemas import <PERSON><PERSON><PERSON>, RoleOut
from app.utils import create_record, update_record, delete_record
from app.helper import StandardResponse, ErrorResponse

router = APIRouter(prefix="/roles", tags=["Roles"])


@router.post("/", response_model=StandardResponse)
async def create_role(payload: RoleCreate, db: AsyncSession = Depends(get_db)):
    try:
        # Check for duplicates
        existing = await db.execute(select(Roles).where(Roles.role_name == payload.role_name))
        if existing.scalars().first():
            return ErrorResponse(status_code=400, message="Role already exists")

        # Create role
        new_role = await create_record(db, Roles, payload.dict())

        # Eagerly load permissions to avoid async loading during serialization
        result = await db.execute(
            select(Roles).options(selectinload(Roles.permissions)).where(Roles.id == new_role.id)
        )
        role_with_permissions = result.scalars().first()

        # Serialize
        data = RoleOut.model_validate(role_with_permissions, from_attributes=True)

        return StandardResponse(
            status_code=201,
            data=data,
            message="Role created successfully"
        )
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))


@router.get("/", response_model=StandardResponse)
async def get_all_roles(db: AsyncSession = Depends(get_db)):
    try:
        result = await db.execute(
            select(Roles).options(selectinload(Roles.permissions))
        )
        roles = result.scalars().all()

        role_data = [RoleOut.model_validate(role, from_attributes=True) for role in roles]

        return StandardResponse(status_code=200, data=role_data, message="Roles fetched successfully")
    
    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))
    
@router.get("/{role_id}", response_model=StandardResponse)
async def get_role_by_id(role_id: UUID, db: AsyncSession = Depends(get_db)):
    try:
        result = await db.execute(
            select(Roles).options(selectinload(Roles.permissions)).where(Roles.id == role_id)
        )
        role = result.scalars().first()

        if not role:
            return ErrorResponse(status_code=404, message="Role not found")

        role_data = RoleOut.model_validate(role, from_attributes=True)
        return StandardResponse(status_code=200, data=role_data, message="Roles fetched successfully")

    except Exception as e:
        return ErrorResponse(status_code=500, message=str(e))


@router.put("/{role_id}", response_model=StandardResponse) # update API No need 
async def update_role(role_id: UUID, payload: RoleCreate, db: AsyncSession = Depends(get_db)):
    try:
        # Check if role exists
        result = await db.execute(
            select(Roles).options(selectinload(Roles.permissions)).where(Roles.id == role_id)
        )
        role = result.scalars().first()

        if not role:
            return ErrorResponse(status_code=404, message="Role not found")

        for key, value in payload.dict(exclude_unset=True).items():
            setattr(role, key, value)

        await db.commit()
        await db.refresh(role)

        return StandardResponse(
            status_code=200,
            data=RoleOut.model_validate(role, from_attributes=True),
            message="Role updated successfully"
        )
    except IntegrityError as ie:
        await db.rollback()
        if "roles_role_name_key" in str(ie):
            return ErrorResponse(status_code=400, message="Role name already exists.")
        return ErrorResponse(status_code=400, message="Integrity constraint violated.")
    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message=str(e))


@router.delete("/{role_id}", response_model=StandardResponse)
async def delete_role(role_id: UUID, db: AsyncSession = Depends(get_db)):
    try:
        result = await db.execute(select(Roles).where(Roles.id == role_id))
        role = result.scalars().first()

        if not role:
            return ErrorResponse(status_code=404, message="Role not found")

        await db.delete(role)
        await db.commit()

        return StandardResponse(status_code=200, message="Role deleted successfully")

    except Exception as e:
        await db.rollback()
        return ErrorResponse(status_code=500, message=str(e))
