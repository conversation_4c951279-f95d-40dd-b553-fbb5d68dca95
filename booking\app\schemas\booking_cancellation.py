from pydantic import BaseModel
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from app.utils.enums import UserType, BookingCancellationStatus
from fastapi import Form

class BookingCancellationBase(BaseModel):
    user_id: UUID
    artisan_id: UUID
    agent_id: Optional[UUID] = None
    booking_id: UUID
    transaction_id: Optional[UUID] = None
    cancellation_reason_id: Optional[UUID] = None
    user_type: UserType
    status: BookingCancellationStatus = BookingCancellationStatus.INITIATED
    assigned_agent_id: Optional[UUID] = None
    payment_method: Optional[str] = None
    booking_amount: Optional[float] = None
    penalty_user_id: Optional[UUID] = None
    penalty: Optional[float] = None
    penalty_needed: Optional[bool] = None
    refund_needed: Optional[bool] = None
    resolved_at: Optional[datetime] = None
    description: Optional[str] = None
    attachments: Optional[str] = None
    refund_amount: Optional[float] = None
    reason_for_refund: Optional[str] = None
    reason_for_penalty: Optional[str] = None
    is_auto_refund: Optional[bool] = None
    comments: Optional[str] = None
    

class BookingCancellationCreate:
    def __init__(
        self,
        booking_id: UUID = Form(...),
        cancellation_reason_id: Optional[UUID] = Form(None),
        comments: Optional[str] = Form(None),
        user_type: UserType = Form(...),
        is_booking_timeout: Optional[bool] = Form(None),
        agent_mobile: Optional[str] = Form(None),
    ):
        self.booking_id = booking_id
        self.cancellation_reason_id = cancellation_reason_id
        self.user_type = user_type
        self.comments = comments
        self.is_booking_timeout = is_booking_timeout
        self.agent_mobile = agent_mobile

    # booking_id: UUID
    # cancellation_reason_id: Optional[UUID] = None
    # user_type: UserType

class BookingCancellationUpdate(BaseModel):
    assigned_agent_id: Optional[UUID] = None
    cancel_booking: Optional[bool] = None
    status: Optional[str] = None
class BookingDetails(BaseModel):
    id: UUID
    user_id: UUID
    artisan_id: UUID
    service_id: UUID
    description: Optional[str] = None
    status: str
    
    class Config:
        from_attributes = True

class BookingCancellationResponse(BookingCancellationBase):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_active: bool

    class Config:
        from_attributes = True


class ApproveBookingCancellation(BaseModel):
    # status: Optional[BookingCancellationStatus] = BookingCancellationStatus.PENDING
    # reason_for_refund: str
    reason_for_penalty: Optional[str] = None
    # penalty_user_id: Optional[UUID] = None
    penalty: Optional[float] = 0
    # refund_amount: Optional[float] = 0
    # user_type: str
    # resolved_at: Optional[datetime] = datetime.now()

class BookingCancellationFilter(BaseModel):
    skip: int = 1
    limit: int = 10
    search: Optional[str] = None
    booking_id: Optional[str] = None
    user_id: Optional[str] = None
    artisan_id: Optional[str] = None
    status: Optional[str] = None
    assigned_agent_id: Optional[str] = None
