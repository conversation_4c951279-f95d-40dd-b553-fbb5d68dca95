from app.models import Booking, ServiceProvider, ServiceProviderServiceMapping, ServiceProviderLeave
from datetime import datetime, timedelta
from geoalchemy2.functions import ST_DWithin, ST_MakePoint, ST_Point
from sqlalchemy import and_, or_, not_, exists

def get_conflict_artisan_ids(session, request_start: datetime, request_end: datetime, buffer_hours: int = 1):
    buffer = timedelta(hours=buffer_hours)

    # Subquery: find artisans with conflicting bookings (with buffer)
    conflict_subquery = (
        session.query(Booking.artisan_id)
        .filter(
            (Booking.start_time - buffer) < request_end,
            (Booking.end_time + buffer) > request_start
        )
        .all()
    )

    return conflict_subquery

    # # Select artisans with no conflicts
    # available_artisans = (
    #     session.query(Booking.artisan_id)
    #     .distinct()
    #     .filter(~Booking.artisan_id.in_(select(conflict_subquery.c.artisan_id)))
    #     .all()
    # )

    # return [a[0] for a in available_artisans]


from sqlalchemy import func, and_, select, cast, text, Integer
from sqlalchemy.orm import aliased
from sqlalchemy.types import Interval

# With buffer logic
def get_available_artisan_ids(session, request_data, buffer_hours: int = 1):
    print('calling get_available_artisan_ids')
    request_start_dt = datetime.combine(request_data['booking_date'], request_data['start_time'])
    request_end_dt = datetime.combine(request_data['booking_date'], request_data['end_time'])

    print(request_start_dt, "request_start_dt")
    print(request_end_dt, "request_end_dt")

    buffer_interval = f"{buffer_hours} hours"

    B = aliased(Booking)

    # Corrected: cast each EXTRACT to Integer
    booking_start = func.make_timestamp(
        cast(func.extract("year", B.booking_date), Integer),
        cast(func.extract("month", B.booking_date), Integer),
        cast(func.extract("day", B.booking_date), Integer),
        cast(func.extract("hour", B.start_time), Integer),
        cast(func.extract("minute", B.start_time), Integer),
        cast(func.extract("second", B.start_time), Integer),
    )

    booking_end = func.make_timestamp(
        cast(func.extract("year", B.booking_end_date), Integer),
        cast(func.extract("month", B.booking_end_date), Integer),
        cast(func.extract("day", B.booking_end_date), Integer),
        cast(func.extract("hour", B.end_time), Integer),
        cast(func.extract("minute", B.end_time), Integer),
        cast(func.extract("second", B.end_time), Integer),
    )


    print(booking_start, "booking_start")
    print(booking_end, "booking_end")

    
    buffered_start = booking_start - cast(buffer_interval, Interval())
    buffered_end = booking_end + cast(buffer_interval, Interval())

    conflict_subquery = (
        session.query(B.artisan_id)
        .filter(
            # B.status == 'PENDING',
            # B.service_id == '90817e8c-1bb6-4835-b904-76efab9ac69d',
            buffered_start < request_end_dt,
            buffered_end > request_start_dt,
        )
        .subquery()
    )

    print(conflict_subquery, "conflict_subquery")

    # conflict_artisan_ids = (
    #     session.query(ServiceProvider.id)
    #     .filter(ServiceProvider.id.in_(select(conflict_subquery.c.artisan_id)))
    #     .all()
    # )

    # print(conflict_artisan_ids, "available_artisan_ids")

    artisans_query = (
            session.query(
                ServiceProvider,
                func.ST_Distance(
                    ST_Point(ServiceProvider.longitude, ServiceProvider.latitude),
                    ST_MakePoint(request_data['req_long'], request_data['req_lat']),
                ).label("distance_km"),
            )
            .join(
                ServiceProviderServiceMapping,
                ServiceProvider.id == ServiceProviderServiceMapping.service_provider_id,
            )
            .filter(
                ServiceProviderServiceMapping.services_id
                == request_data['service_id'],  # Filter by service
                ServiceProvider.status == "approved",
                ST_DWithin(
                    ST_Point(ServiceProvider.longitude, ServiceProvider.latitude),
                    ST_MakePoint(request_data['req_long'], request_data['req_lat']),
                    request_data['max_distance'] * 1000,  # Convert km to meters
                ),
                cast(request_data['current_weekday'], Integer)
                == func.any(ServiceProvider.weekdays),  # Check weekday availability
                and_(
                    ServiceProvider.work_from_hrs <= request_data['start_time'],
                    ServiceProvider.work_to_hrs >= request_data['end_time'],
                ),
                and_(ServiceProvider.latitude != 0, ServiceProvider.longitude != 0),
                # 1. Check that start time is not within break hours
                or_(
                    ServiceProvider.break_from_hrs.is_(None),  # If no break is set
                    and_(
                        not_(
                            and_(
                                request_data['start_time'] >= ServiceProvider.break_from_hrs,
                                request_data['start_time'] < ServiceProvider.break_to_hrs,
                            )
                        ),
                    ),
                ),
                # Check for leaves
                ~exists().where(
                    and_(
                        ServiceProviderLeave.service_provider_id == ServiceProvider.id,
                        ServiceProviderLeave.leave_date == request_data['booking_date'],
                    )
                ),
                *( [ServiceProvider.id == request_data['service_provider_id']] if request_data['service_provider_id'] else [] ),
                ~ServiceProvider.id.in_(select(conflict_subquery.c.artisan_id))
                # ~exists().where(ServiceProvider.id.in_(select(conflict_subquery.c.artisan_id)))
                # 2. Enhanced booking conflict check with buffer
                # ~exists().where((BookingAlias.start_time - buffer) < end_time,
                #     (BookingAlias.end_time + buffer) > start_time,
                # )
            )
        )

    # return [a[0] for a in available_artisan_ids]
    return artisans_query

from geoalchemy2 import Geography

# Without buffer logic
def get_available_artisans_query(session, request_data):
    print('calling get_available_artisan_ids')
    artisans_query = (
            session.query(
                ServiceProvider
            )
            .join(
                ServiceProviderServiceMapping,
                ServiceProvider.id == ServiceProviderServiceMapping.service_provider_id,
            )
            .filter(
                ServiceProviderServiceMapping.services_id
                == request_data['service_id'],  # Filter by service
                ServiceProvider.status == "approved",
                func.ST_DWithin(
                func.ST_SetSRID(func.ST_MakePoint(ServiceProvider.longitude, ServiceProvider.latitude), 4326).cast(Geography),
                func.ST_SetSRID(func.ST_MakePoint(request_data['req_long'], request_data['req_lat']), 4326).cast(Geography),
                request_data['max_distance'] * 1000  # Convert km to meters
                ),
                cast(request_data['current_weekday'], Integer)
                == func.any(ServiceProvider.weekdays),  # Check weekday availability
                and_(ServiceProvider.latitude != 0, ServiceProvider.longitude != 0),
            )
        )
    return artisans_query