import json
from app.kafka_consumer.config import create_cognito_consumer, create_db_consumer, create_delete_consumer
from app.bulk_upload import process_cognito_creation, process_sp_creation, process_bulk_delete


cognito_consumer = create_cognito_consumer()
db_consumer = create_db_consumer()
delete_consumer = create_delete_consumer()


async def consume_cognito_consumer():
    async for msg in cognito_consumer:
        data = msg.value.decode()
        data_dict = json.loads(data)
        try:
            await process_cognito_creation(data_dict)
        except BaseException as err:
            print(f"Exception caused due to: {err}")


async def consume_db_consumer():
    async for msg in db_consumer:
        data = msg.value.decode()
        data_dict = json.loads(data)
        try:
            await process_sp_creation(data_dict)
        except BaseException as err:
            print(f"Exception caused due to: {err}")


async def consume_delete_consumer():
    async for msg in delete_consumer:
        data = msg.value.decode()
        data_dict = json.loads(data)
        print(f"Data received in delete consumer: {data_dict}")
        try:
            await process_bulk_delete(data_dict)
        except BaseException as err:
            print(f"Exception caused due to: {err}")
