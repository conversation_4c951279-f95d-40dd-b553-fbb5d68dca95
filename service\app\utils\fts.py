from sqlalchemy import text

# Function to return a query string that matches exact whole words (no prefix)
def preprocess_exact_query(query: str) -> str:
    terms = query.strip().split()
    formatted_query = ' | '.join(term.replace("'", "''") for term in terms)
    return formatted_query

# Function to return a query string that matches partial/prefix words using :*
def preprocess_prefix_query(query: str) -> str:
    terms = query.strip().split()
    formatted_terms = [term.replace("'", "''") + ':*' for term in terms]
    return ' | '.join(formatted_terms)

def full_text_search(session, q, skip, limit):
    # query = preprocess_exact_query(q)
    query = preprocess_prefix_query(q)
    print(query, "query")
    sql = text("""
            SELECT *
            FROM services, to_tsquery('english', :query) AS query
            WHERE fts @@ query
            ORDER BY ts_rank(fts, query) DESC
            OFFSET :skip LIMIT :limit;
        """)
    result = session.execute(sql, {"query": query, "skip": skip, "limit": limit})
    return result.mappings().all()
