import pandas as pd
import uuid
import re
from typing import List, Dict, Any, Optional
from app.kafka_producer.producer import db_producer , delete_producer
from app.database import get_db_session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import delete
from app.models import ServiceProvider, ServiceProviderServiceMapping
from app.utils import ServiceProviderStatusType, Gender, generate_cognito_password
from app.cognito_utils import (
    admin_get_user,
    create_cognito_user,
    update_cognito_attributes,
    add_user_to_group,
    delete_cognito_user,
)
from geoalchemy2.functions import ST_Point
import logging
from datetime import time
import os
import requests

logger = logging.getLogger(__name__)


async def get_category_by_name(db: AsyncSession, category_name: str, token: str):
    """
    Query the category table to find a category by name (case-insensitive).
    This function makes a request to the service-management service.
    """
    try:
        # Get service management URL from environment
        service_mgmt_url = os.getenv(
            "BE_SERVICE_API_URL", "http://service-management:8003"
        )

        # Use the simplified endpoint to avoid recursion issues
        response = requests.get(
            f"{service_mgmt_url}/category-list-simple",
            headers={"Authorization": f"Bearer {token}"},
        )

        if response.status_code != 200:
            logger.error(f"Failed to get categories: {response.text}")
            return None

        # Parse the response
        response_data = response.json()
        categories = response_data.get("data", {}).get("data", [])
        
        # Clean and normalize the input category name
        input_name = category_name.lower().strip()
        input_name = re.sub(r'\s+', '', input_name)  # Remove all whitespace
        input_name = re.sub(r'[^a-z0-9]', '', input_name)  # Remove special characters

        # First try exact match
        for category in categories:
            cat_name = str(category.get("name", "")).lower().strip()
            cat_name = re.sub(r'\s+', '', cat_name)  # Remove all whitespace
            cat_name = re.sub(r'[^a-z0-9]', '', cat_name)  # Remove special characters
                        
            if cat_name == input_name:
                return category

        # If no exact match, try partial match
        for category in categories:
            cat_name = str(category.get("name", "")).lower().strip()
            cat_name = re.sub(r'\s+', '', cat_name)  # Remove all whitespace
            cat_name = re.sub(r'[^a-z0-9]', '', cat_name)  # Remove special characters
            
            # Check if input name is contained within category name or vice versa
            if input_name in cat_name or cat_name in input_name:
                print(f"Found partial match for category: {category.get('name')}")  # Debug log
                return category

        # If still no match, try to find the closest match
        closest_match = None
        closest_score = 0
        
        for category in categories:
            cat_name = str(category.get("name", "")).lower().strip()
            cat_name = re.sub(r'\s+', '', cat_name)  # Remove all whitespace
            cat_name = re.sub(r'[^a-z0-9]', '', cat_name)  # Remove special characters
            
            # Calculate similarity score
            score = sum(1 for a, b in zip(input_name, cat_name) if a == b)
            if score > closest_score:
                closest_score = score
                closest_match = category
        
        if closest_match and closest_score > 0:
            print(f"Found closest match for category: {closest_match.get('name')} with score {closest_score}")  # Debug log
            return closest_match

        return None
    except Exception as e:
        logger.error(f"Error getting category by name: {e}")
        import traceback
        print(traceback.format_exc())  # Print full traceback
        return None


async def get_services_by_category_id(db: AsyncSession, category_id: str, token: str):
    """
    Get all services that belong to a specific category.
    This function makes a request to the service-management service.
    """
    try:
        # Get service management URL from environment
        service_mgmt_url = os.getenv(
            "BE_SERVICE_API_URL", "http://service-management:8003"
        )

        # Use the simplified endpoint to avoid recursion issues
        response = requests.get(
            f"{service_mgmt_url}/services-list-simple?parent_id={category_id}",
            headers={"Authorization": f"Bearer {token}"},
        )

        if response.status_code != 200:
            logger.error(f"Failed to get services: {response.text}")
            return []

        # Parse the response
        response_data = response.json()
        services = response_data.get("data", {}).get("data", [])
        return services
    except Exception as e:
        logger.error(f"Error getting services by category ID: {e}")
        return []


def geocode_location(district: str, region: str) -> tuple:
    """
    Use Google Maps Geocoding API to get latitude and longitude for a location.

    Args:
        district: The district or town
        region: The region or province

    Returns:
        Tuple of (latitude, longitude)
    """
    try:
        # Get Google Maps API key from environment
        api_key = os.getenv("GOOGLE_API_KEY")

        if not api_key:
            logger.warning("Google Maps API key not found in environment variables")
            return (0.0, 0.0)  # Default coordinates if API key is missing

        # Format the address for geocoding
        address = f"{district}, {region}"

        # Make request to Google Maps Geocoding API
        url = f"https://maps.googleapis.com/maps/api/geocode/json?address={address}&key={api_key}"
        response = requests.get(url)

        if response.status_code == 200:
            data = response.json()

            if data["status"] == "OK" and len(data["results"]) > 0:
                location = data["results"][0]["geometry"]["location"]
                return (location["lat"], location["lng"])

        logger.warning(f"Failed to geocode address: {address}")
        return (0.0, 0.0)  # Default coordinates if geocoding fails

    except Exception as e:
        logger.error(f"Error geocoding location: {e}")
        return (0.0, 0.0)  # Default coordinates on error


def format_phone_number(phone_number: str, country_code: str = "233") -> str:
    """
    Format a phone number to ensure it has the proper country code.

    Args:
        phone_number: The raw phone number from the Excel file
        country_code: The country ISD code (default: 233 for Ghana)

    Returns:
        Properly formatted phone number with country code
    """
    # Remove any non-digit characters
    phone_number = re.sub(r"\D", "", phone_number)

    # If it already has a plus sign, it's likely already formatted
    if phone_number.startswith("+"):
        return phone_number

    # If it starts with the country code without plus, add the plus
    if phone_number.startswith(country_code):
        return f"+{phone_number}"

    # If it starts with a leading zero, remove it and add country code
    if phone_number.startswith("0"):
        return f"+{country_code}{phone_number[1:]}"

    # Otherwise, just add the country code
    return f"+{country_code}{phone_number}"


async def process_excel_file(
    db: AsyncSession, file_path: str, token: str
) -> Dict[str, Any]:
    """
    Process the Excel file and create service providers.

    Returns:
        Dict with success count, error count, and error details
    """
    try:
        # Read Excel file
        df = pd.read_excel(file_path)
        print(f"Read Excel file with {len(df)} rows")  # Debug log

        # Initialize counters and error tracking
        total_rows = len(df)
        success_count = 0
        skipped_count = 0
        error_count = 0
        error_details = []
        skipped_details = []

        # Validate required columns
        required_columns = [
            "S/N",
            "Trainee Reg. Code",
            "Trade Area(s)",
            "Trainee First Name",
            "Trainee Surname",
            "Other names of Trainee",
            "Region",
            "District/Town",
            "Trainee Contact Number"
        ]

        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return {
                "success": False,
                "message": f"Missing required columns: {', '.join(missing_columns)}",
                "created": 0,
                "total": total_rows,
                "skipped": 0,
                "errors": [{"error": f"Missing columns: {', '.join(missing_columns)}"}],
                "skipped_details": []
            }

        # Check if latitude and longitude columns exist
        has_coordinates = "Latitude" in df.columns and "Longitude" in df.columns

        # Process each row
        for index, row in df.iterrows():
            try:
                print(f"Processing row {index + 2}")  # Debug log
                # Extract data from row
                reg_code = str(row["Trainee Reg. Code"]).strip()
                surname = str(row["Trainee Surname"]).strip()
                first_name = str(row["Trainee First Name"]).strip()
                other_names = str(row["Other names of Trainee"]).strip() if pd.notna(row["Other names of Trainee"]) else ""
                trade_area = str(row["Trade Area(s)"]).strip()
                region = str(row["Region"]).strip()
                district = str(row["District/Town"]).strip()

                # Validate required fields
                if not all([reg_code, surname, first_name, trade_area, region, district]):
                    error_details.append({
                        "row": index + 2,
                        "error": "Missing required fields",
                        "details": {
                            "reg_code": reg_code,
                            "surname": surname,
                            "first_name": first_name,
                            "trade_area": trade_area,
                            "region": region,
                            "district": district
                        }
                    })
                    error_count += 1
                    continue

                # Get coordinates either from Excel or geocode them
                if has_coordinates:
                    try:
                        latitude = float(row["Latitude"])
                        longitude = float(row["Longitude"])
                    except (ValueError, TypeError):
                        # If coordinates in Excel are invalid, geocode them
                        latitude, longitude = geocode_location(district, region)
                else:
                    # Geocode the location if coordinates aren't provided
                    latitude, longitude = geocode_location(district, region)

                # Format phone number
                phone_number = str(row["Trainee Contact Number"]).strip()
                phone_number = format_phone_number(phone_number)

                # Find category by trade area name
                category = await get_category_by_name(db, trade_area, token)
                print(f"Found category: {category}")  # Debug log

                if not category:
                    error_details.append({
                        "row": index + 2,
                        "error": f"Category not found for trade area: {trade_area}",
                        "details": {"trade_area": trade_area}
                    })
                    error_count += 1
                    continue

                # Create service provider in Cognito
                try:
                    cognito_exists_check = admin_get_user(phone_number)
                    print(f"Cognito check result: {cognito_exists_check}")  # Debug log
                    
                    if cognito_exists_check is not None:
                        skipped_details.append({
                            "row": index + 2,
                            "reason": "User already exists in Cognito",
                            "details": {"phone_number": phone_number}
                        })
                        skipped_count += 1
                        continue

                    auth_response = create_cognito_user(
                        phone_number, generate_cognito_password(), ""
                    )
                    print(f"Created Cognito user: {auth_response}")  # Debug log
                    auth_id = auth_response.get("User", {}).get("Username", "")
                    print(f"Auth ID: {auth_id}")  # Debug log
                    
                    # Add user to the "artisan" group in Cognito
                    add_user_to_group(auth_id, "artisan")

                except Exception as e:
                    print(f"Error creating Cognito user: {str(e)}")  # Debug log
                    error_details.append({
                        "row": index + 2,
                        "error": f"Failed to create Cognito user: {str(e)}",
                        "details": {"phone_number": phone_number}
                    })
                    error_count += 1
                    continue

                # Set default working hours (9 AM to 7 PM)
                work_from_hrs = time(9, 0)  # 9:00 AM
                work_to_hrs = time(19, 0)  # 7:00 PM

                # Set default weekdays (1-7, Monday to Sunday)
                weekdays = [1, 2, 3, 4, 5, 6, 7]

                # Combine first name and other names if available
                full_first_name = f"{first_name} {other_names}".strip()

                sp_data = {
                    "auth_id": auth_id,
                    "first_name": full_first_name,
                    "last_name": surname,
                    "email": None,
                    "phone_number": phone_number,
                    "primary_location": f"{district}, {region}",
                    "status": ServiceProviderStatusType.APPROVED,
                    "gender": Gender.MALE,  # Default value, can be updated later
                    "latitude": latitude,
                    "longitude": longitude,
                    "location": ST_Point(longitude, latitude),
                    "reg_code": reg_code,
                    "notification_uuid": str(uuid.uuid4()),
                    "skill": trade_area,
                    "skill_level": "Trainee",
                    "work_from_hrs": work_from_hrs,
                    "work_to_hrs": work_to_hrs,
                    "weekdays": weekdays,
                }

                print(f"Creating service provider with data: {sp_data}")  # Debug log
                # Create service provider record
                sp_obj = ServiceProvider(**sp_data)
                db.add(sp_obj)
                await db.flush()
                print(f"Created service provider with ID: {sp_obj.id}")  # Debug log

                # Get services for the category
                services = await get_services_by_category_id(db, category["id"], token)
                print(f"Found {len(services)} services for category")  # Debug log

                # Create service mappings
                for service in services:
                    service_mapping = ServiceProviderServiceMapping(
                        id=uuid.uuid4(),
                        service_provider_id=sp_obj.id,
                        services_id=uuid.UUID(service["id"]),
                    )
                    db.add(service_mapping)

                # Update Cognito with account ID
                await update_cognito_attributes(
                    auth_id, {"custom:account_id": str(sp_obj.id)}, False
                )

                success_count += 1
                print(f"Successfully processed row {index + 2}")  # Debug log

            except Exception as e:
                print(f"Error processing row {index + 2}: {str(e)}")  # Debug log
                import traceback
                print(traceback.format_exc())  # Print full traceback
                error_details.append({
                    "row": index + 2,
                    "error": str(e),
                    "details": {"traceback": traceback.format_exc()}
                })
                error_count += 1

        # Commit all changes
        await db.commit()
        print(f"Committed {success_count} successful records")  # Debug log

        return {
            "success": True,
            "message": f"Processed {total_rows} rows with {success_count} successful creations",
            "total": total_rows,
            "created": success_count,
            "skipped": skipped_count,
            "errors": error_count,
            "error_details": error_details,
            "skipped_details": skipped_details
        }

    except Exception as e:
        await db.rollback()
        print(f"Error processing Excel file: {str(e)}")  # Debug log
        import traceback
        print(traceback.format_exc())  # Print full traceback
        return {
            "success": False,
            "message": f"Failed to process Excel file: {str(e)}",
            "total": total_rows,
            "created": 0,
            "skipped": 0,
            "errors": 1,
            "error_details": [{"error": str(e), "details": {"traceback": traceback.format_exc()}}],
            "skipped_details": []
        }
    

async def process_cognito_creation(data):
    db = await get_db_session()
    """
    Process the Excel file and create service providers.

    Returns:
        Dict with success count, error count, and error details
    """
    try:
        token = data.pop("token")
        # Check if latitude and longitude columns exist
        if data.get("Latitude") and data.get("Longitude"):
            try:
                latitude = float(data["Latitude"])
                longitude = float(data["Longitude"])
            except (ValueError, TypeError):
                # If coordinates in Excel are invalid, geocode them
                latitude, longitude = geocode_location(data['District/Town'], data['Region'])
        else:
            latitude, longitude = geocode_location(data['District/Town'], data['Region'])
        
        try:
            # Extract data from row
            reg_code = str(data["Trainee Reg. Code"]).strip()
            surname = str(data["Trainee Surname"]).strip()
            first_name = str(data["Trainee First Name"]).strip()
            other_names = str(data["Other names of Trainee"]).strip() if pd.notna(data["Other names of Trainee"]) else ""
            trade_area = str(data["Trade Area(s)"]).strip()
            region = str(data["Region"]).strip()
            district = str(data["District/Town"]).strip()

            # Format phone number
            phone_number = str(data["Trainee Contact Number"]).strip()
            phone_number = format_phone_number(phone_number)

            # Find category by trade area name
            category = await get_category_by_name(db, trade_area, token)
            print(f"Found category: {category}")  # Debug log

            if not category:
                print(f"Category not found for trade area: {trade_area}")  # Debug log
                return None

            # Create service provider in Cognito
            try:
                cognito_exists_check = admin_get_user(phone_number)
                
                if cognito_exists_check is not None:
                    print(f"User already exists in Cognito: {phone_number}")  # Debug log
                    return None

                auth_response = create_cognito_user(
                    phone_number, generate_cognito_password(), ""
                )
                print(f"Created Cognito user: {auth_response}")  # Debug log
                auth_id = auth_response.get("User", {}).get("Username", "")
                
                # Add user to the "artisan" group in Cognito
                add_user_to_group(auth_id, "artisan")

                # Process cognito user to kafka topic
                sp_data = {
                    'first_name': first_name,
                    'surname': surname,
                    'other_names': other_names,
                    'phone_number': phone_number,
                    'auth_id': auth_id,
                    'latitude': latitude,
                    'longitude': longitude,
                    'reg_code': reg_code,
                    'trade_area': trade_area,
                    'region': region,
                    'district': district,
                    'token': token,
                    'category_id': category["id"]
                }
                await db_producer(sp_data)
            except Exception as e:
                print(f"Error creating Cognito user: {str(e)}")  # Debug log
                return None
        except Exception as e:
            print(f"Error processing row: {str(e)}")  # Debug log
            return None
    except Exception as e:
        print(f"Error processing Excel file: {str(e)}")  # Debug log
        return None
    finally:
        await db.close()
            

async def process_sp_creation(data):
    db = await get_db_session()
    try:
        token = data.pop("token")
        category_id = data.pop("category_id")
        
        # Set default working hours (9 AM to 7 PM)
        work_from_hrs = time(9, 0)  # 9:00 AM
        work_to_hrs = time(19, 0)  # 7:00 PM

        # Set default weekdays (1-7, Monday to Sunday)
        weekdays = [1, 2, 3, 4, 5, 6, 7]

        # Combine first name and other names if available
        full_first_name = f"{data.get('first_name', '')} {data.get('other_names', '')}".strip()

        sp_data = {
            "auth_id": data["auth_id"],
            "first_name": full_first_name,
            "last_name": data["surname"],
            "email": None,
            "phone_number": data["phone_number"],
            "primary_location": f"{data['district']}, {data['region']}",
            "status": ServiceProviderStatusType.APPROVED,
            "gender": Gender.MALE,  # Default value, can be updated later
            "latitude": data['latitude'],
            "longitude": data['longitude'],
            "location": ST_Point(data['longitude'], data['latitude']),
            "reg_code": data['reg_code'],
            "notification_uuid": str(uuid.uuid4()),
            "skill": data['trade_area'],
            "skill_level": "Trainee",
            "work_from_hrs": work_from_hrs,
            "work_to_hrs": work_to_hrs,
            "weekdays": weekdays,
            "is_profile_complete": True,
        }
        # Create service provider record
        sp_obj = ServiceProvider(**sp_data)
        db.add(sp_obj)
        await db.flush()
        print(f"Created service provider with ID: {data['phone_number']}")  # Debug log

        # Get services for the category
        services = await get_services_by_category_id(db, category_id, token)
        print(f"Found {len(services)} services for category")  # Debug log

        # Create service mappings
        for service in services:
            service_mapping = ServiceProviderServiceMapping(
                id=uuid.uuid4(),
                service_provider_id=sp_obj.id,
                services_id=uuid.UUID(service["id"]),
            )
            db.add(service_mapping)

        # Update Cognito with account ID
        await update_cognito_attributes(
            data["auth_id"], {"custom:account_id": str(sp_obj.id)}, False
        )
        # Commit all changes
        await db.commit()
    except Exception as e:
        print(f"Error processing {data['phone_number']}: {str(e)}")  # Debug log
    finally:
        await db.close()


async def process_bulk_delete(data):
    """
    Process bulk deletion of service providers from Cognito and database.
    This function will be called by Kafka consumer.
    """
    db = await get_db_session()
    try:
        phone_number = data.get('phone_number')
        auth_id = data.get('auth_id')
        account_id = data.get('account_id')
        source = data.get('source', 'cognito')  # Default to 'cognito' for backward compatibility
        
        # Delete from Cognito only if we have an auth_id and source is cognito
        if source == 'cognito' and auth_id:
            try:
                await delete_cognito_user(auth_id)
                logger.info(f"Successfully deleted from Cognito: {phone_number}")
            except Exception as e:
                logger.error(f"Error deleting from Cognito: {str(e)}")
                # Continue with database deletion even if Cognito deletion fails

        # Delete from database
        try:
            if account_id:
                # Delete service mappings first
                await db.execute(
                    delete(ServiceProviderServiceMapping)
                    .where(ServiceProviderServiceMapping.service_provider_id == account_id)
                )
                
                # Delete service provider
                await db.execute(
                    delete(ServiceProvider)
                    .where(ServiceProvider.id == account_id)
                )
                
                await db.commit()
                logger.info(f"Successfully deleted service provider from database: {phone_number}")
            else:
                logger.warning(f"No account_id provided for database deletion: {phone_number}")
                
        except Exception as e:
            logger.error(f"Error deleting from database: {str(e)}")
            await db.rollback()
            return None
            
    except Exception as e:
        logger.error(f"Error in bulk delete process: {str(e)}")
        return None
    finally:
        await db.close()


async def process_delete_df(df, token):
    """
    Process DataFrame for bulk deletion.
    """
    db = await get_db_session()
    df = df.fillna("")
    for index, row in df.iterrows():
        try:
            phone_number = str(row["Trainee Contact Number"]).strip()
            phone_number = format_phone_number(phone_number)
            logger.info(f"Processing deletion for phone number: {phone_number}")
            
            # Try to get user from Cognito
            auth_id = None
            try:
                logger.info(f"Attempting to get user from Cognito for phone: {phone_number}")
                user = admin_get_user(phone_number)
                
                if user:
                    logger.info(f"User found in Cognito: {user}")
                    auth_id = user.get('Username')
                    account_id = user.get('Attributes', {}).get('custom:account_id')
                    
                    if auth_id:
                        # Send Cognito deletion to Kafka
                        data_dict = {
                            'phone_number': phone_number,
                            'auth_id': auth_id,
                            'account_id': account_id,
                            'token': token,
                            'source': 'cognito'
                        }
                        logger.info(f"Sending Cognito deletion data to Kafka: {data_dict}")
                        await delete_producer(data_dict)
                else:
                    logger.warning(f"No user found in Cognito for phone: {phone_number}")
                
            except Exception as cognito_error:
                logger.error(f"Error getting user from Cognito: {str(cognito_error)}")
            
            # Try to find in database
            try:
                logger.info(f"Checking database for phone: {phone_number}")
                query = select(ServiceProvider).where(ServiceProvider.phone_number == phone_number)
                result = await db.execute(query)
                sp_obj = result.scalars().first()
                
                if sp_obj:
                    logger.info(f"Service provider found in database: {sp_obj.id}")
                    # Send database deletion to Kafka
                    data_dict = {
                        'phone_number': phone_number,
                        'auth_id': auth_id,  # May be None if not found in Cognito
                        'account_id': str(sp_obj.id),
                        'token': token,
                        'source': 'database'
                    }
                    logger.info(f"Sending database deletion data to Kafka: {data_dict}")
                    await delete_producer(data_dict)
                else:
                    logger.warning(f"No service provider found in database for phone: {phone_number}")
                    
            except Exception as db_error:
                logger.error(f"Error checking database: {str(db_error)}")
                
        except Exception as e:
            logger.error(f"Error processing row {index}: {str(e)}")
            continue
            
    await db.close()
    return

