import boto3
from fastapi import UploadFile
from app.config import get_settings
from io import BytesIO


class S3Uploader:
    def __init__(self):
        print(
            get_settings().S3_ACCESS_KEY_ID,
            get_settings().S3_SECRET_ACCESS_KEY,
            get_settings().S3_REGION,
            "s333333333",
        )
        self.s3_client = boto3.client(
            "s3",
            aws_access_key_id=get_settings().S3_ACCESS_KEY_ID,
            aws_secret_access_key=get_settings().S3_SECRET_ACCESS_KEY,
            region_name=get_settings().S3_REGION,
        )

    def generate_presigned_url(
        self,
        file_name: str,
        content_type: str,
        bucket_name: str,
        expiration: int = 3600,
    ):
        response = self.s3_client.generate_presigned_url(
            "put_object",
            Params={
                "Bucket": bucket_name,
                "Key": file_name,
                # "ContentType": content_type,
            },
            ExpiresIn=expiration,
        )
        file_url = f"https://{get_settings().S3_BUCKET}.s3.{get_settings().S3_REGION}.amazonaws.com/{file_name}"
        return {"presigned_url": file_url, "method": "PUT", "data": response}

    def initiate_multipart_upload(
        self,
        file_name: str,
        bucket_name: str,
    ):
        response = self.s3_client.create_multipart_upload(
            Bucket=bucket_name, Key=file_name
        )
        return response

    def complete_multipart_upload(
        self,
        file_name: str,
        upload_id: str,
        parts: list,
        bucket_name: str,
    ):
        response = self.s3_client.complete_multipart_upload(
            Bucket=bucket_name,
            Key=file_name,
            UploadId=upload_id,
            MultipartUpload={"Parts": parts},
        )
        file_url = f"https://{get_settings().S3_BUCKET}.s3.{get_settings().S3_REGION}.amazonaws.com/{file_name}"
        return {
            "message": "Upload completed successfully",
            "file_url": file_url,
            "data": response,
        }

    def check_file_exists(
        self,
        file_name: str,
        bucket_name: str,
    ):
        try:
            self.s3_client.head_object(Bucket=bucket_name, Key=file_name)
            return {"exists": True}
        except self.s3_client.exceptions.ClientError:
            return {"exists": False}

    def delete_file(
        self,
        file_name: str,
        bucket_name: str,
    ):
        self.s3_client.delete_object(Bucket=bucket_name, Key=file_name)
        return {"message": "File deleted successfully"}

    def get_presigned_url(self, bucket_name, object_key, expiration=3600):
        # Generate a presigned URL to share the S3 object
        presigned_url = self.s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": bucket_name, "Key": object_key},
            ExpiresIn=expiration,
        )
        print("Presigned URL:", presigned_url)
        return presigned_url
        
    async def upload_file_direct(
        self,
        file: UploadFile,
        bucket_name: str,
        file_path: str,
    ):
        # self.s3_client.upload_file(file, bucket_name, file_path)
        # Upload file to S3
        # response = self.s3_client.generate_presigned_url(
        #     "put_object",
        #     Params={
        #         "Bucket": bucket_name,
        #         "Key": file_path,
        #     },
        #     ExpiresIn=3600,
        # )
        try:
            key = f"{file_path}/{file.filename}"
            file_content = await file.read()
            
            # Determine content type based on file extension
            content_type = "application/octet-stream"  # Default content type
            if file.filename.lower().endswith(('.jpg', '.jpeg')):
                content_type = "image/jpeg"
            elif file.filename.lower().endswith('.png'):
                content_type = "image/png"
            elif file.filename.lower().endswith('.pdf'):
                content_type = "application/pdf"
            elif file.filename.lower().endswith('.doc'):
                content_type = "application/msword"
            elif file.filename.lower().endswith('.docx'):
                content_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            
            self.s3_client.put_object(
                Bucket=bucket_name,
                Key=key,
                Body=BytesIO(file_content),
                ContentType=content_type,
            )
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(e)
            return {"message": "Error uploading file"}

        # response = self.generate_presigned_url(
        #     file_name=key, content_type="", bucket_name=bucket_name
        # )
        # response = self.get_presigned_url(bucket_name=bucket_name,object_key=key)
        # file_url = f"https://{get_settings().S3_BUCKET}.s3.{get_settings().S3_REGION}.amazonaws.com/{key}"
        print(key, "keyyyyyyyyyyyyyyyyyyy")

        return {"filename": key}
